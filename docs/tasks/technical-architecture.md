# QuikSkope Technical Architecture Framework

## Overview

This document defines the comprehensive technical architecture for QuikSkope using Supabase as the primary backend infrastructure. The architecture is designed to support a secure logistics verification platform with real-time capabilities, mobile-first design, and agent-friendly development patterns.

## Supabase Backend Architecture

### Database Schema Design

#### Core Tables

**users** (Supabase Auth Extended)
```sql
- id (uuid, primary key, references auth.users)
- user_type (enum: 'driver', 'organization_member', 'admin')
- profile_data (jsonb)
- verification_status (enum: 'unverified', 'pending', 'verified', 'rejected')
- created_at (timestamp)
- updated_at (timestamp)
```

**organizations**
```sql
- id (uuid, primary key)
- name (text)
- organization_type (enum: 'broker', 'shipper', 'carrier', 'logistics_company')
- settings (jsonb)
- subscription_tier (enum: 'basic', 'professional', 'enterprise')
- created_at (timestamp)
- updated_at (timestamp)
```

**organization_members**
```sql
- id (uuid, primary key)
- organization_id (uuid, foreign key)
- user_id (uuid, foreign key)
- role (enum: 'admin', 'manager', 'operator', 'viewer')
- permissions (jsonb)
- created_at (timestamp)
```

**drivers**
```sql
- id (uuid, primary key)
- user_id (uuid, foreign key, unique)
- cdl_number (text, encrypted)
- mc_number (text)
- vehicle_info (jsonb)
- verification_documents (jsonb)
- earnings_data (jsonb)
- location_data (jsonb)
- created_at (timestamp)
- updated_at (timestamp)
```

**shipments**
```sql
- id (uuid, primary key)
- organization_id (uuid, foreign key)
- driver_id (uuid, foreign key, nullable)
- origin (jsonb) -- address, coordinates, geofence
- destination (jsonb)
- cargo_details (jsonb)
- status (enum: 'created', 'assigned', 'in_transit', 'delivered', 'cancelled')
- integrity_protocol_data (jsonb)
- tracking_data (jsonb)
- created_at (timestamp)
- updated_at (timestamp)
```

**verification_records**
```sql
- id (uuid, primary key)
- shipment_id (uuid, foreign key)
- driver_id (uuid, foreign key)
- verification_type (enum: 'pickup', 'delivery', 'checkpoint')
- location_data (jsonb)
- photo_evidence (jsonb)
- ai_analysis_results (jsonb)
- status (enum: 'pending', 'approved', 'rejected')
- created_at (timestamp)
```

**documents**
```sql
- id (uuid, primary key)
- owner_id (uuid, foreign key)
- owner_type (enum: 'driver', 'organization')
- document_type (enum: 'cdl', 'medical_card', 'insurance', 'registration', 'bol')
- file_path (text)
- metadata (jsonb)
- ai_extracted_data (jsonb)
- verification_status (enum: 'pending', 'verified', 'rejected')
- created_at (timestamp)
```

**payments**
```sql
- id (uuid, primary key)
- user_id (uuid, foreign key)
- stripe_payment_intent_id (text)
- amount (integer) -- in cents
- currency (text, default 'usd')
- payment_type (enum: 'verification_fee', 'subscription', 'transaction_fee')
- status (enum: 'pending', 'succeeded', 'failed', 'cancelled')
- created_at (timestamp)
```

### Row Level Security (RLS) Policies

#### Users Table
```sql
-- Users can only read/update their own profile
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);
```

#### Organizations Table
```sql
-- Organization members can view their organization
CREATE POLICY "Members can view organization" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );
```

#### Shipments Table
```sql
-- Drivers can view assigned shipments
CREATE POLICY "Drivers can view assigned shipments" ON shipments
  FOR SELECT USING (driver_id = auth.uid());

-- Organization members can view their organization's shipments
CREATE POLICY "Organization members can view shipments" ON shipments
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );
```

### Supabase Edge Functions

#### Authentication & User Management

**`create-user-profile`**
- Triggered on user signup
- Creates appropriate profile (driver or organization member)
- Sets up initial permissions and settings
- Sends welcome email

**`verify-driver-credentials`**
- Processes driver verification documents
- Integrates with DMV/DOT databases
- Updates verification status
- Triggers payment flow for successful verification

#### Document Processing

**`process-document-upload`**
- Handles document uploads to Supabase Storage
- Extracts metadata and EXIF data
- Triggers AI analysis for document validation
- Updates document verification status

**`ai-document-analysis`**
- Integrates with AI services for document validation
- Extracts text and data from documents
- Validates document authenticity and currency
- Returns structured analysis results

#### Integrity Protocol

**`initiate-verification`**
- Starts the Integrity Protocol verification process
- Validates driver location and proximity
- Creates verification record
- Sends notifications to relevant parties

**`process-photo-evidence`**
- Analyzes submitted photo evidence
- Validates EXIF data and image authenticity
- Performs AI-powered image analysis
- Updates verification status

**`generate-pickup-code`**
- Creates secure pickup codes after verification
- Implements time-based expiration
- Sends code to verified driver
- Logs code generation for audit trail

#### Payment Processing

**`process-verification-payment`**
- Handles Stripe payment processing
- Updates user verification status on successful payment
- Manages payment failures and retries
- Sends payment confirmation notifications

**`manage-subscriptions`**
- Handles organization subscription management
- Processes recurring billing
- Manages subscription upgrades/downgrades
- Handles subscription cancellations

#### Real-Time Features

**`location-tracking`**
- Processes real-time location updates
- Updates shipment tracking data
- Triggers geofence alerts
- Manages location history

**`notification-service`**
- Sends push notifications to mobile apps
- Manages email notifications
- Handles SMS alerts for critical events
- Manages notification preferences

### Real-Time Subscriptions

#### Driver Location Tracking
```javascript
// Subscribe to driver location updates
const subscription = supabase
  .channel('driver-location')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'drivers',
    filter: `id=eq.${driverId}`
  }, (payload) => {
    updateDriverLocation(payload.new.location_data);
  })
  .subscribe();
```

#### Shipment Status Updates
```javascript
// Subscribe to shipment status changes
const subscription = supabase
  .channel('shipment-updates')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'shipments',
    filter: `organization_id=eq.${organizationId}`
  }, (payload) => {
    updateShipmentStatus(payload.new);
  })
  .subscribe();
```

#### Verification Process Updates
```javascript
// Subscribe to verification status changes
const subscription = supabase
  .channel('verification-updates')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'verification_records',
    filter: `driver_id=eq.${driverId}`
  }, (payload) => {
    updateVerificationStatus(payload);
  })
  .subscribe();
```

## Frontend Architecture

### Supabase Client Integration

#### Authentication Setup
```javascript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

#### Database Operations
```javascript
// Direct database operations from frontend
const { data: shipments, error } = await supabase
  .from('shipments')
  .select('*')
  .eq('organization_id', organizationId)
  .order('created_at', { ascending: false });
```

#### File Upload to Storage
```javascript
// Direct file upload to Supabase Storage
const { data, error } = await supabase.storage
  .from('documents')
  .upload(`${userId}/${documentType}/${fileName}`, file);
```

### Mobile App Architecture

#### React Native with Supabase
```javascript
// Mobile-specific Supabase configuration
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
```

#### Offline Capabilities
```javascript
// Offline data management
import { SQLiteAdapter } from '@supabase/sqlite-adapter';

// Cache critical data locally
const cacheShipmentData = async (shipments) => {
  await AsyncStorage.setItem('cached_shipments', JSON.stringify(shipments));
};

// Sync when connection restored
const syncOfflineData = async () => {
  const offlineActions = await AsyncStorage.getItem('offline_actions');
  if (offlineActions) {
    // Process queued actions
    await processOfflineActions(JSON.parse(offlineActions));
  }
};
```

## Security Architecture

### Data Encryption
- All sensitive data encrypted at rest using Supabase encryption
- Transport layer security with TLS 1.3
- Client-side encryption for highly sensitive data (CDL numbers, SSNs)

### Authentication & Authorization
- Supabase Auth with JWT tokens
- Role-based access control (RBAC)
- Multi-factor authentication for sensitive operations
- Session management with automatic refresh

### API Security
- Rate limiting on Edge Functions
- Input validation and sanitization
- SQL injection prevention through parameterized queries
- CORS configuration for frontend domains

## Development Workflow

### Agent-Friendly Patterns

#### Database Task Template
```markdown
**Task**: Create [table_name] table with RLS policies
**Inputs**: Table schema definition, security requirements
**Outputs**: SQL migration file, RLS policies, test data
**Acceptance Criteria**: 
- Table created with proper constraints
- RLS policies enforce security requirements
- Test data validates functionality
**Estimated Time**: 20 minutes
```

#### Edge Function Task Template
```markdown
**Task**: Implement [function_name] Edge Function
**Inputs**: Function specification, API requirements
**Outputs**: TypeScript function, error handling, tests
**Acceptance Criteria**:
- Function handles all specified use cases
- Proper error handling and logging
- Unit tests achieve 90% coverage
**Estimated Time**: 20 minutes
```

### Testing Strategy
- Unit tests for Edge Functions using Deno test framework
- Integration tests for database operations
- End-to-end tests for critical user flows
- Performance testing for real-time features
- Security testing for authentication and authorization

---

*This technical architecture provides the foundation for implementing QuikSkope using Supabase as the primary backend infrastructure, designed for agent-friendly development and scalable operations.*
