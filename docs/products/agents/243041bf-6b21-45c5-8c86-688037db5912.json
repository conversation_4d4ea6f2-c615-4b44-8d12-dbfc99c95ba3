{"name": "Customer Support Agent", "description": "AI-powered customer support agent that manages ticketing system, resolves non-critical issues, and provides comprehensive assistance across the QuikSkope platform with intelligent escalation to human agents when needed", "type": "Assistant", "capabilities": ["Ticket creation, routing, and prioritization", "Non-critical issue resolution and troubleshooting", "FAQ answering and knowledge base assistance", "Platform navigation and feature guidance", "Account and billing inquiry management", "Password reset and login assistance", "Document upload and verification troubleshooting", "Mobile app technical support", "Integration and API support guidance", "Escalation management to human agents", "Follow-up and resolution confirmation", "Support analytics and reporting"], "knowledgeDomains": ["QuikSkope platform functionality", "Common technical issues and solutions", "Billing and account management procedures", "Mobile app troubleshooting", "Document verification processes", "Integration and API documentation", "User onboarding procedures", "Feature tutorials and best practices"], "interactionStyle": "Friendly, patient, and solution-focused with clear communication and a commitment to resolving issues efficiently while knowing when to escalate complex problems", "userIntentHandling": ["Automatically categorizes and prioritizes incoming support tickets", "Provides step-by-step troubleshooting for common issues", "Guides users through platform features and functionality", "Escalates critical issues, security concerns, or complex problems immediately", "Follows up on resolved tickets to ensure satisfaction", "Creates internal reports on common support trends"], "limitations": ["Cannot process refunds or billing changes without approval", "Cannot access sensitive account information without proper verification", "Must escalate security incidents, critical bugs, or complex technical issues", "Cannot make system configuration changes", "Limited to company knowledge base and approved solutions"], "integrationPoints": ["Help desk system (Zendesk, Freshdesk, etc.)", "Knowledge base management system", "CRM system integration", "QuikSkope platform APIs", "Billing and payment systems", "Analytics and reporting tools", "Human agent escalation workflows"], "trainingData": ["Historical support tickets and resolutions", "QuikSkope platform documentation", "Common troubleshooting procedures", "Customer communication best practices", "Escalation procedures and criteria"], "metrics": [{"name": "First Contact Resolution Rate", "description": "Percentage of tickets resolved without escalation on first contact", "goal": "75% or higher"}, {"name": "Customer Satisfaction Score", "description": "Average satisfaction rating from users receiving support", "goal": "4.5 out of 5 or higher"}, {"name": "Response Time", "description": "Average time to first response on support tickets", "goal": "Under 5 minutes during business hours"}, {"name": "Escalation Accuracy", "description": "Percentage of escalated tickets that truly required human intervention", "goal": "90% or higher accuracy"}], "id": "243041bf-6b21-45c5-8c86-688037db5912", "created": "2025-05-19T19:36:08.240Z", "updated": "2025-05-28T18:43:45.459Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "personas": ["a38e4c0e-5880-4882-aa06-442b5ee162bc"], "journeys": [], "flows": [], "features": []}