{"title": "Verified Driver: Finding & Booking Priced Loads", "description": "Outlines the process for a verified driver to search for available shipments, view their pricing, and book a load through the QuikSkope platform.", "startPoint": "Verified driver logs into the QuikSkope Mobile App or dashboard with the intent to find a load.", "endPoint": "Driver successfully books a priced shipment and receives confirmation.", "parentRelationship": "QuikSkope Driver Operations", "splitJustification": "N/A", "connectionPoints": ["ProductFeature:060dc3ef-a496-4801-b443-f9019e33c75e", "ProductFeature:e29eda5a-76f7-4b85-8047-6c3401c9124e", "ProductFeature:3b96257d-1d49-48ad-8c64-39d94b8b4207", "ProductFeature:cc1cc3ea-72d6-4076-907f-e2daf5338e33"], "touchpoints": [{"id": "vd_login_access_search", "description": "Verified driver logs in and navigates to 'Smart Load Search'.", "likelyEmotion": "Purposeful/Hopeful", "recommendedIntervention": "Quick access to search, prominent CTA."}, {"id": "vd_set_search_filters", "description": "Driver applies search filters (location, equipment, destination, etc.).", "likelyEmotion": "Focused", "recommendedIntervention": "Intuitive filter controls, saved search options."}, {"id": "vd_view_search_results", "description": "Driver reviews the list of available loads matching criteria.", "likelyEmotion": "Evaluative", "recommendedIntervention": "Clear, concise load summaries, easy scrolling."}, {"id": "vd_select_load_view_details", "description": "Driver selects a specific load to view detailed information, including displayed price.", "likelyEmotion": "Interested/Analytical", "recommendedIntervention": "Comprehensive load details page, prominent pricing information."}, {"id": "vd_assess_load_profitability", "description": "Driver assesses the load's suitability and profitability based on price and requirements.", "likelyEmotion": "Calculating", "recommendedIntervention": "Tools for quick calculation (e.g., mileage estimate)."}, {"id": "vd_initiate_booking", "description": "Driver decides to book the load and clicks 'Book Load'.", "likelyEmotion": "Decisive/Committed", "recommendedIntervention": "Clear booking button, simple confirmation step."}, {"id": "vd_confirm_booking_details", "description": "Driver confirms booking details and accepts terms.", "likelyEmotion": "Anticipation", "recommendedIntervention": "Final summary before commitment."}, {"id": "vd_receive_booking_confirmation", "description": "Driver receives instant confirmation of the booked load, including pickup details and rate confirmation.", "likelyEmotion": "Accomplishment/Secured", "recommendedIntervention": "Clear success message, add to schedule option, easy access to load info."}], "decisionPoints": [{"id": "dp_vd_filter_adjustment", "description": "Driver decides if current search filters are optimal or need adjustment.", "options": ["Refine Filters", "View Results", "Use Saved Search"]}, {"id": "dp_vd_accept_or_reject_load", "description": "After viewing details, driver decides whether to book this specific load or look for others.", "options": ["Book This Load", "View Other Loads", "Save for Later"]}], "contexts": ["Freight Marketplace", "Load Booking", "Driver Operations", "Mobile Application Usage", "Price Transparency"], "stakeholders": ["Verified Truck Drivers", "QuikSkope Platform"], "emotionalNotes": "The journey should empower verified drivers by providing transparent pricing and an easy booking process, leading to a sense of control and opportunity.", "id": "fd7548cc-d104-4a2c-b1ab-fdb571ff3bfb", "created": "2025-05-28T15:01:06.473Z", "updated": "2025-05-28T15:01:06.473Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "personaId": "b0c786fa-c3b3-4eea-a90a-157088c7bee4", "flows": [], "features": [], "agents": []}