{"title": "Shipper/Broker: End-to-End Shipment Management", "description": "Encompasses the entire lifecycle of a shipment from the shipper/broker's perspective, from initial posting/assignment through tracking, issue resolution, to final delivery confirmation and payment/invoice reconciliation.", "startPoint": "Shipper/Broker needs to move a shipment and decides to use QuikSkope.", "endPoint": "Shipment is successfully delivered, all documentation is complete, and financial reconciliation (payment/invoice) is finalized.", "parentRelationship": "QuikSkope Core Operations", "splitJustification": "N/A", "connectionPoints": ["ProductFeature:22a07625-c59e-402e-bcb9-27f86691f3e3", "ProductFeature:df9276f2-d8c2-48cc-aaf8-6e2415071dc7", "ProductFeature:056555ce-03d6-4608-9267-ae18e491c9aa", "ProductFeature:ff9e0a58-fd35-481c-9d6c-ac5cd329bc38", "ProductFeature:71fc4edd-c483-4e3f-b2bf-3667da6abb33", "ProductFeature:e29eda5a-76f7-4b85-8047-6c3401c9124e", "ProductFeature:c124b0e0-c75f-4755-83b6-3186d887e1fe"], "touchpoints": [{"id": "sb_define_shipment_need", "description": "Shipper/Broker identifies a shipment requirement.", "likelyEmotion": "Planning", "recommendedIntervention": "Easy access to 'Create Shipment' or 'Find Driver'."}, {"id": "sb_post_or_assign_load", "description": "Uses platform to post load to marketplace or assign to a known driver.", "likelyEmotion": "Proactive", "recommendedIntervention": "Streamlined posting/assignment interface."}, {"id": "sb_monitor_load_acceptance_assignment", "description": "Tracks load acceptance by a driver or confirms assignment.", "likelyEmotion": "Expectant", "recommendedIntervention": "Real-time notifications, clear status updates."}, {"id": "sb_track_shipment_pickup", "description": "Monitors driver's progress to pickup location and Integrity Protocol steps.", "likelyEmotion": "Vigilant/Trusting", "recommendedIntervention": "Detailed tracking map, Integrity Protocol status updates."}, {"id": "sb_manage_pickup_verification", "description": "Reviews driver's verification evidence and approves pickup code release.", "likelyEmotion": "Responsible/Secure", "recommendedIntervention": "Clear evidence presentation, quick approval action."}, {"id": "sb_track_shipment_in_transit", "description": "Monitors shipment progress in real-time during transit.", "likelyEmotion": "Informed/Reassured", "recommendedIntervention": "Accurate GPS tracking, ETA updates, delay notifications."}, {"id": "sb_handle_in_transit_issues", "description": "(If issues arise) Communicates with driver or QuikSkope support, manages exceptions.", "likelyEmotion": "Concerned/Problem-solving", "recommendedIntervention": "In-app communication tools, easy access to support, exception reporting features."}, {"id": "sb_track_shipment_delivery", "description": "Monitors driver's arrival at destination and POD process.", "likelyEmotion": "Anticipatory", "recommendedIntervention": "Delivery ETA updates, POD status notifications."}, {"id": "sb_review_pod_approve_completion", "description": "Reviews submitted POD, confirms delivery, and approves shipment completion for payout/invoicing.", "likelyEmotion": "Satisfied/Validating", "recommendedIntervention": "Easy access to POD, clear approval workflow."}, {"id": "sb_manage_invoicing_payment", "description": "Handles invoicing for their client (if broker) or reconciles payment to driver.", "likelyEmotion": "Administrative/Finalizing", "recommendedIntervention": "Integrated financial summaries, payment history."}, {"id": "sb_archive_review_shipment_history", "description": "Shipment is archived; reviews shipment history and performance analytics.", "likelyEmotion": "Reflective/Analytical", "recommendedIntervention": "Comprehensive shipment records, reporting tools."}], "decisionPoints": [{"id": "dp_sb_marketplace_vs_direct", "description": "Decide whether to post to open marketplace or assign directly to a preferred driver.", "options": ["Post to Marketplace", "Assign Directly", "Request Quotes"]}, {"id": "dp_sb_handle_delay_exception", "description": "How to respond to an in-transit delay or issue reported by the driver or system.", "options": ["Contact Driver", "Update Consignee", "Contact QuikSkope Support", "Reroute (if possible)"]}, {"id": "dp_sb_dispute_pod_charges", "description": "Decide whether to approve POD and associated charges or dispute them.", "options": ["Approve All", "Dispute Charges (with reason)", "Request Clarification"]}], "contexts": ["Logistics Management", "Freight Operations", "B2B Transactions", "Supply Chain Visibility", "Financial Reconciliation"], "stakeholders": ["Shippers", "Freight Brokers", "Logistics Coordinators", "Consignees (indirectly)", "QuikSkope Platform"], "emotionalNotes": "This journey should provide shippers/brokers with a sense of control, transparency, and efficiency throughout the entire shipment lifecycle, fostering trust and reliance on the platform.", "id": "e9b43b44-34c4-4a2f-8d70-bc559042c7d2", "created": "2025-05-28T15:02:41.178Z", "updated": "2025-05-28T15:02:41.178Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "personaId": "bfbd80d8-155e-4c0c-b109-2eb7addedfaf", "flows": [], "features": [], "agents": []}