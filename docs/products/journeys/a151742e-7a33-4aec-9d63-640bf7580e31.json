{"title": "Verified Driver: Shipment Completion & Payout", "description": "Covers the steps a verified driver takes from delivering a shipment to receiving payment, including secure POD submission via QR code system, review, and payout processing via Stripe Connect.", "startPoint": "Driver arrives at the destination and is ready to unload and complete the delivery.", "endPoint": "Driver receives confirmed payout for the completed shipment in their linked bank account.", "parentRelationship": "QuikSkope Driver Operations", "splitJustification": "N/A", "connectionPoints": ["ProductFeature:71fc4edd-c483-4e3f-b2bf-3667da6abb33", "ProductFeature:c124b0e0-c75f-4755-83b6-3186d887e1fe", "ProductFeature:3b96257d-1d49-48ad-8c64-39d94b8b4207", "ProductFeature:e29eda5a-76f7-4b85-8047-6c3401c9124e", "ProductFeature:056555ce-03d6-4608-9267-ae18e491c9aa"], "touchpoints": [{"id": "vd_arrival_at_destination", "description": "Driver arrives at destination; system updates shipper/broker automatically.", "likelyEmotion": "Relief/Focused", "recommendedIntervention": "Clear notification to consignee/shipper with ETA confirmation."}, {"id": "vd_initiate_delivery_confirmation", "description": "Driver uses app to indicate arrival and readiness for delivery confirmation process.", "likelyEmotion": "Proactive", "recommendedIntervention": "Simple 'Confirm Delivery' action in app with clear next steps."}, {"id": "vd_unloading_inspection", "description": "Consignee inspects goods during unloading process for damage or discrepancies.", "likelyEmotion": "(Driver) Expectant, (Consignee) Diligent", "recommendedIntervention": "Clear documentation process for any exceptions or damages found."}, {"id": "vd_generate_pod_qr_code", "description": "Driver generates secure QR code in mobile app for consignee POD signature process. 🚨 FRAUD RISK: QR code must be session-specific, time-limited, and device-fingerprinted to prevent replay attacks.", "likelyEmotion": "(Driver) Confident in security process", "recommendedIntervention": "Clear instructions for consignee on QR scanning process, visible QR code expiration timer."}, {"id": "vd_consignee_scans_qr_signs_pod", "description": "Consignee scans QR code with their own device and completes e-signature on secure web interface. 🚨 FRAUD RISK: Must validate consignee device identity, prevent signature spoofing, and ensure QR code hasn't been tampered with or screenshots.", "likelyEmotion": "(Consignee) Cautious but cooperative", "recommendedIntervention": "Secure web interface with clear company branding, option for notes/photos of exceptions, identity verification prompts."}, {"id": "vd_signature_verification_security", "description": "System performs multi-layer verification: QR code authenticity, device fingerprinting, signature validation, and timestamp verification. 🚨 FRAUD RISK: Must prevent man-in-the-middle attacks, validate signing device isn't compromised, and ensure signature matches consignee identity records.", "likelyEmotion": "(System) Processing with high security standards", "recommendedIntervention": "Real-time security validation with clear success/failure indicators to both parties."}, {"id": "vd_driver_confirms_completion", "description": "Driver confirms delivery completion in app after witnessing consignee signature process.", "likelyEmotion": "(Driver) Accomplished/Relieved", "recommendedIntervention": "Clear confirmation of successful POD capture, estimated payout timeline."}, {"id": "vd_system_processes_pod", "description": "QuikSkope system processes signed POD, validates all security checks, and notifies shipper/broker for final payout approval. 🚨 FRAUD RISK: Must validate POD hasn't been altered post-signature, all parties are legitimate, and delivery actually occurred at correct location/time.", "likelyEmotion": "(System) Thorough processing", "recommendedIntervention": "Automated notification to shipper/broker with complete delivery documentation."}, {"id": "vd_shipper_approves_payout", "description": "Shipper/Broker reviews comprehensive POD documentation and approves payout.", "likelyEmotion": "(Shipper) Satisfied with security/transparency", "recommendedIntervention": "Quick link to review all POD details, one-click approval with audit trail."}, {"id": "vd_payout_initiated_stripe", "description": "System initiates payout to driver's linked Stripe Connect account with full audit trail.", "likelyEmotion": "(System) Processing with compliance", "recommendedIntervention": "Clear notification to driver about payout initiation and expected deposit timeline."}, {"id": "vd_driver_receives_payout_notification", "description": "Driver receives notification of successful payout processing with transaction details.", "likelyEmotion": "Satisfied/Rewarded", "recommendedIntervention": "Transparent payout details, link to complete transaction history, earnings summary."}, {"id": "vd_funds_deposited_bank", "description": "Funds are securely deposited into driver's verified bank account.", "likelyEmotion": "Rewarded/Content", "recommendedIntervention": "N/A (Bank process, but app shows 'Paid' status confirmation)."}], "decisionPoints": [{"id": "dp_vd_pod_discrepancy_consignee", "description": "Consignee notes discrepancies or damages during delivery inspection.", "options": ["Sign with Exceptions (documented)", "Refuse Signature (escalate to shipper)", "Request Shipper Contact", "Document Issues with Photos"]}, {"id": "dp_vd_qr_code_security_failure", "description": "🚨 SECURITY DECISION: QR code security validation fails (expired, tampered, wrong device, etc.).", "options": ["Generate New QR Code", "Escalate to Manual Verification", "Contact Support", "Cancel Delivery Process"]}, {"id": "dp_vd_shipper_payout_dispute", "description": "Shipper/Broker disputes POD or payout amount based on delivery documentation.", "options": ["Approve Payout (standard)", "Dispute Payout (with documented reason)", "Request Additional Verification", "Escalate to Dispute Resolution Agent"]}], "contexts": ["Shipment Delivery", "Proof of Delivery", "Payment Processing", "Driver Compensation", "Stripe Integration", "Security Verification", "QR Code Authentication"], "stakeholders": ["Verified Truck Drivers", "Consignees", "Shippers", "Brokers", "QuikSkope Platform", "Security Systems"], "emotionalNotes": "This journey should provide a sense of security and trust through the QR code process, with transparent and efficient payout processing that reinforces confidence in the platform's security measures.", "id": "a151742e-7a33-4aec-9d63-640bf7580e31", "created": "2025-05-28T15:01:48.003Z", "updated": "2025-05-28T19:49:32.050Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": null, "flows": [], "features": ["eb9d532a-a220-4e12-a789-0730d44bff23"], "agents": []}