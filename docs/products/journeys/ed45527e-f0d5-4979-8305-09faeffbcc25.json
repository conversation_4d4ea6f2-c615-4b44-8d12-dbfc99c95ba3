{"title": "Shipper/Broker Onboarding & First Load Posting", "description": "Guides new shippers or brokers through initial platform registration, optional company verification, dashboard familiarization, and posting their first shipment or assigning a driver.", "startPoint": "Shipper/Broker discovers QuikSkope and initiates registration.", "endPoint": "Shipper/Broker successfully posts their first shipment to the platform or assigns a driver.", "parentRelationship": "QuikSkope Platform Adoption", "splitJustification": "N/A", "connectionPoints": ["ProductFeature:22a07625-c59e-402e-bcb9-27f86691f3e3", "ProductFeature:df9276f2-d8c2-48cc-aaf8-6e2415071dc7"], "touchpoints": [{"id": "sb_discover_platform", "description": "Learns about QuikSkope's benefits for shippers/brokers.", "likelyEmotion": "Curiosity/Interest", "recommendedIntervention": "Clear value proposition, testimonials."}, {"id": "sb_initiate_registration", "description": "Clicks 'Sign Up' and starts the registration form.", "likelyEmotion": "Hope/Expectation", "recommendedIntervention": "Simple, streamlined registration form."}, {"id": "sb_complete_profile", "description": "Fills in basic profile and company information.", "likelyEmotion": "Focused", "recommendedIntervention": "Progress indicators, clear field labels."}, {"id": "sb_company_verification_prompt", "description": "(If applicable) Prompted for company verification documents.", "likelyEmotion": "Slight_Hesitation/Compliance", "recommendedIntervention": "Explain benefits of verification, clear instructions."}, {"id": "sb_dashboard_introduction", "description": "First login; sees the main dashboard.", "likelyEmotion": "Overwhelmed/Excited", "recommendedIntervention": "Interactive guided tour, contextual help tips."}, {"id": "sb_navigate_to_post_load", "description": "Finds and navigates to the 'Post a Load' or 'Manage Shipments' section.", "likelyEmotion": "Purposeful", "recommendedIntervention": "Intuitive navigation, clear calls to action."}, {"id": "sb_enter_shipment_details", "description": "Enters all necessary details for the new shipment.", "likelyEmotion": "Diligent", "recommendedIntervention": "Smart defaults, address lookup, easy form fields."}, {"id": "sb_choose_posting_type", "description": "Decides to post to open marketplace or assign to a specific driver.", "likelyEmotion": "Strategic", "recommendedIntervention": "Clear explanation of options."}, {"id": "sb_confirm_post_assignment", "description": "Reviews and confirms the shipment posting or assignment.", "likelyEmotion": "Anticipation", "recommendedIntervention": "Summary of details before final submission."}, {"id": "sb_receive_confirmation", "description": "Receives confirmation that the shipment is live or driver is notified.", "likelyEmotion": "Accomplishment/Relief", "recommendedIntervention": "Clear success message, link to view shipment, next steps guidance."}], "decisionPoints": [{"id": "dp_sb_verify_company", "description": "Decide whether to complete company verification immediately or later.", "options": ["Verify Now", "Skip for Now", "Learn More"]}, {"id": "dp_sb_load_visibility", "description": "Choose to make the load available to all verified drivers or assign to a specific pre-known driver.", "options": ["Post to Marketplace", "Assign to Specific Driver"]}], "contexts": ["B2B Logistics", "New User Onboarding", "Supply Chain Management", "Freight Posting"], "stakeholders": ["Shippers", "Freight Brokers", "Logistics Coordinators", "QuikSkope Platform Administrators"], "emotionalNotes": "The journey should quickly move the shipper/broker from initial interest to successfully utilizing a core platform function, building confidence and demonstrating value.", "id": "ed45527e-f0d5-4979-8305-09faeffbcc25", "created": "2025-05-28T14:59:43.575Z", "updated": "2025-05-28T14:59:43.577Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "personaId": "bfbd80d8-155e-4c0c-b109-2eb7addedfaf", "flows": [], "features": [], "agents": []}