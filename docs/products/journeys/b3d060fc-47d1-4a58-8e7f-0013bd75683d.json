{"title": "Secure Shipment Handoff (Integrity Protocol)", "description": "Details the multi-step verification process for drivers and shippers/brokers during shipment pickup, utilizing the Integrity Protocol to ensure security and authenticity.", "startPoint": "Driver arrives at shipper location for a scheduled pickup.", "endPoint": "Shipment is securely handed off, pickup code is used, and initial BOL is e-signed by both parties.", "parentRelationship": "QuikSkope Core Operations", "splitJustification": "N/A", "connectionPoints": ["ProductFeature:ff9e0a58-fd35-481c-9d6c-ac5cd329bc38", "ProductFeature:3b96257d-1d49-48ad-8c64-39d94b8b4207", "ProductFeature:056555ce-03d6-4608-9267-ae18e491c9aa", "ProductFeature:71fc4edd-c483-4e3f-b2bf-3667da6abb33", "ProductFeature:c124b0e0-c75f-4755-83b6-3186d887e1fe"], "touchpoints": [{"id": "sh_driver_arrival_notification", "description": "Driver arrives; system notifies shipper/broker (via Real-Time Tracking).", "likelyEmotion": "(Driver) Ready, (Shipper) Alerted", "recommendedIntervention": "Clear notification to shipper."}, {"id": "sh_driver_initiates_verification", "description": "Driver uses QuikSkope Mobile App to start Integrity Protocol verification.", "likelyEmotion": "(Driver) Focused", "recommendedIntervention": "Intuitive app interface."}, {"id": "sh_driver_submits_location", "description": "Driver confirms location via GPS (geofence check).", "likelyEmotion": "(Driver) Compliant", "recommendedIntervention": "Accurate geofencing, clear feedback."}, {"id": "sh_driver_submits_photo_evidence", "description": "Driver captures and submits required photos (truck, trailer) via app.", "likelyEmotion": "(Driver) Diligent", "recommendedIntervention": "In-app camera guidance, EXIF validation."}, {"id": "sh_system_analyzes_evidence", "description": "AI analyzes photo evidence for recency and authenticity.", "likelyEmotion": "(System) Processing", "recommendedIntervention": "Quick processing, clear status updates."}, {"id": "sh_shipper_reviews_evidence", "description": "Ship<PERSON>/Broker receives notification and reviews driver's submitted evidence via dashboard/app.", "likelyEmotion": "(Ship<PERSON>) Scrutinizing/Trustful", "recommendedIntervention": "Clear presentation of evidence, easy comparison."}, {"id": "sh_shipper_approves_releases_code", "description": "Shipper/Broker approves verification and system releases secure pickup code to driver.", "likelyEmotion": "(Shipper) Confident, (Driver) Relieved", "recommendedIntervention": "Secure code delivery, confirmation to both parties."}, {"id": "sh_driver_presents_code", "description": "Driver presents pickup code to shipper personnel.", "likelyEmotion": "(Driver) Authorized", "recommendedIntervention": "N/A (physical interaction)."}, {"id": "sh_shipper_validates_code_loads", "description": "Shipper personnel validate code and proceed with loading.", "likelyEmotion": "(Shipper) Secure", "recommendedIntervention": "Easy code validation method for shipper staff."}, {"id": "sh_bol_preparation_esign", "description": "Digital BOL is prepared/confirmed; both driver and shipper e-sign via app/dashboard.", "likelyEmotion": "(Both) Formalizing/Agreement", "recommendedIntervention": "Seamless e-signature flow, accessible BOL details."}, {"id": "sh_handoff_complete_notification", "description": "System confirms handoff and BOL signature; shipment status updates.", "likelyEmotion": "(Both) Accomplished", "recommendedIntervention": "Clear confirmation, updated tracking."}], "decisionPoints": [{"id": "dp_sh_shipper_verification_decision", "description": "Ship<PERSON>/Broker decides to approve or reject driver's verification evidence.", "options": ["Approve Verification", "Reject Verification (with reason)", "Request More Info"]}, {"id": "dp_sh_bol_discrepancy", "description": "During e-signature, a party notices a BOL discrepancy.", "options": ["Proceed with Signing", "Request BOL Correction", "Escalate Issue"]}], "contexts": ["Shipment Pickup", "Security Protocol", "Mobile Application Usage", "Real-time Verification", "Logistics Operations"], "stakeholders": ["Truck Drivers", "Shippers", "Brokers", "Shipper On-site Personnel", "QuikSkope Platform"], "emotionalNotes": "The journey aims to build trust and security through a transparent yet rigorous process, leading to a sense of confidence for both parties.", "id": "b3d060fc-47d1-4a58-8e7f-0013bd75683d", "created": "2025-05-28T15:00:38.405Z", "updated": "2025-05-28T15:00:38.405Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "personaId": "b0c786fa-c3b3-4eea-a90a-157088c7bee4", "flows": [], "features": [], "agents": []}