{"name": "Fleet Manager Registration Flow", "description": "Specialized registration flow for fleet managers and dispatchers with multi-driver management setup, bulk verification options, and fleet-scale operational tools.", "entryPoints": ["Fleet management landing page", "Trucking company referral", "Industry association recommendation", "Fleet operations search"], "exitPoints": ["Registration complete with fleet management access", "Fleet size verification failed", "Registration abandoned", "Account created with multi-driver capabilities"], "steps": [{"type": "action", "description": "User accesses registration page and selects 'Fleet Manager' or 'Dispatcher' account type", "actor": "Fleet Manager", "nextSteps": ["company_details"], "conditions": "User identifies as fleet manager or dispatch coordinator"}, {"type": "action", "description": "User enters trucking company information, MC/DOT numbers, and fleet size details", "actor": "Fleet Manager", "nextSteps": ["fleet_verification"], "conditions": "Company authority and fleet size information provided"}, {"type": "system", "description": "System verifies company operating authority and validates fleet size against FMCSA records", "actor": "Company Verification System", "nextSteps": ["driver_management_setup", "verification_failed"], "conditions": "Fleet meets minimum size requirements and has valid authority"}, {"type": "action", "description": "User provides driver count, typical operational areas, and load management preferences", "actor": "Fleet Manager", "nextSteps": ["bulk_features"], "conditions": "Fleet operational profile completed"}, {"type": "action", "description": "User selects bulk verification options, driver onboarding preferences, and management tools needed", "actor": "Fleet Manager", "nextSteps": ["account_creation"], "conditions": "Fleet management preferences and bulk options selected"}, {"type": "system", "description": "System creates fleet management account with multi-driver capabilities and bulk pricing", "actor": "Registration System", "nextSteps": ["email_verification"], "conditions": "Fleet account configured with appropriate permissions and pricing"}, {"type": "action", "description": "User verifies email address and activates fleet management account", "actor": "Fleet Manager", "nextSteps": ["dashboard_configuration"], "conditions": "Email verification completed and account activated"}, {"type": "system", "description": "Platform configures fleet dashboard with driver management, load assignment, and compliance tools", "actor": "Integrated Logistics Dashboard", "nextSteps": ["driver_invitation"], "conditions": "Fleet management interface configured and ready for use"}, {"type": "system", "description": "Platform Adoption Agent guides fleet manager through driver invitation and bulk verification process", "actor": "Platform Adoption & Driver Onboarding Agent", "nextSteps": [], "conditions": "Registration completed with fleet-scale operational capabilities"}], "id": "694abf65-8c4e-4e3c-ab93-2b897ad233f0", "created": "2025-05-28T19:27:42.575Z", "updated": "2025-05-28T19:27:42.575Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "bc78fcea-9106-4ab4-adef-b4f14cfff8fb", "journey": null, "features": ["114f98d5-ab05-4b33-8840-97939c14616b"], "agents": ["07732ddb-512e-4d3b-bf2f-0f4936279280"]}