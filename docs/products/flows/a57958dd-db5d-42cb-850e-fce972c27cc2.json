{"name": "Dispute Resolution Flow", "description": "Comprehensive mediation flow handling payment disputes, delivery conflicts, and service issues between drivers, shippers, and brokers through automated resolution and human escalation when needed.", "entryPoints": ["Dispute reported by any party", "Automatic conflict detection", "Payment issue flagged", "Delivery discrepancy identified"], "exitPoints": ["Dispute resolved to all parties satisfaction", "Escalated to human mediator", "Formal complaint filed", "Relationship terminated due to unresolved conflict"], "steps": [{"type": "action", "description": "User reports dispute through platform interface with detailed description and evidence", "actor": "Driver/Shipper/Broker", "nextSteps": ["dispute_categorization"], "conditions": "Dispute involves platform-mediated transaction or service"}, {"type": "system", "description": "AI categorizes dispute type and severity level for appropriate routing", "actor": "Dispute Resolution Agent", "nextSteps": ["evidence_collection"], "conditions": "Dispute meets platform mediation criteria"}, {"type": "system", "description": "System automatically collects relevant evidence including documents, tracking data, and communication records", "actor": "Dispute Resolution Agent", "nextSteps": ["party_notification"], "conditions": "Sufficient evidence available for analysis"}, {"type": "action", "description": "All involved parties are notified and invited to provide additional evidence or statements", "actor": "All Parties", "nextSteps": ["evidence_analysis"], "conditions": "All parties acknowledge dispute and provide requested information"}, {"type": "system", "description": "AI analyzes evidence against platform policies and industry standards to determine facts", "actor": "Dispute Resolution Agent", "nextSteps": ["resolution_recommendation"], "conditions": "Evidence analysis completed with clear findings"}, {"type": "system", "description": "System generates fair resolution recommendation based on evidence and platform policies", "actor": "Dispute Resolution Agent", "nextSteps": ["party_review"], "conditions": "Resolution aligns with platform terms and industry standards"}, {"type": "action", "description": "All parties review proposed resolution and accept or request modifications", "actor": "All Parties", "nextSteps": ["resolution_implementation", "escalation_required"], "conditions": "Parties have opportunity to respond within specified timeframe"}, {"type": "decision", "description": "System determines if resolution is accepted by all parties or requires escalation", "actor": "Dispute Resolution Agent", "nextSteps": ["resolution_finalization", "human_escalation"], "conditions": "All parties acceptance status evaluated"}, {"type": "system", "description": "Accepted resolution is implemented including any payment adjustments or corrective actions", "actor": "Dispute Resolution Agent", "nextSteps": ["follow_up"], "conditions": "Resolution accepted and implementation possible"}, {"type": "system", "description": "Follow-up communication sent to ensure resolution compliance and satisfaction", "actor": "Dispute Resolution Agent", "nextSteps": [], "conditions": "Resolution implemented successfully"}], "id": "a57958dd-db5d-42cb-850e-fce972c27cc2", "created": "2025-05-28T19:22:27.353Z", "updated": "2025-05-28T19:22:27.353Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": null, "journey": null, "features": [], "agents": ["43086034-2c91-4beb-a98a-fc99e2cd79dd"]}