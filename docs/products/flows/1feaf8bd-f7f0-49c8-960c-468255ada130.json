{"name": "Secure Pickup Verification Flow", "description": "Multi-step security verification process using the Integrity Protocol to ensure legitimate driver identity and prevent fraud before releasing pickup codes to drivers at shipper locations.", "entryPoints": ["Driver arrival at pickup location", "Geofence trigger notification", "Manual verification request", "Scheduled pickup time approach"], "exitPoints": ["Pickup code released and load secured", "Verification failed with driver blocked", "Manual review escalation required", "Emergency override by shipper"], "steps": [{"type": "system", "description": "Geofence detects driver within 2-mile radius of pickup location", "actor": "Real-Time Tracking System", "nextSteps": ["driver_notification"], "conditions": "Driver GPS location matches expected pickup area"}, {"type": "action", "description": "Driver receives notification to begin verification process via mobile app", "actor": "Driver", "nextSteps": ["photo_submission"], "conditions": "Driver acknowledges verification prompt"}, {"type": "action", "description": "Driver captures and submits truck and trailer photos with EXIF metadata", "actor": "Driver", "nextSteps": ["photo_analysis"], "conditions": "Photos meet quality standards and contain required truck identification"}, {"type": "system", "description": "AI analyzes photos for authenticity, recency, and matches against driver profile", "actor": "Security Analytics Engine", "nextSteps": ["location_verification"], "conditions": "Photo analysis passes authenticity and recency checks"}, {"type": "system", "description": "System verifies driver location matches pickup address within acceptable range", "actor": "Integrity Protocol", "nextSteps": ["shipper_review"], "conditions": "GPS coordinates confirm driver presence at correct location"}, {"type": "action", "description": "Shipper reviews verification evidence and approves or rejects pickup authorization", "actor": "Shipper", "nextSteps": ["code_release", "verification_denied"], "conditions": "All verification evidence meets shipper security requirements"}, {"type": "system", "description": "System generates and releases secure pickup code to verified driver", "actor": "Integrity Protocol", "nextSteps": ["pickup_completion"], "conditions": "Shipper approval received and all security checks passed"}, {"type": "action", "description": "Driver presents pickup code to shipper personnel and load handoff begins", "actor": "Driver", "nextSteps": [], "conditions": "Valid pickup code presented and accepted by shipper"}], "id": "1feaf8bd-f7f0-49c8-960c-468255ada130", "created": "2025-05-28T19:21:26.753Z", "updated": "2025-05-28T19:21:26.753Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "a38e4c0e-5880-4882-aa06-442b5ee162bc", "journey": null, "features": ["ff9e0a58-fd35-481c-9d6c-ac5cd329bc38"], "agents": []}