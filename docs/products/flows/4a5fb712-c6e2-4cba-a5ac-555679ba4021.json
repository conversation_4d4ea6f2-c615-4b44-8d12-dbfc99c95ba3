{"name": "Load Booking & Assignment Flow", "description": "Core transaction flow enabling verified drivers to search, evaluate, and book available loads through the Smart Load Search & Matching system with transparent pricing and instant confirmation.", "entryPoints": ["Driver accesses load search from dashboard", "Push notification about matching loads", "Saved search alert triggered", "Manual load board browsing"], "exitPoints": ["Load successfully booked and confirmed", "No suitable loads found", "Booking failed due to assignment conflict", "User abandons search process"], "steps": [{"type": "action", "description": "Verified driver accesses Smart Load Search feature from mobile app or dashboard", "actor": "Driver", "nextSteps": ["search_filters"], "conditions": "Driver has verified status and access to premium features"}, {"type": "action", "description": "Driver sets search filters including location, destination, equipment type, and pay rate", "actor": "Driver", "nextSteps": ["load_results"], "conditions": "At least origin location and equipment type specified"}, {"type": "system", "description": "AI matching algorithm returns relevant loads based on driver profile and search criteria", "actor": "Smart Load Search System", "nextSteps": ["load_evaluation"], "conditions": "Available loads match driver qualifications and preferences"}, {"type": "action", "description": "Driver reviews load details including rate, miles, pickup/delivery times, and special requirements", "actor": "Driver", "nextSteps": ["booking_decision"], "conditions": "Load details are complete and transparent"}, {"type": "decision", "description": "Driver decides whether to book the selected load based on profitability and preferences", "actor": "Driver", "nextSteps": ["booking_initiation", "continue_searching"], "conditions": "Driver evaluates load suitability and profitability"}, {"type": "action", "description": "Driver initiates booking process by clicking 'Book Load' and confirming commitment", "actor": "Driver", "nextSteps": ["availability_check"], "conditions": "Driver commits to load requirements and schedule"}, {"type": "system", "description": "System checks real-time load availability and prevents double-booking", "actor": "Load Management System", "nextSteps": ["booking_confirmation", "booking_conflict"], "conditions": "Load remains available and no conflicts exist"}, {"type": "system", "description": "System confirms booking, updates all parties, and adds load to driver's schedule", "actor": "Load Management System", "nextSteps": ["pickup_preparation"], "conditions": "Booking successfully processed and confirmed"}, {"type": "system", "description": "Driver receives booking confirmation with pickup details and next steps", "actor": "Notification System", "nextSteps": [], "conditions": "Load booking completed and driver notified"}], "id": "4a5fb712-c6e2-4cba-a5ac-555679ba4021", "created": "2025-05-28T19:21:45.960Z", "updated": "2025-05-28T19:21:45.960Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "b0c786fa-c3b3-4eea-a90a-157088c7bee4", "journey": null, "features": ["060dc3ef-a496-4801-b443-f9019e33c75e"], "agents": []}