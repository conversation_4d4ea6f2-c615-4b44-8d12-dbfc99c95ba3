{"name": "Driver Registration Flow", "description": "Specialized registration flow for truck drivers (company drivers and owner-operators) with CDL verification, equipment capture, and direct path to verification and load opportunities.", "entryPoints": ["Driver-specific landing page", "Driver referral link", "Mobile app download", "Social media driver recruitment"], "exitPoints": ["Registration complete with profile setup", "CDL verification failed", "Registration abandoned", "Account created awaiting verification"], "steps": [{"type": "action", "description": "Driver accesses registration page and selects 'Driver' account type", "actor": "Driver", "nextSteps": ["cdl_entry"], "conditions": "Driver identifies as truck driver seeking loads"}, {"type": "action", "description": "Driver enters CDL number, name, email, and phone number", "actor": "Driver", "nextSteps": ["cdl_verification"], "conditions": "Valid CDL format and contact information provided"}, {"type": "system", "description": "System performs initial CDL validation against state databases", "actor": "Driver Verification System", "nextSteps": ["equipment_setup", "cdl_invalid"], "conditions": "CDL number exists and is currently valid"}, {"type": "action", "description": "Driver specifies equipment type, trailer preferences, and operating regions", "actor": "Driver", "nextSteps": ["experience_capture"], "conditions": "Equipment and operational preferences specified"}, {"type": "action", "description": "Driver provides experience level, safety record, and preferred load types", "actor": "Driver", "nextSteps": ["account_creation"], "conditions": "Professional background information completed"}, {"type": "system", "description": "System creates driver account and sends email verification", "actor": "Registration System", "nextSteps": ["email_verification"], "conditions": "All required information validated and account created"}, {"type": "action", "description": "Driver verifies email address and completes account activation", "actor": "Driver", "nextSteps": ["verification_opportunity"], "conditions": "Email verification link clicked and confirmed"}, {"type": "system", "description": "Platform presents $25 verification opportunity with benefits explanation", "actor": "Platform Adoption & Driver Onboarding Agent", "nextSteps": ["dashboard_access"], "conditions": "Account activated and driver ready for next steps"}, {"type": "action", "description": "Driver accesses dashboard with verification prompt and basic load browsing capabilities", "actor": "Driver", "nextSteps": [], "conditions": "Registration completed successfully"}], "id": "10e0810e-ee30-40b6-8819-e8d63e66ee0d", "created": "2025-05-28T19:27:04.695Z", "updated": "2025-05-28T19:27:04.695Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "36f861e4-7a48-4fed-a130-6e179bdaf440", "journey": null, "features": [], "agents": []}