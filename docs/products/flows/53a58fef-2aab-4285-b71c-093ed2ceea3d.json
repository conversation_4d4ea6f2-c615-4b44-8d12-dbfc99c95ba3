{"name": "Document Digitization & E-Signature Flow", "description": "Streamlined flow for capturing physical logistics documents via mobile camera, processing them through AI for data extraction, and completing digital signatures for legally binding documentation.", "entryPoints": ["Driver captures BOL at pickup", "Document upload from mobile app", "Photo capture during delivery", "Manual document processing request"], "exitPoints": ["Document fully processed and signed", "Document processing failed requiring retry", "Signature workflow completed", "Document archived and shared"], "steps": [{"type": "action", "description": "User captures physical document using mobile camera with Document Tag System guidance", "actor": "Driver/Shipper", "nextSteps": ["image_quality_check"], "conditions": "Document is clearly visible and properly lit"}, {"type": "system", "description": "System performs real-time image quality assessment and requests retake if necessary", "actor": "Document Tag System", "nextSteps": ["document_processing", "retake_required"], "conditions": "Image meets minimum quality standards for OCR processing"}, {"type": "system", "description": "AI processes document image, extracts text and data, and identifies document type", "actor": "Document Tag System", "nextSteps": ["data_validation"], "conditions": "Document type recognized and data extraction successful"}, {"type": "system", "description": "Extracted data is validated against shipment details and checked for accuracy", "actor": "Document Tag System", "nextSteps": ["template_formatting"], "conditions": "Data validation passes accuracy and completeness checks"}, {"type": "system", "description": "Document is formatted into standardized digital template with extracted data populated", "actor": "Digital BOL System", "nextSteps": ["review_approval"], "conditions": "Template formatting completed successfully"}, {"type": "action", "description": "User reviews digitized document for accuracy and approves for signature process", "actor": "Driver/Shipper", "nextSteps": ["signature_initiation"], "conditions": "Document review completed and approved by user"}, {"type": "system", "description": "E-signature workflow initiated with all required parties identified and notified", "actor": "Secure E-Signature Platform", "nextSteps": ["signature_collection"], "conditions": "All signing parties identified and signature sequence established"}, {"type": "action", "description": "Required parties complete digital signatures using secure e-signature platform", "actor": "All Signers", "nextSteps": ["signature_verification"], "conditions": "All required signatures obtained within specified timeframe"}, {"type": "system", "description": "System verifies signature authenticity and creates tamper-evident final document", "actor": "Secure E-Signature Platform", "nextSteps": ["document_distribution"], "conditions": "All signatures verified and document integrity confirmed"}, {"type": "system", "description": "Completed document is distributed to all parties and archived with full audit trail", "actor": "Document Management System", "nextSteps": [], "conditions": "Document processing and signature workflow completed successfully"}], "id": "53a58fef-2aab-4285-b71c-093ed2ceea3d", "created": "2025-05-28T19:22:50.521Z", "updated": "2025-05-28T19:22:50.521Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": null, "journey": null, "features": ["65f0847a-c266-4d94-bb2e-56cf7aca07d2", "71fc4edd-c483-4e3f-b2bf-3667da6abb33", "c124b0e0-c75f-4755-83b6-3186d887e1fe", "eb9d532a-a220-4e12-a789-0730d44bff23"], "agents": []}