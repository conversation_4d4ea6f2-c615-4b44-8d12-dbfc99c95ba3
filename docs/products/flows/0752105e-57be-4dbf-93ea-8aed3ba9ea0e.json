{"name": "Driver Onboarding & First Load Flow", "description": "Complete driver success flow from registration through verification to booking and completing their first profitable load, ensuring maximum platform engagement and early revenue generation.", "entryPoints": ["New driver registration completion", "Driver referral signup", "Mobile app download and signup", "Marketing campaign landing page"], "exitPoints": ["First load completed successfully", "Verification abandoned", "No suitable loads available", "Driver churns before first load"], "steps": [{"type": "action", "description": "New driver completes basic registration with CDL and contact information", "actor": "Driver", "nextSteps": ["profile_setup"], "conditions": "Valid CDL number and contact information provided"}, {"type": "action", "description": "Driver sets up profile including equipment type, preferred routes, and experience level", "actor": "Driver", "nextSteps": ["verification_prompt"], "conditions": "Essential profile information completed"}, {"type": "system", "description": "Platform Adoption Agent presents verification value proposition and $25 fee benefits", "actor": "Platform Adoption & Driver Onboarding Agent", "nextSteps": ["verification_decision"], "conditions": "Driver profile indicates potential for premium features"}, {"type": "decision", "description": "Driver decides whether to proceed with $25 verification for premium features", "actor": "Driver", "nextSteps": ["verification_flow", "basic_browsing"], "conditions": "Driver understands verification benefits and value proposition"}, {"type": "system", "description": "Driver enters verification flow (payment → document upload → approval)", "actor": "Driver Verification System", "nextSteps": ["verification_success"], "conditions": "Driver completes verification process successfully"}, {"type": "system", "description": "Verified driver gains access to Smart Load Search and premium load marketplace", "actor": "Smart Load Search System", "nextSteps": ["first_search"], "conditions": "Verification approved and premium features unlocked"}, {"type": "action", "description": "Driver conducts first load search with guidance from onboarding agent", "actor": "Driver", "nextSteps": ["load_selection"], "conditions": "Driver understands search filters and matching system"}, {"type": "action", "description": "Driver selects and books their first profitable load through the platform", "actor": "Driver", "nextSteps": ["pickup_process"], "conditions": "Suitable load found and booking confirmed"}, {"type": "system", "description": "Driver completes Integrity Protocol verification and secure pickup process", "actor": "Integrity Protocol", "nextSteps": ["delivery_completion"], "conditions": "Pickup verification successful and load secured"}, {"type": "action", "description": "Driver completes delivery, submits POD, and receives payment confirmation", "actor": "Driver", "nextSteps": ["success_celebration"], "conditions": "Delivery confirmed and payment processed"}, {"type": "system", "description": "Platform celebrates first load success and guides driver to next opportunities", "actor": "Platform Adoption & Driver Onboarding Agent", "nextSteps": [], "conditions": "First load completed successfully with positive experience"}], "id": "0752105e-57be-4dbf-93ea-8aed3ba9ea0e", "created": "2025-05-28T19:26:40.558Z", "updated": "2025-05-28T19:26:40.558Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "36f861e4-7a48-4fed-a130-6e179bdaf440", "journey": null, "features": [], "agents": ["aaf67381-663d-4e5b-820d-f0e2348396cf"]}