{"name": "Shipment Posting & Carrier Assignment Flow", "description": "End-to-end flow for shippers and brokers to post loads to the platform, review qualified drivers, and assign carriers with integrated tracking and communication capabilities.", "entryPoints": ["Shipper dashboard load posting", "TMS integration API call", "Broker portal load entry", "Bulk upload import process"], "exitPoints": ["Carrier successfully assigned and notified", "No qualified carriers available", "Load posting expired or cancelled", "Assignment rejected by selected carrier"], "steps": [{"type": "action", "description": "Shipper or broker accesses load posting interface from dashboard", "actor": "Shipper/Broker", "nextSteps": ["load_details_entry"], "conditions": "User has appropriate permissions and active account"}, {"type": "action", "description": "User enters comprehensive load details including pickup/delivery locations, dates, equipment, and special requirements", "actor": "Shipper/Broker", "nextSteps": ["rate_setting"], "conditions": "All required fields completed with valid information"}, {"type": "action", "description": "User sets competitive rate and payment terms for the shipment", "actor": "Shipper/Broker", "nextSteps": ["carrier_requirements"], "conditions": "Rate meets minimum platform standards"}, {"type": "action", "description": "User specifies carrier qualification requirements and preferences", "actor": "Shipper/Broker", "nextSteps": ["load_posting"], "conditions": "Requirements align with available verified carriers"}, {"type": "system", "description": "System posts load to marketplace and notifies qualified carriers via smart matching", "actor": "Load Management System", "nextSteps": ["carrier_applications"], "conditions": "Load passes validation and quality checks"}, {"type": "action", "description": "Qualified verified drivers view and apply for the posted load", "actor": "Driver", "nextSteps": ["application_review"], "conditions": "Drivers meet specified qualification requirements"}, {"type": "action", "description": "Shipper/broker reviews driver applications and profiles to select best carrier", "actor": "Shipper/Broker", "nextSteps": ["carrier_assignment"], "conditions": "Suitable applications received within timeframe"}, {"type": "system", "description": "System assigns selected carrier and sends confirmation to all parties", "actor": "Load Management System", "nextSteps": ["tracking_activation"], "conditions": "Carrier accepts assignment and confirms availability"}, {"type": "system", "description": "Real-time tracking activates and pickup preparation begins", "actor": "Real-Time Tracking System", "nextSteps": [], "conditions": "Assignment confirmed and driver en route to pickup"}], "id": "1897595d-d85c-4df0-a7ce-cfba24066e87", "created": "2025-05-28T19:22:06.701Z", "updated": "2025-05-28T19:22:06.701Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "bfbd80d8-155e-4c0c-b109-2eb7addedfaf", "journey": null, "features": [], "agents": []}