{"name": "Driver Verification & Payment Flow", "description": "Core revenue-generating flow where drivers complete identity verification by uploading all required documents first, then pay the $25 fee at the end to activate premium platform features and verified status.", "entryPoints": ["Driver registration completion", "Mobile app onboarding", "Website signup verification prompt", "Referral from existing verified driver"], "exitPoints": ["Successful verification with premium access", "Payment failure requiring retry", "Verification rejection with remediation steps", "User abandonment during process"], "steps": [{"type": "action", "description": "Driver initiates verification process from dashboard or mobile app", "actor": "Driver", "nextSteps": ["document_requirements"], "conditions": "User has completed basic registration and profile setup"}, {"type": "system", "description": "System displays document requirements and verification process overview", "actor": "Driver Verification System", "nextSteps": ["document_upload"], "conditions": "Driver understands requirements and process"}, {"type": "action", "description": "Driver uploads required documents using Document Tag System (CDL, medical card, insurance, vehicle registration)", "actor": "Driver", "nextSteps": ["document_processing"], "conditions": "All required documents captured via mobile camera or file upload"}, {"type": "system", "description": "AI processes documents, extracts data, and validates against DMV and DOT databases", "actor": "Document Tag System", "nextSteps": ["document_validation"], "conditions": "Document quality meets requirements and data extraction successful"}, {"type": "system", "description": "System validates all documents for authenticity, currency, and completeness", "actor": "Driver Verification System", "nextSteps": ["verification_preview"], "conditions": "All documents pass initial validation checks"}, {"type": "system", "description": "System shows verification summary and premium features that will be unlocked", "actor": "Driver Verification System", "nextSteps": ["payment_prompt"], "conditions": "Documents validated and driver ready to complete verification"}, {"type": "system", "description": "System presents $25 verification fee with clear value proposition and Stripe payment interface", "actor": "Payment Processing System", "nextSteps": ["payment_action"], "conditions": "Driver sees full value of verification and is ready to pay"}, {"type": "action", "description": "Driver completes payment using credit card, debit card, or bank account via Stripe", "actor": "Driver", "nextSteps": ["payment_processing"], "conditions": "Valid payment method provided and driver commits to verification"}, {"type": "system", "description": "Stripe processes payment, system grants verified status, and unlocks premium features", "actor": "Payment Processing System & Platform System", "nextSteps": ["verification_complete"], "conditions": "Payment authorization successful and verification approved"}, {"type": "system", "description": "Driver receives verification badge, confirmation email, and immediate access to Smart Load Search", "actor": "Platform System", "nextSteps": [], "conditions": "Verification completed successfully and premium features activated"}], "id": "f9b619c5-0f16-4dee-a4d5-5be28ad0bdfb", "created": "2025-05-28T19:21:07.687Z", "updated": "2025-05-28T19:34:49.131Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "36f861e4-7a48-4fed-a130-6e179bdaf440", "journey": null, "features": ["cc1cc3ea-72d6-4076-907f-e2daf5338e33", "f3fbf74e-c571-4a78-81f0-a3a6e35f6074"], "agents": ["aaf67381-663d-4e5b-820d-f0e2348396cf"]}