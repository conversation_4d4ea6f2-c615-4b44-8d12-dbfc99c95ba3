{"name": "Shipper/Broker Registration Flow", "description": "Specialized registration flow for freight brokers and shippers with company verification, freight volume assessment, and direct path to load posting and carrier management features.", "entryPoints": ["Shipper/broker landing page", "B2B sales referral", "Industry conference lead", "LinkedIn outreach response"], "exitPoints": ["Registration complete with company verified", "Company verification failed", "Registration abandoned", "Account created awaiting first load post"], "steps": [{"type": "action", "description": "User accesses registration page and selects 'Shipper' or 'Broker' account type", "actor": "Shipper/Broker", "nextSteps": ["company_info"], "conditions": "User identifies as freight shipper or licensed broker"}, {"type": "action", "description": "User enters company name, MC/DOT numbers, business address, and contact information", "actor": "Shipper/Broker", "nextSteps": ["authority_verification"], "conditions": "Complete company information and operating authority provided"}, {"type": "system", "description": "System verifies company registration and freight authority against FMCSA databases", "actor": "Company Verification System", "nextSteps": ["volume_assessment", "verification_failed"], "conditions": "Company exists with valid operating authority"}, {"type": "action", "description": "User provides freight volume estimates, primary lanes, and typical shipment types", "actor": "Shipper/Broker", "nextSteps": ["security_requirements"], "conditions": "Business profile information completed"}, {"type": "action", "description": "User specifies security requirements, carrier qualification preferences, and verification needs", "actor": "Shipper/Broker", "nextSteps": ["account_creation"], "conditions": "Security and carrier requirements defined"}, {"type": "system", "description": "System creates company account with appropriate permissions and sends verification email", "actor": "Registration System", "nextSteps": ["email_verification"], "conditions": "Company information validated and account provisioned"}, {"type": "action", "description": "User verifies email address and completes company account activation", "actor": "Shipper/Broker", "nextSteps": ["dashboard_setup"], "conditions": "Email verification completed successfully"}, {"type": "system", "description": "Platform configures dashboard with load posting capabilities and carrier management tools", "actor": "Integrated Logistics Dashboard", "nextSteps": ["first_load_guidance"], "conditions": "Account activated with full platform access"}, {"type": "system", "description": "Outreach & Sales Agent provides guidance for posting first load and finding verified carriers", "actor": "Outreach & Sales Agent", "nextSteps": [], "conditions": "Registration completed and user ready for platform utilization"}], "id": "37849b49-ba3f-4177-ae99-b4fd0551dfb3", "created": "2025-05-28T19:27:23.692Z", "updated": "2025-05-28T19:27:23.692Z", "product": "ea528714-5e04-4677-8088-1cbcddce3ac2", "persona": "bfbd80d8-155e-4c0c-b109-2eb7addedfaf", "journey": null, "features": [], "agents": ["07732ddb-512e-4d3b-bf2f-0f4936279280"]}