# Shipment Verification Process

## Overview

The QuikSkope shipment verification process is designed to ensure secure and authenticated pickup operations. This document outlines the verification flow, API endpoints, and integration points for implementing this system into third-party applications.

## Verification Flow

The verification process consists of three main stages:

1. **Location Verification**: Confirming the driver is within the pickup radius
2. **Identity Verification**: Authenticating the driver through image verification of vehicle and required identification numbers
3. **Pickup Authorization**: Generating a pickup number for final verification at the pickup point

### Process Sequence Diagram

```
Driver                     QuikSkope System                     Shipper
  |                               |                               |
  |--- Request Verification ----->|                               |
  |                               |                               |
  |<-- Request Location Access ---|                               |
  |                               |                               |
  |---- Send Location Data ------>|                               |
  |                               |--- Validate Location Range ---|
  |                               |                               |
  |<-- Request Identity Proof ----|                               |
  |                               |                               |
  |---- Upload Verification ----->|                               |
  |        Image/Document         |                               |
  |                               |---- Process Verification ---->|
  |                               |       (Backend Analysis)      |
  |                               |                               |
  |<-- Loading State ---------------|                               |
  |                               |                               |
  |                               |-- Extract & Verify MC/USDOT --|
  |                               |-- Check EXIF Data ------------|
  |                               |-- Detect Image Tampering -----|
  |                               |-- Risk Assessment ------------|
  |                               |                               |
  |<-- Generate Pickup Number ----|                               |
  |      (If Verification         |                               |
  |       Successful)             |---- Notify Shipper ---------->|
  |                               |                               |
  |                               |<---- Shipper Ready Email -----|
  |<-- Notification of Ready -----|                               |
  |                               |                               |
  |------ Present Pickup -------->|                               |
  |        Number at Site         |                               |
```

## Implementation Details

### Location Verification

1. The system requests the driver's current GPS coordinates
2. Coordinates are compared with pickup location coordinates
3. Verification passes if the driver is within a 2-mile radius

```typescript
// Sample code for location verification check
const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number => {
  const R = 3958.8; // Earth's radius in miles
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

// Within 2 miles check
const isInRadius = distance <= 2; // 2 mile radius
```

### Identity Verification

1. Driver uploads an image of their truck showing the MC number and US DOT number
2. Backend verification process includes:
   - Extracting text from the image to verify MC/USDOT numbers match shipment expectations
   - Analyzing EXIF data to confirm the image is current and taken at the proper location
   - Detecting image tampering (picture-in-picture or digital manipulation)
   - Running vision model analysis to extract required identification numbers
   - Calculating a risk assessment score (verification fails if risk is greater than 2%)
3. Driver sees a loading state during the verification process
4. On successful verification, the system generates a pickup number and notifies the shipper
5. On failed verification, both the shipper and QuikSkope staff are notified

#### Additional Identification Support

The system is designed to be flexible enough to handle various vehicle identification numbers:

- MC number (required)
- US DOT number (required)
- KCU numbers
- State-specific regulatory numbers

### Pickup Authorization

1. System generates a unique 6-digit pickup number upon successful verification
2. Notifies the shipper that the driver has been verified
3. Shipper can notify driver via email when ready for loading

## API Integration

### Verification Endpoints

| Endpoint                                    | Method | Description                         |
| ------------------------------------------- | ------ | ----------------------------------- |
| `/drivers/shipments/:id/verify`             | GET    | Retrieves verification requirements |
| `/api/drivers/shipments/verifications/:id`  | POST   | Completes the verification process  |
| `/api/drivers/shipments/verification-image` | POST   | Uploads verification image          |

### Request/Response Examples

#### Complete Verification

**Request:**

```json
{
  "id": "verification_id",
  "document_id": "uploaded_document_id",
  "latitude": 34.052235,
  "longitude": -118.243683
}
```

**Response:**

```json
{
  "success": true,
  "verification": {
    "id": "verification_id",
    "verified_at": "2023-04-10T15:30:45Z",
    "pickup_number": "123456"
  }
}
```

### WebSocket Events

For real-time updates, clients can subscribe to verification status changes:

```typescript
// Example of subscription to verification updates
const channel = supabase
  .channel("schema-db-changes")
  .on(
    "postgres_changes",
    {
      event: "*",
      schema: "public",
      table: "verifications",
      filter: `id=eq.${verificationId}`,
    },
    (payload) => {
      console.log("Verification updated:", payload);
    },
  )
  .subscribe();
```

## Integration Checklist

For third-party applications integrating with QuikSkope's verification system, ensure:

1. ✅ Location access permission is properly requested from users
2. ✅ Secure storage of verification images and documents
3. ✅ Implementation of the pickup number display and verification
4. ✅ Real-time notification system for status updates
5. ✅ Proper error handling for failed verifications

## Security Considerations

- All verification data should be transmitted over HTTPS
- Images should be stored with proper access controls
- Pickup numbers should be single-use and time-limited
- Location data should be handled according to privacy regulations

## Testing

To test the integration, QuikSkope provides a sandbox environment where:

1. Test verification IDs can be used: `test_verification_123`
2. Test location coordinates: `34.052235, -118.243683`
3. Any image upload will pass verification in the sandbox

## Support

For integration assistance, contact QuikSkope developer support:

- Email: <EMAIL>
- API Documentation: https://docs.quikskope.com/api/verification
