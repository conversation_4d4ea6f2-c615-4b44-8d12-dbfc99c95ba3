# Codebase Planning & Discovery

This document outlines the architectural approach for the QuikSkope platform and defines the specialized backend services required to support the features in our [Development Roadmap](./roadmap.md).

---

## Architectural Approach: Frontend vs. Backend

To ensure a scalable and secure application, we will follow a clear separation of concerns between the frontend (React/Web App, React Native/Mobile App) and the backend (Supabase Edge Functions).

- **Frontend Responsibilities (Supabase Client):**
  - **Direct Database Operations:** The frontend applications will use the `supabase-js` client for most standard Create, Read, Update, and Delete (CRUD) operations on the database. This is suitable for fetching lists, displaying details, and submitting simple forms.
  - **Authentication & User Management:** The client will handle user sign-up, login, and session management. Row Level Security (RLS) policies in the database will enforce data access rules at the database layer.
  - **File Uploads:** The frontend will manage direct uploads to Supabase Storage.

- **Backend Responsibilities (Edge Functions):**
  - **Specialized & Complex Logic:** The backend will be reserved for tasks that are too complex or sensitive to run in the browser. This includes multi-step processes, integrations with third-party APIs, and heavy data processing.
  - **Security & Secrets:** Any operation requiring secret keys (e.g., calling the OpenAI API, processing payments with Stripe) **must** be handled in an Edge Function.
  - **Asynchronous & Scheduled Jobs:** Long-running tasks (generating a large report) or scheduled jobs (checking for expired credentials) will be implemented as backend functions.
  - **The services listed below are the identified backend-specific features.**

- **Key Technologies & Patterns:**
  - **Supabase Cron Jobs:** We will leverage Supabase's built-in scheduling to trigger functions for periodic tasks like checking credential expirations and processing monthly invoices.
  - **Event-Driven Architecture:** The `process-events` function forms the core of our event bus. This system is foundational but incomplete; it will be expanded to handle sophisticated routing and subscription logic for both internal workflows and external webhooks.

---

## Backend Services & Features by Phase

This section details the required backend functionality for each phase of the roadmap.

### Phase 2: Document Management

- **AI-Powered Document Processing:** A backend service is needed to receive an uploaded document, identify its type using AI, and extract structured data.
  - **Existing Work:** The `process-document` function provides a strong foundation for this entire workflow.

### Phase 3: Core Logistics System

- **Integrity Protocol Verification:** A secure backend endpoint is required to execute the multi-factor verification process (location, timestamp, AI image analysis).
  - **Existing Work:** The `verification-process` function is the robust, main implementation of this core feature. The related `process-verification` function is likely a helper or an earlier iteration of this flow.

### Phase 4: Driver Core Experience

- **Automated Credential Processing:** A service to take an uploaded driver's license image and use AI to extract key details, automating data entry.
  - **Existing Work:** The `process-credentials` function already handles this specific task.
- **Scheduled Credential Expiration Checker:** A cron job that runs periodically to check for expiring credentials and trigger alerts via the event system.

### Phase 7: Multi-Channel Event System

- **Event Bus & Router:** A central function to ingest events from across the platform and route them to the appropriate services (e.g., notifications, webhooks).
  - **Existing Work:** The `process-events` function serves as the entry point for this system.

### Phase 8: Payments, Marketplace Foundation, and Trust

- **Payment & Invoicing Services:** A suite of secure functions is required to handle all financial logic:
  - A secure endpoint to create Stripe `payment_intent` and `setup_intent` objects for one-time charges and saving customer payment methods.
  - A robust webhook handler to process all asynchronous events from Stripe (e.g., payment success, identity verification updates).
  - Scheduled functions (cron jobs) to handle recurring billing, such as processing monthly invoices for enterprise clients.
  - A service to execute payouts to drivers' connected accounts.

### Phase 9: Advanced Logistics Optimization

- **AI-Powered Matching & Optimization:** Backend services to run the complex algorithms for:
  - `Smart Load Search & Matching`.
  - `AI Smart Dispatch & Backhaul Optimization`.

### Phase 10: Fleet & Carrier Management

- **(No specialized backend services identified for this phase)** Most features are data aggregation and UI, which can be handled by the frontend Supabase client.

### Phase 11: Contracts & Digital Agreements

- **Automated Document Generation:** A backend service to take structured data and merge it with a document template (e.g., a PDF) to create a legally binding contract like a BOL.

### Phase 12: Developer Platform

- **API Gateway & Webhooks:** A single, unified Edge Function will serve as the main API Gateway, using a router like Hono to manage all incoming API requests. This provides a scalable and maintainable entry point for our public API. This gateway will also handle the dispatching of outgoing webhooks to subscribed users, powered by the Phase 7 Event System.

---

## Frontend Architecture & User Experience by Phase

This section outlines the user-facing components, views, and experiences required for each phase of the roadmap. The architecture will be component-based, allowing for reuse across web and mobile platforms.

### Phase 1: Core Platform & Organization Management

**✅ EXISTING COMPONENTS:**

- **Authentication System:** Complete auth flow with Supabase integration
  - `LoginForm`, `SignUpForm`, `ForgotPasswordForm`, `UpdatePasswordForm` in `src/components/authentication/`
  - Auth pages: `sign-in.tsx`, `sign-up.tsx`, `forgot-password.tsx`, `update-password.tsx`
- **Onboarding Flow:** Multi-step organization setup
  - `Onboarding.tsx` with driver and organization flows in `src/pages/app/onboarding/`
- **Organization Management:** Complete team and settings management
  - Organization pages in `src/pages/app/console/organizations/`
  - Team management, invitations, settings, accounts, integrations
  - API hooks: `use-create-organization`, `use-list-members`, `use-create-invitation`, etc.
- **Core Layouts:** Comprehensive layout system
  - `PublicLayout`, `AppLayout`, `ConsoleLayout` in `src/components/layouts/`
  - Responsive navigation with sidebar, header, footer components

**🔧 ENHANCEMENT NEEDED:**

- **Payment Processing Integration:** Stripe Elements components for subscription management
- **Mobile-First Responsive Design:** Enhanced mobile experience for organization management
- **Advanced Permission Management:** Role-based access control UI components

### Phase 2: Core Document Management

**✅ EXISTING COMPONENTS:**

- **Document Upload System:** Comprehensive file capture and upload
  - `UnifiedDropzone` with drag-and-drop, camera, file selection in `src/components/documents/capture/`
  - `ImageCapture`, `VideoCapture`, `AudioCapture`, `FileCapture` components
  - `DocumentUploader` widget with Supabase Storage integration
- **Document Management:** Full document lifecycle management
  - `DocumentViewer`, `DocumentPreviewPane`, `DocumentHeader` in `src/components/documents/`
  - `DocumentAnalysis`, `DocumentExtractedFields` for AI processing results
  - `DocumentFormDialog`, `CreateDocumentDialog` for document creation
  - Document listing in `src/components/documents/listing/`
- **Document Templates:** Pre-built document types
  - `BillOfLadingTemplate`, `ProofOfDeliveryTemplate`, `WeightTicketTemplate`
  - `FuelReceiptTemplate`, `TemperatureLogTemplate`, `DeliveryOrderTemplate`
  - Template router and builder system in `src/components/documents/templates/`
- **API Integration:** Complete document data layer
  - `use-get-document`, `use-list-documents`, `use-build-document` hooks
  - `use-update-avatar` for profile image management

**🔧 ENHANCEMENT NEEDED:**

- **Advanced Document Search:** Full-text search and metadata filtering
- **Document Versioning:** Version control and history tracking UI
- **Batch Operations:** Multi-document selection and bulk actions
- **Document Workflow:** Approval and review process components

### Phase 3: Automated Credential Storage

**✅ EXISTING COMPONENTS:**

- **Credential Processing:** AI-powered credential extraction
  - `DocumentExtractedFields` displays structured credential data
  - Integration with `process-credentials` backend function
  - Qualification management in `src/api/qualifications/`

**🔧 ENHANCEMENT NEEDED:**

- **Dedicated Credential UI:** Specialized credential display and management components
- **Expiration Tracking:** Visual indicators and alerts for expiring credentials
- **Credential Verification:** Status tracking and verification workflow UI

### Phase 4: Logistics Foundational Layer - Shipments & Stops

**✅ EXISTING COMPONENTS:**

- **Comprehensive Forms System:** Complete form infrastructure
  - `ShipmentForm`, `StopForm`, `LoadForm` in `src/components/forms/`
  - `ContactForm`, `LocationForm`, `VehicleForm`, `DriverForm`
  - Form fields with validation using react-hook-form and zod
- **Data Management:** Full CRUD operations for logistics entities
  - Shipments: `use-create-shipment`, `use-list-shipments`, `use-get-shipment`
  - Stops: `use-create-stop`, `use-list-stops`, `use-get-stop`
  - Loads: `use-create-load`, `use-list-loads`, `use-get-load`
  - Locations: `use-create-location`, `use-list-locations`, `use-get-location`
- **Console Pages:** Logistics management interface
  - Shipments, loads, locations, drivers, vehicles pages in `src/pages/app/console/logistics/`
  - List views with filtering, sorting, and pagination
- **Map Integration:** Mapbox-based location visualization
  - `LocationMap` component in `src/components/maps/`
  - Address autocomplete with `use-address-autocomplete` hook

**🔧 ENHANCEMENT NEEDED:**

- **Advanced Shipment Detail View:** Comprehensive shipment overview with timeline
- **Interactive Route Planning:** Drag-and-drop route optimization interface
- **Real-time Tracking Display:** Live shipment status and location updates
- **Stop Management UI:** Enhanced stop sequencing and management tools

### Phase 5: Driver Core Experience

**✅ EXISTING COMPONENTS:**

- **Driver Management System:** Complete driver lifecycle management
  - `DriverForm` for profile creation and editing
  - Driver pages in `src/pages/app/drivers/` (dashboard, profile, documents, payments, etc.)
  - Driver onboarding flow in `src/pages/app/onboarding/driver/`
  - API hooks: `use-create-driver`, `use-list-drivers`, `use-get-driver`
- **Driver Dashboard:** Dedicated driver interface
  - Driver-specific pages for shipments, documents, incidents, vehicles
  - `DriverShipmentContext` for shipment state management
  - Driver analytics with `use-driver-analytics`

**🔧 ENHANCEMENT NEEDED:**

- **Mobile-First Driver App:** React Native components for mobile driver experience
- **Identity Verification Flow:** Stripe Identity integration components
- **Driver Performance Dashboard:** Comprehensive metrics and scoring display
- **Load Matching Interface:** AI-powered load search and recommendation UI

### Phase 6: Multi-Channel Event System

**✅ EXISTING COMPONENTS:**

- **Notifications System:** Real-time notification infrastructure
  - `NotificationsPopover` widget in `src/components/widgets/`
  - `Notifications.tsx` page in `src/pages/app/notifications/`
  - API hooks: `use-list-notifications`, `use-list-unread-notifications`
  - Real-time subscriptions with `use-notifications-subscription`

**🔧 ENHANCEMENT NEEDED:**

- **Multi-Channel Delivery:** Email, SMS, push notification components
- **Notification Preferences:** User preference management UI
- **Event Routing Dashboard:** Admin interface for event system configuration

### Phase 7: Reporting & Reputation

**✅ EXISTING COMPONENTS:**

- **Analytics Infrastructure:** Dashboard and metrics foundation
  - `AnalyticsSection` in console dashboard
  - `use-dashboard-metrics`, `use-analytics-snapshot` hooks
  - Chart components in `src/components/ui/chart.tsx`
- **Console Dashboard:** Executive overview interface
  - `ConsoleDashboardPage` with active shipments, pending incidents
  - `ActiveShipments`, `PendingIncidents`, `RecentDocuments` widgets

**🔧 ENHANCEMENT NEEDED:**

- **Advanced Analytics Platform:** Comprehensive reporting with drill-down capabilities
- **Reputation System UI:** Driver and organization scoring displays
- **Custom Report Builder:** User-configurable report generation interface
- **Performance Metrics Dashboard:** KPI tracking and trend analysis

### Phase 8: Communication Platform

**✅ EXISTING COMPONENTS:**

- **Real-time Chat System:** Complete messaging infrastructure
  - `RealtimeChat` component in `src/components/realtime-chat.tsx`
  - `ChatMessage` component for message display
  - `use-realtime-chat` hook for chat functionality
  - `use-chat-scroll` for auto-scrolling behavior

**🔧 ENHANCEMENT NEEDED:**

- **Multi-Party Chat:** Group conversations for shipment stakeholders
- **File Sharing:** Document and image sharing within chat
- **Chat History:** Message persistence and search functionality

### Phases 9 & 12: Marketplace & Payments

**✅ EXISTING COMPONENTS:**

- **Billing Infrastructure:** Payment and subscription management
  - Billing pages in `src/pages/app/console/billing/`
  - Payment and payroll sections
  - `ConsoleBillingPage` for financial overview

**🔧 ENHANCEMENT NEEDED:**

- **Stripe Elements Integration:** Secure payment form components
- **Marketplace UI:** Load board and bidding interface
- **Payout Management:** Driver earnings and bank account connection
- **Transaction Ledger:** Comprehensive financial transaction history
- **Subscription Management:** Plan selection and billing preferences

### Phase 10: Carrier Platform

**🔧 ENHANCEMENT NEEDED:**

- **Fleet Management Dashboard:** Comprehensive carrier interface for managing drivers and vehicles
- **Dispatch Console:** Advanced load assignment and optimization tools
- **Carrier Analytics:** Performance metrics and fleet utilization reports
- **Private Load Board:** Carrier-specific load posting and management

### Phase 11: Contracts & Digital Agreements

**🔧 ENHANCEMENT NEEDED:**

- **E-Signature Integration:** Digital signature capture and verification
- **Contract Templates:** Legal document generation and management
- **Agreement Workflow:** Multi-party approval and signing process
- **Contract Repository:** Searchable contract storage and retrieval

### Phase 13: Driver Identity Verification

**🔧 ENHANCEMENT NEEDED:**

- **Stripe Identity Integration:** Government ID verification flow
- **Verification Dashboard:** Status tracking and document management
- **Compliance Monitoring:** Ongoing verification status and alerts
- **Trust Score Display:** Driver reputation and verification level indicators

---

## Frontend Architecture Summary

### **Current Component Inventory**

**✅ STRONG FOUNDATION (90%+ Complete):**

- **Authentication & Authorization:** Complete Supabase auth integration
- **Document Management:** Comprehensive upload, processing, and template system
- **Forms & Validation:** Robust form infrastructure with react-hook-form + zod
- **Data Layer:** Complete API hooks for all major entities
- **Layout System:** Responsive layouts for public, app, and console interfaces
- **UI Components:** Full Shadcn/Radix component library with custom theming
- **Real-time Features:** Chat, notifications, and live data subscriptions

**🔧 MODERATE FOUNDATION (50-70% Complete):**

- **Logistics Management:** Core CRUD operations exist, need enhanced UX
- **Driver Experience:** Basic driver interface, needs mobile optimization
- **Analytics & Reporting:** Foundation exists, needs advanced features
- **Organization Management:** Complete backend, needs UI enhancements

**🚧 NEEDS DEVELOPMENT (0-30% Complete):**

- **Payment Processing:** Stripe integration components needed
- **Identity Verification:** Stripe Identity flow components
- **Advanced Analytics:** Custom reporting and business intelligence
- **Marketplace Features:** Load board and matching interfaces
- **Fleet Management:** Carrier-specific management tools
- **E-Signature:** Digital agreement and contract signing

### **Architecture Strengths**

1. **Separation of Concerns:** Clean separation between data (API hooks) and presentation (components)
2. **Type Safety:** Full TypeScript integration with Supabase type generation
3. **Component Reusability:** Well-organized component hierarchy with clear responsibilities
4. **Real-time Capabilities:** Built-in subscriptions and live data updates
5. **Mobile-Ready:** Responsive design patterns and mobile-first considerations
6. **Developer Experience:** Comprehensive tooling with Storybook, testing, and documentation

### **Next Development Priorities**

1. **Mobile Driver App:** React Native implementation for driver-facing features
2. **Payment Integration:** Stripe Elements and Connect implementation
3. **Advanced Analytics:** Business intelligence and custom reporting
4. **Marketplace UI:** Load board and matching algorithm interfaces
5. **Identity Verification:** Stripe Identity integration for driver onboarding
6. **Fleet Management:** Carrier-specific dashboards and dispatch tools

---

## Conclusion & Next Steps

This comprehensive analysis reveals a **robust frontend foundation** with approximately **70% of core components already implemented**. The existing architecture provides excellent scaffolding for rapid feature development.

**Immediate Action Items:**

1. **Mobile Development:** Begin React Native app development using existing component patterns
2. **Payment Integration:** Implement Stripe Elements for subscription and marketplace features
3. **Enhanced UX:** Improve existing logistics and driver interfaces with advanced interactions
4. **Analytics Platform:** Build comprehensive reporting on the existing metrics foundation

The strong component architecture, complete API layer, and real-time infrastructure position the platform for accelerated development of remaining marketplace and advanced features.
