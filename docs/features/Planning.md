# Codebase Planning & Discovery

This document outlines the architectural approach for the QuikSkope platform and defines the specialized backend services required to support the features in our [Development Roadmap](./roadmap.md).

---

## Architectural Approach: Frontend vs. Backend

To ensure a scalable and secure application, we will follow a clear separation of concerns between the frontend (React/Web App, React Native/Mobile App) and the backend (Supabase Edge Functions).

- **Frontend Responsibilities (Supabase Client):**
  - **Direct Database Operations:** The frontend applications will use the `supabase-js` client for most standard Create, Read, Update, and Delete (CRUD) operations on the database. This is suitable for fetching lists, displaying details, and submitting simple forms.
  - **Authentication & User Management:** The client will handle user sign-up, login, and session management. Row Level Security (RLS) policies in the database will enforce data access rules at the database layer.
  - **File Uploads:** The frontend will manage direct uploads to Supabase Storage.

- **Backend Responsibilities (Edge Functions):**
  - **Specialized & Complex Logic:** The backend will be reserved for tasks that are too complex or sensitive to run in the browser. This includes multi-step processes, integrations with third-party APIs, and heavy data processing.
  - **Security & Secrets:** Any operation requiring secret keys (e.g., calling the OpenAI API, processing payments with Stripe) **must** be handled in an Edge Function.
  - **Asynchronous & Scheduled Jobs:** Long-running tasks (generating a large report) or scheduled jobs (checking for expired credentials) will be implemented as backend functions.
  - **The services listed below are the identified backend-specific features.**

- **Key Technologies & Patterns:**
  - **Supabase Cron Jobs:** We will leverage Supabase's built-in scheduling to trigger functions for periodic tasks like checking credential expirations and processing monthly invoices.
  - **Event-Driven Architecture:** The `process-events` function forms the core of our event bus. This system is foundational but incomplete; it will be expanded to handle sophisticated routing and subscription logic for both internal workflows and external webhooks.

---

## Backend Services & Features by Phase

This section details the required backend functionality for each phase of the roadmap.

### Phase 2: Document Management

- **AI-Powered Document Processing:** A backend service is needed to receive an uploaded document, identify its type using AI, and extract structured data.
  - **Existing Work:** The `process-document` function provides a strong foundation for this entire workflow.

### Phase 3: Core Logistics System

- **Integrity Protocol Verification:** A secure backend endpoint is required to execute the multi-factor verification process (location, timestamp, AI image analysis).
  - **Existing Work:** The `verification-process` function is the robust, main implementation of this core feature. The related `process-verification` function is likely a helper or an earlier iteration of this flow.

### Phase 4: Driver Core Experience

- **Automated Credential Processing:** A service to take an uploaded driver's license image and use AI to extract key details, automating data entry.
  - **Existing Work:** The `process-credentials` function already handles this specific task.
- **Scheduled Credential Expiration Checker:** A cron job that runs periodically to check for expiring credentials and trigger alerts via the event system.

### Phase 7: Multi-Channel Event System

- **Event Bus & Router:** A central function to ingest events from across the platform and route them to the appropriate services (e.g., notifications, webhooks).
  - **Existing Work:** The `process-events` function serves as the entry point for this system.

### Phase 8: Payments, Marketplace Foundation, and Trust

- **Payment & Invoicing Services:** A suite of secure functions is required to handle all financial logic:
  - A secure endpoint to create Stripe `payment_intent` and `setup_intent` objects for one-time charges and saving customer payment methods.
  - A robust webhook handler to process all asynchronous events from Stripe (e.g., payment success, identity verification updates).
  - Scheduled functions (cron jobs) to handle recurring billing, such as processing monthly invoices for enterprise clients.
  - A service to execute payouts to drivers' connected accounts.

### Phase 9: Advanced Logistics Optimization

- **AI-Powered Matching & Optimization:** Backend services to run the complex algorithms for:
  - `Smart Load Search & Matching`.
  - `AI Smart Dispatch & Backhaul Optimization`.

### Phase 10: Fleet & Carrier Management

- **(No specialized backend services identified for this phase)** Most features are data aggregation and UI, which can be handled by the frontend Supabase client.

### Phase 11: Contracts & Digital Agreements

- **Automated Document Generation:** A backend service to take structured data and merge it with a document template (e.g., a PDF) to create a legally binding contract like a BOL.

### Phase 12: Developer Platform

- **API Gateway & Webhooks:** A single, unified Edge Function will serve as the main API Gateway, using a router like Hono to manage all incoming API requests. This provides a scalable and maintainable entry point for our public API. This gateway will also handle the dispatching of outgoing webhooks to subscribed users, powered by the Phase 7 Event System.

---

## Frontend Architecture & User Experience by Phase

This section outlines the user-facing components, views, and experiences required for each phase of the roadmap. The architecture will be component-based, allowing for reuse across web and mobile platforms.

### Phase 1: Core Platform & Organization Management

- **User Authentication:** Forms for login, registration, and password recovery.
- **Onboarding Flow:** A multi-step UI to guide new users through creating their organization and setting up their account.
- **Organization Management:** Dashboards for inviting team members, managing roles/permissions, and updating company settings.
- **Core Layout:** The main application shell, including primary navigation (sidebar/header), and content areas.

### Phase 2: Core Document Management

- **`DocumentUploader`:** A reusable component for file uploads, featuring a drag-and-drop zone.
- **`DocumentsList`:** A data table to display all documents with filtering, sorting, and status indicators.
- **`DocumentViewer`:** A view to render documents (PDFs, images) and display associated metadata extracted by the backend.

### Phase 3: Automated Credential Storage

- **`CredentialDisplay`:** A UI within the `DocumentViewer` or as a standalone component to show extracted credential data in a structured, readable format.

### Phase 4: Logistics Foundational Layer - Shipments & Stops

- **Forms:** Modular forms for creating and editing shipments and their associated stops.
- **`ShipmentsList`:** A data table for viewing all shipments, with statuses (active, pending, completed) and key details.
- **`ShipmentDetailView`:** A comprehensive page showing all data for a single shipment, including its route, stops, and related documents.
- **`MapView`:** An interactive map component to visualize shipment routes, and driver locations.

### Phase 5: Driver Core Experience

- **Driver Onboarding:** A streamlined, mobile-friendly flow for drivers to create a profile.
- **Credential Management:** A simple UI for drivers to upload their licenses and other documents, reusing the `DocumentUploader`.

### Phase 6: Multi-Channel Event System

- **`NotificationsPopover`:** A real-time UI element (e.g., a bell icon) that displays unread notifications.
- **`NotificationsPage`:** A dedicated screen to view a history of all notifications.

### Phase 7: Reporting & Reputation

- **Dashboard Widgets:** A library of visual components for displaying key metrics and analytics at a glance.
- **Analytics Dashboard:** A full-page interface with interactive charts and data visualizations for in-depth reporting.

### Phase 8: Communication Platform

- **`ChatInterface`:** A real-time messaging component enabling communication between stakeholders of a shipment.

### Phases 9 & 12: Marketplace & Payments

- **Payment Forms:** Secure, embedded forms (leveraging Stripe Elements) for adding payment methods and completing transactions.
- **Payouts Dashboard:** A section for drivers/carriers to connect bank accounts via Stripe Connect, view earnings, and track payout history.
- **Transaction History:** A ledger-style view to display all payments, invoices, and payouts.

### Phase 10: Carrier Platform

- **Fleet Management UI:** Dashboards for carriers to view and manage their drivers and vehicles.
- **Dispatch UI:** Interactive tools for assigning drivers and vehicles to shipments.

### Phase 11: Contracts & Digital Agreements

- **`E-SignatureInterface`:** A component to view and electronically sign documents, securely capturing user consent.

### Phase 13: Driver Identity Verification

- **`VerificationFlow`:** A guided, step-by-step UI for the identity verification process, likely integrating a third-party SDK like Stripe Identity.

---

## Conclusion & Next Steps

This breakdown provides a clear plan for our backend development. We have a strong foundation with the existing AI and verification functions.

The next logical step is to begin **frontend discovery**, planning the user interfaces and components that will interact with these backend services and the Supabase database. We can now confidently plan the UI for features like `Document Management` and `Driver Onboarding`, knowing what backend capabilities are already in place or planned.
