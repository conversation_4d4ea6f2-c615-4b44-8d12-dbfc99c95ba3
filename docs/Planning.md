# Planning

## Phase 1

- [ ] Update forms to use zod and react-hook-form
- [ ] move all the api calls to the api folder
- [ ] separate data layer from presentation layer in pages
- [ ] Work on documentation and knowledge prompts

## Phase 2

- [ ] Work on getting the database right
- [ ] Change avatar_url to avatar (and make it work as expected)
- [ ] Move Loads to be the core of the shipment management
- [ ] make loads not tied to a shipment or verification, rather a load can have many shipments and verifications
- [ ] Make loads composable from many "containers" (containers are the actual shipment items, and content types are the different types of items that can be added to a shipment)
- [ ] Remove stops from loads and replace with locations, make each stop have a load
- [ ] setup documents from the start to handle avatars, documents and files

## Phase 3

- [ ] Create fields and forms for all the things
- [ ] Start with base enums and types (create fields, labels, etc)
- [ ] Create the organization management (users, roles, permissions, etc)
- [ ] finish TMS

## Backend

- [ ] Add ability to create contracts via PDF renderer and templates
- [ ] Add realtime positioning and correlate it with shipment and load
- [ ] Add document scanner via vision model AI
- [ ] Add importing of containers and loads from images
- [ ] Genlogs integration
- [ ] Add carriers and more identification fields (ky, ca numbers)

## Frontend

- [ ] Mapbox react query integration

## Features

- [ ] Notifications (fetch, mark read, preferences)
- [ ] User management (create, update, delete, etc)
- [ ] Organization management (create, update, delete, etc)
- [ ] Driver management (create, update, delete, etc)
- [ ] Vehicle management (create, update, delete, etc)
- [ ] Qualification management (create, update, delete, etc)
- [ ] Incident management (create, update, delete, etc)
- [ ] Load management (create, update, delete, etc)
- [ ] Shipment management (create, update, delete, etc)
- [ ] Document management (create, update, delete, etc)
- [ ] Contract management (create, update, delete, etc)
- [ ] Billing management (create, update, delete, etc)
- [ ] Analytics and reporting

## AI

- [ ] Analyze documents and extract data
- [ ] Analyze cargo and create load item
