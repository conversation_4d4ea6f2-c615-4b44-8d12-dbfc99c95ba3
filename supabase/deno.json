{"root": true, "imports": {"zod": "npm:zod@3.25.74", "ai": "npm:ai@4.3.16", "@ai-sdk/openai": "npm:@ai-sdk/openai@1.3.22", "@turf/turf": "npm:@turf/turf@7.2.0"}, "compilerOptions": {"bundle": true, "target": "es2023", "lib": ["deno.ns", "ES2023", "DOM"], "module": "ESNext", "types": [], "allowJs": false, "checkJs": false, "noImplicitAny": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true}, "lint": {"rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars"]}}, "fmt": {"useTabs": true, "lineWidth": 80, "indentWidth": 4, "semiColons": false, "singleQuote": true, "proseWrap": "preserve", "include": ["supabase"]}, "lock": true, "nodeModulesDir": "auto", "unstable": ["webgpu"], "test": {"include": ["__tests__/"], "exclude": ["__tests__/fixtures/**/*.ts"]}, "tasks": {"start": "deno run --allow-read supabase/functions/build-document/index.ts"}, "exclude": ["dist/"]}