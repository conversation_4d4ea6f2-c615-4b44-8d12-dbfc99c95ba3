enum user_role {
  system
  admin
  user

  @@schema("public")
}

model users {
  id                String    @id @default(cuid()) @db.Uuid
  created_at        DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at        DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at        DateTime? @db.Timestamptz(6)
  accepted_terms_at DateTime? @db.Timestamptz(6)
  onboarded_at      DateTime? @db.Timestamptz(6)

  role         user_role @default(user)
  username     String?
  first_name   String
  last_name    String
  phone_number String    @unique
  email        String    @unique
  avatar       String?

  driver        drivers?
  memberships   members[]
  notifications notifications[]
  invitations   invitations[]
  documents     documents[]
  verifications verifications[]

  @@schema("public")
}

enum notification_type {
  shipment_update
  incident_report
  system_alert
  payment_update

  @@schema("public")
}

model notifications {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)
  read_at    DateTime? @db.Timestamptz(6)

  type    notification_type
  title   String
  message String

  user_id String @db.Uuid
  user    users  @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@schema("public")
}

enum document_type {
  file
  contract

  @@schema("public")
}

model documents {
  id           String    @id @default(cuid()) @db.Uuid
  created_at   DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at   DateTime? @db.Timestamptz(6)
  // bucket
  bucket_id    String    @default("documents")
  storage_path String    @unique
  content_type String?
  size         BigInt?
  name         String
  description  String?

  url      String        @default("")
  type     document_type @default(file)
  metadata Json?         @default("{}")

  latitude  Decimal? @db.Decimal
  longitude Decimal? @db.Decimal

  created_by      String?        @db.Uuid
  user            users?         @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  driver_id       String?        @db.Uuid
  driver          drivers?       @relation(fields: [driver_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organization_id String?        @db.Uuid
  organization    organizations? @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  verification    verifications? @relation(fields: [verification_id], references: [id])
  verification_id String?        @db.Uuid

  qualification qualifications?

  @@index([driver_id])
  @@index([organization_id])
  @@index([type])
  @@index([url])
  @@schema("public")
}

enum members_role {
  owner
  admin
  billing
  member
  viewer

  @@schema("public")
}

enum membership_status {
  pending
  active
  inactive
  declined

  @@schema("public")
}

model members {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  email  String
  role   members_role      @default(member)
  status membership_status @default(pending)

  organization_id String        @db.Uuid
  organization    organizations @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user_id         String?       @db.Uuid
  user            users?        @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([organization_id, email], map: "unique_member_per_organization")
  @@schema("public")
}

enum invitation_status {
  pending
  accepted
  revoked
  rejected
  expired

  @@schema("public")
}

model invitations {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)
  expires_at DateTime? @db.Timestamptz(6)

  email  String
  status invitation_status @default(pending)
  role   members_role?     @default(member)

  created_by      String         @db.Uuid
  user            users          @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organization_id String?        @db.Uuid
  organization    organizations? @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([email, organization_id], map: "invitations_email_organization_unique")
  @@schema("public")
}

enum organization_status {
  pending
  active
  suspended
  inactive

  @@schema("public")
}

enum organization_type {
  individual
  private
  non_profit
  government

  @@schema("public")
}

enum organization_functions {
  logistics
  carrier
  warehousing
  distribution

  @@schema("public")
}

model organizations {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  // integrations - stripe
  stripe_customer_id String? @unique
  stripe_account_id  String? @unique

  functions organization_functions[] @default([logistics])
  status    organization_status      @default(pending)
  type      organization_type        @default(private)
  name      String
  avatar    String?
  size      String?
  industry  String?
  website   String?
  phone     String?
  email     String?

  location_id String?    @unique @db.Uuid
  location    locations? @relation(fields: [location_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  documents   documents[]
  invitations invitations[]
  loads       loads[]
  members     members[]
  shipments   shipments[]
  drivers     drivers[]
  vehicles    vehicles[]

  // settings
  settings Json? @default("{}")

  @@schema("public")
}

enum driver_status {
  active
  inactive
  suspended

  @@schema("public")
}

model drivers {
  id          String    @id @default(cuid()) @db.Uuid
  created_at  DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at  DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at  DateTime? @db.Timestamptz(6)
  verified_at DateTime? @db.Timestamptz(6)

  // integrations - stripe
  stripe_customer_id String? @unique
  stripe_account_id  String? @unique

  status driver_status? @default(active)
  score  Int            @default(100)
  tier   String         @default("free")

  user_id         String?        @unique @db.Uuid
  user            users?         @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organization_id String?        @db.Uuid
  organization    organizations? @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  location_id     String?        @unique @db.Uuid
  location        locations?     @relation(fields: [location_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  vehicles       vehicles[]       @relation("driver_vehicles")
  documents      documents[]
  qualifications qualifications[]
  shipments      shipments[]
  verifications  verifications[]
  incidents      incidents[]

  // settings
  settings Json? @default("{}")

  @@schema("public")
}

enum qualification_status {
  pending
  verified
  expired
  revoked
  invalid

  @@schema("public")
}

enum qualification_type {
  commercial_drivers_license
  hazmat_endorsement
  medical_certificate
  defensive_driving_certificate
  tanker_endorsement
  doubles_triples_endorsement
  other

  @@schema("public")
}

model qualifications {
  id          String    @id @default(cuid()) @db.Uuid
  created_at  DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at  DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at  DateTime? @db.Timestamptz(6)
  issued_at   DateTime  @db.Timestamptz(6)
  expires_at  DateTime? @db.Timestamptz(6)
  verified_at DateTime? @db.Timestamptz(6)

  type          qualification_type
  status        qualification_status @default(pending)
  issuing_state String

  document_id String?    @unique @db.Uuid
  document    documents? @relation(fields: [document_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  driver_id   String     @db.Uuid
  driver      drivers    @relation(fields: [driver_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([driver_id], map: "idx_qualifications_driver_id")
  @@schema("public")
}

model vehicles {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  make          String
  model         String
  year          Int
  license_state String
  license_plate String
  vin           String @unique

  us_dot    String
  mc_number String

  drivers       drivers[]       @relation("driver_vehicles")
  verifications verifications[]

  organization_id String?        @db.Uuid
  organization    organizations? @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

model trailers {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  @@schema("public")
}
