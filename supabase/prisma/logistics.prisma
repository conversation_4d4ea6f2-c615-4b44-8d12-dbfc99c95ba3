enum location_type {
  billing
  residential
  retail
  commercial
  industrial
  manufacturing
  warehouse
  distribution_center
  port
  rail_terminal
  other

  @@schema("public")
}

enum location_sector {
  public
  private
  government
  military
  educational
  healthcare
  religious

  @@schema("public")
}

model locations {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  type         location_type   @default(other)
  sector       location_sector @default(private)
  formatted    String
  street       String?
  suite        String?
  city         String?
  neighborhood String?
  postal       String?
  state        String?
  country      String
  latitude     Float
  longitude    Float

  organization organizations?
  driver       drivers?

  stops                 stops[]
  shipment_origins      shipments[] @relation("origin")
  shipment_destinations shipments[] @relation("destination")
  load_origins          loads[]     @relation("origin")
  load_destinations     loads[]     @relation("destination")

  @@schema("public")
}

model positions {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  latitude  Decimal @db.Decimal(10, 8)
  longitude Decimal @db.Decimal(11, 8)

  shipment_id String    @db.Uuid
  shipment    shipments @relation(fields: [shipment_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([shipment_id], map: "idx_positions_shipment_id")
  @@schema("public")
}

enum load_status {
  draft
  pending
  assigned
  packaged
  loaded
  in_transit
  out_for_delivery
  delivered
  exception
  missing
  rejected
  returned
  cancelled
  customs_hold

  @@schema("public")
}

enum load_category {
  dry_goods
  liquid_bulk
  dry_bulk
  refrigerated
  frozen
  perishable
  hazmat
  oversized
  heavy_haul
  high_value
  fragile
  automotive
  construction_materials
  retail_consumer
  industrial_equipment

  @@schema("public")
}

enum physical_state {
  solid
  liquid
  gas
  bulk_solid
  bulk_liquid
  packaged
  palletized
  loose

  @@schema("public")
}

enum temperature_requirement {
  ambient
  refrigerated
  frozen
  heated
  temperature_controlled
  dry_ice_required

  @@schema("public")
}

enum hazmat_class {
  none
  class_1_explosives
  class_2_gases
  class_3_flammable_liquids
  class_4_flammable_solids
  class_5_oxidizers
  class_6_toxic_substances
  class_7_radioactive
  class_8_corrosives
  class_9_miscellaneous

  @@schema("public")
}

enum loading_method {
  dock_high
  ground_level
  crane_required
  forklift
  hand_load
  pump_load
  gravity_feed
  conveyor
  side_load
  top_load

  @@schema("public")
}

// Segregation requirements
enum segregation_level {
  none
  same_compartment_ok
  separate_compartments
  separate_trailers
  cannot_ship_together

  @@schema("public")
}

enum security_level {
  standard
  high_value
  theft_target
  secured_facility_only
  bonded_carrier_required
  escort_required

  @@schema("public")
}

enum trailer_type {
  dry_van
  refrigerated
  flatbed
  step_deck
  lowboy
  tanker
  hopper
  container
  car_carrier
  livestock
  dump
  curtain_side
  conestoga
  specialized

  @@schema("public")
}

// New enums for logistics
enum service_level {
  standard
  expedited
  overnight
  same_day
  white_glove

  @@schema("public")
}

model loads {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  pickup_date_from   DateTime?
  pickup_date_to     DateTime?
  delivery_date_from DateTime?
  delivery_date_to   DateTime?

  status               load_status     @default(pending)
  priority             Int             @default(1) // 1=highest, 5=lowest
  service_level        service_level   @default(standard)
  description          String?
  commodity_code       String? // NMFC or other commodity codes
  primary_category     load_category
  secondary_categories load_category[]

  physical_state          physical_state
  temperature_requirement temperature_requirement
  temp_range_min          Float?
  temp_range_max          Float?

  quantity      Int     @default(1)
  length        Float?
  width         Float?
  height        Float?
  volume        Float?
  volume_unit   String?
  weight        Float?
  weight_unit   String?
  is_oversized  Boolean @default(false)
  is_overweight Boolean @default(false)
  max_length    Float?
  max_width     Float?
  max_height    Float?
  max_weight    Float?

  // Hazmat classification
  hazmat_class    hazmat_class @default(none)
  hazmat_subclass String? // e.g., "2.1", "6.1"
  un_number       String? // UN identification number

  security_level security_level @default(standard)
  declared_value Decimal?

  loading_method         loading_method[]
  requires_special_equip Boolean          @default(false)
  special_equipment      String?

  can_mix_with_food      Boolean @default(true)
  can_mix_with_chemicals Boolean @default(false)
  requires_clean_trailer Boolean @default(false)
  contamination_risk     Boolean @default(false)
  odor_risk              Boolean @default(false)

  recommended_trailers trailer_type[]
  prohibited_trailers  trailer_type[]

  requires_certified_driver Boolean @default(false)
  requires_permits          Boolean @default(false)

  is_fragile           Boolean @default(false)
  is_stackable         Boolean @default(true)
  is_times_sensitive   Boolean @default(false)
  max_transit_time     Int?
  requires_air_ride    Boolean @default(false)
  special_handling     String?
  special_instructions String?

  origin_id       String?        @db.Uuid
  origin          locations?     @relation("origin", fields: [origin_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  destination_id  String?        @db.Uuid
  destination     locations?     @relation("destination", fields: [destination_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organization_id String?        @db.Uuid
  organization    organizations? @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  shipment_id     String?        @db.Uuid
  shipment        shipments?     @relation(fields: [shipment_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  incidents     incidents[]
  stops         stops[]         @relation("load_stops")
  verifications verifications[] @relation("load_verifications")

  @@index([shipment_id], map: "idx_cargo_shipment_id")
  @@index([destination_id], map: "idx_cargo_destination")
  @@index([origin_id], map: "idx_cargo_origin")
  @@index([organization_id], map: "idx_cargo_organization_id")
  @@schema("public")
}

enum shipment_mode {
  open
  closed

  @@schema("public")
}

enum shipment_source {
  driver
  organization
  system

  @@schema("public")
}

enum shipment_status {
  pending
  scheduled
  assigned
  confirmed
  in_progress
  completed
  cancelled

  @@schema("public")
}

enum shipment_type {
  air
  ocean
  ground
  other

  @@schema("public")
}

model shipments {
  id           String    @id @default(cuid()) @db.Uuid
  created_at   DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at   DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at   DateTime? @db.Timestamptz(6)
  assigned_at  DateTime? @db.Timestamptz(6)
  confirmed_at DateTime? @db.Timestamptz(6)
  started_at   DateTime? @db.Timestamptz(6)
  completed_at DateTime? @db.Timestamptz(6)
  cancelled_at DateTime? @db.Timestamptz(6)

  status    shipment_status @default(pending)
  source    shipment_source @default(system)
  type      shipment_type   @default(ground)
  mode      shipment_mode   @default(closed)
  valuation Decimal?        @db.Decimal
  distance  Decimal?        @db.Decimal
  weight    Decimal?        @db.Decimal

  positions     positions[]
  loads         loads[]
  stops         stops[]
  verifications verifications[]
  incidents     incidents[]

  origin_id      String?    @db.Uuid
  origin         locations? @relation("origin", fields: [origin_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  destination_id String?    @db.Uuid
  destination    locations? @relation("destination", fields: [destination_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  driver_id       String?        @db.Uuid
  driver          drivers?       @relation(fields: [driver_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organization_id String?        @db.Uuid
  organization    organizations? @relation(fields: [organization_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([organization_id], map: "idx_shipments_organization_id")
  @@schema("public")
}

enum stop_type {
  pickup
  drop_off
  rest_stop
  gas_station
  maintenance_facility
  customs_facility
  weigh_station
  other

  @@schema("public")
}

model stops {
  id          String    @id @default(cuid()) @db.Uuid
  created_at  DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at  DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at  DateTime? @db.Timestamptz(6)
  arrived_at  DateTime? @db.Timestamptz(6)
  departed_at DateTime? @db.Timestamptz(6)

  sequence_number Int
  latitude        Float?
  longitude       Float?
  type            stop_type @default(other)
  label           String?
  tags            String[]

  loads     loads[]     @relation("load_stops")
  incidents incidents[]

  verification verifications?

  location_id String?    @db.Uuid
  location    locations? @relation(fields: [location_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  shipment_id String     @db.Uuid
  shipment    shipments  @relation(fields: [shipment_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([shipment_id, sequence_number])
  @@index([latitude, longitude], map: "idx_stops_coords")
  @@index([type], map: "idx_stops_type")
  @@schema("public")
}

model verifications {
  id          String    @id @default(cuid()) @db.Uuid
  created_at  DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at  DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at  DateTime? @db.Timestamptz(6)
  verified_at DateTime? @db.Timestamptz(6)

  notes     String?
  latitude  Decimal? @db.Decimal
  longitude Decimal? @db.Decimal

  loads     loads[]     @relation("load_verifications")
  documents documents[]
  incidents incidents[]

  verified_by String?    @db.Uuid
  user        users?     @relation(fields: [verified_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  driver_id   String?    @db.Uuid
  driver      drivers?   @relation(fields: [driver_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  vehicle_id  String?    @db.Uuid
  vehicle     vehicles?  @relation(fields: [vehicle_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  shipment_id String?    @db.Uuid
  shipment    shipments? @relation(fields: [shipment_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  stop_id     String     @unique @db.Uuid
  stop        stops      @relation(fields: [stop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([vehicle_id], map: "idx_verifications_vehicle_id")
  @@index([shipment_id], map: "idx_verifications_shipment_id")
  @@index([stop_id], map: "idx_verifications_stop_id")
  @@schema("public")
}

enum incident_severity {
  low
  medium
  high
  critical

  @@schema("public")
}

enum incident_status {
  reported
  investigating
  resolved
  closed

  @@schema("public")
}

enum incident_type {
  verification
  accident
  delay
  damage
  theft
  weather
  mechanical
  other

  @@schema("public")
}

model incidents {
  id         String    @id @default(cuid()) @db.Uuid
  created_at DateTime  @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)
  updated_at DateTime  @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  title    String
  type     incident_type
  status   incident_status   @default(reported)
  severity incident_severity
  summary  String?

  shipment_id     String         @db.Uuid
  shipment        shipments      @relation(fields: [shipment_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  load_id         String         @db.Uuid
  load            loads          @relation(fields: [load_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  stop_id         String?        @db.Uuid
  stop            stops?         @relation(fields: [stop_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  verification_id String?        @db.Uuid
  verification    verifications? @relation(fields: [verification_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  driver_id       String?        @db.Uuid
  driver          drivers?       @relation(fields: [driver_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}
