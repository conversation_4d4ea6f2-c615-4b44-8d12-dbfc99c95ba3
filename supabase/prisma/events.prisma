enum event_type {
  // Cargo lifecycle events
  cargo_created
  cargo_updated
  cargo_assigned_to_shipment
  cargo_removed_from_shipment
  cargo_cancelled

  // Pickup events
  pickup_scheduled
  pickup_verification_required
  pickup_verification_started
  pickup_verified
  pickup_verification_failed
  pickup_completed
  pickup_exception

  // Transit events
  in_transit
  shipment_departed
  shipment_arrived_at_stop
  location_update
  route_deviation

  // Delivery events
  delivery_scheduled
  delivery_verification_required
  delivery_verification_started
  delivery_verified
  delivery_verification_failed
  delivery_completed
  delivery_exception
  delivery_failed

  // Shipment lifecycle events
  shipment_created
  shipment_updated
  route_planned
  route_optimized
  route_changed
  driver_assigned
  driver_changed
  trailer_assigned
  trailer_changed
  shipment_dispatched
  shipment_completed
  shipment_cancelled

  // Exception events
  delay_reported
  cargo_damaged
  accident_reported
  breakdown_reported
  weather_delay
  traffic_delay
  security_incident

  // System events
  system_auto_update
  manual_override
  status_sync
  notification_sent

  @@schema("public")
}

enum event_severity {
  info
  warning
  error
  critical

  @@schema("public")
}

enum entity_type {
  cargo
  shipment
  driver
  vehicle
  trailer
  location
  customer
  verification
  route

  @@schema("public")
}

enum actor_type {
  driver
  dispatcher
  customer
  system
  api
  admin
  sensor

  @@schema("public")
}

model events {
  id         String   @id @default(cuid())
  created_at DateTime @default(dbgenerated("timezone('utc'::text, now())")) @db.Timestamptz(6)

  event_type  event_type
  entity_type entity_type
  entity_id   String

  actor_type actor_type
  actor_id   String?
  actor_name String?

  location_id   String?
  location_name String?
  latitude      Float?
  longitude     Float?

  metadata Json?

  related_entity_ids String[]
  correlation_id     String?
  parent_event_id    String?

  title       String?
  description String?

  is_public           Boolean        @default(true)
  is_system_generated Boolean        @default(false)
  severity            event_severity @default(info)

  source     String?
  device_id  String?
  ip_address String?
  user_agent String?

  @@index([entity_id, created_at])
  @@index([event_type, created_at])
  @@index([actor_id, created_at])
  @@index([location_id, created_at])
  @@index([correlation_id])
  @@map("events")
  @@schema("public")
}
