/*
  Warnings:

  - You are about to drop the `_loadsTostops` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_loadsToverifications` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."_loadsTostops" DROP CONSTRAINT "_loadsTostops_A_fkey";

-- DropForeignKey
ALTER TABLE "public"."_loadsTostops" DROP CONSTRAINT "_loadsTostops_B_fkey";

-- DropForeignKey
ALTER TABLE "public"."_loadsToverifications" DROP CONSTRAINT "_loadsToverifications_A_fkey";

-- DropForeignKey
ALTER TABLE "public"."_loadsToverifications" DROP CONSTRAINT "_loadsToverifications_B_fkey";

-- DropTable
DROP TABLE "public"."_loadsTostops";

-- DropTable
DROP TABLE "public"."_loadsToverifications";

-- CreateTable
CREATE TABLE "public"."_load_stops" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_load_stops_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "public"."_load_verifications" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_load_verifications_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_load_stops_B_index" ON "public"."_load_stops"("B");

-- CreateIndex
CREATE INDEX "_load_verifications_B_index" ON "public"."_load_verifications"("B");

-- AddForeignKey
ALTER TABLE "public"."_load_stops" ADD CONSTRAINT "_load_stops_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."loads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_load_stops" ADD CONSTRAINT "_load_stops_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."stops"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_load_verifications" ADD CONSTRAINT "_load_verifications_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."loads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_load_verifications" ADD CONSTRAINT "_load_verifications_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."verifications"("id") ON DELETE CASCADE ON UPDATE CASCADE;
