/*
  Warnings:

  - You are about to drop the `_driversTovehicles` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."_driversTovehicles" DROP CONSTRAINT "_driversTovehicles_A_fkey";

-- DropForeignKey
ALTER TABLE "public"."_driversTovehicles" DROP CONSTRAINT "_driversTovehicles_B_fkey";

-- DropTable
DROP TABLE "public"."_driversTovehicles";

-- CreateTable
CREATE TABLE "public"."_driver_vehicles" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_driver_vehicles_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_driver_vehicles_B_index" ON "public"."_driver_vehicles"("B");

-- AddForeignKey
ALTER TABLE "public"."_driver_vehicles" ADD CONSTRAINT "_driver_vehicles_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."drivers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_driver_vehicles" ADD CONSTRAINT "_driver_vehicles_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."vehicles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
