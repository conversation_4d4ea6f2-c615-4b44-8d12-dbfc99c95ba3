-- CreateEnum
CREATE TYPE "public"."user_role" AS ENUM ('system', 'admin', 'user');

-- CreateEnum
CREATE TYPE "public"."notification_type" AS ENUM ('shipment_update', 'incident_report', 'system_alert', 'payment_update');

-- CreateEnum
CREATE TYPE "public"."document_type" AS ENUM ('file', 'contract');

-- CreateEnum
CREATE TYPE "public"."members_role" AS ENUM ('owner', 'admin', 'billing', 'member', 'viewer');

-- CreateEnum
CREATE TYPE "public"."membership_status" AS ENUM ('pending', 'active', 'inactive', 'declined');

-- CreateEnum
CREATE TYPE "public"."invitation_status" AS ENUM ('pending', 'accepted', 'revoked', 'rejected', 'expired');

-- CreateEnum
CREATE TYPE "public"."organization_status" AS ENUM ('pending', 'active', 'suspended', 'inactive');

-- CreateEnum
CREATE TYPE "public"."organization_type" AS ENUM ('individual', 'private', 'non_profit', 'government');

-- CreateEnum
CREATE TYPE "public"."organization_functions" AS ENUM ('logistics', 'carrier', 'warehousing', 'distribution');

-- CreateEnum
CREATE TYPE "public"."driver_status" AS ENUM ('active', 'inactive', 'suspended');

-- CreateEnum
CREATE TYPE "public"."qualification_status" AS ENUM ('pending', 'verified', 'expired', 'revoked', 'invalid');

-- CreateEnum
CREATE TYPE "public"."qualification_type" AS ENUM ('commercial_drivers_license', 'hazmat_endorsement', 'medical_certificate', 'defensive_driving_certificate', 'tanker_endorsement', 'doubles_triples_endorsement', 'other');

-- CreateEnum
CREATE TYPE "public"."event_type" AS ENUM ('cargo_created', 'cargo_updated', 'cargo_assigned_to_shipment', 'cargo_removed_from_shipment', 'cargo_cancelled', 'pickup_scheduled', 'pickup_verification_required', 'pickup_verification_started', 'pickup_verified', 'pickup_verification_failed', 'pickup_completed', 'pickup_exception', 'in_transit', 'shipment_departed', 'shipment_arrived_at_stop', 'location_update', 'route_deviation', 'delivery_scheduled', 'delivery_verification_required', 'delivery_verification_started', 'delivery_verified', 'delivery_verification_failed', 'delivery_completed', 'delivery_exception', 'delivery_failed', 'shipment_created', 'shipment_updated', 'route_planned', 'route_optimized', 'route_changed', 'driver_assigned', 'driver_changed', 'trailer_assigned', 'trailer_changed', 'shipment_dispatched', 'shipment_completed', 'shipment_cancelled', 'delay_reported', 'cargo_damaged', 'accident_reported', 'breakdown_reported', 'weather_delay', 'traffic_delay', 'security_incident', 'system_auto_update', 'manual_override', 'status_sync', 'notification_sent');

-- CreateEnum
CREATE TYPE "public"."event_severity" AS ENUM ('info', 'warning', 'error', 'critical');

-- CreateEnum
CREATE TYPE "public"."entity_type" AS ENUM ('cargo', 'shipment', 'driver', 'vehicle', 'trailer', 'location', 'customer', 'verification', 'route');

-- CreateEnum
CREATE TYPE "public"."actor_type" AS ENUM ('driver', 'dispatcher', 'customer', 'system', 'api', 'admin', 'sensor');

-- CreateEnum
CREATE TYPE "public"."location_type" AS ENUM ('billing', 'residential', 'retail', 'commercial', 'industrial', 'manufacturing', 'warehouse', 'distribution_center', 'port', 'rail_terminal', 'other');

-- CreateEnum
CREATE TYPE "public"."location_sector" AS ENUM ('public', 'private', 'government', 'military', 'educational', 'healthcare', 'religious');

-- CreateEnum
CREATE TYPE "public"."load_status" AS ENUM ('draft', 'pending', 'assigned', 'packaged', 'loaded', 'in_transit', 'out_for_delivery', 'delivered', 'exception', 'missing', 'rejected', 'returned', 'cancelled', 'customs_hold');

-- CreateEnum
CREATE TYPE "public"."load_category" AS ENUM ('dry_goods', 'liquid_bulk', 'dry_bulk', 'refrigerated', 'frozen', 'perishable', 'hazmat', 'oversized', 'heavy_haul', 'high_value', 'fragile', 'automotive', 'construction_materials', 'retail_consumer', 'industrial_equipment');

-- CreateEnum
CREATE TYPE "public"."physical_state" AS ENUM ('solid', 'liquid', 'gas', 'bulk_solid', 'bulk_liquid', 'packaged', 'palletized', 'loose');

-- CreateEnum
CREATE TYPE "public"."temperature_requirement" AS ENUM ('ambient', 'refrigerated', 'frozen', 'heated', 'temperature_controlled', 'dry_ice_required');

-- CreateEnum
CREATE TYPE "public"."hazmat_class" AS ENUM ('none', 'class_1_explosives', 'class_2_gases', 'class_3_flammable_liquids', 'class_4_flammable_solids', 'class_5_oxidizers', 'class_6_toxic_substances', 'class_7_radioactive', 'class_8_corrosives', 'class_9_miscellaneous');

-- CreateEnum
CREATE TYPE "public"."loading_method" AS ENUM ('dock_high', 'ground_level', 'crane_required', 'forklift', 'hand_load', 'pump_load', 'gravity_feed', 'conveyor', 'side_load', 'top_load');

-- CreateEnum
CREATE TYPE "public"."segregation_level" AS ENUM ('none', 'same_compartment_ok', 'separate_compartments', 'separate_trailers', 'cannot_ship_together');

-- CreateEnum
CREATE TYPE "public"."security_level" AS ENUM ('standard', 'high_value', 'theft_target', 'secured_facility_only', 'bonded_carrier_required', 'escort_required');

-- CreateEnum
CREATE TYPE "public"."trailer_type" AS ENUM ('dry_van', 'refrigerated', 'flatbed', 'step_deck', 'lowboy', 'tanker', 'hopper', 'container', 'car_carrier', 'livestock', 'dump', 'curtain_side', 'conestoga', 'specialized');

-- CreateEnum
CREATE TYPE "public"."service_level" AS ENUM ('standard', 'expedited', 'overnight', 'same_day', 'white_glove');

-- CreateEnum
CREATE TYPE "public"."shipment_mode" AS ENUM ('open', 'closed');

-- CreateEnum
CREATE TYPE "public"."shipment_source" AS ENUM ('driver', 'organization', 'system');

-- CreateEnum
CREATE TYPE "public"."shipment_status" AS ENUM ('pending', 'scheduled', 'assigned', 'confirmed', 'in_progress', 'completed', 'cancelled');

-- CreateEnum
CREATE TYPE "public"."shipment_type" AS ENUM ('air', 'ocean', 'ground', 'other');

-- CreateEnum
CREATE TYPE "public"."stop_type" AS ENUM ('pickup', 'drop_off', 'rest_stop', 'gas_station', 'maintenance_facility', 'customs_facility', 'weigh_station', 'other');

-- CreateEnum
CREATE TYPE "public"."incident_severity" AS ENUM ('low', 'medium', 'high', 'critical');

-- CreateEnum
CREATE TYPE "public"."incident_status" AS ENUM ('reported', 'investigating', 'resolved', 'closed');

-- CreateEnum
CREATE TYPE "public"."incident_type" AS ENUM ('verification', 'accident', 'delay', 'damage', 'theft', 'weather', 'mechanical', 'other');

-- CreateTable
CREATE TABLE "public"."users" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "accepted_terms_at" TIMESTAMPTZ(6),
    "onboarded_at" TIMESTAMPTZ(6),
    "role" "public"."user_role" NOT NULL DEFAULT 'user',
    "username" TEXT,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "avatar" TEXT,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."notifications" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "read_at" TIMESTAMPTZ(6),
    "type" "public"."notification_type" NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "user_id" UUID NOT NULL,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."documents" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "bucket_id" TEXT NOT NULL DEFAULT 'documents',
    "storage_path" TEXT NOT NULL,
    "content_type" TEXT,
    "size" BIGINT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "url" TEXT NOT NULL DEFAULT '',
    "type" "public"."document_type" NOT NULL DEFAULT 'file',
    "metadata" JSONB DEFAULT '{}',
    "latitude" DECIMAL,
    "longitude" DECIMAL,
    "created_by" UUID,
    "driver_id" UUID,
    "organization_id" UUID,
    "verification_id" UUID,

    CONSTRAINT "documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."members" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "email" TEXT NOT NULL,
    "role" "public"."members_role" NOT NULL DEFAULT 'member',
    "status" "public"."membership_status" NOT NULL DEFAULT 'pending',
    "organization_id" UUID NOT NULL,
    "user_id" UUID,

    CONSTRAINT "members_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."invitations" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "expires_at" TIMESTAMPTZ(6),
    "email" TEXT NOT NULL,
    "status" "public"."invitation_status" NOT NULL DEFAULT 'pending',
    "role" "public"."members_role" DEFAULT 'member',
    "created_by" UUID NOT NULL,
    "organization_id" UUID,

    CONSTRAINT "invitations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."organizations" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "stripe_customer_id" TEXT,
    "stripe_account_id" TEXT,
    "functions" "public"."organization_functions"[] DEFAULT ARRAY['logistics']::"public"."organization_functions"[],
    "status" "public"."organization_status" NOT NULL DEFAULT 'pending',
    "type" "public"."organization_type" NOT NULL DEFAULT 'private',
    "name" TEXT NOT NULL,
    "avatar" TEXT,
    "size" TEXT,
    "industry" TEXT,
    "website" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "location_id" UUID,
    "settings" JSONB DEFAULT '{}',

    CONSTRAINT "organizations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."drivers" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "verified_at" TIMESTAMPTZ(6),
    "stripe_customer_id" TEXT,
    "stripe_account_id" TEXT,
    "status" "public"."driver_status" DEFAULT 'active',
    "score" INTEGER NOT NULL DEFAULT 100,
    "tier" TEXT NOT NULL DEFAULT 'free',
    "user_id" UUID,
    "organization_id" UUID,
    "location_id" UUID,
    "settings" JSONB DEFAULT '{}',

    CONSTRAINT "drivers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."qualifications" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "issued_at" TIMESTAMPTZ(6) NOT NULL,
    "expires_at" TIMESTAMPTZ(6),
    "verified_at" TIMESTAMPTZ(6),
    "type" "public"."qualification_type" NOT NULL,
    "status" "public"."qualification_status" NOT NULL DEFAULT 'pending',
    "issuing_state" TEXT NOT NULL,
    "document_id" UUID,
    "driver_id" UUID NOT NULL,

    CONSTRAINT "qualifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."vehicles" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "make" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "license_state" TEXT NOT NULL,
    "license_plate" TEXT NOT NULL,
    "vin" TEXT NOT NULL,
    "us_dot" TEXT NOT NULL,
    "mc_number" TEXT NOT NULL,
    "organization_id" UUID,

    CONSTRAINT "vehicles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."trailers" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),

    CONSTRAINT "trailers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."events" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "event_type" "public"."event_type" NOT NULL,
    "entity_type" "public"."entity_type" NOT NULL,
    "entity_id" TEXT NOT NULL,
    "actor_type" "public"."actor_type" NOT NULL,
    "actor_id" TEXT,
    "actor_name" TEXT,
    "location_id" TEXT,
    "location_name" TEXT,
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "metadata" JSONB,
    "related_entity_ids" TEXT[],
    "correlation_id" TEXT,
    "parent_event_id" TEXT,
    "title" TEXT,
    "description" TEXT,
    "is_public" BOOLEAN NOT NULL DEFAULT true,
    "is_system_generated" BOOLEAN NOT NULL DEFAULT false,
    "severity" "public"."event_severity" NOT NULL DEFAULT 'info',
    "source" TEXT,
    "device_id" TEXT,
    "ip_address" TEXT,
    "user_agent" TEXT,

    CONSTRAINT "events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."locations" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "type" "public"."location_type" NOT NULL DEFAULT 'other',
    "sector" "public"."location_sector" NOT NULL DEFAULT 'private',
    "formatted" TEXT NOT NULL,
    "street" TEXT,
    "suite" TEXT,
    "city" TEXT,
    "neighborhood" TEXT,
    "postal" TEXT,
    "state" TEXT,
    "country" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "locations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."positions" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "latitude" DECIMAL(10,8) NOT NULL,
    "longitude" DECIMAL(11,8) NOT NULL,
    "shipment_id" UUID NOT NULL,

    CONSTRAINT "positions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."loads" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "pickup_date_from" TIMESTAMP(3),
    "pickup_date_to" TIMESTAMP(3),
    "delivery_date_from" TIMESTAMP(3),
    "delivery_date_to" TIMESTAMP(3),
    "status" "public"."load_status" NOT NULL DEFAULT 'pending',
    "priority" INTEGER NOT NULL DEFAULT 1,
    "service_level" "public"."service_level" NOT NULL DEFAULT 'standard',
    "description" TEXT,
    "commodity_code" TEXT,
    "primary_category" "public"."load_category" NOT NULL,
    "secondary_categories" "public"."load_category"[],
    "physical_state" "public"."physical_state" NOT NULL,
    "temperature_requirement" "public"."temperature_requirement" NOT NULL,
    "temp_range_min" DOUBLE PRECISION,
    "temp_range_max" DOUBLE PRECISION,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "length" DOUBLE PRECISION,
    "width" DOUBLE PRECISION,
    "height" DOUBLE PRECISION,
    "volume" DOUBLE PRECISION,
    "volume_unit" TEXT,
    "weight" DOUBLE PRECISION,
    "weight_unit" TEXT,
    "is_oversized" BOOLEAN NOT NULL DEFAULT false,
    "is_overweight" BOOLEAN NOT NULL DEFAULT false,
    "max_length" DOUBLE PRECISION,
    "max_width" DOUBLE PRECISION,
    "max_height" DOUBLE PRECISION,
    "max_weight" DOUBLE PRECISION,
    "hazmat_class" "public"."hazmat_class" NOT NULL DEFAULT 'none',
    "hazmat_subclass" TEXT,
    "un_number" TEXT,
    "security_level" "public"."security_level" NOT NULL DEFAULT 'standard',
    "declared_value" DECIMAL(65,30),
    "loading_method" "public"."loading_method"[],
    "requires_special_equip" BOOLEAN NOT NULL DEFAULT false,
    "special_equipment" TEXT,
    "can_mix_with_food" BOOLEAN NOT NULL DEFAULT true,
    "can_mix_with_chemicals" BOOLEAN NOT NULL DEFAULT false,
    "requires_clean_trailer" BOOLEAN NOT NULL DEFAULT false,
    "contamination_risk" BOOLEAN NOT NULL DEFAULT false,
    "odor_risk" BOOLEAN NOT NULL DEFAULT false,
    "recommended_trailers" "public"."trailer_type"[],
    "prohibited_trailers" "public"."trailer_type"[],
    "requires_certified_driver" BOOLEAN NOT NULL DEFAULT false,
    "requires_permits" BOOLEAN NOT NULL DEFAULT false,
    "is_fragile" BOOLEAN NOT NULL DEFAULT false,
    "is_stackable" BOOLEAN NOT NULL DEFAULT true,
    "is_times_sensitive" BOOLEAN NOT NULL DEFAULT false,
    "max_transit_time" INTEGER,
    "requires_air_ride" BOOLEAN NOT NULL DEFAULT false,
    "special_handling" TEXT,
    "special_instructions" TEXT,
    "origin_id" UUID,
    "destination_id" UUID,
    "organization_id" UUID,
    "shipment_id" UUID,

    CONSTRAINT "loads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."shipments" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "assigned_at" TIMESTAMPTZ(6),
    "confirmed_at" TIMESTAMPTZ(6),
    "started_at" TIMESTAMPTZ(6),
    "completed_at" TIMESTAMPTZ(6),
    "cancelled_at" TIMESTAMPTZ(6),
    "status" "public"."shipment_status" NOT NULL DEFAULT 'pending',
    "source" "public"."shipment_source" NOT NULL DEFAULT 'system',
    "type" "public"."shipment_type" NOT NULL DEFAULT 'ground',
    "mode" "public"."shipment_mode" NOT NULL DEFAULT 'closed',
    "valuation" DECIMAL,
    "distance" DECIMAL,
    "weight" DECIMAL,
    "origin_id" UUID,
    "destination_id" UUID,
    "driver_id" UUID,
    "organization_id" UUID,

    CONSTRAINT "shipments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."stops" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "arrived_at" TIMESTAMPTZ(6),
    "departed_at" TIMESTAMPTZ(6),
    "sequence_number" INTEGER NOT NULL,
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "type" "public"."stop_type" NOT NULL DEFAULT 'other',
    "label" TEXT,
    "tags" TEXT[],
    "location_id" UUID,
    "shipment_id" UUID NOT NULL,

    CONSTRAINT "stops_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."verifications" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "verified_at" TIMESTAMPTZ(6),
    "notes" TEXT,
    "latitude" DECIMAL,
    "longitude" DECIMAL,
    "verified_by" UUID,
    "driver_id" UUID,
    "vehicle_id" UUID,
    "shipment_id" UUID,
    "stop_id" UUID NOT NULL,

    CONSTRAINT "verifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."incidents" (
    "id" UUID NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" TIMESTAMPTZ(6) NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "title" TEXT NOT NULL,
    "type" "public"."incident_type" NOT NULL,
    "status" "public"."incident_status" NOT NULL DEFAULT 'reported',
    "severity" "public"."incident_severity" NOT NULL,
    "summary" TEXT,
    "shipment_id" UUID NOT NULL,
    "load_id" UUID NOT NULL,
    "stop_id" UUID,
    "verification_id" UUID,
    "driver_id" UUID,

    CONSTRAINT "incidents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."_driversTovehicles" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_driversTovehicles_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "public"."_loadsTostops" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_loadsTostops_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "public"."_loadsToverifications" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_loadsToverifications_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_phone_number_key" ON "public"."users"("phone_number");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "documents_storage_path_key" ON "public"."documents"("storage_path");

-- CreateIndex
CREATE INDEX "documents_driver_id_idx" ON "public"."documents"("driver_id");

-- CreateIndex
CREATE INDEX "documents_organization_id_idx" ON "public"."documents"("organization_id");

-- CreateIndex
CREATE INDEX "documents_type_idx" ON "public"."documents"("type");

-- CreateIndex
CREATE INDEX "documents_url_idx" ON "public"."documents"("url");

-- CreateIndex
CREATE UNIQUE INDEX "unique_member_per_organization" ON "public"."members"("organization_id", "email");

-- CreateIndex
CREATE UNIQUE INDEX "invitations_email_organization_unique" ON "public"."invitations"("email", "organization_id");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_stripe_customer_id_key" ON "public"."organizations"("stripe_customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_stripe_account_id_key" ON "public"."organizations"("stripe_account_id");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_location_id_key" ON "public"."organizations"("location_id");

-- CreateIndex
CREATE UNIQUE INDEX "drivers_stripe_customer_id_key" ON "public"."drivers"("stripe_customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "drivers_stripe_account_id_key" ON "public"."drivers"("stripe_account_id");

-- CreateIndex
CREATE UNIQUE INDEX "drivers_user_id_key" ON "public"."drivers"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "drivers_location_id_key" ON "public"."drivers"("location_id");

-- CreateIndex
CREATE UNIQUE INDEX "qualifications_document_id_key" ON "public"."qualifications"("document_id");

-- CreateIndex
CREATE INDEX "idx_qualifications_driver_id" ON "public"."qualifications"("driver_id");

-- CreateIndex
CREATE UNIQUE INDEX "vehicles_vin_key" ON "public"."vehicles"("vin");

-- CreateIndex
CREATE INDEX "events_entity_id_created_at_idx" ON "public"."events"("entity_id", "created_at");

-- CreateIndex
CREATE INDEX "events_event_type_created_at_idx" ON "public"."events"("event_type", "created_at");

-- CreateIndex
CREATE INDEX "events_actor_id_created_at_idx" ON "public"."events"("actor_id", "created_at");

-- CreateIndex
CREATE INDEX "events_location_id_created_at_idx" ON "public"."events"("location_id", "created_at");

-- CreateIndex
CREATE INDEX "events_correlation_id_idx" ON "public"."events"("correlation_id");

-- CreateIndex
CREATE INDEX "idx_positions_shipment_id" ON "public"."positions"("shipment_id");

-- CreateIndex
CREATE INDEX "idx_cargo_shipment_id" ON "public"."loads"("shipment_id");

-- CreateIndex
CREATE INDEX "idx_cargo_destination" ON "public"."loads"("destination_id");

-- CreateIndex
CREATE INDEX "idx_cargo_origin" ON "public"."loads"("origin_id");

-- CreateIndex
CREATE INDEX "idx_cargo_organization_id" ON "public"."loads"("organization_id");

-- CreateIndex
CREATE INDEX "idx_shipments_organization_id" ON "public"."shipments"("organization_id");

-- CreateIndex
CREATE INDEX "idx_stops_coords" ON "public"."stops"("latitude", "longitude");

-- CreateIndex
CREATE INDEX "idx_stops_type" ON "public"."stops"("type");

-- CreateIndex
CREATE UNIQUE INDEX "stops_shipment_id_sequence_number_key" ON "public"."stops"("shipment_id", "sequence_number");

-- CreateIndex
CREATE UNIQUE INDEX "verifications_stop_id_key" ON "public"."verifications"("stop_id");

-- CreateIndex
CREATE INDEX "idx_verifications_vehicle_id" ON "public"."verifications"("vehicle_id");

-- CreateIndex
CREATE INDEX "idx_verifications_shipment_id" ON "public"."verifications"("shipment_id");

-- CreateIndex
CREATE INDEX "idx_verifications_stop_id" ON "public"."verifications"("stop_id");

-- CreateIndex
CREATE INDEX "_driversTovehicles_B_index" ON "public"."_driversTovehicles"("B");

-- CreateIndex
CREATE INDEX "_loadsTostops_B_index" ON "public"."_loadsTostops"("B");

-- CreateIndex
CREATE INDEX "_loadsToverifications_B_index" ON "public"."_loadsToverifications"("B");

-- AddForeignKey
ALTER TABLE "public"."notifications" ADD CONSTRAINT "notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."documents" ADD CONSTRAINT "documents_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."documents" ADD CONSTRAINT "documents_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "public"."drivers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."documents" ADD CONSTRAINT "documents_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."documents" ADD CONSTRAINT "documents_verification_id_fkey" FOREIGN KEY ("verification_id") REFERENCES "public"."verifications"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."members" ADD CONSTRAINT "members_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."members" ADD CONSTRAINT "members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."invitations" ADD CONSTRAINT "invitations_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."invitations" ADD CONSTRAINT "invitations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."organizations" ADD CONSTRAINT "organizations_location_id_fkey" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."drivers" ADD CONSTRAINT "drivers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."drivers" ADD CONSTRAINT "drivers_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."drivers" ADD CONSTRAINT "drivers_location_id_fkey" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."qualifications" ADD CONSTRAINT "qualifications_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."qualifications" ADD CONSTRAINT "qualifications_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "public"."drivers"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."vehicles" ADD CONSTRAINT "vehicles_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."positions" ADD CONSTRAINT "positions_shipment_id_fkey" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."loads" ADD CONSTRAINT "loads_origin_id_fkey" FOREIGN KEY ("origin_id") REFERENCES "public"."locations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."loads" ADD CONSTRAINT "loads_destination_id_fkey" FOREIGN KEY ("destination_id") REFERENCES "public"."locations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."loads" ADD CONSTRAINT "loads_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."loads" ADD CONSTRAINT "loads_shipment_id_fkey" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."shipments" ADD CONSTRAINT "shipments_origin_id_fkey" FOREIGN KEY ("origin_id") REFERENCES "public"."locations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."shipments" ADD CONSTRAINT "shipments_destination_id_fkey" FOREIGN KEY ("destination_id") REFERENCES "public"."locations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."shipments" ADD CONSTRAINT "shipments_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "public"."drivers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."shipments" ADD CONSTRAINT "shipments_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."stops" ADD CONSTRAINT "stops_location_id_fkey" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."stops" ADD CONSTRAINT "stops_shipment_id_fkey" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."verifications" ADD CONSTRAINT "verifications_verified_by_fkey" FOREIGN KEY ("verified_by") REFERENCES "public"."users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."verifications" ADD CONSTRAINT "verifications_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "public"."drivers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."verifications" ADD CONSTRAINT "verifications_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."verifications" ADD CONSTRAINT "verifications_shipment_id_fkey" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."verifications" ADD CONSTRAINT "verifications_stop_id_fkey" FOREIGN KEY ("stop_id") REFERENCES "public"."stops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."incidents" ADD CONSTRAINT "incidents_shipment_id_fkey" FOREIGN KEY ("shipment_id") REFERENCES "public"."shipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."incidents" ADD CONSTRAINT "incidents_load_id_fkey" FOREIGN KEY ("load_id") REFERENCES "public"."loads"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."incidents" ADD CONSTRAINT "incidents_stop_id_fkey" FOREIGN KEY ("stop_id") REFERENCES "public"."stops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."incidents" ADD CONSTRAINT "incidents_verification_id_fkey" FOREIGN KEY ("verification_id") REFERENCES "public"."verifications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."incidents" ADD CONSTRAINT "incidents_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "public"."drivers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."_driversTovehicles" ADD CONSTRAINT "_driversTovehicles_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."drivers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_driversTovehicles" ADD CONSTRAINT "_driversTovehicles_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."vehicles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_loadsTostops" ADD CONSTRAINT "_loadsTostops_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."loads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_loadsTostops" ADD CONSTRAINT "_loadsTostops_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."stops"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_loadsToverifications" ADD CONSTRAINT "_loadsToverifications_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."loads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_loadsToverifications" ADD CONSTRAINT "_loadsToverifications_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."verifications"("id") ON DELETE CASCADE ON UPDATE CASCADE;
