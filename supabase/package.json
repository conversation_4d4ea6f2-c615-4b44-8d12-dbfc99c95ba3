{"name": "@quikskope/supabase", "version": "0.0.0", "private": true, "prisma": {"schema": "./prisma"}, "scripts": {"db:types": "bunx supabase gen types --linked > functions/_shared/types.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "functions:build": "supabase functions build", "functions:start": "supabase functions start", "functions:dev": "supabase functions dev", "functions:deploy": "supabase functions deploy"}, "dependencies": {"@prisma/client": "6.10.1", "prisma": "^6.10.1", "supabase": "2.26.9"}}