import { distance, point } from "npm:@turf/turf";
import { z } from "npm:zod";

import { generateSchema } from "../_shared/ai/openai.ts";
import { corsHeaders } from "../_shared/cors.ts";
import {
  ExtractedExifData,
  extractExifData,
} from "../_shared/documents/exif.ts";
import { isSameDay } from "../_shared/utils/index.ts";

interface Shipment {
  mc_id: string;
  usdot_id: string;
  license_plate: string;
  license_state: string;
}

interface VerificationResponse {
  success: boolean;
  verification_id: string;
  verified: boolean;
  confidence_score: number;
  warnings: string[];
  info: string[];
  errors?: string[];
  processing_time: {
    image_processing_ms: number;
    metadata_extraction_ms: number;
    ai_analysis_ms: number;
    exif_analysis_ms: number;
    location_analysis_ms: number;
    total_ms: number;
  };
}

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  } else if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }

  try {
    const startTime = performance.now();
    const requestData = await req.json();
    const { verificationId, latitude, longitude } = requestData as {
      verificationId: string;
      latitude: number;
      longitude: number;
    };

    // Validate input
    if (!verificationId) {
      return new Response(
        JSON.stringify({ error: "Missing required parameters" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const { supabase } = await import("../_shared/supabase.ts");

    const { data: verification, error: verificationError } = await supabase
      .from("verifications")
      .select("*")
      .eq("id", verificationId)
      .single();

    if (verificationError || !verification) {
      console.error("Verification error:", verificationError);
      return new Response(JSON.stringify({ error: "Verification not found" }), {
        status: 404,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Check if already verified
    if (verification.verified_at) {
      return new Response(
        JSON.stringify({ error: "Verification already completed" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const { data: image, error: imageError } = await supabase
      .from("documents")
      .select("*")
      .eq("verification_id", verificationId)
      .single();

    if (imageError || !image) {
      console.error("Image error:", imageError);
      return new Response(JSON.stringify({ error: "Image not found" }), {
        status: 404,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const imageProcessingStart = performance.now();
    const imageResponse = await fetch(image.url);

    const imageArrayBuffer = await imageResponse.arrayBuffer();
    const imageBase64 = btoa(
      String.fromCharCode(...new Uint8Array(imageArrayBuffer)),
    );
    const imageMetadata = image.metadata as { timestamp: string };
    const imageProcessingTime = performance.now();

    // Extract metadata from the request
    const metadataExtractionStart = performance.now();
    const warnings: string[] = [];
    const errors: string[] = [];
    const info: string[] = [];

    // Check if image was taken today based on request metadata
    const imageTimestamp = imageMetadata?.timestamp;
    if (imageTimestamp) {
      const imageDate = new Date(imageTimestamp);
      const today = new Date();
      if (imageDate.toDateString() !== today.toDateString()) {
        warnings.push(
          `Image was not taken today. It was taken on ${imageDate.toDateString()}`,
        );
      } else {
        info.push(
          `Image was taken today at ${imageDate.toLocaleTimeString()} 📅`,
        );
      }
    } else {
      warnings.push("Image metadata does not have a timestamp");
    }

    const metadataExtractionTime = performance.now();

    // Process EXIF data from the image
    const exifAnalysisStart = performance.now();
    let extractedExif: ExtractedExifData = {
      creationDate: null,
      latitude: null,
      longitude: null,
    };

    try {
      extractedExif = await extractExifData(imageArrayBuffer);

      // Check for image creation time from EXIF
      if (extractedExif.creationDate) {
        const today = new Date();
        if (!isSameDay(extractedExif.creationDate, today)) {
          warnings.push(
            `EXIF data shows image was not created today. It was created on ${extractedExif.creationDate.toDateString()}`,
          );
        } else {
          info.push(
            `EXIF data confirms image was created today at ${extractedExif.creationDate.toLocaleTimeString()} 📅`,
          );
        }
      } else {
        warnings.push("No creation date found in EXIF data");
      }
    } catch (error) {
      console.error("Error processing EXIF data:", error);
      warnings.push("Failed to process EXIF data from image");
    }
    const exifAnalysisTime = performance.now();

    // Analyze location data using Turf.js
    const locationAnalysisStart = performance.now();

    if (extractedExif.latitude !== null && extractedExif.longitude !== null) {
      try {
        // Calculate distance between EXIF coordinates and provided coordinates
        const distanceInMiles = distance(
          point([extractedExif.longitude, extractedExif.latitude]),
          point([longitude, latitude]),
          { units: "miles" },
        );

        console.log("Distance calculation:", {
          exifCoords: [extractedExif.latitude, extractedExif.longitude],
          providedCoords: [latitude, longitude],
          distanceInMiles,
        });

        if (distanceInMiles > 10) {
          warnings.push(
            `Image EXIF location is ${distanceInMiles.toFixed(2)} miles away from provided location (exceeds 10 mile limit)`,
          );
        } else {
          info.push(
            `Image EXIF location is ${distanceInMiles.toFixed(2)} miles from provided location 📍`,
          );
        }
      } catch (error) {
        console.error("Error analyzing location data:", error);
        warnings.push("Error analyzing location data from EXIF");
      }
    } else {
      warnings.push("Image does not contain GPS location data in EXIF");
    }

    const locationAnalysisTime = performance.now();

    // Analyze image using OpenAI
    console.log("Starting AI analysis...");
    const aiAnalysisStart = performance.now();

    const verificationSchema = z.object({
      verified: z.boolean(),
      confidence_score: z.number(),
      warnings: z.array(z.string()),
      info: z.array(z.string()),
      errors: z.array(z.string()),
      processing_time: z.number(),
      isPictureInPicture: z.boolean(),
      isTruck: z.boolean(),
      mcId: z.string(),
      usdotId: z.string(),
      licensePlate: z.string(),
      licenseState: z.string(),
    });
    // Prepare metadata for verification
    const verificationMetadata = await generateSchema({
      messages: [
        {
          role: "system",
          content:
            "You are a helpful assistant that scans images for freight scanning purposes.",
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze this image and determine if it's a truck. Look for MC ID, USDOT ID, license plate, and license state.",
            },
            {
              type: "image",
              image: `data:image/jpeg;base64,${imageBase64}`,
            },
          ],
        },
      ],
      schema: verificationSchema,
    });

    const aiAnalysisTime = performance.now();

    const user = await supabase.auth.getUser();
    // Update verification status, document, and coordinates
    const { error: updateError } = await supabase
      .from("verifications")
      .update({
        document_id: image.id,
        verified_at: new Date().toISOString(),
        verified_by: user.data.user?.id,
        latitude: latitude || null,
        longitude: longitude || null,
        verified: verificationMetadata.verified,
        verification_metadata: verificationMetadata,
      })
      .eq("id", verificationId);

    if (updateError) {
      throw updateError;
    }

    const { data: shipment, error: shipmentError } = await supabase
      .from("shipments")
      .select("*")
      .eq("id", verification.shipment_id)
      .single();

    if (shipmentError) {
      warnings.push(
        `Could not fetch shipment details: ${shipmentError.message}`,
      );
    }

    // Process the AI results
    if (verificationMetadata.verified === false) {
      errors.push("Image analysis failed");
    } else if (shipment) {
      if (verificationMetadata.isPictureInPicture) {
        warnings.push("Image is a picture of another picture");
      } else {
        info.push("Image is not a picture of another picture");
      }

      if (!verificationMetadata.isTruck) {
        warnings.push("Image does not contain a truck");
      } else {
        info.push("Image is of a truck");
      }

      // Check MC ID
      if (!verificationMetadata.mcId) {
        warnings.push("MC ID not found in image");
      } else if (
        verificationMetadata.mcId !== (shipment as unknown as Shipment).mc_id
      ) {
        warnings.push(
          `MC ID does not match manifest (${verificationMetadata.mcId} vs ${
            (shipment as unknown as Shipment).mc_id
          })`,
        );
      } else {
        info.push("MC ID matches manifest 🚚");
      }

      // Check USDOT ID
      if (!verificationMetadata.usdotId) {
        warnings.push("US DOT ID not found in image");
      } else if (
        verificationMetadata.usdotId !==
        (shipment as unknown as Shipment).usdot_id
      ) {
        warnings.push(
          `US DOT ID does not match manifest (${verificationMetadata.usdotId} vs ${
            (shipment as unknown as Shipment).usdot_id
          })`,
        );
      } else {
        info.push("US DOT ID matches manifest 🚛");
      }

      // Check license plate
      if (!verificationMetadata.licensePlate) {
        warnings.push("License plate not found in image");
      } else if (
        verificationMetadata.licensePlate !==
        (shipment as unknown as Shipment).license_plate
      ) {
        warnings.push(
          `License plate does not match manifest (${
            verificationMetadata.licensePlate
          } vs ${(shipment as unknown as Shipment).license_plate})`,
        );
      } else {
        info.push("License plate matches manifest 🚗");
      }

      // Check license state
      if (!verificationMetadata.licenseState) {
        warnings.push("License state not found in image");
      } else if (
        verificationMetadata.licenseState !==
        (shipment as unknown as Shipment).license_state
      ) {
        warnings.push(
          `License state does not match manifest (${
            verificationMetadata.licenseState
          } vs ${(shipment as unknown as Shipment).license_state})`,
        );
      } else {
        info.push("License state matches manifest 🏁");
      }
    }

    const endTime = performance.now();
    const totalProcessingTime = endTime - startTime;

    // Create the response
    const response: VerificationResponse = {
      success: errors.length === 0,
      verification_id: verificationId,
      verified: errors.length === 0 && warnings.length < 3, // Consider verified if fewer than 3 warnings and no errors
      confidence_score: verificationMetadata.confidence_score || 0,
      warnings,
      info,
      errors: errors.length > 0 ? errors : undefined,
      processing_time: {
        image_processing_ms: Math.round(
          imageProcessingTime - imageProcessingStart,
        ),
        metadata_extraction_ms: Math.round(
          metadataExtractionTime - metadataExtractionStart,
        ),
        ai_analysis_ms: Math.round(aiAnalysisTime - aiAnalysisStart),
        exif_analysis_ms: Math.round(exifAnalysisTime - exifAnalysisStart),
        location_analysis_ms: Math.round(
          locationAnalysisTime - locationAnalysisStart,
        ),
        total_ms: Math.round(totalProcessingTime),
      },
    };

    console.log("Verification processing complete:", response);

    // TODO: trigger events for verification completion

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (err) {
    return new Response(JSON.stringify({ error: (err as Error).message }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
