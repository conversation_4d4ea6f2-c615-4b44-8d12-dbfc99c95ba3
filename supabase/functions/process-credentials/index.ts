import { z } from "npm:zod";

import { generateSchema } from "../_shared/ai/openai.ts";
import { corsHeaders } from "../_shared/cors.ts";

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  } else if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }

  try {
    const { documentId } = (await req.json()) as { documentId: string };

    const { supabase } = await import("../_shared/supabase.ts");

    const { data: image, error: imageError } = await supabase
      .from("documents")
      .select("*")
      .eq("id", documentId)
      .single();

    if (imageError || !image) {
      console.error("Image error:", imageError);
      return new Response(JSON.stringify({ error: "Image not found" }), {
        status: 404,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const imageResponse = await fetch(image.url);

    const imageArrayBuffer = await imageResponse.arrayBuffer();
    const imageBase64 = btoa(
      String.fromCharCode(...new Uint8Array(imageArrayBuffer)),
    );

    if (imageError) {
      return new Response(JSON.stringify({ error: imageError.message }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } else if (!image) {
      return new Response(JSON.stringify({ error: "Document not found" }), {
        status: 404,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const {
      id,
      name,
      issueDate,
      expirationDate,
      licenseNumber,
      licenseState,
      licenseClass,
      licenseType,
    } = await generateSchema({
      messages: [
        {
          role: "system",
          content:
            "You are a helpful assistant that extracts information from documents.",
        },
        {
          role: "user",
          content: [
            {
              type: "image",
              image: `data:image/jpeg;base64,${imageBase64}`,
            },
          ],
        },
      ],
      schema: z.object({
        id: z.string(),
        name: z.string(),
        issueDate: z.string(),
        expirationDate: z.string(),
        licenseNumber: z.string(),
        licenseState: z.string(),
        licenseClass: z.string(),
        licenseType: z.string(),
      }),
    });

    return new Response(
      JSON.stringify({
        id,
        name,
        issueDate,
        expirationDate,
        licenseNumber,
        licenseState,
        licenseClass,
        licenseType,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    return new Response(JSON.stringify({ error: (error as Error).message }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
