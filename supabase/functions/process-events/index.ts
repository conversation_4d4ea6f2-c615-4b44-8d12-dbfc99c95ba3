import { z } from "npm:zod";

import { corsHeaders } from "../_shared/cors.ts";

const eventSchema = z.object({
  type: z.string(),
  payload: z.any(),
});

function runEventHandler(eventType: string, payload: unknown): null {
  const eventTypes = [
    // Cargo lifecycle events
    "cargo_created",
    "cargo_updated",
    "cargo_assigned_to_shipment",
    "cargo_removed_from_shipment",
    "cargo_cancelled",
    // Pickup events
    "pickup_scheduled",
    "pickup_verification_required",
    "pickup_verification_started",
    "pickup_verified",
    "pickup_verification_failed",
    "pickup_completed",
    "pickup_exception",
    // Transit events
    "in_transit",
    "shipment_departed",
    "shipment_arrived_at_stop",
    "location_update",
    "route_deviation",
    // Delivery events
    "delivery_scheduled",
    "delivery_verification_required",
    "delivery_verification_started",
    "delivery_verified",
    "delivery_verification_failed",
    "delivery_completed",
    "delivery_exception",
    "delivery_failed",
    // Shipment lifecycle events
    "shipment_created",
    "shipment_updated",
    "route_planned",
    "route_optimized",
    "route_changed",
    "driver_assigned",
    "driver_changed",
    "trailer_assigned",
    "trailer_changed",
    "shipment_dispatched",
    "shipment_completed",
    "shipment_cancelled",
    // Exception events
    "delay_reported",
    "cargo_damaged",
    "accident_reported",
    "breakdown_reported",
    "weather_delay",
    "traffic_delay",
    "security_incident",
    // System events
    "system_auto_update",
    "manual_override",
    "status_sync",
    "notification_sent",
  ];

  if (eventTypes.includes(eventType)) {
    return null;
  } else {
    throw new Error(`Unhandled event type: ${eventType}`);
  }
}

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  } else if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }

  try {
    const { type: eventType, payload } = eventSchema.parse(await req.json());
    const result = runEventHandler(eventType, payload);

    return new Response(
      JSON.stringify({
        success: true,
        processed_event: eventType,
        result: result,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: error instanceof z.ZodError ? 400 : 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
