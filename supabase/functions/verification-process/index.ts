import { distance, point } from "https://esm.sh/@turf/turf@7";
import OpenAI from "https://esm.sh/openai@5";

import { corsHeaders } from "../_shared/cors.ts";
import {
  ExtractedExifData,
  extractExifData,
} from "../_shared/documents/exif.ts";
import { isSameDay } from "../_shared/utils/index.ts";

interface VerificationRequest {
  verification_id: string;
  latitude: number;
  longitude: number;
  image_url: string;
  metadata: {
    device_id?: string;
    timestamp: string;
    accuracy?: number;
    shipping_info?: {
      manifestNumber?: string;
      mcId?: string;
      usdotId?: string;
      licensePlate?: string;
      licenseState?: string;
    };
  };
}

interface VerificationResponse {
  success: boolean;
  verification_id: string;
  verified: boolean;
  confidence_score: number;
  warnings: string[];
  info: string[];
  errors?: string[];
  processing_time: {
    image_processing_ms: number;
    metadata_extraction_ms: number;
    ai_analysis_ms: number;
    exif_analysis_ms: number;
    location_analysis_ms: number;
    total_ms: number;
  };
}

interface ScanResults {
  success: boolean;
  isPictureInPicture?: boolean;
  isTruck?: boolean;
  confidence: number;
  mcId?: string | null;
  usdotId?: string | null;
  licensePlate?: string | null;
  licenseState?: string | null;
}

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  } else if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }

  try {
    // Parse the request body
    const requestData: VerificationRequest = await req.json();
    console.log("Received verification request:", requestData);

    // Validate required fields
    if (
      !requestData.verification_id ||
      !requestData.latitude ||
      !requestData.longitude ||
      !requestData.image_url
    ) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Start processing times for metrics
    const startTime = performance.now();

    // Start image processing time
    const imageProcessingStart = performance.now();

    // Fetch the image from the URL
    console.log("Fetching image from URL:", requestData.image_url);
    const imageResponse = await fetch(requestData.image_url);
    if (!imageResponse.ok) {
      return new Response(JSON.stringify({ error: "Failed to fetch image" }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Convert image to buffer for processing
    const imageArrayBuffer = await imageResponse.arrayBuffer();
    const imageBase64 = btoa(
      String.fromCharCode(...new Uint8Array(imageArrayBuffer)),
    );
    const imageProcessingTime = performance.now();

    // Extract metadata from the request
    const metadataExtractionStart = performance.now();
    const shippingInfo = requestData.metadata?.shipping_info;
    const warnings: string[] = [];
    const errors: string[] = [];
    const info: string[] = [];

    // Validate shipping info if provided
    if (!shippingInfo) {
      warnings.push("No shipping information provided");
    }

    // Check if image was taken today based on request metadata
    const imageTimestamp = requestData.metadata?.timestamp;
    if (imageTimestamp) {
      const imageDate = new Date(imageTimestamp);
      const today = new Date();
      if (imageDate.toDateString() !== today.toDateString()) {
        warnings.push(
          `Image was not taken today. It was taken on ${imageDate.toDateString()}`,
        );
      } else {
        info.push(
          `Image was taken today at ${imageDate.toLocaleTimeString()} 📅`,
        );
      }
    } else {
      warnings.push("Image metadata does not have a timestamp");
    }

    const metadataExtractionTime = performance.now();

    // Process EXIF data from the image
    const exifAnalysisStart = performance.now();
    let extractedExif: ExtractedExifData = {
      creationDate: null,
      latitude: null,
      longitude: null,
    };

    try {
      extractedExif = await extractExifData(imageArrayBuffer);

      // Check for image creation time from EXIF
      if (extractedExif.creationDate) {
        const today = new Date();
        if (!isSameDay(extractedExif.creationDate, today)) {
          warnings.push(
            `EXIF data shows image was not created today. It was created on ${extractedExif.creationDate.toDateString()}`,
          );
        } else {
          info.push(
            `EXIF data confirms image was created today at ${extractedExif.creationDate.toLocaleTimeString()} 📅`,
          );
        }
      } else {
        warnings.push("No creation date found in EXIF data");
      }
    } catch (error) {
      console.error("Error processing EXIF data:", error);
      warnings.push("Failed to process EXIF data from image");
    }
    const exifAnalysisTime = performance.now();

    // Analyze location data using Turf.js
    const locationAnalysisStart = performance.now();

    if (extractedExif.latitude !== null && extractedExif.longitude !== null) {
      try {
        // Calculate distance between EXIF coordinates and provided coordinates
        const distanceInMiles = distance(
          point([extractedExif.longitude, extractedExif.latitude]),
          point([requestData.longitude, requestData.latitude]),
          { units: "miles" },
        );

        console.log("Distance calculation:", {
          exifCoords: [extractedExif.latitude, extractedExif.longitude],
          providedCoords: [requestData.latitude, requestData.longitude],
          distanceInMiles,
        });

        if (distanceInMiles > 10) {
          warnings.push(
            `Image EXIF location is ${distanceInMiles.toFixed(2)} miles away from provided location (exceeds 10 mile limit)`,
          );
        } else {
          info.push(
            `Image EXIF location is ${distanceInMiles.toFixed(2)} miles from provided location 📍`,
          );
        }
      } catch (error) {
        console.error("Error analyzing location data:", error);
        warnings.push("Error analyzing location data from EXIF");
      }
    } else {
      warnings.push("Image does not contain GPS location data in EXIF");
    }

    const locationAnalysisTime = performance.now();

    // Analyze image using OpenAI
    console.log("Starting AI analysis...");
    const aiAnalysisStart = performance.now();

    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: Deno.env.get("OPENAI_API_KEY") || "",
    });

    let scanResults: ScanResults = {
      success: false,
      confidence: 0,
    };

    try {
      // Send the image to OpenAI for analysis
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content:
              "You are a helpful assistant that scans images for freight scanning purposes.",
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Analyze this image and determine if it's a truck. Look for MC ID, USDOT ID, license plate, and license state. Return a JSON object with your findings.",
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`,
                },
              },
            ],
          },
        ],
        response_format: { type: "json_object" },
      });

      const responseContent = response.choices[0]?.message.content;
      if (responseContent) {
        scanResults = JSON.parse(responseContent) as ScanResults;
        console.log("AI analysis results:", scanResults);
      }
    } catch (error) {
      console.error("Error during OpenAI analysis:", error);
      errors.push("AI analysis failed: " + (error as Error).message);
    }

    const aiAnalysisTime = performance.now();

    // Process the AI results
    if (scanResults.success === false) {
      errors.push("Image analysis failed");
    } else {
      if (scanResults.isPictureInPicture) {
        warnings.push("Image is a picture of another picture");
      } else {
        info.push("Image is not a picture of another picture");
      }

      if (!scanResults.isTruck) {
        warnings.push("Image does not contain a truck");
      } else {
        info.push("Image is of a truck");
      }

      if (shippingInfo) {
        // Check MC ID
        if (!scanResults.mcId) {
          warnings.push("MC ID not found in image");
        } else if (scanResults.mcId !== shippingInfo.mcId) {
          warnings.push(
            `MC ID does not match manifest (${scanResults.mcId} vs ${shippingInfo.mcId})`,
          );
        } else {
          info.push("MC ID matches manifest 🚚");
        }

        // Check USDOT ID
        if (!scanResults.usdotId) {
          warnings.push("US DOT ID not found in image");
        } else if (scanResults.usdotId !== shippingInfo.usdotId) {
          warnings.push(
            `US DOT ID does not match manifest (${scanResults.usdotId} vs ${shippingInfo.usdotId})`,
          );
        } else {
          info.push("US DOT ID matches manifest 🚛");
        }

        // Check license plate
        if (!scanResults.licensePlate) {
          warnings.push("License plate not found in image");
        } else if (scanResults.licensePlate !== shippingInfo.licensePlate) {
          warnings.push(
            `License plate does not match manifest (${scanResults.licensePlate} vs ${shippingInfo.licensePlate})`,
          );
        } else {
          info.push("License plate matches manifest 🚗");
        }

        // Check license state
        if (!scanResults.licenseState) {
          warnings.push("License state not found in image");
        } else if (scanResults.licenseState !== shippingInfo.licenseState) {
          warnings.push(
            `License state does not match manifest (${scanResults.licenseState} vs ${shippingInfo.licenseState})`,
          );
        } else {
          info.push("License state matches manifest 🏁");
        }
      }
    }

    const endTime = performance.now();
    const totalProcessingTime = endTime - startTime;

    // Create the response
    const response: VerificationResponse = {
      success: errors.length === 0,
      verification_id: requestData.verification_id,
      verified: errors.length === 0 && warnings.length < 3, // Consider verified if fewer than 3 warnings and no errors
      confidence_score: scanResults.confidence || 0,
      warnings,
      info,
      errors: errors.length > 0 ? errors : undefined,
      processing_time: {
        image_processing_ms: Math.round(
          imageProcessingTime - imageProcessingStart,
        ),
        metadata_extraction_ms: Math.round(
          metadataExtractionTime - metadataExtractionStart,
        ),
        ai_analysis_ms: Math.round(aiAnalysisTime - aiAnalysisStart),
        exif_analysis_ms: Math.round(exifAnalysisTime - exifAnalysisStart),
        location_analysis_ms: Math.round(
          locationAnalysisTime - locationAnalysisStart,
        ),
        total_ms: Math.round(totalProcessingTime),
      },
    };

    console.log("Verification processing complete:", response);

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Error processing verification:", error);

    return new Response(
      JSON.stringify({
        error: (error as Error).message || "An unexpected error occurred",
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  }
});
