import { z } from "npm:zod";

export const fuelReceiptSchema = z.object({
  receiptNumber: z.string().optional(),
  receiptDate: z.string().optional(),
  receiptTime: z.string().optional(),
  stationName: z.string().optional(),
  stationAddress: z.string().optional(),
  stationNumber: z.string().optional(),
  pumpNumber: z.string().optional(),
  fuelType: z.string().optional(),
  fuelGrade: z.string().optional(),
  gallonsPurchased: z.number().optional(),
  pricePerGallon: z.number().optional(),
  subtotal: z.number().optional(),
  taxes: z.number().optional(),
  totalAmount: z.number().optional(),
  discountAmount: z.number().optional(),
  vehicleId: z.string().optional(),
  odometer: z.number().optional(),
  driverNumber: z.string().optional(),
  fleetCard: z.string().optional(),
  paymentMethod: z.string().optional(),
  cardType: z.string().optional(),
  cardLastFour: z.string().optional(),
  authorizationCode: z.string().optional(),
  operatorName: z.string().optional(),
  customerName: z.string().optional(),
  accountNumber: z.string().optional(),
  reference: z.string().optional(),
  biofuelPercentage: z.number().optional(),
  taxExempt: z.boolean().optional(),
  invoiceNumber: z.string().optional(),
});

export type FuelReceipt = z.infer<typeof fuelReceiptSchema>;
