import { z } from "npm:zod";

export const weightTicketSchema = z.object({
  documentIdentification: z
    .object({
      documentNumber: z.string().optional(),
      weighDate: z.string().optional(),
      weighTime: z.string().optional(),
    })
    .optional(),

  weightMeasurements: z
    .object({
      grossWeight: z.number().optional(),
      tareWeight: z.number().optional(),
      netWeight: z.number().optional(),
      weightUnit: z.string().optional(),
    })
    .optional(),

  vehicleInformation: z
    .object({
      vehicleId: z.string().optional(),
      trailerId: z.string().optional(),
      licensePlate: z.string().optional(),
      vehicleMakeModel: z.string().optional(),
      vehicleYear: z.number().optional(),
    })
    .optional(),

  driverInformation: z
    .object({
      driverName: z.string().optional(),
      driverLicense: z.string().optional(),
      driverSignature: z.boolean().optional(),
    })
    .optional(),

  scaleInformation: z
    .object({
      scaleId: z.string().optional(),
      scaleLocation: z.string().optional(),
      scaleCapacity: z.number().optional(),
      scaleCertification: z.string().optional(),
      lastCalibration: z.string().optional(),
    })
    .optional(),

  operatorInformation: z
    .object({
      certifiedOperator: z.string().optional(),
      operatorLicense: z.string().optional(),
      operatorSignature: z.boolean().optional(),
    })
    .optional(),

  cargoInformation: z
    .object({
      cargoDescription: z.string().optional(),
      shipmentNumber: z.string().optional(),
      customerName: z.string().optional(),
      destinationAddress: z.string().optional(),
    })
    .optional(),

  regulatoryAndCompliance: z
    .object({
      dotNumber: z.string().optional(),
      mcNumber: z.string().optional(),
      purpose: z.string().optional(),
      certifiedWeight: z.boolean().optional(),
    })
    .optional(),

  environmentalConditions: z
    .object({
      temperature: z.number().optional(),
      humidity: z.number().optional(),
      weatherConditions: z.string().optional(),
    })
    .optional(),

  additionalInformation: z
    .object({
      officialSeal: z.string().optional(),
      complianceStandard: z.string().optional(),
      remarks: z.string().optional(),
    })
    .optional(),

  billingAndAccounting: z
    .object({
      billingWeight: z.number().optional(),
      weightCharges: z.number().optional(),
    })
    .optional(),
});

export type WeightTicket = z.infer<typeof weightTicketSchema>;
