import { z } from "npm:zod";

export const proofOfDeliverySchema = z.object({
  documentNumber: z.string().optional(),
  documentDate: z.string().optional(),

  deliveryConfirmation: z
    .object({
      deliveryDate: z.string().optional(),
      deliveryTime: z.string().optional(),
    })
    .optional(),

  references: z
    .object({
      trackingNumber: z.string().optional(),
      bolNumber: z.string().optional(),
      podNumber: z.string().optional(),
    })
    .optional(),

  recipient: z
    .object({
      consignee: z.string().optional(),
      receiverName: z.string().optional(),
      receiverTitle: z.string().optional(),
    })
    .optional(),

  deliveryLocation: z.string().optional(),

  shipment: z
    .object({
      piecesDelivered: z.number().optional(),
      weightDelivered: z.number().optional(),
      cargoDescription: z.string().optional(),
    })
    .optional(),

  condition: z
    .object({
      deliveryCondition: z.string().optional(),
      exceptions: z.string().optional(),
    })
    .optional(),

  confirmation: z
    .object({
      signature: z.string().optional(),
      driverName: z.string().optional(),
      carrierName: z.string().optional(),
    })
    .optional(),

  specialInstructions: z.string().optional(),
});

export type ProofOfDelivery = z.infer<typeof proofOfDeliverySchema>;
