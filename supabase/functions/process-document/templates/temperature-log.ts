import { z } from "npm:zod";

const temperatureReadingSchema = z.object({
  timestamp: z.string().optional(),
  temperature: z.number().optional(),
});

const monitoringZoneSchema = z.object({
  zone: z.string().optional(),
  minTemp: z.number().optional(),
  maxTemp: z.number().optional(),
});

const monitoredItemSchema = z.object({
  itemDescription: z.string().optional(),
  temperatureReadings: z.array(z.number()).optional(),
});

const alarmEventSchema = z.object({
  timestamp: z.string().optional(),
  eventType: z.string().optional(),
  details: z.string().optional(),
});

const temperatureSummarySchema = z.object({
  totalReadings: z.number().optional(),
  minTemperature: z.number().optional(),
  maxTemperature: z.number().optional(),
  averageTemperature: z.number().optional(),
  temperatureRange: z.number().optional(),
  readingCount: z.number().optional(),
});

export const temperatureLogSchema = z.object({
  documentNumber: z.string().optional(),
  documentDate: z.string().optional(),

  equipment: z
    .object({
      recorderId: z.string().optional(),
      equipmentType: z.string().optional(),
      containerNumber: z.string().optional(),
    })
    .optional(),

  shipment: z
    .object({
      bolReference: z.string().optional(),
      commodity: z.string().optional(),
    })
    .optional(),

  settings: z
    .object({
      setPointTemp: z.number().optional(),
      temperatureUnit: z.string().optional(),
      recordingInterval: z.string().optional(),
    })
    .optional(),

  temperatureReadings: z.array(temperatureReadingSchema).optional(),
  monitoringZones: z.array(monitoringZoneSchema).optional(),
  monitoredItems: z.array(monitoredItemSchema).optional(),
  alarmEvents: z.array(alarmEventSchema).optional(),
  temperatureSummary: temperatureSummarySchema.optional(),

  timePeriod: z
    .object({
      startDateTime: z.string().optional(),
      endDateTime: z.string().optional(),
      duration: z.string().optional(),
    })
    .optional(),

  alarms: z
    .object({
      count: z.number().optional(),
      details: z.string().optional(),
    })
    .optional(),

  locations: z
    .object({
      origin: z.string().optional(),
      destination: z.string().optional(),
    })
    .optional(),

  compliance: z
    .object({
      status: z.string().optional(),
      certifiedBy: z.string().optional(),
    })
    .optional(),

  notes: z.string().optional(),

  logistics: z
    .object({
      shipper: z.string().optional(),
      consignee: z.string().optional(),
      driverSignature: z.string().optional(),
      deliveryTime: z.string().optional(),
    })
    .optional(),
});

export type TemperatureLog = z.infer<typeof temperatureLogSchema>;
