import { z } from "npm:zod";

const partySchema = z.object({
  company: z.string().optional(),
  address: z.string().optional(),
  suite: z.string().optional(),
  contact: z.string().optional(),
  phone: z.string().optional(),
});

const cargoItemSchema = z.object({
  description: z.string().optional(),
  marks: z.string().optional(),
  packages: z.number().optional(),
  packageType: z.string().optional(),
  weight: z.number().optional(),
  volume: z.number().optional(),
  freightClass: z.string().optional(),
});

export const billOfLadingSchema = z.object({
  documentNumber: z.string().optional(),
  documentDate: z.string().optional(),
  billType: z.string().optional(),
  referenceNumber: z.string().optional(),

  shipper: partySchema.optional(),
  consignee: partySchema.optional(),
  notifyParty: partySchema.optional(),

  carrier: z
    .object({
      company: z.string().optional(),
      scac: z.string().optional(),
      freightForwarder: z.string().optional(),
    })
    .optional(),

  transport: z
    .object({
      vesselName: z.string().optional(),
      voyageNumber: z.string().optional(),
      flag: z.string().optional(),
    })
    .optional(),

  locations: z
    .object({
      portOfLoading: z.string().optional(),
      portOfDischarge: z.string().optional(),
      placeOfReceipt: z.string().optional(),
      placeOfDelivery: z.string().optional(),
    })
    .optional(),

  equipment: z
    .object({
      containerNumbers: z.array(z.string()).optional(),
      containerSeals: z.array(z.string()).optional(),
      containerType: z.string().optional(),
    })
    .optional(),

  cargo: z.array(cargoItemSchema).optional(),

  financials: z
    .object({
      freightCharges: z.number().optional(),
      freightPrepaid: z.boolean().optional(),
      freightCollect: z.boolean().optional(),
    })
    .optional(),

  handling: z
    .object({
      hazmatFlag: z.boolean().optional(),
      hazmatClass: z.string().optional(),
      temperatureControlled: z.boolean().optional(),
      specialInstructions: z.string().optional(),
    })
    .optional(),

  certification: z
    .object({
      placeOfIssue: z.string().optional(),
      dateOfIssue: z.string().optional(),
      signedBy: z.string().optional(),
      carrierSignature: z.boolean().optional(),
    })
    .optional(),

  additional: z
    .object({
      bookingNumber: z.string().optional(),
      exportReferences: z.string().optional(),
      forwardingAgent: z.string().optional(),
    })
    .optional(),
});

export type BillOfLading = z.infer<typeof billOfLadingSchema>;
