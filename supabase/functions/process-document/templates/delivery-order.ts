import { z } from "npm:zod";

const consigneeSchema = z.object({
  company: z.string().optional(),
  address: z.string().optional(),
});

export const deliveryOrderSchema = z.object({
  documentNumber: z.string().optional(),
  documentDate: z.string().optional(),
  bolReference: z.string().optional(),

  authorization: z
    .object({
      issuingAgent: z.string().optional(),
      authorizedBy: z.string().optional(),
      releaseDate: z.string().optional(),
    })
    .optional(),

  consignee: consigneeSchema.optional(),
  notifyParty: z.string().optional(),

  cargo: z
    .object({
      description: z.string().optional(),
      containerNumbers: z.array(z.string()).optional(),
      sealNumbers: z.array(z.string()).optional(),
      totalWeight: z.number().optional(),
    })
    .optional(),

  vessel: z
    .object({
      name: z.string().optional(),
      voyageNumber: z.string().optional(),
    })
    .optional(),

  delivery: z
    .object({
      location: z.string().optional(),
      conditions: z.string().optional(),
      instructions: z.string().optional(),
    })
    .optional(),
});

export type DeliveryOrder = z.infer<typeof deliveryOrderSchema>;
