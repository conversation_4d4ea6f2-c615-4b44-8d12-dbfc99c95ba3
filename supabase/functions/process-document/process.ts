import { z } from "npm:zod";

import type { DocumentIdentification } from "./identify.ts";
import type { FilePackage, ProcessingResult } from "./types.ts";

import { generateSchema } from "../_shared/ai/openai.ts";
import * as templates from "./templates/index.ts";

type DocumentType = keyof typeof templates;

function getDocumentTemplate(
  documentType: DocumentType,
): z.ZodObject<z.ZodRawShape> | null {
  const schemaName = `${documentType.replace(/_([a-z])/g, (g) =>
    g[1].toUpperCase(),
  )}Schema`;
  return (
    (templates as Record<string, z.ZodObject<z.ZodRawShape>>)[schemaName] ||
    null
  );
}

async function processWithTemplate(
  identification: DocumentIdentification,
  template: z.ZodObject<z.ZodRawShape>,
): Promise<ProcessingResult> {
  const result = await generateSchema({
    schema: template,
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: `Extract the following fields from the document: ${Object.keys(
              template.shape,
            ).join(", ")}`,
          },
        ],
      },
    ],
    system: "You are an expert data extraction assistant.",
  });

  return {
    documentType: identification.documentType,
    confidence: identification.confidence,
    extractedData: result,
    processingErrors: [],
    warnings: [],
  };
}

export async function processDocument(
  _filePackage: FilePackage,
  identification: DocumentIdentification,
): Promise<ProcessingResult> {
  const template = getDocumentTemplate(
    identification.documentType as DocumentType,
  );

  if (!template) {
    return {
      documentType: identification.documentType,
      confidence: identification.confidence,
      processingErrors: [
        `No template found for document type: ${identification.documentType}`,
      ],
      extractedData: null,
      warnings: [],
    };
  }

  return await processWithTemplate(identification, template);
}
