import { z } from "npm:zod";

import type { FilePackage } from "./types.ts";

import { generateSchema } from "../_shared/ai/openai.ts";

const identificationSchema = z.object({
  documentType: z.enum([
    "bill_of_lading",
    "delivery_order",
    "proof_of_delivery",
    "fuel_receipt",
    "temperature_log",
    "weight_ticket",
    "physical_seal",
    "dot_placard",
    "mc_placard",
    "identification_plate",
    "generic",
  ]),
  confidence: z.number().min(0).max(100),
  extractedFields: z.record(z.unknown()),
  warnings: z.array(z.string()),
});

export type DocumentIdentification = z.infer<typeof identificationSchema>;

/**
 * Step 2: Enhanced document identification using AI analysis with logistics domain expertise
 * Routes to appropriate AI model based on file type with sophisticated prompts
 */
export async function identifyDocument(
  filePackage: FilePackage,
): Promise<DocumentIdentification> {
  try {
    const result = await generateSchema({
      schema: identificationSchema,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Identify the document type and extract all fields.",
            },
            {
              type: "image",
              image: `data:${filePackage.contentType};base64,${filePackage.base64Data}`,
            },
          ],
        },
      ],
      system: `
        You are an expert document analysis system. Your task is to identify the type of a given document
        from a list of known types and perform a preliminary data extraction.

        Analyze the document and determine its type, then extract any visible fields as key-value pairs.
        Do not invent or infer data that isn't explicitly present in the document.
      `,
    });

    return result;
  } catch (error) {
    console.error("Error identifying document:", error);
    return {
      documentType: "generic",
      confidence: 0,
      extractedFields: {},
      warnings: ["Error during AI identification."],
    };
  }
}
