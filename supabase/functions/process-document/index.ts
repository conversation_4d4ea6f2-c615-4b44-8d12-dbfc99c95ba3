import type { ProcessingResult, SafeFilePackage } from "./types.ts";

import { corsHeaders } from "../_shared/cors.ts";
import { identifyDocument } from "./identify.ts";
import { getProcessingRoute, normalizeFile } from "./normalize.ts";
import { processDocument } from "./process.ts";

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  } else if (req.method !== "POST") {
    return new Response(null, {
      status: 405,
      headers: { ...corsHeaders, Allow: "POST" },
    });
  }

  try {
    console.log("Document processing pipeline started");

    // Extract file from form data
    const formData = await req.formData();
    const file = formData.get("file");

    if (!file || !(file instanceof File)) {
      console.error("No file provided in request");
      return new Response(
        JSON.stringify({
          error: "Missing required fields: file is required",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        },
      );
    }

    console.log(`Starting pipeline for: ${file.name} (${file.size} bytes)`);

    // Step 1: Normalize file into structured package
    console.log("Step 1: Normalizing file...");
    const filePackage = await normalizeFile(file);
    const processingRoute = getProcessingRoute(filePackage);
    console.log(`Processing route determined: ${processingRoute}`);

    // Step 2: Identify document type using AI
    console.log("Step 2: Identifying document type...");
    const identification = await identifyDocument(filePackage);
    console.log(
      `Document identified: ${identification.documentType} (${identification.confidence}% confidence)`,
    );

    // Step 3: Enhanced processing based on identification
    console.log("Step 3: Processing document with specialized logic...");
    const processingResult = await processDocument(filePackage, identification);
    console.log("Document processing completed successfully");

    // Prepare final result (omit sensitive data)
    const safeFilePackage: SafeFilePackage = {
      originalName: filePackage.originalName,
      sanitizedName: filePackage.sanitizedName,
      contentType: filePackage.contentType,
      fileSize: filePackage.fileSize,
      fileExtension: filePackage.fileExtension,
      fileId: filePackage.fileId,
      filePath: filePackage.filePath,
    };

    const result: ProcessingResult = {
      documentType: identification.documentType,
      confidence: identification.confidence,
      extractedData: processingResult.extractedData,
      processingErrors: processingResult.processingErrors,
      warnings: processingResult.warnings,
    };

    console.log("Returning processing results");
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Document processing pipeline error:", error);

    // Determine error type for better user feedback
    let status = 500;
    let errorMessage = "Document processing failed";

    if (error instanceof Error) {
      if (error.message.includes("File size exceeds")) {
        status = 413; // Payload Too Large
        errorMessage = "File too large";
      } else if (error.message.includes("Unsupported file type")) {
        status = 415; // Unsupported Media Type
        errorMessage = "Unsupported file type";
      } else if (error.message.includes("API key")) {
        status = 503; // Service Unavailable
        errorMessage = "AI service temporarily unavailable";
      }
    }

    return new Response(
      JSON.stringify({
        error: errorMessage,
        details: (error as Error).message,
        pipelineVersion: "V2_MODULAR",
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status,
      },
    );
  }
});
