export interface FilePackage {
  // Original file information
  originalFile: File;
  originalName: string;
  sanitizedName: string;

  // File metadata
  contentType: string;
  fileSize: number;
  fileExtension: string;

  // Generated identifiers
  fileId: string;
  filePath: string;

  // File content
  buffer: ArrayBuffer;
  base64Data: string;
}

export interface DocumentIdentification {
  documentType: string;
  confidence: number;
  extractedFields: Record<string, unknown>;
  pageCount?: number;
  warnings: string[];
}

export interface SafeFilePackage {
  // Original file information (excluding File object and sensitive data)
  originalName: string;
  sanitizedName: string;

  // File metadata
  contentType: string;
  fileSize: number;
  fileExtension: string;

  // Generated identifiers
  fileId: string;
  filePath: string;
}

export interface ProcessingResult {
  documentType: string;
  confidence: number;
  extractedData: Record<string, unknown> | null;
  processingErrors: string[];
  warnings: string[];
}

export interface ProcessingStep<T> {
  execute(input: T): Promise<T>;
}
