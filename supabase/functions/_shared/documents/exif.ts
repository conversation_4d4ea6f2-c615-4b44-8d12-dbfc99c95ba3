import ExifReader from "https://esm.sh/exifreader@4.20.0";

export interface ExtractedExifData {
  creationDate: Date | null;
  latitude: number | null;
  longitude: number | null;
}

export async function extractExifData(
  imageArrayBuffer: ArrayBuffer,
): Promise<ExtractedExifData> {
  let creationDate: Date | null = null;
  let latitude: number | null = null;
  let longitude: number | null = null;

  try {
    const exifData = await ExifReader.load(imageArrayBuffer);
    console.log("EXIF data extracted:", Object.keys(exifData));

    // --- Extract Creation Date ---
    const imageDateTime = exifData.DateTimeOriginal || exifData.DateTime;
    const imageCreationTime = exifData["Creation Time"]; // Corrected property name

    if (imageDateTime?.description) {
      try {
        // Convert EXIF date format (YYYY:MM:DD HH:MM:SS) to JS Date
        const dateStr = imageDateTime.description as string;
        const [datePart, timePart] = dateStr.split(" ");
        if (datePart && timePart) {
          const formattedDate = datePart.replace(/:/g, "-"); // Convert YYYY:MM:DD to YYYY-MM-DD
          creationDate = new Date(`${formattedDate}T${timePart}`);
          if (isNaN(creationDate.getTime())) {
            // Check if date is valid
            console.warn(
              "Parsed EXIF DateTime resulted in invalid Date:",
              `${formattedDate}T${timePart}`,
            );
            creationDate = null; // Reset if invalid
          }
        } else {
          console.warn("Could not parse EXIF DateTime string:", dateStr);
        }
      } catch (e) {
        console.warn("Error parsing EXIF DateTime:", e);
        creationDate = null;
      }
    } else if (imageCreationTime?.description) {
      try {
        creationDate = new Date(imageCreationTime.description as string);
        if (isNaN(creationDate.getTime())) {
          // Check if date is valid
          console.warn(
            "Parsed EXIF Creation Time resulted in invalid Date:",
            imageCreationTime.description,
          );
          creationDate = null; // Reset if invalid
        }
      } catch (e) {
        console.warn("Error parsing EXIF Creation Time:", e);
        creationDate = null;
      }
    }

    if (creationDate) {
      console.log("Extracted Creation Date:", creationDate.toISOString());
    } else {
      console.log("No valid creation date found in EXIF.");
    }

    // --- Extract GPS Coordinates ---
    if (
      exifData.GPSLatitude?.description &&
      exifData.GPSLongitude?.description
    ) {
      try {
        const latDirection =
          (exifData.GPSLatitudeRef?.value[0] as "N" | "S") || "N";
        const longDirection =
          (exifData.GPSLongitudeRef?.value[0] as "E" | "W") || "E";

        const latValue = parseFloat(exifData.GPSLatitude.description as string);
        const longValue = parseFloat(
          exifData.GPSLongitude.description as string,
        );

        if (!isNaN(latValue) && !isNaN(longValue)) {
          latitude = latValue * (latDirection === "N" ? 1 : -1);
          longitude = longValue * (longDirection === "E" ? 1 : -1);
          console.log("Extracted GPS Coordinates:", { latitude, longitude });
        } else {
          console.warn(
            "Could not parse GPS coordinates from EXIF descriptions.",
          );
        }
      } catch (error) {
        console.error("Error parsing GPS data from EXIF:", error);
      }
    } else {
      console.log("No GPS Latitude/Longitude found in EXIF.");
    }
  } catch (error) {
    console.error("Error loading EXIF data:", error);
    // Return nulls if EXIF loading fails entirely
    return { creationDate: null, latitude: null, longitude: null };
  }

  return { creationDate, latitude, longitude };
}
