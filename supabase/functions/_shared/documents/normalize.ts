import { Buffer } from "node:buffer";

export interface FilePackage {
  originalFile: File;
  originalName: string;
  sanitizedName: string;
  contentType: string;
  fileSize: number;
  fileExtension: string;
  fileId: string;
  filePath: string;
  buffer: ArrayBuffer;
  base64Data: string;
}

/**
 * Step 1: Normalize file input into a structured package
 * Extracts file information, validates content, and prepares for processing
 */
export async function normalizeFile(file: File): Promise<FilePackage> {
  console.log(
    `📄 Normalizing file: ${file.name} (${file.size} bytes, type: ${file.type})`,
  );

  // Extract file metadata
  const originalName = file.name;
  const sanitizedName = file.name.replace(/[^\x20-\x7E]/g, ""); // Remove non-printable and non-ASCII characters
  const contentType = file.type || "application/octet-stream";
  const fileSize = file.size;
  const fileExtension = file.name.split(".").pop() || "unknown";

  // Generate unique identifiers
  const fileId = crypto.randomUUID();
  const filePath = `_internal_/docs/${fileId}.${fileExtension}`;

  console.log(`Generated file ID: ${fileId}, extension: ${fileExtension}`);

  // Extract file content
  console.log(`Extracting content for ${originalName} (${fileSize} bytes)`);
  const buffer = await file.arrayBuffer();
  const base64Data = Buffer.from(buffer).toString("base64");

  console.log(
    `✅ Content extracted: ${base64Data.length} characters in base64`,
  );

  // Validate file size (optional - add limits if needed)
  const maxSize = 50 * 1024 * 1024; // 50MB limit
  if (fileSize > maxSize) {
    console.error(
      `❌ File size ${fileSize} exceeds maximum limit of ${maxSize} bytes`,
    );
    throw new Error(`File size exceeds maximum limit of ${maxSize} bytes`);
  }

  // Validate file type (optional - add type restrictions if needed)
  const supportedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "text/plain",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];

  if (!supportedTypes.includes(contentType)) {
    console.warn(
      `⚠️ Potentially unsupported file type: ${contentType} - will attempt processing anyway`,
    );
  } else {
    console.log(`✅ File type ${contentType} is supported`);
  }

  const filePackage: FilePackage = {
    originalFile: file,
    originalName,
    sanitizedName,
    contentType,
    fileSize,
    fileExtension,
    fileId,
    filePath,
    buffer,
    base64Data,
  };

  console.log(`✅ File normalized successfully: ${fileId} (${contentType})`);
  console.log(
    `Package details: ${originalName} -> ${sanitizedName}, ${fileSize} bytes, .${fileExtension}`,
  );
  return filePackage;
}

/**
 * Determine the preprocessing route based on file type
 */
export function getProcessingRoute(filePackage: FilePackage): string {
  const { contentType } = filePackage;

  let route: string;
  if (contentType === "application/pdf") {
    route = "pdf-document";
  } else if (contentType.startsWith("image/")) {
    route = "image-vision";
  } else if (contentType.startsWith("text/")) {
    route = "text-analysis";
  } else if (contentType.includes("word") || contentType.includes("document")) {
    route = "document-extraction";
  } else {
    route = "binary-analysis";
  }

  console.log(
    `🛤️ Processing route determined: ${route} for content type: ${contentType}`,
  );
  return route;
}
