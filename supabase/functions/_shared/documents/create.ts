import { autoTable } from "https://esm.sh/jspdf-autotable@4";
import { jsPDF } from "https://esm.sh/jspdf@3";

export class DocumentCreationError extends Error {
  code: number;
  constructor(message: string, code: number) {
    super(message);
    this.name = "DocumentCreationError";
    this.code = code;
  }
}

interface CreateDocumentParams {
  shipmentId: string;
  organizationId: string;
  documentType?: string;
  content: string;
}

interface DocumentRequest {
  templateData: unknown;
  title: string;
  metadata: {
    shipmentId: string;
  };
}

const documentTemplates = {
  bill_of_lading: (pdf: jsPDF, data: DocumentRequest) => {
    const { templateData } = data;

    if (!templateData) {
      console.error("Template data is required for bill of lading");
      return;
    }

    // Header with title
    pdf.setFontSize(18);
    pdf.setTextColor(0, 0, 0);
    pdf.setFont("helvetica", "bold");
    pdf.text("BILL OF LADING", 105, 20, { align: "center" });

    // Document ID and date
    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    pdf.text(`Document ID: ${crypto.randomUUID()}`, 195, 10, {
      align: "right",
    });
    pdf.text(`Generated: ${new Date().toLocaleString()}`, 195, 15, {
      align: "right",
    });

    // Shipper and consignee section
    pdf.setFontSize(10);
    pdf.setTextColor(0, 0, 0);
    pdf.setFont("helvetica", "bold");

    // Shipper box
    pdf.rect(15, 30, 85, 35);
    pdf.text("SHIPPER", 18, 38);
    pdf.setFont("helvetica", "normal");
    pdf.text(templateData.shipper?.name || "N/A", 18, 45);
    pdf.text(templateData.shipper?.address || "N/A", 18, 52);
    pdf.text(templateData.shipper?.contact || "N/A", 18, 59);

    // Consignee box
    pdf.rect(110, 30, 85, 35);
    pdf.setFont("helvetica", "bold");
    pdf.text("CONSIGNEE", 113, 38);
    pdf.setFont("helvetica", "normal");
    pdf.text(templateData.consignee?.name || "N/A", 113, 45);
    pdf.text(templateData.consignee?.address || "N/A", 113, 52);
    pdf.text(templateData.consignee?.contact || "N/A", 113, 59);

    // Carrier information
    pdf.rect(15, 75, 180, 25);
    pdf.setFont("helvetica", "bold");
    pdf.text("CARRIER INFORMATION", 18, 83);
    pdf.setFont("helvetica", "normal");
    pdf.text(`Carrier: ${templateData.carrier?.name || "N/A"}`, 18, 90);
    pdf.text(`SCAC: ${templateData.carrier?.scac || "N/A"}`, 105, 90);
    pdf.text(`Contact: ${templateData.carrier?.contact || "N/A"}`, 18, 97);

    // Shipment information
    pdf.rect(15, 110, 180, 30);
    pdf.setFont("helvetica", "bold");
    pdf.text("SHIPMENT INFORMATION", 18, 118);
    pdf.setFont("helvetica", "normal");
    pdf.text(`Shipment #: ${templateData.shipment?.number || "N/A"}`, 18, 125);
    pdf.text(
      `Date: ${templateData.shipment?.date || new Date().toLocaleDateString()}`,
      105,
      125,
    );
    pdf.text(
      `Reference: ${templateData.shipment?.reference || "N/A"}`,
      18,
      132,
    );
    pdf.text(
      `Special Instructions: ${templateData.shipment?.specialInstructions || "None"}`,
      18,
      139,
    );

    // Items table
    const itemRows =
      templateData.items?.map((item) => [
        item.description || "N/A",
        item.weight || "0",
        item.pieces?.toString() || "0",
        item.packageType || "N/A",
        item.hazmat ? "Yes" : "No",
      ]) || [];

    autoTable(pdf, {
      startY: 150,
      head: [["Description", "Weight", "Pieces", "Package Type", "Hazmat"]],
      body: itemRows,
      headStyles: { fillColor: [60, 60, 60], textColor: [255, 255, 255] },
      styles: { fontSize: 9 },
      theme: "grid",
    });

    // Signature section
    const finalY =
      (pdf as unknown as { lastAutoTable: { finalY: number } }).lastAutoTable
        .finalY || 200;
    pdf.rect(15, finalY + 10, 180, 40);
    pdf.setFont("helvetica", "bold");
    pdf.text("SIGNATURES", 18, finalY + 20);

    // Shipper signature
    pdf.setFont("helvetica", "normal");
    pdf.text("Shipper:", 18, finalY + 30);
    pdf.line(45, finalY + 30, 95, finalY + 30);

    // Driver signature
    pdf.text("Driver:", 105, finalY + 30);
    pdf.line(132, finalY + 30, 182, finalY + 30);

    // Consignee signature
    pdf.text("Consignee:", 18, finalY + 40);
    pdf.line(52, finalY + 40, 95, finalY + 40);

    // Date
    pdf.text("Date:", 105, finalY + 40);
    pdf.line(125, finalY + 40, 182, finalY + 40);

    // Add footer
    const pageCount = pdf.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setTextColor(150, 150, 150);
      pdf.text(
        `Page ${i} of ${pageCount} | Generated by QuikSkope Document System`,
        pdf.internal.pageSize.getWidth() / 2,
        pdf.internal.pageSize.getHeight() - 10,
        { align: "center" },
      );
    }
  },

  // More templates can be added here for the other document types
  contract: (pdf: jsPDF, data: DocumentRequest) => {
    // Implement contract template
    // For now use general template
    documentTemplates.general(pdf, data);
  },

  manifest: (pdf: jsPDF, data: DocumentRequest) => {
    // Implement manifest template
    // For now use general template
    documentTemplates.general(pdf, data);
  },

  verification: (pdf: jsPDF, data: DocumentRequest) => {
    // Implement verification template
    // For now use general template
    documentTemplates.general(pdf, data);
  },

  general: (pdf: jsPDF, data: DocumentRequest) => {
    // Add header with logo placeholder and title
    pdf.setFontSize(20);
    pdf.setTextColor(0, 58, 94);
    pdf.text(data.title, 105, 20, { align: "center" });

    // Add metadata section
    pdf.setFontSize(10);
    pdf.setTextColor(100, 100, 100);
    pdf.text(`Document ID: ${crypto.randomUUID()}`, 20, 35);
    pdf.text(`Type: ${data.documentType}`, 20, 40);
    pdf.text(`Generated: ${new Date().toLocaleString()}`, 20, 45);

    if (data.metadata.shipmentId) {
      pdf.text(`Shipment ID: ${data.metadata.shipmentId}`, 20, 50);
    }

    // Add main content
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);

    // Handle content based on type
    if (data.tableData) {
      // Add table data if provided
      autoTable(pdf, {
        head: [data.tableData.headers],
        body: data.tableData.rows,
        startY: 60,
        margin: { top: 60 },
        styles: { fontSize: 10 },
        headStyles: { fillColor: [0, 58, 94] },
      });

      // Add content after table
      const finalY = pdf.lastAutoTable.finalY || 60;
      pdf.text(data.content, 20, finalY + 15);
    } else {
      // Just add content with line breaks
      const contentLines = pdf.splitTextToSize(data.content, 170);
      pdf.text(contentLines, 20, 60);
    }
  },

  other: (pdf: jsPDF, data: DocumentRequest) => {
    // Use general template for "other" document types
    documentTemplates.general(pdf, data);
  },
};

export async function createDocument({
  shipmentId,
  organizationId,
  documentType = "manifest",
  content,
}: CreateDocumentParams) {
  if (!shipmentId || !content || !organizationId) {
    throw new DocumentCreationError(
      "Missing required fields: shipmentId, organizationId, and content are required",
      400,
    );
  }

  const { supabase } = await import("../supabase.ts");

  const { data: shipment, error: shipmentError } = await supabase
    .from("shipments")
    .select("*")
    .eq("id", shipmentId)
    .single();

  if (shipmentError) {
    console.error("Error fetching shipment:", shipmentError);
    throw new DocumentCreationError("Shipment not found", 404);
  }

  const pdf = new jsPDF();
  const timestamp = new Date().toISOString();
  pdf.setFontSize(16);
  pdf.text("Shipment Document", 20, 20);

  pdf.setFontSize(12);
  pdf.text(`Generated: ${new Date().toLocaleString()}`, 20, 30);
  pdf.text(`Shipment ID: ${shipmentId}`, 20, 40);
  pdf.text(`Type: ${documentType}`, 20, 50);

  pdf.setFontSize(11);
  pdf.text(content, 20, 70);

  const pdfBlob = new Blob([pdf.output("blob")], { type: "application/pdf" });
  const pdfFile = new File([pdfBlob], `${documentType}_${shipmentId}.pdf`, {
    type: "application/pdf",
  });

  const storagePath = `${shipmentId}/${crypto.randomUUID()}.pdf`;

  const { error: uploadError } = await supabase.storage
    .from("documents")
    .upload(storagePath, pdfFile, {
      contentType: "application/pdf",
      upsert: false,
    });

  if (uploadError) {
    console.error("Error uploading file:", uploadError);
    throw new DocumentCreationError("Failed to upload file", 500);
  }

  const {
    data: { publicUrl },
  } = supabase.storage.from("documents").getPublicUrl(storagePath);

  const { data: document, error: documentError } = await supabase
    .from("documents")
    .insert({
      name: `${documentType}_${shipmentId}.pdf`,
      storage_path: storagePath,
      url: publicUrl,
      content_type: "application/pdf",
      size: pdfBlob.size,
      organization_id: organizationId,
      type: documentType,
      metadata: {
        generated_at: timestamp,
        shipment_id: shipmentId,
        shipment_status: shipment.status,
        document_type: documentType,
        generation_method: "automatic",
      },
    })
    .select()
    .single();

  if (documentError) {
    console.error("Error creating document:", documentError);
    throw new DocumentCreationError("Failed to create document record", 500);
  }

  return document;
}
