import { DateTime } from "https://esm.sh/luxon@3";

/**
 * Checks if two Date objects fall on the same calendar day.
 * @param date1 The first date.
 * @param date2 The second date.
 * @returns True if both dates are on the same day, false otherwise.
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  const dt1 = DateTime.fromJSDate(date1);
  const dt2 = DateTime.fromJSDate(date2);
  // Explicitly check year, month, and day
  return (
    dt1.year === dt2.year && dt1.month === dt2.month && dt1.day === dt2.day
  );
}
