import type { CoreMessage } from "npm:ai";

import { createOpenAI } from "npm:@ai-sdk/openai";
import { generateObject } from "npm:ai";
import { z } from "npm:zod";

export const openai = createOpenAI({
  apiKey: Deno.env.get("OPENAI_API_KEY") || "",
});

export async function generateSchema<T extends z.ZodTypeAny>({
  schema,
  messages,
  system,
}: {
  schema: T;
  messages: CoreMessage[];
  system?: string;
}) {
  const result = // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore - TODO: fix this
    (await generateObject({
      model: openai("gpt-4o"),
      system,
      messages,
      schema,
    })) as {
      object: z.infer<T>;
    };

  return result.object;
}
