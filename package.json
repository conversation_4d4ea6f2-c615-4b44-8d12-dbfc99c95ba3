{"name": "@quikskope/web", "private": true, "version": "0.0.0", "type": "module", "packageManager": "bun@1.2.17", "workspaces": ["supabase"], "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "format": "prettier --check . --ignore-path .gitignore", "format:fix": "prettier --write . --ignore-path .gitignore", "typecheck": "bun typecheck:src && bun typecheck:node", "typecheck:src": "tsc --noEmit --project ./tsconfig.app.json", "typecheck:node": "tsc --noEmit --project ./tsconfig.node.json", "preview": "vite preview", "storybook": "storybook dev -p 6002", "build-storybook": "storybook build", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "db:types": "bunx supabase gen types --linked > src/supabase/types.ts"}, "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@hookform/resolvers": "^4.1.3", "@mapbox/mapbox-sdk": "^0.16.1", "@maskito/core": "^2.5.0", "@maskito/kit": "^2.5.0", "@maskito/react": "^2.5.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-aria/utils": "^3.29.1", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/postgrest-js": "*", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "@turf/turf": "^7.2.0", "@types/uuid": "^10.0.0", "caniuse-lite": "^1.0.30001726", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.525.0", "luxon": "^3.6.1", "mapbox-gl": "^3.13.0", "motion": "^12.19.2", "next-themes": "^0.4.6", "openai": "^5.8.2", "react": "^19.1.0", "react-aria": "^3.41.1", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.59.0", "react-resizable-panels": "^3.0.3", "react-router": "^7.6.3", "react-stately": "^3.39.0", "recharts": "^3.0.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@eslint/js": "^9.30.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@storybook/addon-a11y": "^9.0.14", "@storybook/addon-docs": "^9.0.14", "@storybook/addon-vitest": "^9.0.14", "@storybook/react-vite": "^9.0.14", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.30.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.0.14", "globals": "^16.2.0", "lovable-tagger": "^1.1.8", "msw": "^2.10.2", "msw-storybook-addon": "^2.0.5", "playwright": "^1.53.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "storybook": "^9.0.14", "storybook-addon-remix-react-router": "^5.0.0", "supabase": "^2.26.9", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0", "vitest": "^3.2.4"}}