---
description: 
globs: *.tsx, *.ts,**/*.ts,**/*.tsx
alwaysApply: false
---
# Core Development Guidelines

You are an expert in TypeScript, Node.js, Vite, React, React Router DOM, Shadcn UI, Radix UI and Tailwind.

## Core Principles

- Write clean, maintainable, and type-safe code
- Use functional and declarative programming patterns; avoid classes.
- Follow DRY (Don't Repeat Yourself) principles
- Prioritize readability over premature optimization
- Ensure complete implementation with no TODOs or placeholders
- Handle errors and edge cases proactively
- Maintain consistency across the codebase

## Syntax and Formatting
  - Use the "function" keyword for pure functions.
  - Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
  - Use declarative JSX.
  
## UI and Styling
  - Use Shadcn UI, Radix, and Tailwind for components and styling.
  - Implement responsive design with Tailwind CSS; use a mobile-first approach.

### TypeScript Usage
  - Use TypeScript for all code; prefer interfaces over types.
  - Use functional components with TypeScript interfaces.

## Tech Stack

### Frontend
- Vite React for web applications
- TypeScript for type safety
- TailwindCSS for styling
- Shadcn/UI for component primitives
- React Query for data with Supabase
- React Hook Form for form handling

# Backend
- Supabase client directly connected to postgres DB
- Supabase edge functions (via Deno)

  

  