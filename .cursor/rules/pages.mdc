---
description: 
globs: src/pages/**
alwaysApply: false
---
# Page Development Workflow

When creating or modifying pages, follow this workflow to ensure consistency and maintainability:

1. **Check and Use API Types**
   - Always use types directly from the API layer instead of creating custom interfaces
   - Use `ReturnType<typeof useApiHook>["data"]` to type data from API hooks:
     ```typescript
     // Example from Locations.tsx
     locations?: ReturnType<typeof useListLocations>["data"];
     ```
   - Import enum types from Supabase for type safety:
     ```typescript
     // Example from Locations.tsx
     import { Enums } from "@/supabase/types";
     type: locationType ? (locationType as Enums<"location_type">) : undefined,
     ```
   - Leverage TypeScript's inference capabilities with API hooks

2. **Identify Reusable Components**
   - Check for existing shared components before creating new ones:
     - Dialog components (`DialogConfirmation`)
     - Error handling components (`ErrorAlert`)
     - Search and filter components
   - Use existing hooks for common functionality:
     ```typescript
     // Example from Locations.tsx - reusing search hooks
     const pagination = useSearchPaginationValue({
       group: "location",
       defaultPageSize: 10,
       defaultPageIndex: 0,
     });
     const locationsQuery = useSearchTextValue("location");
     const locationType = useSearchFilterValue<string>("type", "location");
     ```

3. **Separate Data and Presentation Layers**
   - Create two components for each page:
     - Data component (e.g., `LocationsPage`): Handles data fetching, state, and mutations
     - Presentation component (e.g., `LocationsView`): Purely presentational, receives data and callbacks as props
   - Example structure:
     ```typescript
     // Data component
     export default function LocationsPage() {
       // Data fetching, state management, and handlers
       return <LocationsView {...props} />;
     }
     
     // Presentation component
     export function LocationsView({
       loading,
       error,
       data,
       onAction,
     }: Props) {
       // UI rendering only
     }
     ```

4. **Implement Data Layer**
   - Set up state management for the page
     ```typescript
     const [deleteLocationId, setDeleteLocationId] = useState<string | null>(null);
     ```
   - Connect to API hooks with proper parameters
     ```typescript
     const {
       data: locations,
       isLoading,
       error,
       refetch,
     } = useListLocations({
       pageIndex: pagination.pageIndex,
       pageSize: pagination.pageSize,
       search: locationsQuery,
       type: locationType ? (locationType as Enums<"location_type">) : undefined,
     });
     ```
   - Implement handlers for user actions
     ```typescript
     const handleDelete = async (locationId: string) => {
       try {
         await deleteLocation.mutateAsync({ id: locationId });
         await refetch();
         toast({
           title: "Success",
           description: i18n.en.toast.deleteSuccess,
         });
       } catch (error) {
         toast({
           variant: "destructive",
           title: "Error",
           description: i18n.en.toast.deleteError,
         });
         throw error;
       }
     };
     ```

5. **Implement Presentation Layer**
   - Focus solely on rendering UI based on props
   - Handle loading and error states appropriately
   - Implement responsive design
   - Pass callbacks for user actions

6. **Additional Best Practices**
   - Use internationalization objects for text content
     ```typescript
     const i18n = {
       en: {
         title: "Locations",
         addButton: "Add Location",
         // ...
       },
       links: {
         create: "/app/console/locations/create",
       },
     };
     ```
   - Implement proper error handling and user feedback
   - Refetch data after successful mutations
   - Use consistent prop naming and typing
   - Disable interactive elements during loading states
     ```typescript
     <Button asChild disabled={loading}>
       <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
     </Button>
     ```

Following this workflow ensures consistent, maintainable, and type-safe pages throughout the application.
