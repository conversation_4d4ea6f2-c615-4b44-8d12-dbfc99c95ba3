---
description: 
globs: src/api/**
alwaysApply: false
---
# Working with the API Layer

1. Create hooks in the appropriate domain folder under `src/api/`
2. Use the `use-` prefix for all hook  names (e.g., `useLoads`, `useCreateShipment`)
3. Follow the established query pattern for consistency:
   - List queries: `["resources", "list", { filters }]`
   - Single resource: `["resources", "get", id]`
   - Nested resources: `["parent", "child", "list", parentId]`
4. Import types from `@/supabase/types` for type safety
5. Extract mutation logic to a separate `mutationFn`
6. Invalidate relevant queries on successful mutations
7. Implement consistent error handling
8. For edge functions, call through the Supabase client and follow the mutation pattern

For full convention details please see [README.md](mdc:src/api/README.md)