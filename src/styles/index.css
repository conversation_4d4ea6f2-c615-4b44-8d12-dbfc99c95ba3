@import "tailwindcss";

@plugin '@tailwindcss/typography';
@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

:root {
  /* sizes */
  --radius: 1rem;
  /* colors */
  --background: hsl(0 0% 100%);
  --foreground: hsl(224 71.4% 4.1%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(224 71.4% 4.1%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(224 71.4% 4.1%);
  --primary: hsl(198.3deg 100% 43.73%);
  --primary-foreground: hsl(210 20% 98%);
  --secondary: hsl(12 100% 63%);
  --secondary-foreground: hsl(0 0% 100%);
  --muted: hsl(220 14.3% 95.9%);
  --muted-foreground: hsl(220 8.9% 46.1%);
  --accent: hsl(210 20% 98%);
  --accent-foreground: hsl(210 15% 20%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 20% 98%);
  --border: hsl(220 13% 91%);
  --input: hsl(220 13% 91%);
  --ring: hsl(12 100% 63%);
  --chart-1: hsl(198.3deg 100% 43.73%);
  --chart-2: hsl(12 100% 63%);
  --chart-3: hsl(18 72% 52%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar-background: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);

  --color-azure-radiance-50: #eff8ff;
  --color-azure-radiance-100: #dbeefe;
  --color-azure-radiance-200: #c0e2fd;
  --color-azure-radiance-300: #94d2fc;
  --color-azure-radiance-400: #62b7f8;
  --color-azure-radiance-500: #3d99f4;
  --color-azure-radiance-600: #3483ea;
  --color-azure-radiance-700: #1f65d6;
  --color-azure-radiance-800: #2052ad;
  --color-azure-radiance-900: #1f4889;
  --color-azure-radiance-950: #182c53;

  --color-lightning-yellow-50: #fffbeb;
  --color-lightning-yellow-100: #fff3c6;
  --color-lightning-yellow-200: #ffe888;
  --color-lightning-yellow-300: #fed54b;
  --color-lightning-yellow-400: #fec223;
  --color-lightning-yellow-500: #f8a008;
  --color-lightning-yellow-600: #dc7803;
  --color-lightning-yellow-700: #b65407;
  --color-lightning-yellow-800: #94410c;
  --color-lightning-yellow-900: #79360e;
  --color-lightning-yellow-950: #461a02;

  --color-red-damask-50: #fdf5ef;
  --color-red-damask-100: #fae8da;
  --color-red-damask-200: #f4cfb4;
  --color-red-damask-300: #edae84;
  --color-red-damask-400: #e48453;
  --color-red-damask-500: #dd612c;
  --color-red-damask-600: #cf4d27;
  --color-red-damask-700: #ac3b22;
  --color-red-damask-800: #8a3122;
  --color-red-damask-900: #6f2a1f;
  --color-red-damask-950: #3c130e;
}

.dark {
  --background: hsl(224 71.4% 4.1%);
  --foreground: hsl(210 20% 98%);
  --card: hsl(224 71.4% 4.1%);
  --card-foreground: hsl(210 20% 98%);
  --popover: hsl(224 71.4% 4.1%);
  --popover-foreground: hsl(210 20% 98%);
  --primary: hsl(198.3deg 100% 43.73%);
  --primary-foreground: hsl(220 85% 15%);
  --secondary: hsl(12 100% 63%);
  --secondary-foreground: hsl(0 0% 100%);
  --muted: hsl(215 27.9% 16.9%);
  --muted-foreground: hsl(217.9 10.6% 64.9%);
  --accent: hsl(210 15% 10%);
  --accent-foreground: hsl(210 15% 80%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 20% 98%);
  --border: hsl(215 27.9% 16.9%);
  --input: hsl(215 27.9% 16.9%);
  --ring: hsl(12 100% 63%);
  --chart-1: hsl(198.81deg 90.77% 25.49%);
  --chart-2: hsl(12 100% 63%);
  --chart-3: hsl(18 72% 52%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar-background: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@theme {
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-sidebar: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-float: float 6s ease-in-out infinite;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border);
  }
}

@utility glass-effect {
  @apply border border-white/10 bg-white/5 backdrop-blur-lg;
}

@utility hover-lift {
  @apply transition-transform duration-300 hover:-translate-y-1;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
