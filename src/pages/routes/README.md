# Routes

This directory contains the routing configuration for the application. Routes are organized by application section and use React Router for declarative routing.

> [!KNOWLEDGE]
>
> **Route Organization**
> Routes are organized by feature and protection level:
>
> - `public.tsx` - Marketing, landing pages, docs (no auth)
> - `auth.tsx` - Sign in/up, password reset, OAuth (no session)
> - `app.tsx` - Protected application views (requires auth)
> - `console.tsx` - Admin/management views (requires roles)
> - `drivers.tsx` - Driver-specific features
> - `components.tsx` - Shared route components
>
> **Route Structure**
> Each route file follows this pattern:
>
> ```tsx
> export const someRoutes = [
>   {
>     element: <AuthGuard />, // Protection wrapper
>     children: [
>       {
>         element: <Layout />, // Layout wrapper
>         children: [
>           {
>             path: "entity", // Feature routes
>             children: [
>               { index: true, element: <List /> },
>               { path: "create", element: <Create /> },
>               { path: ":id", element: <Detail /> },
>               { path: ":id/edit", element: <Edit /> },
>             ],
>           },
>         ],
>       },
>     ],
>   },
> ];
> ```
>
> **Route Integration**
> Routes connect pages to the app:
>
> - Pages live in `src/pages/` matching route structure
> - Guards from `src/components/auth` handle protection
> - Layouts from `src/components/layouts` wrap content
> - Error boundaries catch and display errors
> - Lazy loading splits code by route (optional, we have chunks currently active per group)
>
> **Route Types**
> TypeScript ensures type safety:
>
> ```tsx
> // Route params are typed
> interface EntityParams {
>   id: string;
> }
> const { id } = useParams<EntityParams>();
>
> // Loaders are typed
> interface EntityData {
>   /* ... */
> }
> const loader: LoaderFunction = async ({ params }) => {
>   return json<EntityData>({
>     /* ... */
>   });
> };
>
> // Actions are typed
> interface ActionData {
>   /* ... */
> }
> const action: ActionFunction = async ({ request }) => {
>   return json<ActionData>({
>     /* ... */
>   });
> };
> ```

## Directory Structure

```
routes/
├── index.tsx       # Root router configuration
├── components.tsx  # Reusable route components
├── app.tsx         # Protected application routes
├── auth.tsx        # Authentication routes
├── drivers.tsx     # Driver routes
├── console.tsx     # Admin console routes
├── public.tsx      # Public/marketing routes
└── README.md       # This file
```

## Route Types

### Public Routes (`public.tsx`)

- Marketing pages
- Landing pages
- Documentation
- Contact pages
- No auth required

### Auth Routes (`auth.tsx`)

- Sign in/up pages
- Password reset
- Email verification
- OAuth flows
- Requires no active session

### App Routes (`app.tsx`)

- Protected application views
- User dashboard
- Data management
- Requires authentication

### Console Routes (`console.tsx`)

- Admin interfaces
- Management views
- Configuration pages
- Requires specific roles

## Best Practices

1. **Route Organization**
   - Group routes by section
   - Use nested routes for related views
   - Implement proper guards
   - Handle loading states

2. **Route Protection**
   - Use auth guards consistently
   - Check role requirements
   - Handle unauthorized access
   - Redirect appropriately

3. **Route Loading**
   - Implement lazy loading
   - Handle suspense boundaries
   - Show loading indicators
   - Cache route data

4. **Route Parameters**
   - Type route params
   - Validate parameters
   - Handle missing params
   - Support search queries

5. **Error Handling**
   - Use error boundaries
   - Handle 404 cases
   - Show error messages
   - Provide recovery options

## Common Patterns

### Basic Routes

```tsx
// src/routes/app.tsx
export const appRoutes = [
  {
    path: "loads",
    element: <LoadsLayout />,
    children: [
      { index: true, element: <LoadsPage /> },
      { path: "create", element: <CreateLoadPage /> },
      { path: ":id", element: <LoadDetailPage /> },
      { path: ":id/edit", element: <EditLoadPage /> },
    ],
  },
];
```

### Protected Routes

```tsx
// src/routes/console.tsx
export const consoleRoutes = [
  {
    element: <RequireRole role="admin" />,
    children: [
      {
        path: "console",
        element: <ConsoleLayout />,
        children: [
          {
            path: "organizations",
            children: [
              { index: true, element: <OrganizationsPage /> },
              { path: ":id", element: <OrganizationDetailPage /> },
            ],
          },
        ],
      },
    ],
  },
];
```

### Lazy Loading

```tsx
// src/routes/app.tsx
const Settings = lazy(() => import("@/pages/app/settings"));

export const appRoutes = [
  {
    path: "settings",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Settings />
      </Suspense>
    ),
  },
];
```

## Error Handling

Routes should implement proper error boundaries:

```tsx
// src/routes/index.tsx
export const router = createBrowserRouter([
  {
    path: "/",
    element: <RootLayout />,
    errorElement: <ErrorPage />,
    children: [
      {
        path: "app/*",
        element: <AppErrorBoundary />,
        children: appRoutes,
      },
    ],
  },
]);
```

## Route Types

Use TypeScript for route parameters:

```tsx
// src/routes/types.ts
export interface LoadRouteParams {
  id: string;
}

export interface ShipmentRouteParams {
  loadId: string;
  shipmentId: string;
}

// Usage
function LoadDetailPage() {
  const { id } = useParams<LoadRouteParams>();
  // ...
}
```
