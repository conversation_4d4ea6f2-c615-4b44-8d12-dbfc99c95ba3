import { Outlet, RouteObject } from "react-router";

import { PublicLayout } from "@/components/layouts/public";
import {
  DemoDashboard,
  DocumentCreate,
  DocumentDetails,
  Documents,
  DocumentScan,
  LogisticsPage,
  ShipmentDetails,
  VerificationDetails,
  VerificationPage,
  VerificationsList,
} from "@/pages/public/demo";
import { DocumentProvider } from "@/pages/public/demo/documents/document-context";
import { DashboardLayout } from "@/pages/public/demo/layout";
import Developers from "@/pages/public/developers";
import Drivers from "@/pages/public/drivers/index";
import Home from "@/pages/public/home";
import Compliance from "@/pages/public/legal/Compliance";
import Legal from "@/pages/public/legal/Legal";
import Privacy from "@/pages/public/legal/Privacy";
import Terms from "@/pages/public/legal/Terms";
import Roadmap from "@/pages/public/roadmap";
import Logistics from "@/pages/public/shippers";

export const publicRoutes = (): RouteObject[] => [
  {
    element: (
      <PublicLayout>
        <Outlet />
      </PublicLayout>
    ),
    children: [
      { index: true, element: <Home /> },
      {
        path: "shippers",
        children: [{ index: true, element: <Logistics /> }],
      },
      {
        path: "drivers",
        children: [{ index: true, element: <Drivers /> }],
      },
      {
        path: "developers",
        children: [{ index: true, element: <Developers /> }],
      },
      {
        path: "roadmap",
        children: [{ index: true, element: <Roadmap /> }],
      },
      {
        path: "legal",
        children: [
          { index: true, element: <Legal /> },
          { path: "terms-of-service", element: <Terms /> },
          { path: "privacy-policy", element: <Privacy /> },
          { path: "compliance", element: <Compliance /> },
        ],
      },
      // demo pages
      {
        path: "demo",
        element: (
          <DashboardLayout defaultTab="dashboard">
            <Outlet />
          </DashboardLayout>
        ),
        children: [
          { index: true, element: <DemoDashboard /> },
          {
            path: "shipments",
            children: [{ path: ":id", element: <ShipmentDetails /> }],
          },
          {
            path: "logistics",
            children: [{ index: true, element: <LogisticsPage /> }],
          },
          {
            path: "verifications",
            children: [
              { index: true, element: <VerificationsList /> },
              { path: ":id", element: <VerificationDetails /> },
              { path: ":id/verify", element: <VerificationPage /> },
            ],
          },
          {
            path: "documents",
            element: (
              <DocumentProvider>
                <Outlet />
              </DocumentProvider>
            ),
            children: [
              { index: true, element: <Documents /> },
              { path: ":id", element: <DocumentDetails /> },
              { path: "create", element: <DocumentCreate /> },
              { path: "scan", element: <DocumentScan /> },
            ],
          },
        ],
      },
    ],
  },
];
