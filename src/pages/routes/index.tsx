import { useMemo } from "react";
import { BrowserRouter, useRoutes } from "react-router";

import { useUser } from "@/contexts/User";
import { appRoutes } from "./app";
import { authRoutes } from "./auth";
import { consoleRoutes } from "./console";
import { driverRoutes } from "./driver";
import { publicRoutes } from "./public";

export function Routes() {
  const { session, isLoading } = useUser();

  return useRoutes(
    useMemo(
      () => [
        ...publicRoutes(),
        ...authRoutes(session, isLoading),
        ...appRoutes(session, isLoading),
        ...consoleRoutes(session, isLoading),
        ...driverRoutes(session, isLoading),
      ],
      [session, isLoading],
    ),
  );
}

export function Router({ children }: { children: React.ReactNode }) {
  return <BrowserRouter>{children}</BrowserRouter>;
}
