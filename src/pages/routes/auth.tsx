import { Session } from "@supabase/supabase-js";
import { Navigate, Outlet, RouteObject } from "react-router";

import {
  ForgotPassword,
  Invitation,
  SignIn,
  SignUp,
  SignUpSuccess,
  UpdatePassword,
} from "@/pages/auth";

export const authRoutes = (
  session: Session | null,
  isLoading: boolean,
): RouteObject[] => [
  {
    path: "/auth",
    element: session ? <Navigate to="/app" replace /> : <Outlet />,
    children: [
      {
        index: true,
        element: <Navigate to="/auth/sign-in" replace />,
      },
      {
        path: "sign-in",
        element: <SignIn />,
      },
      {
        path: "sign-up",
        element: <SignUp />,
      },
      {
        path: "forgot-password",
        element: <ForgotPassword />,
      },
      {
        path: "sign-up-success",
        element: <SignUpSuccess />,
      },
      {
        path: "update-password",
        element: <UpdatePassword />,
      },
      {
        path: "invitation/:id",
        element: <Invitation />,
      },
    ],
  },
];
