import { Session } from "@supabase/supabase-js";
import {
  AlertTriangle,
  Banknote,
  Boxes,
  BriefcaseBusiness,
  Building,
  Castle,
  CircleUserRound,
  FileText,
  Landmark,
  LayoutDashboard,
  MapPin,
  Notebook,
  Route,
  ScrollText,
  Settings,
  ShieldCheck,
  Truck,
  Users,
  Waypoints,
  Workflow,
} from "lucide-react";
import { Outlet, RouteObject } from "react-router";

import { ConsoleLayout } from "@/components/layouts/console";
import { OrganizationProvider } from "@/contexts/Organization";
import BillingPage from "@/pages/app/console/billing/Billing";
import PaymentsPage from "@/pages/app/console/billing/payments/Payments";
import PayrollPage from "@/pages/app/console/billing/payroll/Payroll";
import CompliancePage from "@/pages/app/console/compliance/Compliance";
import ContractDetails from "@/pages/app/console/compliance/contracts/ContractDetails";
import ContractsPage from "@/pages/app/console/compliance/contracts/Contracts";
import CreateContractPage from "@/pages/app/console/compliance/contracts/CreateContract";
import EditContractPage from "@/pages/app/console/compliance/contracts/EditContract";
import CreateDocumentPage from "@/pages/app/console/compliance/documents/Create";
import DocumentDetails from "@/pages/app/console/compliance/documents/DocumentDetails";
import DocumentsPage from "@/pages/app/console/compliance/documents/Documents";
import EditDocumentPage from "@/pages/app/console/compliance/documents/Edit";
import CreateVerificationPage from "@/pages/app/console/compliance/verifications/CreateVerification";
import EditVerificationPage from "@/pages/app/console/compliance/verifications/EditVerification";
import VerificationDetails from "@/pages/app/console/compliance/verifications/VerificationDetails";
import VerificationsPage from "@/pages/app/console/compliance/verifications/Verifications";
import ConsoleDashboard from "@/pages/app/console/dashboard/Dashboard";
import ContactsPage from "@/pages/app/console/logistics/contacts/Contacts";
import CreateContactPage from "@/pages/app/console/logistics/contacts/CreateContact";
import EditContactPage from "@/pages/app/console/logistics/contacts/EditContact";
import CreateDriverPage from "@/pages/app/console/logistics/drivers/CreateDriver";
import DriverDetails from "@/pages/app/console/logistics/drivers/DriverDetails";
import DriversConsolePage from "@/pages/app/console/logistics/drivers/Drivers";
import EditDriverPage from "@/pages/app/console/logistics/drivers/EditDriver";
import CreateIncidentPage from "@/pages/app/console/logistics/incidents/CreateIncident";
import EditIncidentPage from "@/pages/app/console/logistics/incidents/EditIncident";
import IncidentDetails from "@/pages/app/console/logistics/incidents/IncidentDetails";
import IncidentsPage from "@/pages/app/console/logistics/incidents/Incidents";
import CreateLoadPage from "@/pages/app/console/logistics/loads/CreateLoad";
import EditLoadPage from "@/pages/app/console/logistics/loads/EditLoad";
import LoadDetails from "@/pages/app/console/logistics/loads/LoadDetails";
import LoadPage from "@/pages/app/console/logistics/loads/Loads";
import CreateLocationPage from "@/pages/app/console/logistics/locations/CreateLocation";
import EditLocationPage from "@/pages/app/console/logistics/locations/EditLocation";
import LocationDetails from "@/pages/app/console/logistics/locations/LocationDetails";
import Locations from "@/pages/app/console/logistics/locations/Locations";
import LogisticsPage from "@/pages/app/console/logistics/Logistics";
import CreateShipmentPage from "@/pages/app/console/logistics/shipments/CreateShipment";
import EditShipmentPage from "@/pages/app/console/logistics/shipments/EditShipment";
import ShipmentDetails from "@/pages/app/console/logistics/shipments/ShipmentDetails";
import ShipmentsPage from "@/pages/app/console/logistics/shipments/Shipments";
import CreateVehiclePage from "@/pages/app/console/logistics/vehicles/CreateVehicle";
import EditVehiclePage from "@/pages/app/console/logistics/vehicles/EditVehicle";
import VehicleDetails from "@/pages/app/console/logistics/vehicles/VehicleDetails";
import VehiclesPage from "@/pages/app/console/logistics/vehicles/Vehicles";
import AccountsPage from "@/pages/app/console/organizations/Accounts";
import IntegrationsPage from "@/pages/app/console/organizations/Integrations";
import OrganizationPage from "@/pages/app/console/organizations/Organization";
import SettingsPage from "@/pages/app/console/organizations/Settings";
import SubscriptionPage from "@/pages/app/console/organizations/Subscription";
import TeamPage from "@/pages/app/console/organizations/Team";
import { ProtectedRoute } from "@/pages/routes/components";

const links = [
  {
    title: "Dashboard",
    url: "/app/console",
    icon: LayoutDashboard,
  },
  {
    title: "Logistics",
    url: "/app/console/logistics",
    icon: Route,
    items: [
      {
        title: "Shipments",
        url: "/app/console/shipments",
        icon: Waypoints,
      },
      {
        title: "Loads",
        url: "/app/console/loads",
        icon: Boxes,
      },
      {
        title: "Incidents",
        url: "/app/console/incidents",
        icon: AlertTriangle,
      },
      {
        title: "Drivers",
        url: "/app/console/drivers",
        icon: CircleUserRound,
      },
      {
        title: "Vehicles",
        url: "/app/console/vehicles",
        icon: Truck,
      },
      {
        title: "Locations",
        url: "/app/console/locations",
        icon: MapPin,
      },
      {
        title: "Contacts",
        url: "/app/console/contacts",
        icon: Users,
      },
    ],
  },
  {
    title: "Compliance",
    url: "/app/console/compliance",
    icon: Castle,
    items: [
      {
        title: "Verifications",
        url: "/app/console/verifications",
        icon: ShieldCheck,
      },
      {
        title: "Contracts",
        url: "/app/console/contracts",
        icon: ScrollText,
      },
      {
        title: "Documents",
        url: "/app/console/documents",
        icon: FileText,
      },
    ],
  },
  {
    title: "Billing",
    url: "/app/console/billing",
    icon: Landmark,
    items: [
      {
        title: "Payroll",
        url: "/app/console/billing/payroll",
        icon: Notebook,
      },
      {
        title: "Payments",
        url: "/app/console/billing/payments",
        icon: Banknote,
      },
    ],
  },
  {
    title: "Organization",
    url: "/app/console/organization",
    icon: BriefcaseBusiness,
    items: [
      {
        title: "Team",
        url: "/app/console/organization/team",
        icon: Users,
      },
      {
        title: "Accounts",
        url: "/app/console/organization/accounts",
        icon: Building,
      },
      {
        title: "Integrations",
        url: "/app/console/organization/integrations",
        icon: Workflow,
      },
      {
        title: "Settings",
        url: "/app/console/organization/settings",
        icon: Settings,
      },
    ],
  },
];

export const consoleRoutes = (
  session: Session | null,
  isLoading: boolean,
): RouteObject[] => [
  {
    path: "/app/console",
    element: <ProtectedRoute isLoading={isLoading} session={session} />,
    children: [
      {
        element: (
          <OrganizationProvider>
            <ConsoleLayout loading={isLoading} links={links}>
              <Outlet />
            </ConsoleLayout>
          </OrganizationProvider>
        ),
        children: [
          { index: true, element: <ConsoleDashboard /> },
          // Logistics
          { path: "logistics", element: <LogisticsPage /> },
          { path: "contacts", element: <ContactsPage /> },
          { path: "contacts/create", element: <CreateContactPage /> },
          { path: "contacts/:id/edit", element: <EditContactPage /> },
          { path: "locations", element: <Locations /> },
          { path: "locations/create", element: <CreateLocationPage /> },
          { path: "locations/:id", element: <LocationDetails /> },
          { path: "locations/:id/edit", element: <EditLocationPage /> },
          { path: "loads", element: <LoadPage /> },
          { path: "loads/create", element: <CreateLoadPage /> },
          { path: "loads/:id", element: <LoadDetails /> },
          { path: "loads/:id/edit", element: <EditLoadPage /> },
          { path: "shipments", element: <ShipmentsPage /> },
          { path: "shipments/create", element: <CreateShipmentPage /> },
          { path: "shipments/:id", element: <ShipmentDetails /> },
          { path: "shipments/:id/edit", element: <EditShipmentPage /> },
          { path: "vehicles", element: <VehiclesPage /> },
          { path: "vehicles/create", element: <CreateVehiclePage /> },
          { path: "vehicles/:id", element: <VehicleDetails /> },
          { path: "vehicles/:id/edit", element: <EditVehiclePage /> },
          { path: "incidents", element: <IncidentsPage /> },
          { path: "incidents/create", element: <CreateIncidentPage /> },
          { path: "incidents/:id", element: <IncidentDetails /> },
          { path: "incidents/:id/edit", element: <EditIncidentPage /> },
          { path: "drivers", element: <DriversConsolePage /> },
          { path: "drivers/create", element: <CreateDriverPage /> },
          { path: "drivers/:id", element: <DriverDetails /> },
          { path: "drivers/:id/edit", element: <EditDriverPage /> },
          // Compliance
          { path: "compliance", element: <CompliancePage /> },
          { path: "verifications", element: <VerificationsPage /> },
          { path: "verifications/create", element: <CreateVerificationPage /> },
          { path: "verifications/:id", element: <VerificationDetails /> },
          { path: "verifications/:id/edit", element: <EditVerificationPage /> },
          { path: "documents", element: <DocumentsPage /> },
          { path: "documents/create", element: <CreateDocumentPage /> },
          { path: "documents/:id", element: <DocumentDetails /> },
          { path: "documents/:id/edit", element: <EditDocumentPage /> },
          { path: "contracts", element: <ContractsPage /> },
          { path: "contracts/create", element: <CreateContractPage /> },
          { path: "contracts/:id", element: <ContractDetails /> },
          { path: "contracts/:id/edit", element: <EditContractPage /> },
          // Billing
          { path: "billing", element: <BillingPage /> },
          { path: "billing/payroll", element: <PayrollPage /> },
          { path: "billing/payments", element: <PaymentsPage /> },
          // Organization
          { path: "organization", element: <OrganizationPage /> },
          { path: "organization/accounts", element: <AccountsPage /> },
          { path: "organization/integrations", element: <IntegrationsPage /> },
          { path: "organization/subscription", element: <SubscriptionPage /> },
          { path: "organization/settings", element: <SettingsPage /> },
          { path: "organization/team", element: <TeamPage /> },
        ],
      },
    ],
  },
];
