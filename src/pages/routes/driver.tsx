import { Session } from "@supabase/supabase-js";
import { Outlet, RouteObject } from "react-router";

import { AppLayout } from "@/components/layouts/app";
import { DriverShipmentProvider } from "@/contexts/DriverShipmentContext";
import DriversDashboard from "@/pages/app/drivers/dashboard/Dashboard";
import DriverLoadBoard from "@/pages/app/drivers/dispatcher/LoadBoard";
import DriverDocumentCreate from "@/pages/app/drivers/documents/DocumentCreate";
import DriverDocumentDetails from "@/pages/app/drivers/documents/DocumentDetails";
import DriverDocuments from "@/pages/app/drivers/documents/Documents";
import DriverDocumentScan from "@/pages/app/drivers/documents/DocumentScan";
import DriverIncident from "@/pages/app/drivers/incidents/DriverIncident";
import DriverIncidentCreate from "@/pages/app/drivers/incidents/DriverIncidentCreate";
import DriverIncidents from "@/pages/app/drivers/incidents/DriverIncidents";
import DriverPayments from "@/pages/app/drivers/payments/Payments";
import DriverSetupPayments from "@/pages/app/drivers/payments/SetupPayments";
import DriverTransactions from "@/pages/app/drivers/payments/Transactions";
import DriverAnalytics from "@/pages/app/drivers/profile/Analytics";
import DriverProfile from "@/pages/app/drivers/profile/Profile";
import Qualifications from "@/pages/app/drivers/profile/Qualifications";
import DriverVerification from "@/pages/app/drivers/profile/Verification";
import DriverShipmentEngagements from "@/pages/app/drivers/shipments/engagements/ShipmentEngagements";
import DriverShipmentSearch from "@/pages/app/drivers/shipments/Search";
import DriverShipmentDetails from "@/pages/app/drivers/shipments/ShipmentDetails";
import DriverShipments from "@/pages/app/drivers/shipments/Shipments";
import ShipmentVerification from "@/pages/app/drivers/shipments/verification/ShipmentVerification";
// import DriverAiDispatcher from "@/pages/public/drivers/AiDispatcher";
import { ProtectedRoute } from "@/pages/routes/components";

export const driverRoutes = (
  session: Session | null,
  isLoading: boolean,
): RouteObject[] => [
  {
    path: "/app/drivers",
    element: <ProtectedRoute isLoading={isLoading} session={session} />,
    children: [
      {
        element: (
          <AppLayout loading={isLoading}>
            <DriverShipmentProvider>
              <Outlet />
            </DriverShipmentProvider>
          </AppLayout>
        ),
        children: [
          { index: true, element: <DriversDashboard /> },
          // { path: "dispatcher", element: <DriverAiDispatcher /> },
          { path: "dispatcher/load-board", element: <DriverLoadBoard /> },
          { path: "profile", element: <DriverProfile /> },
          { path: "profile/verification", element: <DriverVerification /> },
          { path: "profile/qualifications", element: <Qualifications /> },
          { path: "profile/analytics", element: <DriverAnalytics /> },
          { path: "shipments", element: <DriverShipments /> },
          { path: "shipments/search", element: <DriverShipmentSearch /> },
          {
            path: "shipments/engagements",
            element: <DriverShipmentEngagements />,
          },
          { path: "shipments/:id", element: <DriverShipmentDetails /> },
          { path: "shipments/:id/verify", element: <ShipmentVerification /> },
          {
            path: "shipments/:id/engagements",
            element: <DriverShipmentEngagements />,
          },
          { path: "incidents", element: <DriverIncidents /> },
          { path: "incidents/create", element: <DriverIncidentCreate /> },
          { path: "incidents/:id", element: <DriverIncident /> },
          { path: "documents", element: <DriverDocuments /> },
          { path: "documents/scan", element: <DriverDocumentScan /> },
          { path: "documents/create", element: <DriverDocumentCreate /> },
          { path: "documents/:id", element: <DriverDocumentDetails /> },
          { path: "payments", element: <DriverPayments /> },
          { path: "payments/setup", element: <DriverSetupPayments /> },
          { path: "payments/transactions", element: <DriverTransactions /> },
        ],
      },
    ],
  },
];
