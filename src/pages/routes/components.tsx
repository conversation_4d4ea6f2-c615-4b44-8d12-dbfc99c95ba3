import { Session } from "@supabase/supabase-js";
import { Loader2 } from "lucide-react";
import { Navigate, Outlet } from "react-router";

import { useUser } from "@/contexts/User";

export function Placeholder() {
  return (
    <div className="flex h-full items-center justify-center">
      <span className="text-muted-foreground">Placeholder</span>
    </div>
  );
}

export function RouteLoader() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

export function AppRedirect() {
  const {
    driver,
    memberships,
    isLoading,
    isDriverLoading,
    isMembershipsLoading,
    user,
  } = useUser();

  // Wait for initial session/user loading
  if (isLoading) {
    return <RouteLoader />;
  }

  // If no user, something went wrong - redirect to auth
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // If we have driver data already, redirect to driver dashboard
  if (driver) {
    return <Navigate to="/app/drivers" replace />;
  }

  // If we have membership data already, redirect to console
  if (memberships.length > 0) {
    return <Navigate to="/app/console" replace />;
  }

  // If still loading driver or membership data, wait a bit longer
  // This prevents premature redirect to onboarding
  if (isDriverLoading || isMembershipsLoading) {
    return <RouteLoader />;
  }

  // If all data is loaded and user has no driver profile or memberships,
  // redirect to onboarding
  return <Navigate to="/app/onboarding" replace />;
}

export function ProtectedRoute({
  isLoading,
  session,
  children = <Navigate to="/auth" replace />,
}: {
  isLoading: boolean;
  session: Session | null;
  children?: React.ReactNode;
}) {
  if (isLoading) {
    return <RouteLoader />;
  }

  return session ? <Outlet /> : children;
}
