import { Session } from "@supabase/supabase-js";
import { Outlet, RouteObject } from "react-router";

import { AppLayout } from "@/components/layouts/app";
import AccountPage from "@/pages/app/account/Account";
import NotificationsPage from "@/pages/app/notifications/Notifications";
import DriverOnboarding from "@/pages/app/onboarding/driver/DriverOnboardingPage";
import OnboardingPage from "@/pages/app/onboarding/Onboarding";
import OrganizationOnboarding from "@/pages/app/onboarding/organization/OrganizationOnboardingPage";
import { AppRedirect, ProtectedRoute } from "@/pages/routes/components";

export const appRoutes = (
  session: Session | null,
  isLoading: boolean,
): RouteObject[] => [
  {
    path: "/app",
    element: <ProtectedRoute isLoading={isLoading} session={session} />,
    children: [
      {
        element: (
          <AppLayout loading={isLoading}>
            <Outlet />
          </AppLayout>
        ),
        children: [
          { index: true, element: <AppRedirect /> },
          { path: "notifications", element: <NotificationsPage /> },
          { path: "account", element: <AccountPage /> },
          { path: "onboarding", element: <OnboardingPage /> },
          { path: "onboarding/driver", element: <DriverOnboarding /> },
          {
            path: "onboarding/organization",
            element: <OrganizationOnboarding />,
          },
        ],
      },
    ],
  },
];
