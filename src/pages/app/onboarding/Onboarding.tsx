import {
  Building2,
  ChartBar,
  Clock,
  Globe,
  Shield,
  Star,
  Truck,
  Wallet,
} from "lucide-react";
import { <PERSON> } from "react-router";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const i18n = {
  en: {
    welcome: "Welcome to QuikSkope",
    choosePath: "Choose your path to get started",
    driver: "I'm a Driver",
    organization: "I'm an Organization",
    driverDescription: "Create a driver profile to start managing deliveries",
    organizationDescription:
      "Set up your organization to manage shipments and drivers",
    driverFeatures: [
      {
        icon: Wallet,
        title: "Competitive Earnings",
        description: "Access competitive rates and quick payments",
      },
      {
        icon: Clock,
        title: "Flexible Schedule",
        description: "Choose your own hours and routes",
      },
      {
        icon: Shield,
        title: "Insurance Coverage",
        description: "Comprehensive coverage for peace of mind",
      },
    ],
    organizationFeatures: [
      {
        icon: Globe,
        title: "Global Network",
        description: "Access a worldwide network of qualified drivers",
      },
      {
        icon: ChartBar,
        title: "Real-time Analytics",
        description: "Track performance and optimize operations",
      },
      {
        icon: Star,
        title: "Quality Assurance",
        description: "Verified drivers and reliable service",
      },
    ],
    links: {
      driver: "/app/onboarding/driver",
      organization: "/app/onboarding/organization",
    },
  },
} as const;

export default function Onboarding() {
  return (
    <div className="container mx-auto max-w-6xl py-12">
      <h1 className="mb-4 text-center text-4xl font-bold">{i18n.en.welcome}</h1>
      <p className="text-muted-foreground mb-12 text-center text-xl">
        {i18n.en.choosePath}
      </p>

      <div className="grid gap-8 md:grid-cols-2">
        {/* Driver Track */}
        <Card className="group relative overflow-hidden transition-all hover:shadow-lg">
          <Button
            asChild
            variant="ghost"
            className="hover:bg-secondary/5 hover:text-primary h-auto w-full p-0 hover:no-underline"
          >
            <Link to={i18n.en.links.driver}>
              <CardContent className="space-y-6 p-6">
                <div className="flex flex-col items-center space-y-4">
                  <div className="bg-primary/10 rounded-full p-4 transition-all group-hover:scale-110">
                    <Truck className="text-primary h-12 w-12" />
                  </div>
                  <div className="text-center">
                    <h2 className="text-2xl font-semibold">{i18n.en.driver}</h2>
                    <p className="text-muted-foreground">
                      {i18n.en.driverDescription}
                    </p>
                  </div>
                </div>

                <div className="grid gap-4">
                  {i18n.en.driverFeatures.map((feature, index) => (
                    <div
                      key={index}
                      className="hover:bg-primary/5 flex items-start gap-4 rounded-lg border p-4 transition-all"
                    >
                      <feature.icon className="text-primary h-6 w-6 shrink-0" />
                      <div>
                        <h3 className="font-medium">{feature.title}</h3>
                        <p className="text-muted-foreground text-sm">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                <Button size="lg" className="w-full">
                  Get Started as a Driver
                </Button>
              </CardContent>
            </Link>
          </Button>
        </Card>

        {/* Organization Track */}
        <Card className="group relative overflow-hidden transition-all hover:shadow-lg">
          <Button
            asChild
            variant="ghost"
            className="hover:bg-secondary/5 hover:text-primary h-auto w-full p-0 hover:no-underline"
          >
            <Link to={i18n.en.links.organization}>
              <CardContent className="space-y-6 p-6">
                <div className="flex flex-col items-center space-y-4">
                  <div className="bg-secondary/10 rounded-full p-4 transition-all group-hover:scale-110">
                    <Building2 className="text-secondary h-12 w-12" />
                  </div>
                  <div className="text-center">
                    <h2 className="text-2xl font-semibold">
                      {i18n.en.organization}
                    </h2>
                    <p className="text-muted-foreground">
                      {i18n.en.organizationDescription}
                    </p>
                  </div>
                </div>

                <div className="grid gap-4">
                  {i18n.en.organizationFeatures.map((feature, index) => (
                    <div
                      key={index}
                      className="hover:bg-secondary/10 flex items-start gap-4 rounded-lg border p-4 transition-all"
                    >
                      <feature.icon className="text-secondary h-6 w-6 shrink-0" />
                      <div>
                        <h3 className="font-medium">{feature.title}</h3>
                        <p className="text-muted-foreground text-sm">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                <Button size="lg" variant="secondary" className="w-full">
                  Get Started as an Organization
                </Button>
              </CardContent>
            </Link>
          </Button>
        </Card>
      </div>
    </div>
  );
}
