import { useState } from "react";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router";

import { CreateOrgButton } from "@/components/dev-tools";
import {
  OrganizationOnboardingMultiStep,
  OrganizationOnboardingValues,
} from "@/components/forms/onboarding/organization/OrganizationOnboardingMultiStep";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useUser } from "@/contexts/User";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/supabase/client";

const OrganizationOnboarding = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (values: OrganizationOnboardingValues) => {
    try {
      setIsLoading(true);
      setError(null);

      const userId = (await supabase.auth.getUser()).data.user?.id;

      if (!userId) {
        throw new Error("No authenticated user found");
      }

      // Create organization
      const { data: organization, error: orgError } = await supabase
        .from("organizations")
        .insert([
          {
            name: values.name,
            industry: values.industry,
            size: values.size,
            status: "pending",
            type: values.type as
              | "government"
              | "individual"
              | "private"
              | "non_profit",
            location_id: null,
            avatar: null,
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (orgError) throw orgError;

      await supabase.from("members").insert({
        organization_id: organization.id,
        user_id: userId,
        email: user.email,
        role: "owner",
      });

      // If avatar was uploaded, store it in storage
      if (values.avatar) {
        const fileExt = values.avatar.name.split(".").pop();
        const fileName = `${organization.id}-${Date.now()}.${fileExt}`;
        const filePath = `organization-logos/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from("organization-assets")
          .upload(filePath, values.avatar);

        if (uploadError) {
          console.error("Error uploading logo:", uploadError);
          toast({
            title: "Organization created but failed to upload logo",
            variant: "destructive",
          });
        } else {
          // Update organization with logo URL
          const { error: updateError } = await supabase
            .from("organizations")
            .update({
              avatar: filePath,
            })
            .eq("id", organization.id);

          if (updateError) {
            console.error(
              "Error updating organization with logo:",
              updateError,
            );
          }
        }
      }

      // Only insert team members if there are any
      if (values.members.length > 0) {
        const { error: membersError } = await supabase.from("members").insert(
          values.members.map((member) => ({
            organization_id: organization.id,
            email: member.email,
            role: member.role,
          })),
        );

        if (membersError) throw membersError;
      }

      toast({
        title: "Organization created successfully!",
        description:
          values.members.length > 0
            ? "Your team members will receive email invitations shortly."
            : "You can invite team members later from the organization settings.",
      });
      navigate("/app/console");
    } catch (err) {
      console.error("Error creating organization:", err);
      setError(
        err instanceof Error ? err.message : "Failed to create organization",
      );
      toast({
        title: "Error creating organization",
        description: "Please try again later.",
        variant: "destructive",
      });
      throw err; // Re-throw to be caught by the form's error handling
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <main className="container overflow-visible py-16">
      <div className="mx-auto max-w-xl">
        <h1 className="mb-4 text-center text-4xl font-bold">
          Organization Setup
        </h1>
        <p className="text-muted-foreground mb-8 text-center">
          Set up your organization profile to start managing shipments and
          drivers
        </p>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <OrganizationOnboardingMultiStep onSubmit={handleSubmit} />

        {/* Dev Tools Button - Only visible in development */}
        <CreateOrgButton />
      </div>
    </main>
  );
};

export default OrganizationOnboarding;
