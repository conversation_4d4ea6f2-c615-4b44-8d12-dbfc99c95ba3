import { Loader2 } from "lucide-react";

import { CreateOrgButton } from "@/components/dev-tools";
import {
  OrganizationOnboardingMultiStep,
  OrganizationOnboardingValues,
} from "@/components/forms/onboarding/organization/OrganizationOnboardingMultiStep";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { UserContextType } from "@/contexts/User";

interface OrganizationOnboardingViewProps {
  user: UserContextType["user"];
  isLoading: boolean;
  error: string | null;
  handleSubmit: (values: OrganizationOnboardingValues) => Promise<void>;
}

export function OrganizationOnboardingView({
  user,
  isLoading,
  error,
  handleSubmit,
}: OrganizationOnboardingViewProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <main className="container overflow-visible py-16">
      <div className="mx-auto max-w-xl">
        <h1 className="mb-4 text-center text-4xl font-bold">
          Organization Setup
        </h1>
        <p className="text-muted-foreground mb-8 text-center">
          Set up your organization profile to start managing shipments and
          drivers
        </p>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <OrganizationOnboardingMultiStep onSubmit={handleSubmit} />

        {/* Dev Tools Button - Only visible in development */}
        <CreateOrgButton />
      </div>
    </main>
  );
}
