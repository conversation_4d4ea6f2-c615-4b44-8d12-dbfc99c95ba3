import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  with<PERSON><PERSON><PERSON>,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { OrganizationOnboardingView } from "./OrganizationOnboardingView";

const meta: Meta<typeof OrganizationOnboardingView> = {
  title: "Pages/Onboarding/OrganizationOnboardingView",
  component: OrganizationOnboardingView,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/app/onboarding/organization" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    user: {
      id: "123",
      aud: "123",
      created_at: new Date().toISOString(),
      app_metadata: {
        provider: "email",
      },
      user_metadata: {},
    },
    isLoading: false,
    error: null,
    handleSubmit: fn(),
  },
};

export const Loading: Story = {
  args: {
    user: {
      id: "123",
      aud: "123",
      created_at: new Date().toISOString(),
      app_metadata: {
        provider: "email",
      },
      user_metadata: {},
    },
    isLoading: true,
    error: null,
    handleSubmit: fn(),
  },
};

export const WithError: Story = {
  args: {
    user: {
      id: "123",
      aud: "123",
      created_at: new Date().toISOString(),
      app_metadata: {
        provider: "email",
      },
      user_metadata: {},
    },
    isLoading: false,
    error: "Failed to create organization. Please try again.",
    handleSubmit: fn(),
  },
};
