import { useState } from "react";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router";
import { toast } from "sonner";

import {
  DriverOnboardingMultiStep,
  DriverOnboardingValues,
} from "@/components/forms/onboarding/DriverOnboardingMultiStep";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useUser } from "@/contexts/User";
import { supabase } from "@/supabase/client";

const DriverOnboarding = () => {
  const navigate = useNavigate();
  const { user, driver, isLoading } = useUser();
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (values: DriverOnboardingValues) => {
    try {
      setError(null);

      if (!user) {
        throw new Error("No authenticated user found");
      }

      // Check if user already has a driver profile
      if (driver) {
        throw new Error("You already have a driver profile");
      }

      // Create new driver profile with basic info
      const driverData = {
        first_name: values.firstName,
        last_name: values.lastName,
        email: values.email,
        phone_number: values.phoneNumber,
        user_id: user.id,
        address: values.address,
        address_details: values.addressDetails,
      };

      // Add address if provided
      if (values.address) {
        driverData.address = values.address;
      }

      const { error: insertError, data: newDriver } = await supabase
        .from("drivers")
        .insert([driverData])
        .select()
        .single();

      if (insertError) throw insertError;

      // If avatar was uploaded, store it in storage
      if (values.avatar) {
        const fileExt = values.avatar.name.split(".").pop();
        const fileName = `${newDriver.id}-${Date.now()}.${fileExt}`;
        const filePath = `avatars/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from("driver-avatars")
          .upload(filePath, values.avatar);

        if (uploadError) {
          console.error("Error uploading avatar:", uploadError);
          toast.error("Profile created but failed to upload avatar");
        } else {
          // Update driver with avatar URL
          const { error: updateError } = await supabase
            .from("drivers")
            .update({
              // Use a generic object to avoid TypeScript errors with unknown columns
              avatar: filePath,
            })
            .eq("user_id", user.id);

          if (updateError) {
            console.error("Error updating driver with avatar:", updateError);
          }
        }
      }

      toast.success("Driver profile created successfully!");
      navigate("/app/drivers");
    } catch (err) {
      console.error("Error creating driver profile:", err);
      setError(
        err instanceof Error ? err.message : "Failed to create driver profile",
      );
      toast.error("Error creating driver profile");
      throw err; // Re-throw to be caught by the form's error handling
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-xl">
      <h1 className="mb-4 text-center text-4xl font-bold">
        Create Driver Profile
      </h1>
      <p className="text-muted-foreground mb-8 text-center">
        Fill out your information to get started with deliveries
      </p>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <DriverOnboardingMultiStep
        onSubmit={handleSubmit}
        defaultValues={{
          firstName: driver?.first_name ?? "",
          lastName: driver?.last_name ?? "",
          email: driver?.email ?? user?.email ?? "",
          phoneNumber: driver?.phone_number ?? "",
          address: "",
        }}
      />
    </div>
  );
};

export default DriverOnboarding;
