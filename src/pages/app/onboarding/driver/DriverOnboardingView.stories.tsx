import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  with<PERSON>outer,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { DriverOnboardingView } from "./DriverOnboardingView";

const meta: Meta<typeof DriverOnboardingView> = {
  title: "Pages/Onboarding/DriverOnboardingView",
  component: DriverOnboardingView,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/app/onboarding/driver" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    user: {
      id: "123",
      aud: "123",
      created_at: new Date().toISOString(),
      app_metadata: {
        provider: "email",
      },
      user_metadata: {},
    },
    driver: null,
    isLoading: false,
    error: null,
    handleSubmit: fn(),
    defaultValues: {
      firstName: "<PERSON>",
      lastName: "Doe",
      email: "<EMAIL>",
      phoneNumber: "+****************",
      address: "123 Main St, Anytown, ST 12345",
    },
  },
};

export const Loading: Story = {
  args: {
    user: {
      id: "123",
      aud: "123",
      created_at: new Date().toISOString(),
      app_metadata: {
        provider: "email",
      },
      user_metadata: {},
    },
    driver: null,
    isLoading: true,
    error: null,
    handleSubmit: fn(),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      address: "",
    },
  },
};

export const WithError: Story = {
  args: {
    user: {
      id: "123",
      aud: "123",
      created_at: new Date().toISOString(),
      app_metadata: {
        provider: "email",
      },
      user_metadata: {},
    },
    driver: null,
    isLoading: false,
    error: "Failed to create driver profile. Please try again.",
    handleSubmit: fn(),
    defaultValues: {
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phoneNumber: "+****************",
      address: "123 Main St, Anytown, ST 12345",
    },
  },
};
