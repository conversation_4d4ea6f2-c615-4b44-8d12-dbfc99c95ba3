import { Loader2 } from "lucide-react";

import {
  DriverOnboardingMultiStep,
  DriverOnboardingValues,
} from "@/components/forms/onboarding/DriverOnboardingMultiStep";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { UserContextType } from "@/contexts/User";

interface DriverOnboardingViewProps {
  user: UserContextType["user"];
  driver: UserContextType["driver"];
  isLoading: UserContextType["isLoading"];
  error: string | null;
  handleSubmit: (values: DriverOnboardingValues) => Promise<void>;
  defaultValues: {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    address: string;
  };
}

export function DriverOnboardingView({
  user,
  driver,
  isLoading,
  error,
  handleSubmit,
  defaultValues,
}: DriverOnboardingViewProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-xl">
      <h1 className="mb-4 text-center text-4xl font-bold">
        Create Driver Profile
      </h1>
      <p className="text-muted-foreground mb-8 text-center">
        Fill out your information to get started with deliveries
      </p>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <DriverOnboardingMultiStep
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
      />
    </div>
  );
}
