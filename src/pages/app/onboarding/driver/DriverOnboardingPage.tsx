import { useState } from "react";
import { useNavigate } from "react-router";
import { toast } from "sonner";

import { DriverOnboardingValues } from "@/components/forms/onboarding/DriverOnboardingMultiStep";
import { useUser } from "@/contexts/User";
import { supabase } from "@/supabase/client";
import { DriverOnboardingView } from "./DriverOnboardingView";

export function DriverOnboardingPage() {
  const navigate = useNavigate();
  const { user, driver, isLoading } = useUser();
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (values: DriverOnboardingValues) => {
    try {
      setError(null);

      if (!user) {
        throw new Error("No authenticated user found");
      }

      // Check if user already has a driver profile
      if (driver) {
        throw new Error("You already have a driver profile");
      }

      // Create new driver profile with basic info
      const driverData = {
        first_name: values.firstName,
        last_name: values.lastName,
        email: values.email,
        phone_number: values.phoneNumber,
        user_id: user.id,
        address: values.address,
        address_details: values.addressDetails,
      };

      // Add address if provided
      if (values.address) {
        driverData.address = values.address;
      }

      const { error: insertError, data: newDriver } = await supabase
        .from("drivers")
        .insert([driverData])
        .select()
        .single();

      if (insertError) throw insertError;

      // If avatar was uploaded, store it in storage
      if (values.avatar) {
        const fileExt = values.avatar.name.split(".").pop();
        const fileName = `${newDriver.id}-${Date.now()}.${fileExt}`;
        const filePath = `avatars/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from("driver-avatars")
          .upload(filePath, values.avatar);

        if (uploadError) {
          console.error("Error uploading avatar:", uploadError);
          toast.error("Profile created but failed to upload avatar");
        } else {
          // Update driver with avatar URL
          const { error: updateError } = await supabase
            .from("drivers")
            .update({
              // Use a generic object to avoid TypeScript errors with unknown columns
              avatar: filePath,
            })
            .eq("user_id", user.id);

          if (updateError) {
            console.error("Error updating driver with avatar:", updateError);
          }
        }
      }

      toast.success("Driver profile created successfully!");
      navigate("/app/drivers");
    } catch (err) {
      console.error("Error creating driver profile:", err);
      setError(
        err instanceof Error ? err.message : "Failed to create driver profile",
      );
      toast.error("Error creating driver profile");
      throw err; // Re-throw to be caught by the form's error handling
    }
  };

  const defaultValues = {
    firstName: driver?.first_name ?? "",
    lastName: driver?.last_name ?? "",
    email: driver?.email ?? user?.email ?? "",
    phoneNumber: driver?.phone_number ?? "",
    address: "",
  };

  return (
    <DriverOnboardingView
      user={user}
      driver={driver}
      isLoading={isLoading}
      error={error}
      handleSubmit={handleSubmit}
      defaultValues={defaultValues}
    />
  );
}

export default DriverOnboardingPage;
