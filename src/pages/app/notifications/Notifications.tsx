import { format, isToday, isYesterday } from "date-fns";
import { Bell, Loader2 } from "lucide-react";

import { useListNotifications } from "@/api/user/use-list-notifications";
import { NotificationTypeBadge } from "@/components/common/types/NotificationType";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

type Notifications = Awaited<ReturnType<typeof useListNotifications>>["data"];
type Notification = Notifications["items"][number];

export function NotificationsPage({
  notifications,
  isLoading,
}: {
  notifications?: Notifications["items"];
  isLoading: boolean;
}) {
  const groupNotifications = (notifications: Notification[]) => {
    const groups = {
      today: [] as Notification[],
      yesterday: [] as Notification[],
      older: [] as Notification[],
    };

    notifications?.forEach((notification) => {
      const date = new Date(notification.created_at);
      if (isToday(date)) {
        groups.today.push(notification);
      } else if (isYesterday(date)) {
        groups.yesterday.push(notification);
      } else {
        groups.older.push(notification);
      }
    });

    return groups;
  };

  const groups = notifications ? groupNotifications(notifications) : null;

  const NotificationCard = ({
    notification,
  }: {
    notification: Notification;
  }) => (
    <Card className={cn("mb-4", notification.read_at && "opacity-60")}>
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{notification.title}</CardTitle>
          <NotificationTypeBadge type={notification.type} />
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <p className="text-muted-foreground text-sm">{notification.message}</p>
        <p className="text-muted-foreground mt-2 text-xs">
          {format(new Date(notification.created_at), "MMM d, yyyy 'at' h:mm a")}
        </p>
      </CardContent>
    </Card>
  );

  const NotificationGroup = ({
    title,
    notifications,
  }: {
    title: string;
    notifications: Notification[];
  }) => {
    if (notifications.length === 0) return null;

    return (
      <div className="mb-8">
        <h3 className="mb-4 text-lg font-semibold">{title}</h3>
        {notifications.map((notification) => (
          <NotificationCard key={notification.id} notification={notification} />
        ))}
      </div>
    );
  };

  return (
    <div className="container max-w-4xl py-8">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Notifications</h1>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : !notifications?.length ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Bell className="text-muted-foreground mb-4 h-12 w-12" />
            <p className="mb-2 text-xl font-medium">No notifications yet! 🎉</p>
            <p className="text-muted-foreground">
              We'll notify you when something important happens.
            </p>
          </CardContent>
        </Card>
      ) : (
        groups && (
          <>
            <NotificationGroup title="Today" notifications={groups.today} />
            <NotificationGroup
              title="Yesterday"
              notifications={groups.yesterday}
            />
            <NotificationGroup title="Older" notifications={groups.older} />
          </>
        )
      )}
    </div>
  );
}

export default function Notifications() {
  const { data, isLoading } = useListNotifications({
    pageSize: 100,
  });

  return (
    <NotificationsPage notifications={data?.items} isLoading={isLoading} />
  );
}
