import { useState } from "react";
import { Loader2, Trash2 } from "lucide-react";
import { Link, useNavigate } from "react-router";
import { toast } from "sonner";

import type { useDeleteUser } from "@/api/user/use-delete-user";
import type { UserContextType } from "@/contexts/User";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const i18n = {
  en: {
    dangerZone: "Danger Zone",
    deleteAccount: "Delete Account",
    confirmDeleteAccount: "Are you sure you want to delete your account?",
    thisActionCannotBeUndone: "This action cannot be undone.",
    thisWillPermanentlyDeleteYourAccount:
      "This will permanently delete your account and remove all associated data. For more information about data privacy and retention, please read our",
    privacyPolicy: "privacy policy",
    deleteAccountDescription: "Once you delete your account:",
    deleteAccountBullets: [
      "All your personal information will be permanently erased",
      "Your driver profile and history will be removed",
      "Your organization memberships will be revoked",
      "All payment methods will be deleted",
      "You won't be able to recover any of this information",
    ],
  },
};

export default function DangerZone({
  user,
  deleteUserMutation,
}: {
  user: UserContextType["user"];
  deleteUserMutation: ReturnType<typeof useDeleteUser>;
}) {
  const navigate = useNavigate();
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);

  const handleAccountDeletion = async () => {
    try {
      setIsDeletingAccount(true);
      await deleteUserMutation.mutateAsync(user?.id);
      toast.success("Account deleted successfully");
      navigate("/");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message || "Failed to delete account");
      } else {
        toast.error("An unknown error occurred");
      }
    } finally {
      setIsDeletingAccount(false);
    }
  };

  return (
    <Card className="border-destructive">
      <CardHeader>
        <CardTitle className="text-destructive flex items-center">
          <Trash2 className="mr-2 h-5 w-5" />
          {i18n.en.dangerZone}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-muted-foreground text-sm">
          <p className="mb-2">
            {i18n.en.thisWillPermanentlyDeleteYourAccount}{" "}
            <Link to="/legal/privacy" className="text-primary hover:underline">
              {i18n.en.privacyPolicy}
            </Link>
            .
          </p>
          <p className="mb-2 font-medium">{i18n.en.deleteAccountDescription}</p>
          <ul className="list-disc space-y-1 pl-6">
            {i18n.en.deleteAccountBullets.map((bullet, index) => (
              <li key={index}>{bullet}</li>
            ))}
          </ul>
        </div>
        <DialogConfirmation
          title={i18n.en.confirmDeleteAccount}
          description={i18n.en.thisActionCannotBeUndone}
          onClick={handleAccountDeletion}
          variant="destructive"
        >
          <Button variant="destructive" disabled={isDeletingAccount}>
            {isDeletingAccount && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {i18n.en.deleteAccount}
          </Button>
        </DialogConfirmation>
      </CardContent>
    </Card>
  );
}
