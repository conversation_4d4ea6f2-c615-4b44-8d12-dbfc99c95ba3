import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, FileText, Truck } from "lucide-react";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";

const i18n = {
  en: {
    notifications: "Notifications",
    shipmentUpdates: "Shipment Updates",
    documentUpdates: "Document Updates",
    securityAlerts: "Security Alerts",
    shipmentUpdatesDescription:
      "Receive notifications about your shipment status changes",
    documentUpdatesDescription:
      "Get notified when documents are added or updated",
    securityAlertsDescription: "Important security-related notifications",
    actions: {
      save: "Save",
    },
  },
};

export default function NotificationsSection({
  isLoading,
}: {
  isLoading: boolean;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Bell className="mr-2 h-5 w-5" />
          {i18n.en.notifications}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <div className="flex items-center">
              <Truck className="mr-2 h-4 w-4" />
              <span className="font-medium">{i18n.en.shipmentUpdates}</span>
            </div>
            <p className="text-muted-foreground text-sm">
              {i18n.en.shipmentUpdatesDescription}
            </p>
          </div>
          <Switch disabled={isLoading} defaultChecked />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <div className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              <span className="font-medium">{i18n.en.documentUpdates}</span>
            </div>
            <p className="text-muted-foreground text-sm">
              {i18n.en.documentUpdatesDescription}
            </p>
          </div>
          <Switch disabled={isLoading} defaultChecked />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <div className="flex items-center">
              <AlertTriangle className="mr-2 h-4 w-4" />
              <span className="font-medium">{i18n.en.securityAlerts}</span>
            </div>
            <p className="text-muted-foreground text-sm">
              {i18n.en.securityAlertsDescription}
            </p>
          </div>
          <Switch disabled={isLoading} defaultChecked />
        </div>
      </CardContent>
    </Card>
  );
}
