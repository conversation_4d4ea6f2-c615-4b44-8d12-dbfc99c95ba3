import { Loader2, Pause, Truck, User } from "lucide-react";
import { Link } from "react-router";

import type { UserContextType } from "@/contexts/User";

import { useUpdateDriver } from "@/api/drivers";
import PreviewContact from "@/components/common/PreviewContact";
import { DriverStatusBadge } from "@/components/common/types/DriverStatus";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function DriverSection({
  driver,
  isLoading,
  updateDriver,
}: {
  driver: UserContextType["driver"];
  isLoading: UserContextType["isLoading"];
  updateDriver: ReturnType<typeof useUpdateDriver>;
}) {
  const handlePauseAccount = async () => {
    if (!driver?.id) return;
    await updateDriver.mutate({
      id: driver.id,
      status: driver.status === "active" ? "inactive" : "active",
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Truck className="mr-2 h-5 w-5" />
            Driver Account
          </CardTitle>
          {driver && (
            <DriverStatusBadge loading={isLoading} status={driver.status} />
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : driver ? (
          <div className="flex flex-col gap-6 sm:flex-row sm:items-start sm:justify-between">
            <PreviewContact
              firstName={driver.first_name}
              lastName={driver.last_name}
              email={driver.email}
              phone={driver.phone_number}
            />
            <div className="flex flex-row items-center justify-between gap-4 rounded-lg border p-4 sm:flex-col sm:items-center sm:gap-2">
              <div>
                <div className="text-muted-foreground text-sm font-medium">
                  Driver Score
                </div>
                <div className="text-primary text-3xl font-bold">
                  {driver.score}
                </div>
              </div>
              <Button
                asChild
                variant="secondary"
                size="sm"
                className="shrink-0"
              >
                <Link to="/app/drivers/profile">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </Button>
            </div>
          </div>
        ) : (
          <div className="py-4 text-center">
            <p className="text-muted-foreground mb-4">
              You don't have a driver account yet
            </p>
            <Button asChild>
              <Link to="/app/onboarding/driver">Create Driver Account</Link>
            </Button>
          </div>
        )}
      </CardContent>
      {driver && (
        <CardFooter className="flex flex-col gap-2 sm:flex-row">
          <Button variant="default" asChild className="w-full sm:flex-1">
            <Link to="/app/drivers">Go to Driver Dashboard</Link>
          </Button>
          <Button
            variant="outline"
            onClick={handlePauseAccount}
            disabled={updateDriver.isPending || driver.status === "suspended"}
            className="text-muted-foreground hover:text-destructive hover:border-destructive w-full sm:flex-1"
          >
            <Pause className="mr-2 h-4 w-4" />
            {driver.status === "active"
              ? "Pause Driver Account"
              : "Activate Driver Account"}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
