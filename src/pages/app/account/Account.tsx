import { useUpdateDriver } from "@/api/drivers/use-update-driver";
import { useDeleteUser } from "@/api/user/use-delete-user";
import { useLogout } from "@/api/user/use-logout";
import { useUpdatePassword } from "@/api/user/use-update-password";
import { UserContextType, useUser } from "@/contexts/User";
import { useToast } from "@/hooks/use-toast";
import BillingSection from "@/pages/app/account/BillingSection";
import DangerZone from "@/pages/app/account/DangerZone";
import DriverSection from "@/pages/app/account/DriverSection";
import MembershipsSection from "@/pages/app/account/MembershipsSection";
import NotificationsSection from "@/pages/app/account/NotificationsSection";
import ProfileSection from "@/pages/app/account/ProfileSection";
import SecuritySection from "@/pages/app/account/SecuritySection";

const i18n = {
  en: {
    accountSettings: "Account Settings",
    accountSettingsSubtitle:
      "Manage your profile, security, and account preferences",
  },
};

export function UserAccountPage({
  user,
  driver,
  memberships,
  isLoading,
  updatePassword,
  deleteUser,
  session,
  logout,
  updateDriver,
}: {
  user: UserContextType["user"];
  driver: UserContextType["driver"];
  memberships: UserContextType["memberships"];
  isLoading: UserContextType["isLoading"];
  updatePassword: ReturnType<typeof useUpdatePassword>;
  deleteUser: ReturnType<typeof useDeleteUser>;
  session: UserContextType["session"];
  logout: ReturnType<typeof useLogout>;
  updateDriver: ReturnType<typeof useUpdateDriver>;
}) {
  return (
    <div className="container mx-auto max-w-6xl space-y-6 py-8">
      <div className="mb-8">
        <h1 className="mb-2 text-4xl font-bold">{i18n.en.accountSettings}</h1>
        <p className="text-muted-foreground text-lg">
          {i18n.en.accountSettingsSubtitle}
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-[2fr_1fr]">
        {/* Left Column - Profile and Memberships */}
        <div className="space-y-6">
          <ProfileSection user={user} isLoading={isLoading} />
          <DriverSection
            driver={driver}
            isLoading={isLoading}
            updateDriver={updateDriver}
          />
          <MembershipsSection memberships={memberships} isLoading={isLoading} />
          <BillingSection />
        </div>

        {/* Right Column - Security and Notifications */}
        <div className="space-y-6">
          <SecuritySection
            updatePassword={updatePassword}
            isLoading={isLoading}
            user={user}
            session={session}
            logout={logout}
          />
          <NotificationsSection isLoading={isLoading} />
        </div>
      </div>

      {/* Danger Zone - Always at the bottom */}
      <div className="mt-8">
        <DangerZone user={user} deleteUserMutation={deleteUser} />
      </div>
    </div>
  );
}

export default function UserAccount() {
  const { toast } = useToast();

  const { user, driver, memberships, isLoading, session } = useUser();
  const updatePassword = useUpdatePassword();
  const deleteUser = useDeleteUser();
  const logout = useLogout();
  const updateDriverMutation = useUpdateDriver({
    onSuccess: (data) => {
      const isActive = data.status === "active";
      toast({
        title: isActive ? "Driver Account Activated" : "Driver Account Paused",
        description: isActive
          ? "Your driver account is now active and ready to accept jobs."
          : "Your driver account has been paused. You won't receive any new jobs.",
      });
    },
  });

  return (
    <UserAccountPage
      user={user}
      driver={driver}
      memberships={memberships}
      isLoading={isLoading}
      updatePassword={updatePassword}
      deleteUser={deleteUser}
      session={session}
      logout={logout}
      updateDriver={updateDriverMutation}
    />
  );
}
