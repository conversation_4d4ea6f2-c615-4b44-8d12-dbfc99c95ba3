import { Loader2, <PERSON>ci<PERSON> } from "lucide-react";

import type { ProfileFormValues } from "@/components/forms/ProfileForm";
import type { UserContextType } from "@/contexts/User";

import ProfileForm from "@/components/forms/ProfileForm";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function ProfileSection({
  isLoading,
  user,
}: {
  isLoading: boolean;
  user?: UserContextType["user"];
}) {
  if (isLoading) return <Loader2 className="h-6 w-6 animate-spin" />;

  const handleProfileUpdate = async (values: ProfileFormValues) => {
    // TODO: Implement profile update logic
    console.log(values);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-2xl font-bold">Profile</CardTitle>
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              aria-label="Edit profile"
            >
              <Pencil className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogTrigger asChild>
            <Button variant="outline" className="hidden lg:flex">
              <Pencil className="mr-2 h-4 w-4" />
              Edit profile
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit profile</DialogTitle>
            </DialogHeader>
            <ProfileForm user={user} onSubmit={handleProfileUpdate} />
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium">Email</label>
              <p className="text-muted-foreground">{user?.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Username</label>
              <p className="text-muted-foreground">
                {user?.user_metadata?.username || user?.email?.split("@")[0]}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium">User ID</label>
              <p className="text-muted-foreground">{user?.id}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Last Sign In</label>
              <p className="text-muted-foreground">
                {user?.last_sign_in_at
                  ? new Date(user.last_sign_in_at).toLocaleDateString()
                  : "Never"}
              </p>
            </div>
          </div>
          {user?.user_metadata?.avatar && (
            <div>
              <label className="text-sm font-medium">Avatar</label>
              <div className="mt-1">
                <img
                  src={user.user_metadata.avatar}
                  alt="Profile avatar"
                  className="h-20 w-20 rounded-full object-cover"
                />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
