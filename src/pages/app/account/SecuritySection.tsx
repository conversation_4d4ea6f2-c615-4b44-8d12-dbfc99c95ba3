import { useState } from "react";
import { format } from "date-fns";
import { Key, Loader2, LogOut, Shield, UserCircle2 } from "lucide-react";
import { toast } from "sonner";

import type { useUpdatePassword } from "@/api/user/use-update-password";
import type { UserContextType } from "@/contexts/User";

import { useLogout } from "@/api/user/use-logout";
import DialogConfirmation from "@/components/shared/DialogConfirmation";
import TimeAgo from "@/components/shared/TimeAgo";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";

const i18n = {
  en: {
    security: "Security",
    changePassword: "Change Password",
    updatePassword: "Update Password",
    enterNewPassword: "Enter your new password below. Make sure it's secure!",
    newPassword: "New password",
    setupMFA: "Setup Multi-Factor Authentication",
    lastSignIn: "Last sign in",
    ip: "IP Address",
    browser: "Browser",
    activeSessions: "Active Sessions",
    signOutAllSessions: "Sign Out All Sessions",
    confirmSignOutAll: {
      title: "Sign Out All Sessions",
      description:
        "This will sign you out of all devices. You'll need to sign in again.",
    },
  },
};

export default function SecuritySection({
  isLoading,
  updatePassword,
  logout,
  user,
  session,
}: {
  isLoading: boolean;
  updatePassword: ReturnType<typeof useUpdatePassword>;
  logout: ReturnType<typeof useLogout>;
  user: UserContextType["user"];
  session: UserContextType["session"];
}) {
  const [newPassword, setNewPassword] = useState("");
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isSigningOutAll, setIsSigningOutAll] = useState(false);

  const handlePasswordChange = async () => {
    try {
      setIsChangingPassword(true);
      await updatePassword.mutateAsync({ password: newPassword });
      toast.success("Password updated successfully");
      setNewPassword("");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message || "Failed to update password");
      } else {
        toast.error("An unknown error occurred");
      }
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleSignOutAllSessions = async () => {
    try {
      setIsSigningOutAll(true);
      await logout.mutateAsync({ scope: "global" });
      toast.success("Signed out of all sessions");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message || "Failed to sign out of all sessions");
      } else {
        toast.error("An unknown error occurred");
      }
      toast.error("Failed to sign out of all sessions");
    } finally {
      setIsSigningOutAll(false);
    }
  };

  // Extract user agent info
  const browser =
    navigator.userAgent.match(/(chrome|firefox|safari|edge|opera)/i)?.[1] ||
    "Unknown browser";

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          {i18n.en.security}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Session Information */}
        <div className="rounded-lg border p-4">
          <h3 className="mb-4 font-medium">Current Session</h3>
          <div className="text-muted-foreground grid gap-4 text-sm">
            <div>
              <p className="mb-1 font-medium">{i18n.en.lastSignIn}:</p>
              <div className="flex items-center gap-2">
                <TimeAgo date={new Date()} />
                <span className="text-muted-foreground text-xs">
                  ({format(new Date(), "PPP")})
                </span>
              </div>
            </div>
            <div>
              <p className="mb-1 font-medium">{i18n.en.browser}:</p>
              <span className="capitalize">{browser}</span>
            </div>
          </div>
        </div>

        {/* Security Actions */}
        <div className="flex flex-col gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full" disabled={isLoading}>
                <Key className="mr-2 h-4 w-4" />
                {i18n.en.changePassword}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{i18n.en.changePassword}</DialogTitle>
                <DialogDescription>
                  {i18n.en.enterNewPassword}
                </DialogDescription>
              </DialogHeader>
              <Input
                type="password"
                placeholder={i18n.en.newPassword}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
              <DialogFooter>
                <Button
                  onClick={handlePasswordChange}
                  disabled={!newPassword || isChangingPassword}
                >
                  {isChangingPassword && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {i18n.en.updatePassword}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button
            variant="outline"
            className="w-full"
            disabled={true} // Disabled until MFA is implemented
          >
            <UserCircle2 className="mr-2 h-4 w-4" />
            {i18n.en.setupMFA}
          </Button>

          <DialogConfirmation
            title={i18n.en.confirmSignOutAll.title}
            description={i18n.en.confirmSignOutAll.description}
            onClick={handleSignOutAllSessions}
          >
            <Button
              variant="outline"
              className="w-full"
              disabled={isSigningOutAll}
            >
              {isSigningOutAll ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <LogOut className="mr-2 h-4 w-4" />
              )}
              {i18n.en.signOutAllSessions}
            </Button>
          </DialogConfirmation>
        </div>
      </CardContent>
    </Card>
  );
}
