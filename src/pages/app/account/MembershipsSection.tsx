import { Building2, Loader2, LogOut, Users } from "lucide-react";
import { Link } from "react-router";

import type { UserContextType } from "@/contexts/User";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const i18n = {
  en: {
    memberships: "Memberships",
    organization: "Organization",
    industry: "Industry",
    size: "Size",
    role: "Role",
    notAMember: "You are not a member of any organizations",
    createOrganization: "Create Organization",
    leaveOrganization: {
      title: "Leave Organization",
      description:
        "Are you sure you want to leave this organization? This action cannot be undone.",
    },
  },
};

export default function MembershipsSection({
  memberships,
  isLoading,
}: {
  memberships: UserContextType["memberships"];
  isLoading: UserContextType["isLoading"];
}) {
  const handleLeaveOrganization = async (id: string) => {
    // Implement leave organization logic
    console.log("Leave organization:", id);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          {i18n.en.memberships}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : memberships && memberships.length > 0 ? (
          <div className="space-y-2">
            {memberships.map((membership) => (
              <div
                key={membership.id}
                className="flex items-center justify-between rounded-lg border p-4"
              >
                <div className="flex items-center gap-4">
                  <Building2 className="text-muted-foreground h-5 w-5" />
                  <div>
                    <div className="font-medium">
                      {membership.organization.name}
                    </div>
                    <div className="text-muted-foreground flex gap-2 text-sm">
                      <span>{membership.organization.industry}</span>
                      <span>•</span>
                      <span className="capitalize">{membership.role}</span>
                    </div>
                  </div>
                </div>
                <DialogConfirmation
                  title={i18n.en.leaveOrganization.title}
                  description={i18n.en.leaveOrganization.description}
                  onClick={() =>
                    handleLeaveOrganization(membership.organization.id)
                  }
                  variant="ghost"
                  size="sm"
                >
                  <Button variant="ghost" size="sm">
                    <LogOut className="text-muted-foreground hover:text-destructive h-4 w-4" />
                  </Button>
                </DialogConfirmation>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-4 text-center">
            <p className="text-muted-foreground mb-4">{i18n.en.notAMember}</p>
            <Button asChild>
              <Link to="/app/onboarding/organization">
                {i18n.en.createOrganization}
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
      {memberships && memberships.length > 0 && (
        <CardFooter>
          <Button asChild variant="secondary" className="w-full sm:w-auto">
            <Link to="/app/console/organizations">Manage Organizations</Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
