import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { UserAccountPage } from "./Account";

const meta: Meta<typeof UserAccountPage> = {
  title: "Pages/Account",
  component: UserAccountPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    session: null,
    user: {
      id: "123",
      aud: "123",
      created_at: new Date().toISOString(),
      app_metadata: {
        provider: "email",
      },
      user_metadata: {},
    },
    driver: null,
    memberships: [],
    isLoading: false,
    // @ts-expect-error - TODO: fix this
    updatePassword: {
      mutate: fn(),
      isPending: false,
    },
    // @ts-expect-error - TODO: fix this
    deleteUser: {
      mutate: fn(),
      isPending: false,
    },
    // @ts-expect-error - TODO: fix this
    updateDriver: {
      mutate: fn(),
      isPending: false,
    },
    // @ts-expect-error - TODO: fix this
    logout: {
      mutate: fn(),
      isPending: false,
    },
  },
};
