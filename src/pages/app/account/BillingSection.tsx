import { CreditCard, Plus, Trash } from "lucide-react";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const i18n = {
  en: {
    billing: "Billing",
    paymentMethods: "Payment Methods",
    addPaymentMethod: "Add Payment Method",
    noPaymentMethodsAddedYet: "No payment methods added yet",
    removeCard: {
      title: "Remove Payment Method",
      description:
        "Are you sure you want to remove this payment method? This action cannot be undone.",
    },
  },
};

export default function BillingSection() {
  // Mock payment methods - replace with real data later
  const paymentMethods = [
    // { id: '1', last4: '4242', brand: 'visa', expMonth: 12, expYear: 2024 }
  ];

  const handleRemoveCard = async (id: string) => {
    // Implement remove card logic
    console.log("Remove card:", id);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="mr-2 h-5 w-5" />
          {i18n.en.paymentMethods}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {paymentMethods.length > 0 ? (
            <div className="space-y-2">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="flex items-center justify-between rounded-lg border p-4"
                >
                  <div className="flex items-center gap-4">
                    <CreditCard className="h-5 w-5" />
                    <div>
                      <div className="font-medium capitalize">
                        {method.brand} •••• {method.last4}
                      </div>
                      <div className="text-muted-foreground text-sm">
                        Expires {method.expMonth}/{method.expYear}
                      </div>
                    </div>
                  </div>
                  <DialogConfirmation
                    title={i18n.en.removeCard.title}
                    description={i18n.en.removeCard.description}
                    onClick={() => handleRemoveCard(method.id)}
                    variant="ghost"
                    size="sm"
                  >
                    <Button variant="ghost" size="sm">
                      <Trash className="text-muted-foreground hover:text-destructive h-4 w-4" />
                    </Button>
                  </DialogConfirmation>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-4 text-center">
              <p className="text-muted-foreground mb-4">
                {i18n.en.noPaymentMethodsAddedYet}
              </p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          {i18n.en.addPaymentMethod}
        </Button>
      </CardFooter>
    </Card>
  );
}
