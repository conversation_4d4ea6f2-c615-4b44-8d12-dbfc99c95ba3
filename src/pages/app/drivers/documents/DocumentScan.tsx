import { useCallback, useEffect, useRef, useState } from "react";
import { ArrowLeft, Camera, FileUp, Info, Loader2 } from "lucide-react";
import { useNavigate } from "react-router";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { useSupabaseDocuments } from "@/pages/public/demo/documents/useSupabaseDocuments";

type ScanResult = {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
};

export default function DocumentScan() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [activeTab, setActiveTab] = useState("upload");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Use the hook for document processing
  const { processAndStoreDocument, isProcessing } = useSupabaseDocuments();

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  // Process document using the hook
  const processDocument = useCallback(
    async (fileOrBlob: File | Blob) => {
      try {
        // Convert blob to file if necessary
        const file =
          fileOrBlob instanceof File
            ? fileOrBlob
            : new File([fileOrBlob], "document.jpg", { type: "image/jpeg" });

        // Use the hook to process and store the document
        const result = await processAndStoreDocument({
          file,
          scanMethod: activeTab === "camera" ? "camera" : "upload",
          description: "Document scanned by driver",
        });

        if (result) {
          // Create scan result format for compatibility
          setScanResult({
            id: result.document.id,
            name: result.document.name,
            type: result.document.type,
            size: result.document.size,
            url: result.document.url,
          });

          toast({
            title: "Document Processed Successfully",
            description:
              "Your document has been processed and saved successfully.",
          });

          // Navigate to documents page after successful scan
          setTimeout(() => {
            navigate("/app/drivers/documents");
          }, 2000);
        }
      } catch (error) {
        console.error("Error processing document:", error);
        toast({
          title: "Processing Failed",
          description:
            "There was an error processing your document. Please try again.",
          variant: "destructive",
        });
      }
    },
    [activeTab, processAndStoreDocument, navigate, toast],
  );

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsLoading(true);
      await processDocument(selectedFile);
    } catch (error) {
      console.error("Error uploading file:", error);
      toast({
        title: "Upload Failed",
        description:
          "There was an error uploading your document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Stop camera stream
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
      setStream(null);
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    }
  }, [stream]);

  // Start camera stream
  const startCamera = useCallback(async () => {
    try {
      if (stream) {
        stopCamera();
      }

      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" },
        audio: false,
      });

      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error("Error accessing camera:", error);
      toast({
        title: "Camera Access Denied",
        description:
          "Please allow camera access to take a photo of your document.",
        variant: "destructive",
      });
    }
  }, [stream, toast, stopCamera]);

  // Take photo
  const takePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob
    canvas.toBlob(
      async (blob) => {
        if (!blob) return;

        setPreviewUrl(URL.createObjectURL(blob));
        stopCamera();

        // Process the captured image
        setIsLoading(true);
        try {
          await processDocument(blob);
        } catch (error) {
          console.error("Error processing document:", error);
          toast({
            title: "Processing Failed",
            description:
              "There was an error processing your document. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      },
      "image/jpeg",
      0.8,
    );
  }, [stopCamera, toast, processDocument]);

  // Clean up on unmount
  const cleanup = useCallback(() => {
    stopCamera();
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  }, [stopCamera, previewUrl]);

  useEffect(() => {
    return () => cleanup();
  }, [cleanup]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === "camera") {
      startCamera();
    } else {
      stopCamera();
    }
  };

  // Use the hook's loading state along with local loading state
  const isDocumentLoading = isLoading || isProcessing;

  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate("/app/drivers/documents")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Scan Document</h1>
          <p className="text-muted-foreground">
            Upload or take a photo of your document to scan it
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Document Scanner</CardTitle>
              <CardDescription>
                Choose how you want to scan your document
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-6">
                <Info className="h-4 w-4" />
                <AlertTitle>Document Guidelines</AlertTitle>
                <AlertDescription>
                  Ensure your document is well-lit, clearly visible, and all
                  four corners are visible in the frame.
                </AlertDescription>
              </Alert>

              <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="upload">
                    <FileUp className="mr-2 h-4 w-4" />
                    Upload Photo
                  </TabsTrigger>
                  <TabsTrigger value="camera">
                    <Camera className="mr-2 h-4 w-4" />
                    Take Photo
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="upload" className="mt-6">
                  <div className="space-y-4">
                    <div className="border-muted-foreground/25 rounded-lg border-2 border-dashed p-6">
                      <div className="flex flex-col items-center justify-center">
                        <FileUp className="text-muted-foreground/50 mb-4 h-12 w-12" />
                        <p className="text-muted-foreground mb-4 text-center">
                          Drag and drop your document here, or click to browse
                        </p>
                        <input
                          type="file"
                          id="document-upload"
                          className="hidden"
                          accept="image/jpeg,image/png,image/heic,application/pdf"
                          onChange={handleFileSelect}
                        />
                        <label htmlFor="document-upload">
                          <Button variant="outline" className="cursor-pointer">
                            Select Document
                          </Button>
                        </label>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="camera" className="mt-6">
                  <div className="space-y-4">
                    {stream ? (
                      <div className="relative">
                        <video
                          ref={videoRef}
                          autoPlay
                          playsInline
                          muted
                          className="w-full rounded-lg border"
                        />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="pointer-events-none aspect-[8.5/11] w-4/5 rounded-md border-2 border-dashed border-white/70" />
                        </div>
                        <canvas ref={canvasRef} className="hidden" />
                        <Button
                          onClick={takePhoto}
                          className="absolute bottom-4 left-1/2 h-14 w-14 -translate-x-1/2 rounded-full p-0"
                        >
                          <Camera className="h-6 w-6" />
                        </Button>
                      </div>
                    ) : (
                      <div className="border-muted-foreground/25 flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-12">
                        <Camera className="text-muted-foreground/50 mb-4 h-12 w-12" />
                        <p className="text-muted-foreground mb-2 text-center">
                          Camera is not active
                        </p>
                        <Button onClick={startCamera}>Start Camera</Button>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>

              {previewUrl && (
                <div className="mt-6 space-y-4">
                  <h3 className="text-lg font-medium">Document Preview</h3>
                  <div className="overflow-hidden rounded-lg border">
                    <img
                      src={previewUrl}
                      alt="Document preview"
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => navigate("/app/drivers/documents")}
              >
                Cancel
              </Button>
              {previewUrl && !isDocumentLoading && !scanResult && (
                <Button
                  onClick={
                    activeTab === "upload"
                      ? handleFileUpload
                      : () => processDocument(new Blob())
                  }
                >
                  Process Document
                </Button>
              )}
              {isDocumentLoading && (
                <Button disabled>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="space-y-4">
                <li className="flex items-start gap-2">
                  <span className="bg-primary text-primary-foreground flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-xs">
                    1
                  </span>
                  <span>
                    <strong className="block">Upload or Take Photo</strong>
                    <span className="text-muted-foreground text-sm">
                      Choose to upload a photo or use your camera to take a
                      picture of your document.
                    </span>
                  </span>
                </li>

                <li className="flex items-start gap-2">
                  <span className="bg-primary text-primary-foreground flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-xs">
                    2
                  </span>
                  <span>
                    <strong className="block">Process Document</strong>
                    <span className="text-muted-foreground text-sm">
                      Our system automatically detects and extracts information
                      from your document.
                    </span>
                  </span>
                </li>

                <li className="flex items-start gap-2">
                  <span className="bg-primary text-primary-foreground flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-xs">
                    3
                  </span>
                  <span>
                    <strong className="block">Review & Save</strong>
                    <span className="text-muted-foreground text-sm">
                      Review the extracted information and save it to your
                      account.
                    </span>
                  </span>
                </li>
              </ol>
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Supported Documents</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-2 w-2 rounded-full"></div>
                  <span>Driver's License</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-2 w-2 rounded-full"></div>
                  <span>Vehicle Registration</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-2 w-2 rounded-full"></div>
                  <span>Insurance Cards</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-2 w-2 rounded-full"></div>
                  <span>Bill of Lading</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="bg-primary h-2 w-2 rounded-full"></div>
                  <span>Delivery Confirmations</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
