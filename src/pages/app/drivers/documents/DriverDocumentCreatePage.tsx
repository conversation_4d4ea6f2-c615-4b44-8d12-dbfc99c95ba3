import { <PERSON><PERSON><PERSON><PERSON>, Info } from "lucide-react";
import { UseFormReturn } from "react-hook-form";

import DocumentType from "@/components/common/DocumentType";
import { FileUploadField } from "@/components/forms/fields/FileUpload";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Enums } from "@/supabase/types";

// Use the correct type from Supabase types
type DocumentTypeEnum = Enums<"document_type">;

// Document form values type
export interface DocumentFormValues {
  name: string;
  type: DocumentTypeEnum;
  description?: string;
  file: File;
}

export interface DriverDocumentCreatePageProps {
  // Form management
  form: UseFormReturn<DocumentFormValues>;

  // Form data and handlers
  onSubmit: (values: DocumentFormValues) => Promise<void>;

  // Loading and submission states
  isSubmitting: boolean;

  // User data
  user: {
    id: string;
    email?: string;
  } | null;
  driver: {
    id: string;
    first_name?: string;
    last_name?: string;
  } | null;

  // Document type configuration
  documentTypes: Array<{
    value: DocumentTypeEnum;
    label: string;
    contentType: string;
  }>;

  // Navigation handlers
  onNavigateBack: () => void;
  onCancel: () => void;

  // File upload configuration
  fileUploadConfig: {
    maxSize: number;
    accept: string[];
    showPreview: boolean;
    required: boolean;
  };
}

export const DriverDocumentCreatePage = ({
  form,
  onSubmit,
  isSubmitting,
  user,
  driver,
  documentTypes,
  onNavigateBack,
  onCancel,
  fileUploadConfig,
}: DriverDocumentCreatePageProps) => {
  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={onNavigateBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Upload Document</h1>
          <p className="text-muted-foreground">
            Add a new document to your account
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
              <CardDescription>
                Fill in the details and upload your document file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter document name" {...field} />
                        </FormControl>
                        <FormDescription>
                          Give your document a descriptive name
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select document type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {documentTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center gap-2">
                                  <DocumentType
                                    type={type.contentType}
                                    size={16}
                                  />
                                  <span>{type.label}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the type of document you're uploading
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter document description"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Add additional details about this document
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="file"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>File</FormLabel>
                        <FormControl>
                          <FileUploadField
                            name="file"
                            description="Drag and drop your document here, or click to browse"
                            maxSize={fileUploadConfig.maxSize}
                            accept={fileUploadConfig.accept}
                            showPreview={fileUploadConfig.showPreview}
                            required={fileUploadConfig.required}
                          />
                        </FormControl>
                        <FormDescription>
                          Supported file types: PDF, JPEG, PNG, CSV, TXT (Max:
                          {fileUploadConfig.maxSize}MB)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Alert variant="default" className="bg-muted/50">
                    <Info className="h-4 w-4" />
                    <AlertTitle>Important</AlertTitle>
                    <AlertDescription>
                      Make sure you have the right to share any documents you
                      upload. Never upload sensitive personal information unless
                      required.
                    </AlertDescription>
                  </Alert>

                  <div className="flex justify-end gap-3">
                    <Button type="button" variant="outline" onClick={onCancel}>
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting || !form.formState.isValid}
                    >
                      {isSubmitting ? "Uploading..." : "Upload Document"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Upload Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">File Requirements</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>Maximum file size: {fileUploadConfig.maxSize}MB</li>
                  <li>Supported formats: PDF, JPEG, PNG, CSV, TXT</li>
                  <li>Clear, legible documents are more easily processed</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Commonly Uploaded Documents</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>Driver's License</li>
                  <li>Vehicle Registration</li>
                  <li>Insurance Cards</li>
                  <li>Bills of Lading</li>
                  <li>Delivery Confirmations</li>
                  <li>Inspection Reports</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Document Privacy</h3>
                <p className="text-muted-foreground text-sm">
                  By default, uploaded documents are private and only visible to
                  you and system administrators.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
