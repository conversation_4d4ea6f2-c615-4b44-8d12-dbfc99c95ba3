import { useState } from "react";
import { useNavigate } from "react-router";

import { queryFn, useListDocuments } from "@/api/documents/use-list-documents";
import { useUser } from "@/contexts/User";
import { DriverDocumentsPage } from "./DriverDocumentsPage";

type DocumentItem = Awaited<ReturnType<typeof queryFn>>["items"][number];

const filterGroups = [
  {
    id: "type",
    label: "Type",
    options: [
      { value: null, label: "All" },
      { value: "contract", label: "Contract" },
      { value: "manifest", label: "Manifest" },
      { value: "license", label: "License" },
      { value: "insurance", label: "Insurance" },
      { value: "receipt", label: "Receipt" },
      { value: "general", label: "General" },
      { value: "other", label: "Other" },
    ],
  },
];

export default function Documents() {
  const navigate = useNavigate();
  const { driver } = useUser();
  const [activeTab, setActiveTab] = useState("all");

  const {
    data: documentsResponse,
    isLoading,
    error,
  } = useListDocuments({
    driver_id: driver?.id,
    pageIndex: 0,
    pageSize: 10,
  });

  // Navigation handlers
  const handleNavigateBack = () => {
    navigate("/app/drivers");
  };

  const handleNavigateToScan = () => {
    navigate("/app/drivers/documents/scan");
  };

  const handleNavigateToCreate = () => {
    navigate("/app/drivers/documents/create");
  };

  const handleNavigateToDocument = (documentId: string) => {
    navigate(`/app/drivers/documents/${documentId}`);
  };

  const handleDocumentAction = (document: DocumentItem, action: string) => {
    // Handle document actions like delete, download, etc.
    console.log(`Performing ${action} on document:`, document);
  };

  const handleActiveTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <DriverDocumentsPage
      documents={documentsResponse?.items}
      isLoading={isLoading}
      error={error}
      activeTab={activeTab}
      onActiveTabChange={handleActiveTabChange}
      filterGroups={filterGroups}
      onNavigateBack={handleNavigateBack}
      onNavigateToScan={handleNavigateToScan}
      onNavigateToCreate={handleNavigateToCreate}
      onNavigateToDocument={handleNavigateToDocument}
      onDocumentAction={handleDocumentAction}
    />
  );
}
