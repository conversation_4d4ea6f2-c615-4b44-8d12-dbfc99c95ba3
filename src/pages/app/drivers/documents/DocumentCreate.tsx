import { useState } from "react";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { ArrowLeft, Info } from "lucide-react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import * as z from "zod";

import DocumentType from "@/components/common/DocumentType";
import { FileUploadField } from "@/components/forms/fields/FileUpload";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/User";
import { supabase } from "@/supabase/client";
import { Enums } from "@/supabase/types";

// Use the correct type from Supabase types
type DocumentTypeEnum = Enums<"document_type">;

// Form schema validation
const documentSchema = z.object({
  name: z.string().min(3, "Document name must be at least 3 characters"),
  type: z.enum(
    ["manifest", "contract", "general", "other", "verification"] as const,
    {
      required_error: "Please select a document type",
    },
  ),
  description: z.string().optional(),
  file: z.instanceof(File, { message: "Please upload a file" }),
});

type DocumentFormValues = z.infer<typeof documentSchema>;

// Document type options matching the enum values
const documentTypes: {
  value: DocumentTypeEnum;
  label: string;
  contentType: string;
}[] = [
  {
    value: "manifest",
    label: "Manifest Document",
    contentType: "application/pdf",
  },
  { value: "contract", label: "Contract", contentType: "application/pdf" },
  {
    value: "general",
    label: "General Document",
    contentType: "application/pdf",
  },
  {
    value: "verification",
    label: "Verification Document",
    contentType: "image/jpeg",
  },
  { value: "other", label: "Other Document", contentType: "application/pdf" },
];

export default function DocumentCreate() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, driver } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<DocumentFormValues>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      name: "",
      type: "general",
      description: "",
    },
  });

  const onSubmit = async (values: DocumentFormValues) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in to upload documents.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const file = values.file;
      const fileExt = file.name.split(".").pop();
      const filePath = `${user.id}/${Date.now()}-${Math.random().toString(36).substring(2, 9)}.${fileExt}`;

      // Upload file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from("documents")
        .upload(filePath, file, { upsert: true });

      if (uploadError) {
        throw new Error(`Error uploading file: ${uploadError.message}`);
      }

      // Get the public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from("documents").getPublicUrl(filePath);

      // Create document record in the database
      const { error: dbError } = await supabase.from("documents").insert({
        name: values.name,
        type: values.type,
        description: values.description || null,
        url: publicUrl,
        content_type: file.type,
        size: file.size,
        storage_path: filePath,
        driver_id: driver?.id || null,
        created_by: user.id,
        bucket_id: "documents",
      });

      if (dbError) {
        throw new Error(`Error saving document: ${dbError.message}`);
      }

      toast({
        title: "Document Uploaded",
        description: "Your document has been successfully uploaded.",
      });

      navigate("/app/drivers/documents");
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        title: "Upload Failed",
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate("/app/drivers/documents")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Upload Document</h1>
          <p className="text-muted-foreground">
            Add a new document to your account
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
              <CardDescription>
                Fill in the details and upload your document file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter document name" {...field} />
                        </FormControl>
                        <FormDescription>
                          Give your document a descriptive name
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select document type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {documentTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center gap-2">
                                  <DocumentType
                                    type={type.contentType}
                                    size={16}
                                  />
                                  <span>{type.label}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the type of document you're uploading
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter document description"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Add additional details about this document
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="file"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>File</FormLabel>
                        <FormControl>
                          <FileUploadField
                            name="file"
                            description="Drag and drop your document here, or click to browse"
                            maxSize={10} // 10MB
                            accept={[
                              "application/pdf",
                              "image/jpeg",
                              "image/png",
                              "text/plain",
                              "text/csv",
                            ]}
                            showPreview={true}
                            required={true}
                          />
                        </FormControl>
                        <FormDescription>
                          Supported file types: PDF, JPEG, PNG, CSV, TXT (Max:
                          10MB)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Alert variant="default" className="bg-muted/50">
                    <Info className="h-4 w-4" />
                    <AlertTitle>Important</AlertTitle>
                    <AlertDescription>
                      Make sure you have the right to share any documents you
                      upload. Never upload sensitive personal information unless
                      required.
                    </AlertDescription>
                  </Alert>

                  <div className="flex justify-end gap-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate("/app/drivers/documents")}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting || !form.formState.isValid}
                    >
                      {isSubmitting ? "Uploading..." : "Upload Document"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Upload Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">File Requirements</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>Maximum file size: 10MB</li>
                  <li>Supported formats: PDF, JPEG, PNG, CSV, TXT</li>
                  <li>Clear, legible documents are more easily processed</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Commonly Uploaded Documents</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>Driver's License</li>
                  <li>Vehicle Registration</li>
                  <li>Insurance Cards</li>
                  <li>Bills of Lading</li>
                  <li>Delivery Confirmations</li>
                  <li>Inspection Reports</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Document Privacy</h3>
                <p className="text-muted-foreground text-sm">
                  By default, uploaded documents are private and only visible to
                  you and system administrators.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
