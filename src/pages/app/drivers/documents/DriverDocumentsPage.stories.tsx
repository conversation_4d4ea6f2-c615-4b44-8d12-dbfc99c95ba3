import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { DriverDocumentsPage } from "./DriverDocumentsPage";

const meta: Meta<typeof DriverDocumentsPage> = {
  title: "Pages/Drivers/Documents",
  component: DriverDocumentsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const filterGroups = [
  {
    id: "type",
    label: "Type",
    options: [
      { value: null, label: "All" },
      { value: "contract", label: "Contract" },
      { value: "manifest", label: "Manifest" },
      { value: "license", label: "License" },
      { value: "insurance", label: "Insurance" },
      { value: "receipt", label: "Receipt" },
      { value: "general", label: "General" },
      { value: "other", label: "Other" },
    ],
  },
];

const mockDocuments = [
  {
    id: "doc-1",
    name: "Commercial Driver License",
    type: "general" as const,
    content_type: "application/pdf",
    created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    bucket_id: "documents",
    created_by: "driver-123",
    driver_id: "driver-123",
    organization_id: null,
    latitude: 0,
    longitude: 0,
    metadata: {},
    path: "documents/driver-123/license.pdf",
    storage_path: "documents/driver-123/license.pdf",
    size: 2048576,
    url: "https://example.com/documents/driver-123/license.pdf",
    organization: null,
    driver: {
      id: "driver-123",
      first_name: "John",
      last_name: "Smith",
      email: "<EMAIL>",
      phone_number: "+**********",
      avatar: null,
    },
  },
  {
    id: "doc-2",
    name: "Insurance Certificate",
    type: "general" as const,
    content_type: "application/pdf",
    created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    bucket_id: "documents",
    created_by: "driver-123",
    driver_id: "driver-123",
    organization_id: "org-1",
    latitude: 0,
    longitude: 0,
    metadata: {},
    path: "documents/driver-123/insurance.pdf",
    size: 1536000,
    organization: {
      id: "org-1",
      name: "ABC Logistics Inc.",
      type: "private" as const,
      industry: "logistics",
      size: "medium",
      avatar: null,
    },
    driver: {
      id: "driver-123",
      first_name: "John",
      last_name: "Smith",
      email: "<EMAIL>",
      phone_number: "+**********",
      avatar: null,
    },
  },
  {
    id: "doc-3",
    name: "DOT Medical Certificate",
    type: "general" as const,
    content_type: "application/pdf",
    created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    bucket_id: "documents",
    created_by: "driver-123",
    driver_id: "driver-123",
    organization_id: null,
    latitude: 0,
    longitude: 0,
    metadata: {},
    path: "documents/driver-123/medical.pdf",
    size: 1024000,
    organization: null,
    driver: {
      id: "driver-123",
      first_name: "John",
      last_name: "Smith",
      email: "<EMAIL>",
      phone_number: "+**********",
      avatar: null,
    },
  },
  {
    id: "doc-4",
    name: "Load Manifest - Electronics Shipment",
    type: "manifest" as const,
    content_type: "application/pdf",
    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    bucket_id: "documents",
    created_by: "driver-123",
    driver_id: "driver-123",
    organization_id: "org-2",
    latitude: 0,
    longitude: 0,
    metadata: {},
    path: "documents/driver-123/manifest.pdf",
    size: 3072000,
    organization: {
      id: "org-2",
      name: "TechCorp Distribution",
      type: "private" as const,
      industry: "technology",
      size: "large",
      avatar: null,
    },
    driver: {
      id: "driver-123",
      first_name: "John",
      last_name: "Smith",
      email: "<EMAIL>",
      phone_number: "+**********",
      avatar: null,
    },
  },
  {
    id: "doc-5",
    name: "Delivery Receipt - Store #445",
    type: "other" as const,
    content_type: "image/jpeg",
    created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    bucket_id: "documents",
    created_by: "driver-123",
    driver_id: "driver-123",
    organization_id: "org-3",
    latitude: 0,
    longitude: 0,
    metadata: {},
    path: "documents/driver-123/receipt.jpg",
    size: 512000,
    organization: {
      id: "org-3",
      name: "RetailChain Inc.",
      type: "private" as const,
      industry: "retail",
      size: "large",
      avatar: null,
    },
    driver: {
      id: "driver-123",
      first_name: "John",
      last_name: "Smith",
      email: "<EMAIL>",
      phone_number: "+**********",
      avatar: null,
    },
  },
];

const mockProcessingDocuments = [
  {
    id: "doc-processing-1",
    name: "Hazmat Certification",
    type: "license",
    content_type: "application/pdf",
    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    organization: null,
    driver_id: "driver-123",
    status: "processing",
  },
  {
    id: "doc-processing-2",
    name: "Vehicle Inspection Report",
    type: "general",
    content_type: "application/pdf",
    created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
    organization: {
      id: "org-1",
      name: "ABC Logistics Inc.",
    },
    driver_id: "driver-123",
    status: "processing",
  },
];

const mockMixedDocuments = [
  ...mockDocuments.slice(0, 3),
  ...mockProcessingDocuments,
  {
    id: "doc-error-1",
    name: "Damaged License Scan",
    type: "license",
    content_type: "image/jpeg",
    created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
    organization: null,
    driver_id: "driver-123",
    status: "error",
  },
];

export const Default: Story = {
  args: {
    documents: mockDocuments,
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const Loading: Story = {
  args: {
    documents: undefined,
    isLoading: true,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const Empty: Story = {
  args: {
    documents: [],
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const Uploading: Story = {
  args: {
    documents: [...mockDocuments, ...mockProcessingDocuments.slice(0, 1)],
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const UploadError: Story = {
  args: {
    documents: mockDocuments,
    isLoading: false,
    error: new globalThis.Error(
      "Failed to upload document: File size too large",
    ),
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const UploadSuccess: Story = {
  args: {
    documents: [
      {
        id: "doc-new-success",
        name: "New Insurance Policy",
        type: "insurance",
        content_type: "application/pdf",
        created_at: new Date().toISOString(),
        organization: {
          id: "org-1",
          name: "ABC Logistics Inc.",
        },
        driver_id: "driver-123",
        status: "verified",
      },
      ...mockDocuments,
    ],
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const Processing: Story = {
  args: {
    documents: mockProcessingDocuments,
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const MixedStates: Story = {
  args: {
    documents: mockMixedDocuments,
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const LicensesOnly: Story = {
  args: {
    documents: mockDocuments.filter((doc) => doc.type === "license"),
    isLoading: false,
    error: null,
    activeTab: "license",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const ManifestsOnly: Story = {
  args: {
    documents: mockDocuments.filter((doc) => doc.type === "manifest"),
    isLoading: false,
    error: null,
    activeTab: "manifest",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const PersonalDocuments: Story = {
  args: {
    documents: mockDocuments.filter((doc) => !doc.organization),
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};

export const OrganizationDocuments: Story = {
  args: {
    documents: mockDocuments.filter((doc) => doc.organization),
    isLoading: false,
    error: null,
    activeTab: "all",
    onActiveTabChange: fn(),
    filterGroups,
    onNavigateBack: fn(),
    onNavigateToScan: fn(),
    onNavigateToCreate: fn(),
    onNavigateToDocument: fn(),
    onDocumentAction: fn(),
  },
};
