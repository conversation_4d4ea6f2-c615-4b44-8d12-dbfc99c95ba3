import { useNavigate, useParams } from "react-router";

import { useGetDocument } from "@/api/documents/use-get-document";
import DocumentDetailsLoading from "@/components/documents/DocumentDetailsLoading";
import DocumentHeader from "@/components/documents/DocumentHeader";
import DocumentInfoSection from "@/components/documents/DocumentInfoSection";
import DocumentNotFound from "@/components/documents/DocumentNotFound";
import DocumentPreviewSection from "@/components/documents/DocumentPreviewSection";

export default function DocumentDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    data: document,
    isLoading,
    error,
  } = useGetDocument(id || "", {
    refetchOnWindowFocus: false,
  });

  if (isLoading) {
    return <DocumentDetailsLoading />;
  }

  if (error || !document) {
    return <DocumentNotFound />;
  }

  const description =
    document.metadata && typeof document.metadata === "object"
      ? (document.metadata as { description: string }).description
      : "";

  return (
    <div className="container py-8">
      <DocumentHeader
        document={document}
        onSuccess={() => navigate("/app/drivers/documents")}
      />

      <div className="grid gap-6 md:grid-cols-3">
        <DocumentPreviewSection document={document} description={description} />

        <DocumentInfoSection document={document} />
      </div>
    </div>
  );
}
