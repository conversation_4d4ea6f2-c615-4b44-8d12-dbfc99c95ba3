import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";

import { UseFormReturn } from "react-hook-form";
import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import {
  DocumentFormValues,
  DriverDocumentCreatePage,
} from "./DriverDocumentCreatePage";

const meta: Meta<typeof DriverDocumentCreatePage> = {
  title: "Pages/Drivers/DocumentCreate",
  component: DriverDocumentCreatePage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

// Mock user data
const mockUser = {
  id: "user-123",
  email: "<EMAIL>",
};

const mockDriver = {
  id: "driver-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
};

// Mock document types
const mockDocumentTypes = [
  {
    value: "manifest" as const,
    label: "Manifest Document",
    contentType: "application/pdf",
  },
  {
    value: "contract" as const,
    label: "Contract",
    contentType: "application/pdf",
  },
  {
    value: "general" as const,
    label: "General Document",
    contentType: "application/pdf",
  },
  {
    value: "verification" as const,
    label: "Verification Document",
    contentType: "image/jpeg",
  },
  {
    value: "other" as const,
    label: "Other Document",
    contentType: "application/pdf",
  },
];

// Mock file upload configuration
const mockFileUploadConfig = {
  maxSize: 10,
  accept: [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "text/plain",
    "text/csv",
  ],
  showPreview: true,
  required: true,
};

// Mock form object for Storybook - using partial implementation for simplicity
const createMockForm = (overrides = {}) =>
  ({
    handleSubmit: fn((onSubmit) => (e) => {
      e?.preventDefault();
      return Promise.resolve();
    }),
    control: {} as any,
    formState: {
      isValid: true,
      isDirty: false,
      isLoading: false,
      isSubmitted: false,
      isSubmitting: false,
      isSubmitSuccessful: false,
      isValidating: false,
      submitCount: 0,
      errors: {},
      touchedFields: {},
      dirtyFields: {},
      validatingFields: {},
      defaultValues: {},
      disabled: false,
      isReady: true,
      ...overrides,
    },
    watch: fn(),
    getValues: fn(() => ({
      name: "",
      type: "general" as const,
      description: "",
      file: new File([""], "test.pdf", { type: "application/pdf" }),
    })),
    getFieldState: fn(),
    setError: fn(),
    clearErrors: fn(),
    setValue: fn(),
    trigger: fn(),
    reset: fn(),
    resetField: fn(),
    setFocus: fn(),
    unregister: fn(),
    register: fn(),
    subscribe: fn(),
    ...overrides,
  }) as unknown as UseFormReturn<DocumentFormValues>;

export const Default: Story = {
  args: {
    form: createMockForm(),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const Loading: Story = {
  args: {
    form: createMockForm(),
    onSubmit: fn(),
    isSubmitting: false,
    user: null,
    driver: null,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const FileSelected: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const Uploading: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
    }),
    onSubmit: fn(),
    isSubmitting: true,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const Processing: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
    }),
    onSubmit: fn(),
    isSubmitting: true,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const UploadSuccess: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
    }),
    onSubmit: fn(() => Promise.resolve()),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const UploadError: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
        errors: {
          file: {
            message: "File upload failed. Please try again.",
          },
        },
      },
    }),
    onSubmit: fn(() => Promise.reject(new Error("Upload failed"))),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const ValidationError: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: false,
        errors: {
          name: {
            message: "Document name must be at least 3 characters",
          },
          type: {
            message: "Please select a document type",
          },
          file: {
            message: "Please upload a file",
          },
        },
      },
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const FormFilled: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
      getValues: () => ({
        name: "Commercial Driver License",
        type: "general",
        description: "Updated CDL with endorsements",
        file: new File([""], "cdl-license.pdf", { type: "application/pdf" }),
      }),
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const ManifestDocument: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
      getValues: () => ({
        name: "Load Manifest - Electronics Shipment",
        type: "manifest",
        description: "Manifest for electronics delivery to warehouse",
        file: new File([""], "manifest-20241228.pdf", {
          type: "application/pdf",
        }),
      }),
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const VerificationDocument: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
      getValues: () => ({
        name: "Delivery Verification Photo",
        type: "verification",
        description: "Photo verification of completed delivery",
        file: new File([""], "delivery-verification.jpg", {
          type: "image/jpeg",
        }),
      }),
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const FileSizeError: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: false,
        errors: {
          file: {
            message:
              "File size exceeds 10MB limit. Please choose a smaller file.",
          },
        },
      },
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const UnsupportedFileType: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: false,
        errors: {
          file: {
            message:
              "Unsupported file type. Please upload PDF, JPEG, PNG, CSV, or TXT files only.",
          },
        },
      },
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};

export const ContractDocument: Story = {
  args: {
    form: createMockForm({
      formState: {
        isValid: true,
      },
      getValues: () => ({
        name: "Service Agreement Contract",
        type: "contract",
        description: "Service agreement between driver and logistics company",
        file: new File([""], "service-agreement.pdf", {
          type: "application/pdf",
        }),
      }),
    }),
    onSubmit: fn(),
    isSubmitting: false,
    user: mockUser,
    driver: mockDriver,
    documentTypes: mockDocumentTypes,
    onNavigateBack: fn(),
    onCancel: fn(),
    fileUploadConfig: mockFileUploadConfig,
  },
};
