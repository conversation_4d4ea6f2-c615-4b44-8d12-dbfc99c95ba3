import { ArrowLeft, FileText, Plus, Upload } from "lucide-react";

import { queryFn } from "@/api/documents/use-list-documents";
import { DocumentMenu } from "@/components/actions/DocumentMenu";
import DocumentType from "@/components/common/DocumentType";
import TimeAgo from "@/components/shared/TimeAgo";
import ListTable from "@/components/tables/ListTable";
import { Button } from "@/components/ui/button";

type DocumentItem = Awaited<ReturnType<typeof queryFn>>["items"][number];

export interface DriverDocumentsPageProps {
  // Document data
  documents: DocumentItem[] | undefined;

  // Loading states
  isLoading: boolean;

  // Error states
  error: Error | null;

  // Filter states
  activeTab: string;
  onActiveTabChange: (tab: string) => void;

  // Filter configuration
  filterGroups: Array<{
    id: string;
    label: string;
    options: Array<{
      value: string | null;
      label: string;
    }>;
  }>;

  // Navigation handlers
  onNavigateBack: () => void;
  onNavigateToScan: () => void;
  onNavigateToCreate: () => void;
  onNavigateToDocument: (documentId: string) => void;

  // Document operations
  onDocumentAction: (document: DocumentItem, action: string) => void;
}

export const DriverDocumentsPage = ({
  documents,
  isLoading,
  error,
  activeTab,
  onActiveTabChange,
  filterGroups,
  onNavigateBack,
  onNavigateToScan,
  onNavigateToCreate,
  onNavigateToDocument,
  onDocumentAction,
}: DriverDocumentsPageProps) => {
  return (
    <div className="container py-8">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={onNavigateBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="mb-2 text-4xl font-bold">My Documents</h1>
            <p className="text-muted-foreground">
              Manage your documents, licenses, and certifications
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={onNavigateToScan}>
            <Upload className="mr-2 h-4 w-4" />
            Scan Document
          </Button>
          <Button onClick={onNavigateToCreate}>
            <Plus className="mr-2 h-4 w-4" />
            Upload Document
          </Button>
        </div>
      </div>

      <ListTable
        loading={isLoading}
        data={documents}
        defaultPageSize={10}
        filterGroups={filterGroups}
        groupName="documents"
        i18n={{
          emptyText: "No documents found",
          selection: "Selected",
          actions: {
            tableSettings: "Table settings",
            tableActions: "Table actions",
            search: "Search documents...",
          },
        }}
        columns={({ i18n, TableActions }) => [
          {
            id: "type",
            header: "Type",
            accessorKey: "type",
            cell: ({ row }) => {
              return (
                <div className="flex items-center gap-2">
                  <DocumentType
                    type={row.original.content_type || row.original.type}
                    size={16}
                  />
                  <span className="capitalize">{row.original.type}</span>
                </div>
              );
            },
          },
          {
            id: "name",
            header: "Name",
            accessorKey: "name",
            cell: ({ row }) => {
              return (
                <button
                  onClick={() => onNavigateToDocument(row.original.id)}
                  className="text-left font-medium hover:underline"
                >
                  {row.original.name}
                </button>
              );
            },
          },
          {
            id: "created_at",
            header: "Uploaded",
            accessorKey: "created_at",
            cell: ({ row }) => {
              return (
                <TimeAgo
                  date={new Date(row.original.created_at)}
                  className="text-muted-foreground"
                />
              );
            },
          },
          {
            id: "organization",
            header: "Organization",
            accessorKey: "organization.name",
            cell: ({ row }) => {
              return row.original.organization ? (
                <span>{row.original.organization.name}</span>
              ) : (
                <span className="text-muted-foreground">Personal</span>
              );
            },
          },
          {
            id: "actions",
            header: "",
            cell: ({ row }) => {
              return (
                <div className="flex justify-end">
                  <DocumentMenu document={row.original} size="sm" />
                </div>
              );
            },
          },
        ]}
      >
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="text-muted-foreground mb-4 h-12 w-12" />
          <h3 className="text-xl font-semibold">No documents found</h3>
          <p className="text-muted-foreground mt-2 mb-6">
            You haven't uploaded any documents yet
          </p>
          <div className="flex gap-4">
            <Button variant="outline" onClick={onNavigateToScan}>
              <Upload className="mr-2 h-4 w-4" />
              Scan Document
            </Button>
            <Button onClick={onNavigateToCreate}>
              <Plus className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </div>
        </div>
      </ListTable>
    </div>
  );
};
