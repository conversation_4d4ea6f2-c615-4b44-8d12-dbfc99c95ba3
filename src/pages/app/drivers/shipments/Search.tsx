import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  ArrowLeft,
  Loader2,
  MapPinIcon,
  SearchIcon,
  SlidersHorizontal,
} from "lucide-react";
import { useNavigate } from "react-router";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Slider } from "@/components/ui/slider";
import { useUser } from "@/contexts/User";
import { supabase } from "@/supabase/client";

const DriverJobs = () => {
  const { driver } = useUser();
  const [location, setLocation] = useState("");
  const [radius, setRadius] = useState([25]); // Default 25 mile radius
  const navigate = useNavigate();

  const {
    data: availableShipments,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["available-shipments"],
    queryFn: async () => {
      const { data: shipments, error: shipmentsError } = await supabase
        .from("shipments")
        .select(
          `
          id,
          status,
          stops (
            id,
            sequence_number,
            location_id,
            locations (
              formatted
            )
          )
        `,
        )
        .eq("status", "pending")
        .order("created_at", { ascending: false });

      if (shipmentsError) throw shipmentsError;
      return shipments;
    },
  });

  if (isLoading) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-8 flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate("/app/drivers")}
          className="mr-2"
        >
          <ArrowLeft className="h-5 w-5" />
          <span className="sr-only">Back</span>
        </Button>
        <h1 className="text-3xl font-bold">Available Jobs</h1>
      </div>

      <div className="space-y-6">
        {/* Search and Filter Section */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
            <Input
              placeholder="Search by location..."
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              className="pl-9"
            />
          </div>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="gap-2">
                <SlidersHorizontal className="h-4 w-4" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Jobs</SheetTitle>
                <SheetDescription>
                  Adjust your search preferences
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                <div className="space-y-2">
                  <Label>Search Radius (miles)</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      value={radius}
                      onValueChange={setRadius}
                      max={100}
                      min={5}
                      step={5}
                      className="flex-1"
                    />
                    <span className="text-muted-foreground w-12 text-sm">
                      {radius}mi
                    </span>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {error ? (
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              There was an error loading available jobs. Please try again later.
            </AlertDescription>
          </Alert>
        ) : availableShipments && availableShipments.length > 0 ? (
          <div className="space-y-6">
            {availableShipments.map((shipment) => (
              <Card key={shipment.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Shipment #{shipment.id.slice(0, 8)}</span>
                    <Button variant="outline" disabled={!driver}>
                      {driver ? "Apply" : "Create profile to apply"}
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {shipment.stops?.map((stop) => (
                      <div key={stop.id} className="flex items-start space-x-3">
                        <MapPinIcon className="text-muted-foreground mt-1 h-5 w-5" />
                        <div>
                          <p className="font-medium">
                            Stop #{stop.sequence_number}
                          </p>
                          <p className="text-muted-foreground text-sm">
                            {stop.locations?.formatted}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <MapPinIcon className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
              <h2 className="mb-2 text-xl font-semibold">No Available Jobs</h2>
              <p className="text-muted-foreground">
                There are currently no available shipments in your area. Try
                adjusting your search filters.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default DriverJobs;
