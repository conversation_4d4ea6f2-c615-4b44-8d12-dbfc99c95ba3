import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react";
import { Link } from "react-router";

import { ShipmentStatusBadge } from "@/components/common/types/ShipmentStatus";
import TimeAgo from "@/components/shared/TimeAgo";
import ListTable from "@/components/tables/ListTable";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UserContextType } from "@/contexts/User";

type ShipmentStatus =
  | "pending"
  | "scheduled"
  | "assigned"
  | "confirmed"
  | "in_progress"
  | "completed"
  | "cancelled";

export interface ShipmentData {
  id: string;
  status: ShipmentStatus;
  stops: Array<{
    id: string;
    type: string;
    location?: {
      formatted?: string;
    };
  }>;
  created_at: string;
  completed_at?: string;
  updated_at?: string;
}

export interface DriverShipmentContextData {
  activeShipment: ShipmentData | null;
  upcomingShipments: ShipmentData[];
  pastShipments: ShipmentData[];
  isLoading: boolean;
  error: Error | null;
}

export interface ShipmentsListData {
  data: ShipmentData[];
  isLoading: boolean;
  error: Error | null;
}

export interface DriverShipmentsPageProps {
  // User data
  driver: UserContextType["driver"];
  isLoading: boolean;

  // Driver shipment context data
  driverShipmentData: DriverShipmentContextData;

  // Current shipments (active/upcoming)
  currentShipments: ShipmentsListData;

  // Completed shipments (past)
  completedShipments: ShipmentsListData;

  // Tab state
  activeTab: string;
  onActiveTabChange: (tab: string) => void;

  // Utility functions
  getStopsText: (stops: ShipmentData["stops"]) => string;

  // Filter configuration
  filterGroups: Array<{
    id: string;
    label: string;
    options: Array<{
      value: string | null;
      label: string;
    }>;
  }>;
}

export const DriverShipmentsPage = ({
  driver,
  isLoading,
  driverShipmentData,
  currentShipments,
  completedShipments,
  activeTab,
  onActiveTabChange,
  getStopsText,
  filterGroups,
}: DriverShipmentsPageProps) => {
  const {
    activeShipment,
    upcomingShipments,
    pastShipments,
    isLoading: contextLoading,
    error: contextError,
  } = driverShipmentData;

  const loading =
    isLoading ||
    contextLoading ||
    currentShipments.isLoading ||
    completedShipments.isLoading;

  // Transform data for ListTable (expects { items, total } structure)
  const currentShipmentsData = {
    items: currentShipments.data,
    total: currentShipments.data.length,
  };

  const completedShipmentsData = {
    items: completedShipments.data,
    total: completedShipments.data.length,
  };

  return (
    <div className="container py-8">
      <div className="mb-8 flex items-center gap-4">
        <Link to="/app/drivers">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="mb-2 text-4xl font-bold">My Shipments</h1>
          <p className="text-muted-foreground">
            View and manage all your shipments in one place
          </p>
        </div>
      </div>

      {contextError || currentShipments.error || completedShipments.error ? (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error loading your shipments. Please try again later.
          </AlertDescription>
        </Alert>
      ) : loading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="text-primary h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Tabs
          defaultValue="active"
          value={activeTab}
          onValueChange={onActiveTabChange}
        >
          <TabsList className="mb-6 grid w-full grid-cols-3">
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="past">Past</TabsTrigger>
          </TabsList>

          <TabsContent value="active">
            <ListTable
              loading={currentShipments.isLoading}
              data={currentShipmentsData}
              defaultPageSize={10}
              filterGroups={filterGroups}
              groupName="active-shipments"
              i18n={{
                emptyText: "No active shipments found",
                selection: "Selected",
                actions: {
                  tableSettings: "Table settings",
                  tableActions: "Table actions",
                  search: "Search active shipments...",
                },
              }}
              columns={({ i18n, TableActions }) => [
                {
                  id: "id",
                  header: "ID",
                  accessorKey: "id",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="font-medium">
                        <Link
                          to={`/app/drivers/shipments/${shipment.id}`}
                          className="hover:underline"
                        >
                          #{shipment.id.substring(0, 8)}
                        </Link>
                      </div>
                    );
                  },
                },
                {
                  id: "status",
                  header: "Status",
                  accessorKey: "status",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return <ShipmentStatusBadge status={shipment.status} />;
                  },
                },
                {
                  id: "route",
                  header: "Route",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="max-w-md truncate">
                        {getStopsText(shipment.stops)}
                      </div>
                    );
                  },
                },
                {
                  id: "created_at",
                  header: "Created",
                  accessorKey: "created_at",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    const date = shipment.created_at
                      ? new Date(shipment.created_at)
                      : new Date();
                    return <TimeAgo date={date} />;
                  },
                },
                {
                  id: "actions",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="flex justify-end">
                        <Link to={`/app/drivers/shipments/${shipment.id}`}>
                          <Button variant="ghost" size="sm">
                            View
                          </Button>
                        </Link>
                      </div>
                    );
                  },
                },
              ]}
            >
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <h3 className="text-xl font-semibold">No active shipments</h3>
                <p className="text-muted-foreground mt-2 mb-6">
                  You don't have any active shipments at the moment
                </p>
              </div>
            </ListTable>
          </TabsContent>

          <TabsContent value="upcoming">
            <ListTable
              loading={currentShipments.isLoading}
              data={currentShipmentsData}
              defaultPageSize={10}
              filterGroups={filterGroups}
              groupName="upcoming-shipments"
              i18n={{
                emptyText: "No upcoming shipments found",
                selection: "Selected",
                actions: {
                  tableSettings: "Table settings",
                  tableActions: "Table actions",
                  search: "Search upcoming shipments...",
                },
              }}
              columns={({ i18n, TableActions }) => [
                {
                  id: "id",
                  header: "ID",
                  accessorKey: "id",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="font-medium">
                        <Link
                          to={`/app/drivers/shipments/${shipment.id}`}
                          className="hover:underline"
                        >
                          #{shipment.id.substring(0, 8)}
                        </Link>
                      </div>
                    );
                  },
                },
                {
                  id: "status",
                  header: "Status",
                  accessorKey: "status",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return <ShipmentStatusBadge status={shipment.status} />;
                  },
                },
                {
                  id: "route",
                  header: "Route",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="max-w-md truncate">
                        {getStopsText(shipment.stops)}
                      </div>
                    );
                  },
                },
                {
                  id: "created_at",
                  header: "Created",
                  accessorKey: "created_at",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    const date = shipment.created_at
                      ? new Date(shipment.created_at)
                      : new Date();
                    return <TimeAgo date={date} />;
                  },
                },
                {
                  id: "actions",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="flex justify-end">
                        <Link to={`/app/drivers/shipments/${shipment.id}`}>
                          <Button variant="ghost" size="sm">
                            View
                          </Button>
                        </Link>
                      </div>
                    );
                  },
                },
              ]}
            >
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <h3 className="text-xl font-semibold">No upcoming shipments</h3>
                <p className="text-muted-foreground mt-2 mb-6">
                  You don't have any upcoming shipments scheduled
                </p>
              </div>
            </ListTable>
          </TabsContent>

          <TabsContent value="past">
            <ListTable
              loading={completedShipments.isLoading}
              data={completedShipmentsData}
              defaultPageSize={10}
              filterGroups={filterGroups}
              groupName="past-shipments"
              i18n={{
                emptyText: "No past shipments found",
                selection: "Selected",
                actions: {
                  tableSettings: "Table settings",
                  tableActions: "Table actions",
                  search: "Search past shipments...",
                },
              }}
              columns={({ i18n, TableActions }) => [
                {
                  id: "id",
                  header: "ID",
                  accessorKey: "id",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="font-medium">
                        <Link
                          to={`/app/drivers/shipments/${shipment.id}`}
                          className="hover:underline"
                        >
                          #{shipment.id.substring(0, 8)}
                        </Link>
                      </div>
                    );
                  },
                },
                {
                  id: "status",
                  header: "Status",
                  accessorKey: "status",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return <ShipmentStatusBadge status={shipment.status} />;
                  },
                },
                {
                  id: "route",
                  header: "Route",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="max-w-md truncate">
                        {getStopsText(shipment.stops)}
                      </div>
                    );
                  },
                },
                {
                  id: "completed_at",
                  header: "Completed",
                  accessorKey: "completed_at",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return shipment.completed_at ? (
                      <TimeAgo date={new Date(shipment.completed_at)} />
                    ) : (
                      <span className="text-muted-foreground">N/A</span>
                    );
                  },
                },
                {
                  id: "actions",
                  cell: ({ row }) => {
                    const shipment = row.original as ShipmentData;
                    return (
                      <div className="flex justify-end">
                        <Link to={`/app/drivers/shipments/${shipment.id}`}>
                          <Button variant="ghost" size="sm">
                            View
                          </Button>
                        </Link>
                      </div>
                    );
                  },
                },
              ]}
            >
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <h3 className="text-xl font-semibold">No past shipments</h3>
                <p className="text-muted-foreground mt-2 mb-6">
                  You haven't completed any shipments yet
                </p>
              </div>
            </ListTable>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};
