import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import type { ShipmentData } from "./DriverShipmentsPage";

import { DriverShipmentsPage } from "./DriverShipmentsPage";

const meta: Meta<typeof DriverShipmentsPage> = {
  title: "Pages/Drivers/Shipments/DriverShipmentsPage",
  component: DriverShipmentsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/app/drivers/shipments" },
    }),
    docs: {
      description: {
        component:
          "Driver shipments presentation component showing tabbed view of active, upcoming, and past shipments.",
      },
    },
  },
  tags: ["autodocs"],
  args: {
    // Mock all function props with fn()
    onActiveTabChange: fn(),
    getStopsText: fn(),
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data
const mockDriver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone_number: "******-0123",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active" as const,
  tier: "professional",
  created_at: "2024-01-15T08:00:00Z",
  verified_at: "2024-01-20T10:00:00Z",
};

const mockActiveShipments: ShipmentData[] = [
  {
    id: "ship-active-001",
    status: "in_progress",
    stops: [
      {
        id: "stop-001",
        type: "pickup",
        location: { formatted: "Los Angeles, CA" },
      },
      {
        id: "stop-002",
        type: "dropoff",
        location: { formatted: "San Francisco, CA" },
      },
    ],
    created_at: "2024-06-15T09:00:00Z",
    updated_at: "2024-06-15T10:30:00Z",
  },
  {
    id: "ship-active-002",
    status: "confirmed",
    stops: [
      {
        id: "stop-003",
        type: "pickup",
        location: { formatted: "Oakland, CA" },
      },
      {
        id: "stop-004",
        type: "dropoff",
        location: { formatted: "Sacramento, CA" },
      },
    ],
    created_at: "2024-06-15T11:00:00Z",
    updated_at: "2024-06-15T11:15:00Z",
  },
];

const mockUpcomingShipments: ShipmentData[] = [
  {
    id: "ship-upcoming-001",
    status: "scheduled",
    stops: [
      {
        id: "stop-005",
        type: "pickup",
        location: { formatted: "Phoenix, AZ" },
      },
      {
        id: "stop-006",
        type: "dropoff",
        location: { formatted: "Las Vegas, NV" },
      },
    ],
    created_at: "2024-06-16T08:00:00Z",
    updated_at: "2024-06-16T08:15:00Z",
  },
];

const mockPastShipments: ShipmentData[] = [
  {
    id: "ship-past-001",
    status: "completed",
    stops: [
      {
        id: "stop-007",
        type: "pickup",
        location: { formatted: "Portland, OR" },
      },
      {
        id: "stop-008",
        type: "dropoff",
        location: { formatted: "Seattle, WA" },
      },
    ],
    created_at: "2024-06-10T08:00:00Z",
    completed_at: "2024-06-12T16:30:00Z",
    updated_at: "2024-06-12T16:30:00Z",
  },
  {
    id: "ship-past-002",
    status: "completed",
    stops: [
      {
        id: "stop-009",
        type: "pickup",
        location: { formatted: "Denver, CO" },
      },
      {
        id: "stop-010",
        type: "dropoff",
        location: { formatted: "Salt Lake City, UT" },
      },
    ],
    created_at: "2024-06-08T07:30:00Z",
    completed_at: "2024-06-10T14:45:00Z",
    updated_at: "2024-06-10T14:45:00Z",
  },
];

const mockFilterGroups = [
  {
    id: "status",
    label: "Status",
    options: [
      { value: null, label: "All" },
      { value: "pending", label: "Pending" },
      { value: "scheduled", label: "Scheduled" },
      { value: "assigned", label: "Assigned" },
      { value: "confirmed", label: "Confirmed" },
      { value: "in_progress", label: "In Progress" },
      { value: "completed", label: "Completed" },
      { value: "cancelled", label: "Cancelled" },
    ],
  },
  {
    id: "mode",
    label: "Mode",
    options: [
      { value: null, label: "All" },
      { value: "truck", label: "Truck" },
      { value: "rail", label: "Rail" },
      { value: "air", label: "Air" },
      { value: "sea", label: "Sea" },
      { value: "multimodal", label: "Multimodal" },
    ],
  },
];

const mockGetStopsText = (stops: ShipmentData["stops"]) => {
  if (!stops || stops.length === 0) return "No stops";

  const pickups = stops.filter((s) => s.type === "pickup");
  const dropoffs = stops.filter((s) => s.type === "dropoff");

  if (pickups.length === 0 || dropoffs.length === 0) return "Incomplete route";

  const firstPickup = pickups[0]?.location?.formatted || "Unknown";
  const lastDropoff =
    dropoffs[dropoffs.length - 1]?.location?.formatted || "Unknown";

  return `${firstPickup} → ${lastDropoff}`;
};

const defaultArgs = {
  driver: mockDriver,
  isLoading: false,
  driverShipmentData: {
    activeShipment: mockActiveShipments[0],
    upcomingShipments: mockUpcomingShipments,
    pastShipments: mockPastShipments,
    isLoading: false,
    error: null,
  },
  currentShipments: {
    data: [...mockActiveShipments, ...mockUpcomingShipments],
    isLoading: false,
    error: null,
  },
  completedShipments: {
    data: mockPastShipments,
    isLoading: false,
    error: null,
  },
  activeTab: "active",
  onActiveTabChange: fn(),
  getStopsText: mockGetStopsText,
  filterGroups: mockFilterGroups,
};

export const Default: Story = {
  args: defaultArgs,
};

export const Loading: Story = {
  args: {
    ...defaultArgs,
    isLoading: true,
    driverShipmentData: {
      ...defaultArgs.driverShipmentData,
      isLoading: true,
    },
    currentShipments: {
      ...defaultArgs.currentShipments,
      isLoading: true,
    },
    completedShipments: {
      ...defaultArgs.completedShipments,
      isLoading: true,
    },
  },
};

export const ActiveShipments: Story = {
  args: {
    ...defaultArgs,
    activeTab: "active",
    currentShipments: {
      data: mockActiveShipments,
      isLoading: false,
      error: null,
    },
  },
};

export const CompletedShipments: Story = {
  args: {
    ...defaultArgs,
    activeTab: "past",
    completedShipments: {
      data: mockPastShipments,
      isLoading: false,
      error: null,
    },
  },
};

export const PendingShipments: Story = {
  args: {
    ...defaultArgs,
    activeTab: "upcoming",
    currentShipments: {
      data: [
        {
          id: "ship-pending-001",
          status: "pending",
          stops: [
            {
              id: "stop-pending-001",
              type: "pickup",
              location: { formatted: "Chicago, IL" },
            },
            {
              id: "stop-pending-002",
              type: "dropoff",
              location: { formatted: "Detroit, MI" },
            },
          ],
          created_at: "2024-06-16T10:00:00Z",
          updated_at: "2024-06-16T10:00:00Z",
        },
      ],
      isLoading: false,
      error: null,
    },
  },
};

export const NoShipments: Story = {
  args: {
    ...defaultArgs,
    driverShipmentData: {
      activeShipment: null,
      upcomingShipments: [],
      pastShipments: [],
      isLoading: false,
      error: null,
    },
    currentShipments: {
      data: [],
      isLoading: false,
      error: null,
    },
    completedShipments: {
      data: [],
      isLoading: false,
      error: null,
    },
  },
};

export const FilteredView: Story = {
  args: {
    ...defaultArgs,
    currentShipments: {
      data: [
        {
          id: "ship-filtered-001",
          status: "confirmed",
          stops: [
            {
              id: "stop-filtered-001",
              type: "pickup",
              location: { formatted: "Miami, FL" },
            },
            {
              id: "stop-filtered-002",
              type: "dropoff",
              location: { formatted: "Tampa, FL" },
            },
          ],
          created_at: "2024-06-15T12:00:00Z",
          updated_at: "2024-06-15T12:30:00Z",
        },
      ],
      isLoading: false,
      error: null,
    },
  },
};

export const RouteOptimization: Story = {
  args: {
    ...defaultArgs,
    currentShipments: {
      data: [
        {
          id: "ship-route-001",
          status: "in_progress",
          stops: [
            {
              id: "stop-route-001",
              type: "pickup",
              location: { formatted: "Houston, TX" },
            },
            {
              id: "stop-route-002",
              type: "dropoff",
              location: { formatted: "Dallas, TX" },
            },
            {
              id: "stop-route-003",
              type: "pickup",
              location: { formatted: "Austin, TX" },
            },
            {
              id: "stop-route-004",
              type: "dropoff",
              location: { formatted: "San Antonio, TX" },
            },
          ],
          created_at: "2024-06-15T08:00:00Z",
          updated_at: "2024-06-15T14:20:00Z",
        },
      ],
      isLoading: false,
      error: null,
    },
  },
};

export const RealTimeTracking: Story = {
  args: {
    ...defaultArgs,
    driverShipmentData: {
      activeShipment: {
        id: "ship-tracking-001",
        status: "in_progress",
        stops: [
          {
            id: "stop-tracking-001",
            type: "pickup",
            location: { formatted: "Atlanta, GA" },
          },
          {
            id: "stop-tracking-002",
            type: "dropoff",
            location: { formatted: "Charlotte, NC" },
          },
        ],
        created_at: "2024-06-15T06:00:00Z",
        updated_at: "2024-06-15T13:45:00Z",
      },
      upcomingShipments: mockUpcomingShipments,
      pastShipments: mockPastShipments,
      isLoading: false,
      error: null,
    },
  },
};

export const HighPriority: Story = {
  args: {
    ...defaultArgs,
    currentShipments: {
      data: [
        {
          id: "ship-priority-001",
          status: "assigned",
          stops: [
            {
              id: "stop-priority-001",
              type: "pickup",
              location: { formatted: "New York, NY" },
            },
            {
              id: "stop-priority-002",
              type: "dropoff",
              location: { formatted: "Boston, MA" },
            },
          ],
          created_at: "2024-06-15T15:00:00Z",
          updated_at: "2024-06-15T15:30:00Z",
        },
      ],
      isLoading: false,
      error: null,
    },
  },
};

export const ErrorState: Story = {
  args: {
    ...defaultArgs,
    driverShipmentData: {
      ...defaultArgs.driverShipmentData,
      error: new Error("Failed to load driver shipment data"),
    },
    currentShipments: {
      data: [],
      isLoading: false,
      error: new Error("Failed to load current shipments"),
    },
    completedShipments: {
      data: [],
      isLoading: false,
      error: new Error("Failed to load completed shipments"),
    },
  },
};
