import { useState } from "react";

import { useListShipments } from "@/api/shipments/use-list-shipments";
import { useDriverShipment } from "@/contexts/DriverShipmentContext";
import { useUser } from "@/contexts/User";
import { DriverShipmentsPage } from "./DriverShipmentsPage";

const filterGroups = [
  {
    id: "status",
    label: "Status",
    options: [
      { value: null, label: "All" },
      { value: "pending", label: "Pending" },
      { value: "scheduled", label: "Scheduled" },
      { value: "assigned", label: "Assigned" },
      { value: "confirmed", label: "Confirmed" },
      { value: "in_progress", label: "In Progress" },
      { value: "completed", label: "Completed" },
      { value: "cancelled", label: "Cancelled" },
    ],
  },
  {
    id: "mode",
    label: "Mode",
    options: [
      { value: null, label: "All" },
      { value: "truck", label: "Truck" },
      { value: "rail", label: "Rail" },
      { value: "air", label: "Air" },
      { value: "sea", label: "Sea" },
      { value: "multimodal", label: "Multimodal" },
    ],
  },
];

const DriverShipments = () => {
  const { driver } = useUser();
  const { activeShipment, upcomingShipments, pastShipments, isLoading, error } =
    useDriverShipment();
  const [activeTab, setActiveTab] = useState("active");

  // For active and upcoming shipments
  const {
    data: currentShipments,
    isLoading: isLoadingCurrent,
    error: currentError,
  } = useListShipments({
    driver_id: driver?.id,
    status: ["pending", "scheduled", "assigned", "confirmed", "in_progress"],
    pageIndex: 0,
    pageSize: 10,
  });

  // For past shipments
  const {
    data: completedShipments,
    isLoading: isLoadingCompleted,
    error: completedError,
  } = useListShipments({
    driver_id: driver?.id,
    status: ["completed", "cancelled"],
    pageIndex: 0,
    pageSize: 10,
  });

  const getStopsText = (
    stops: Array<{
      id: string;
      type: string;
      location?: { formatted?: string };
    }>,
  ) => {
    if (!stops || stops.length === 0) return "No stops";

    const pickups = stops.filter((s) => s.type === "pickup");
    const dropoffs = stops.filter((s) => s.type === "dropoff");

    if (pickups.length === 0 || dropoffs.length === 0)
      return "Incomplete route";

    const firstPickup = pickups[0]?.location?.formatted || "Unknown";
    const lastDropoff =
      dropoffs[dropoffs.length - 1]?.location?.formatted || "Unknown";

    return `${firstPickup} → ${lastDropoff}`;
  };

  const handleActiveTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <DriverShipmentsPage
      driver={driver}
      isLoading={false}
      driverShipmentData={{
        activeShipment,
        upcomingShipments,
        pastShipments,
        isLoading,
        error,
      }}
      currentShipments={{
        data: currentShipments?.items || [],
        isLoading: isLoadingCurrent,
        error: currentError,
      }}
      completedShipments={{
        data: completedShipments?.items || [],
        isLoading: isLoadingCompleted,
        error: completedError,
      }}
      activeTab={activeTab}
      onActiveTabChange={handleActiveTabChange}
      getStopsText={getStopsText}
      filterGroups={filterGroups}
    />
  );
};

export default DriverShipments;
