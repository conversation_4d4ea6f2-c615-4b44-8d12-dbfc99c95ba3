import { useCallback, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router";

import { useCompleteVerification } from "@/api/drivers/shipments/use-complete-verification";
import { useGetVerification } from "@/api/drivers/shipments/use-get-verification";
import { useUploadVerificationImage } from "@/api/drivers/shipments/use-upload-verification-image";
import { useGeolocation } from "@/hooks/use-geolocation";
import { useToast } from "@/hooks/use-toast";
import { DriverShipmentVerificationPage } from "./DriverShipmentVerificationPage";

export default function ShipmentVerification() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [step, setStep] = useState<number>(1);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [pickupNumber, setPickupNumber] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [verificationComplete, setVerificationComplete] =
    useState<boolean>(false);
  const [isInRadius, setIsInRadius] = useState<boolean>(false);
  const [distanceToPickup, setDistanceToPickup] = useState<number | null>(null);

  // Get the verification data
  const {
    data: verification,
    isLoading: isLoadingVerification,
    error,
  } = useGetVerification(id || "");

  // Location hooks
  const {
    location,
    error: locationError,
    getCurrentLocation,
    loading: isLoadingLocation,
  } = useGeolocation();

  // Mutations
  const uploadImageMutation = useUploadVerificationImage();
  const completeVerificationMutation = useCompleteVerification();

  useEffect(() => {
    // Get the user's location when component mounts
    getCurrentLocation();

    // Generate a random pickup number (would be replaced with actual backend logic)
    const randomNum = Math.floor(100000 + Math.random() * 900000);
    setPickupNumber(randomNum.toString());
  }, []);

  useEffect(() => {
    if (location && verification) {
      // Check if driver is within 2 miles of pickup location
      // This is a simplified calculation - in production use a proper distance calculation
      const pickupLat = verification.latitude;
      const pickupLng = verification.longitude;

      if (pickupLat && pickupLng) {
        // Calculate distance in miles (simplified formula)
        const distance = calculateDistance(
          location.latitude,
          location.longitude,
          Number(pickupLat),
          Number(pickupLng),
        );

        setDistanceToPickup(distance);
        setIsInRadius(distance <= 2); // 2 mile radius
      }
    }
  }, [location, verification]);

  // Function to calculate distance between two coordinates (Haversine formula)
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number => {
    const R = 3958.8; // Earth's radius in miles
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setImagePreview(e.target.result.toString());
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleVerificationSubmit = async () => {
    if (!id || !imageFile) {
      toast({
        title: "Missing information",
        description: "Please upload a verification image",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload verification image
      const uploadResult = await uploadImageMutation.mutateAsync({
        verification_id: id,
        file: imageFile,
      });

      // Complete verification with the new document ID
      await completeVerificationMutation.mutateAsync({
        id,
        document_id: uploadResult.id,
        latitude: location?.latitude,
        longitude: location?.longitude,
      });

      // Send notification to shipper (in a real app, this would be done via an API)
      await notifyShipper(id, pickupNumber);

      toast({
        title: "Verification successful",
        description:
          "The shipper has been notified that you're ready for pickup",
      });

      setVerificationComplete(true);
      setStep(3);
    } catch (error) {
      console.error("Verification failed:", error);
      toast({
        title: "Verification failed",
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to notify shipper (would be a real API call in production)
  const notifyShipper = async (verificationId: string, pickupCode: string) => {
    // In a real application, this would be an API call to notify the shipper
    console.log(
      `Notifying shipper for verification ${verificationId} with pickup code ${pickupCode}`,
    );

    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(resolve, 1000);
    });
  };

  const handleGoBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  const handleRemoveImage = useCallback(() => {
    setImageFile(null);
    setImagePreview(null);
  }, []);

  return (
    <DriverShipmentVerificationPage
      verification={verification}
      isLoadingVerification={isLoadingVerification}
      error={error}
      location={location}
      isLoadingLocation={isLoadingLocation}
      locationError={locationError}
      isInRadius={isInRadius}
      distanceToPickup={distanceToPickup}
      step={step}
      isSubmitting={isSubmitting}
      verificationComplete={verificationComplete}
      pickupNumber={pickupNumber}
      imageFile={imageFile}
      imagePreview={imagePreview}
      setStep={setStep}
      handleFileChange={handleFileChange}
      getCurrentLocation={getCurrentLocation}
      handleVerificationSubmit={handleVerificationSubmit}
      onNavigateBack={handleGoBack}
      onRemoveImage={handleRemoveImage}
    />
  );
}
