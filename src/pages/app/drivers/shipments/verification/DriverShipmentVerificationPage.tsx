import {
  <PERSON><PERSON><PERSON><PERSON>,
  Camera,
  CheckCircle,
  MapPin,
  Upload,
  X,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";

export interface VerificationData {
  id: string;
  shipment_id: string;
  title: string;
  description?: string;
  latitude?: string;
  longitude?: string;
  required_radius?: number;
  status: string;
  created_at: string;
  updated_at?: string;
}

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface DriverShipmentVerificationPageProps {
  // Verification data
  verification: VerificationData | undefined;
  isLoadingVerification: boolean;
  error: Error | null;

  // Location data
  location: LocationData | null;
  isLoadingLocation: boolean;
  locationError: string | null;
  isInRadius: boolean;
  distanceToPickup: number | null;

  // UI state
  step: number;
  isSubmitting: boolean;
  verificationComplete: boolean;
  pickupNumber: string;

  // Image handling
  imageFile: File | null;
  imagePreview: string | null;

  // Event handlers
  setStep: (step: number) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  getCurrentLocation: () => void;
  handleVerificationSubmit: () => Promise<void>;
  onNavigateBack: () => void;
  onRemoveImage: () => void;
}

export const DriverShipmentVerificationPage = ({
  verification,
  isLoadingVerification,
  error,
  location,
  isLoadingLocation,
  locationError,
  isInRadius,
  distanceToPickup,
  step,
  isSubmitting,
  verificationComplete,
  pickupNumber,
  imageFile,
  imagePreview,
  setStep,
  handleFileChange,
  getCurrentLocation,
  handleVerificationSubmit,
  onNavigateBack,
  onRemoveImage,
}: DriverShipmentVerificationPageProps) => {
  if (isLoadingVerification) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  if (error || !verification) {
    return (
      <div className="container py-8">
        <Card className="mx-auto max-w-2xl">
          <CardContent className="flex flex-col items-center px-8 pt-6 pb-8 text-center">
            <div className="text-destructive bg-destructive/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full">
              <X className="h-6 w-6" />
            </div>
            <h2 className="mb-2 text-2xl font-semibold">
              Verification Not Found
            </h2>
            <p className="text-muted-foreground mb-6">
              {error?.message || "Unable to load verification details"}
            </p>
            <Button onClick={onNavigateBack} variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-8 flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={onNavigateBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="mb-2 text-4xl font-bold">Shipment Verification</h1>
            <p className="text-muted-foreground">{verification.title}</p>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${
                  step >= 1
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                1
              </div>
              <span
                className={
                  step >= 1 ? "text-foreground" : "text-muted-foreground"
                }
              >
                Location Check
              </span>
            </div>
            <div className="mx-4 flex-1">
              <Progress
                value={step >= 2 ? 100 : step >= 1 ? 50 : 0}
                className="h-2"
              />
            </div>
            <div className="flex items-center space-x-4">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${
                  step >= 2
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                2
              </div>
              <span
                className={
                  step >= 2 ? "text-foreground" : "text-muted-foreground"
                }
              >
                Photo Verification
              </span>
            </div>
            <div className="mx-4 flex-1">
              <Progress value={step >= 3 ? 100 : 0} className="h-2" />
            </div>
            <div className="flex items-center space-x-4">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${
                  step >= 3
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                3
              </div>
              <span
                className={
                  step >= 3 ? "text-foreground" : "text-muted-foreground"
                }
              >
                Complete
              </span>
            </div>
          </div>
        </div>

        {/* Step Content */}
        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="mr-2 h-5 w-5" />
                Location Verification
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-muted-foreground text-sm">
                We need to verify you're at the pickup location before
                proceeding.
              </div>

              {isLoadingLocation ? (
                <div className="flex items-center justify-center py-8">
                  <div className="border-primary h-6 w-6 animate-spin rounded-full border-b-2"></div>
                  <span className="ml-2">Getting your location...</span>
                </div>
              ) : locationError ? (
                <div className="border-destructive/20 bg-destructive/10 rounded-lg border p-4">
                  <div className="flex items-center">
                    <X className="text-destructive mr-2 h-5 w-5" />
                    <div>
                      <p className="text-destructive font-medium">
                        Location Error
                      </p>
                      <p className="text-destructive/80 text-sm">
                        {locationError}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={getCurrentLocation}
                    className="mt-4"
                    variant="outline"
                  >
                    Try Again
                  </Button>
                </div>
              ) : location ? (
                <div className="space-y-4">
                  <div
                    className={`rounded-lg border p-4 ${
                      isInRadius
                        ? "border-green-200 bg-green-50"
                        : "border-orange-200 bg-orange-50"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">
                          {isInRadius
                            ? "✓ Location Verified"
                            : "⚠ Outside Pickup Area"}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          Distance to pickup: {distanceToPickup?.toFixed(2)}{" "}
                          miles
                        </p>
                      </div>
                      {isInRadius && (
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      )}
                    </div>
                  </div>

                  {isInRadius ? (
                    <Button onClick={() => setStep(2)} className="w-full">
                      Continue to Photo Verification
                    </Button>
                  ) : (
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4 text-sm">
                        You need to be within 2 miles of the pickup location to
                        continue.
                      </p>
                      <Button onClick={getCurrentLocation} variant="outline">
                        Refresh Location
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <Button onClick={getCurrentLocation} className="w-full">
                  Get My Location
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Camera className="mr-2 h-5 w-5" />
                Photo Verification
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-muted-foreground text-sm">
                Take a clear photo of the shipment or location for verification.
              </div>

              {!imagePreview ? (
                <div className="border-muted-foreground/25 rounded-lg border-2 border-dashed p-8 text-center">
                  <Upload className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                  <div className="space-y-2">
                    <p className="text-lg font-medium">
                      Upload Verification Photo
                    </p>
                    <p className="text-muted-foreground text-sm">
                      Choose a file or take a photo with your camera
                    </p>
                  </div>
                  <div className="mt-6">
                    <Label
                      htmlFor="verification-image"
                      className="cursor-pointer"
                    >
                      <Button asChild>
                        <span>
                          <Camera className="mr-2 h-4 w-4" />
                          Take Photo
                        </span>
                      </Button>
                    </Label>
                    <Input
                      id="verification-image"
                      type="file"
                      accept="image/*"
                      capture="environment"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Verification"
                      className="h-64 w-full rounded-lg border object-cover"
                    />
                    <Button
                      onClick={onRemoveImage}
                      size="sm"
                      variant="destructive"
                      className="absolute top-2 right-2"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Label htmlFor="retake-image" className="flex-1">
                      <Button variant="outline" className="w-full" asChild>
                        <span>
                          <Camera className="mr-2 h-4 w-4" />
                          Retake
                        </span>
                      </Button>
                    </Label>
                    <Input
                      id="retake-image"
                      type="file"
                      accept="image/*"
                      capture="environment"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <Button
                      onClick={handleVerificationSubmit}
                      disabled={isSubmitting}
                      className="flex-1"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                          Submitting...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Submit Verification
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {step === 3 && verificationComplete && (
          <Card>
            <CardContent className="flex flex-col items-center px-8 pt-6 pb-8 text-center">
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="mb-2 text-2xl font-semibold">
                Verification Complete!
              </h2>
              <p className="text-muted-foreground mb-6">
                Your verification has been submitted successfully. The shipper
                has been notified.
              </p>
              <div className="bg-muted/50 mb-6 rounded-lg border p-4">
                <p className="text-sm font-medium">Pickup Code</p>
                <p className="text-primary text-2xl font-bold">
                  {pickupNumber}
                </p>
                <p className="text-muted-foreground mt-1 text-sm">
                  Present this code to the shipper
                </p>
              </div>
              <Button onClick={onNavigateBack} className="w-full">
                Return to Shipments
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
