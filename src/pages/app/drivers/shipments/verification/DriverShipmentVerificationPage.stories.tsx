import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import {
  DriverShipmentVerificationPage,
  LocationData,
  VerificationData,
} from "./DriverShipmentVerificationPage";

const meta: Meta<typeof DriverShipmentVerificationPage> = {
  title: "Pages/Drivers/DriverShipmentVerificationPage",
  component: DriverShipmentVerificationPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "verification-123" },
      },
      routing: { path: "/app/drivers/shipments/verification/:id" },
    }),
  },
  tags: ["autodocs"],
  args: {
    // Mock all function props with fn()
    setStep: fn(),
    handleFileChange: fn(),
    getCurrentLocation: fn(),
    handleVerificationSubmit: fn(),
    onNavigateBack: fn(),
    onRemoveImage: fn(),
  },
};
export default meta;

type Story = StoryObj<typeof meta>;

// Mock data
const mockVerificationData: VerificationData = {
  id: "verification-123",
  shipment_id: "shipment-456",
  title: "Pickup Verification - FedEx Distribution Center",
  description:
    "Verify your arrival at the pickup location and take a photo of the shipment",
  latitude: "37.7749",
  longitude: "-122.4194",
  required_radius: 2,
  status: "pending",
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockLocationData: LocationData = {
  latitude: 37.7749,
  longitude: -122.4194,
  accuracy: 10,
};

const mockLocationNearby: LocationData = {
  latitude: 37.775,
  longitude: -122.4195,
  accuracy: 15,
};

const mockLocationFarAway: LocationData = {
  latitude: 37.8049,
  longitude: -122.4494,
  accuracy: 12,
};

// Create a mock image preview (base64 data URL)
const mockImagePreview =
  "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=";

export const Default: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.1,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const LoadingVerification: Story = {
  args: {
    verification: undefined,
    isLoadingVerification: true,
    error: null,
    location: null,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: false,
    distanceToPickup: null,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "",
    imageFile: null,
    imagePreview: null,
  },
};

export const VerificationNotFound: Story = {
  args: {
    verification: undefined,
    isLoadingVerification: false,
    error: new Error("Verification not found"),
    location: null,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: false,
    distanceToPickup: null,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "",
    imageFile: null,
    imagePreview: null,
  },
};

export const LoadingLocation: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: null,
    isLoadingLocation: true,
    locationError: null,
    isInRadius: false,
    distanceToPickup: null,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const LocationError: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: null,
    isLoadingLocation: false,
    locationError:
      "Location access denied. Please enable location services and try again.",
    isInRadius: false,
    distanceToPickup: null,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const LocationVerified: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.1,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const OutsidePickupArea: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationFarAway,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: false,
    distanceToPickup: 3.2,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const CloseToPickupArea: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationNearby,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 1.8,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const PhotoVerificationStep: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.1,
    step: 2,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const PhotoTaken: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.1,
    step: 2,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: new File([""], "verification.jpg", { type: "image/jpeg" }),
    imagePreview: mockImagePreview,
  },
};

export const SubmittingVerification: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.1,
    step: 2,
    isSubmitting: true,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: new File([""], "verification.jpg", { type: "image/jpeg" }),
    imagePreview: mockImagePreview,
  },
};

export const VerificationComplete: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.1,
    step: 3,
    isSubmitting: false,
    verificationComplete: true,
    pickupNumber: "847362",
    imageFile: new File([""], "verification.jpg", { type: "image/jpeg" }),
    imagePreview: mockImagePreview,
  },
};

export const LongDistanceShipment: Story = {
  args: {
    verification: {
      ...mockVerificationData,
      title: "Long Distance Pickup - Cross Country Logistics",
      description:
        "Interstate shipment verification for coast-to-coast delivery",
    },
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.3,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "923847",
    imageFile: null,
    imagePreview: null,
  },
};

export const UrgentShipment: Story = {
  args: {
    verification: {
      ...mockVerificationData,
      title: "URGENT: Medical Supply Pickup",
      description:
        "Time-sensitive medical equipment requires immediate verification",
    },
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.05,
    step: 2,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "456789",
    imageFile: null,
    imagePreview: null,
  },
};

export const HighValueShipment: Story = {
  args: {
    verification: {
      ...mockVerificationData,
      title: "High Value Electronics Pickup",
      description:
        "Secure verification required for valuable technology shipment",
    },
    isLoadingVerification: false,
    error: null,
    location: mockLocationData,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.2,
    step: 3,
    isSubmitting: false,
    verificationComplete: true,
    pickupNumber: "789123",
    imageFile: new File([""], "electronics_verification.jpg", {
      type: "image/jpeg",
    }),
    imagePreview: mockImagePreview,
  },
};

export const WeatherDelayScenario: Story = {
  args: {
    verification: {
      ...mockVerificationData,
      title: "Weather Delayed Pickup - Rescheduled",
      description: "Shipment pickup rescheduled due to weather conditions",
    },
    isLoadingVerification: false,
    error: null,
    location: mockLocationNearby,
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 1.5,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "654321",
    imageFile: null,
    imagePreview: null,
  },
};

export const EdgeCaseDistance: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: {
      latitude: 37.7751,
      longitude: -122.4196,
      accuracy: 25,
    },
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 1.99, // Just within the 2-mile radius
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};

export const PoorGPSAccuracy: Story = {
  args: {
    verification: mockVerificationData,
    isLoadingVerification: false,
    error: null,
    location: {
      latitude: 37.7749,
      longitude: -122.4194,
      accuracy: 150, // Poor GPS accuracy
    },
    isLoadingLocation: false,
    locationError: null,
    isInRadius: true,
    distanceToPickup: 0.1,
    step: 1,
    isSubmitting: false,
    verificationComplete: false,
    pickupNumber: "847362",
    imageFile: null,
    imagePreview: null,
  },
};
