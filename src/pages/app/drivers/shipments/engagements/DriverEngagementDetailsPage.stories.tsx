import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { DriverEngagementDetailsPage } from "./DriverEngagementDetailsPage";

const meta: Meta<typeof DriverEngagementDetailsPage> = {
  title: "Pages/Drivers/Shipments/EngagementDetails",
  component: DriverEngagementDetailsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { engagementId: "eng-001" },
      },
      routing: { path: "/drivers/shipments/engagements/:engagementId" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockDriver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  phone_number: "+**********",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active" as const,
  tier: "professional",
  created_at: new Date().toISOString(),
  verified_at: new Date().toISOString(),
};

const mockParticipants = [
  {
    id: "participant-1",
    name: "Sarah Martinez",
    role: "dispatcher" as const,
    avatar: undefined,
    phone: "(*************",
    email: "<EMAIL>",
    company: "QuikSkope Logistics",
  },
  {
    id: "participant-2",
    name: "Mike Johnson",
    role: "customer" as const,
    avatar: undefined,
    phone: "(*************",
    email: "<EMAIL>",
    company: "Walmart Distribution",
  },
  {
    id: "participant-3",
    name: "Alex Chen",
    role: "shipper" as const,
    avatar: undefined,
    phone: "(*************",
    email: "<EMAIL>",
    company: "Electronics Inc",
  },
];

const mockMessages = [
  {
    id: "msg-1",
    content: "Approaching the distribution center. ETA 15 minutes.",
    sender_id: "driver-123",
    sender_name: "John Smith",
    sender_role: "driver" as const,
    timestamp: new Date(Date.now() - 900000).toISOString(),
    urgent: false,
  },
  {
    id: "msg-2",
    content:
      "Perfect timing! Dock 15 is ready for you. Present your BOL at the security gate.",
    sender_id: "participant-2",
    sender_name: "Mike Johnson",
    sender_role: "customer" as const,
    timestamp: new Date(Date.now() - 600000).toISOString(),
    urgent: false,
  },
  {
    id: "msg-3",
    content: "At security checkpoint now. BOL presented.",
    sender_id: "driver-123",
    sender_name: "John Smith",
    sender_role: "driver" as const,
    timestamp: new Date(Date.now() - 300000).toISOString(),
    urgent: false,
  },
];

const mockUrgentMessages = [
  ...mockMessages,
  {
    id: "msg-4",
    content:
      "URGENT: Please verify item count before unloading. Customer reported discrepancy in previous shipment.",
    sender_id: "participant-1",
    sender_name: "Sarah Martinez",
    sender_role: "dispatcher" as const,
    timestamp: new Date(Date.now() - 120000).toISOString(),
    urgent: true,
  },
];

const mockWorkflowSteps = [
  {
    id: "step-1",
    title: "Arrive at pickup location",
    description: "Check in at the pickup location and verify shipment details",
    status: "completed" as const,
    required: true,
    estimated_duration: 15,
    completed_at: new Date(Date.now() - 7200000).toISOString(),
    notes: "Arrived on time. All items accounted for.",
  },
  {
    id: "step-2",
    title: "Load verification",
    description: "Verify item count and condition before departure",
    status: "completed" as const,
    required: true,
    estimated_duration: 20,
    completed_at: new Date(Date.now() - 6300000).toISOString(),
    notes: "25 boxes electronics. All items in good condition.",
  },
  {
    id: "step-3",
    title: "Route to destination",
    description: "Travel to delivery location following optimal route",
    status: "completed" as const,
    required: true,
    estimated_duration: 120,
    completed_at: new Date(Date.now() - 1800000).toISOString(),
  },
  {
    id: "step-4",
    title: "Arrival and check-in",
    description: "Check in at destination and present documentation",
    status: "in_progress" as const,
    required: true,
    estimated_duration: 10,
  },
  {
    id: "step-5",
    title: "Unload and verify",
    description: "Unload shipment and verify with customer",
    status: "pending" as const,
    required: true,
    estimated_duration: 30,
  },
  {
    id: "step-6",
    title: "Get delivery signature",
    description: "Obtain customer signature on delivery receipt",
    status: "pending" as const,
    required: true,
    estimated_duration: 5,
  },
  {
    id: "step-7",
    title: "Photo documentation",
    description: "Take photos of completed delivery",
    status: "pending" as const,
    required: true,
    estimated_duration: 5,
  },
];

const mockCompletedWorkflowSteps = mockWorkflowSteps.map((step) => ({
  ...step,
  status: "completed" as const,
  completed_at: new Date(Date.now() - Math.random() * 7200000).toISOString(),
}));

const mockDocuments = [
  {
    id: "doc-1",
    type: "bill_of_lading" as const,
    name: "Bill of Lading - Electronics Shipment",
    file_url: "/documents/bol-123.pdf",
    uploaded_at: new Date(Date.now() - 3600000).toISOString(),
    uploaded_by: "driver-123",
    required: true,
    status: "approved" as const,
  },
  {
    id: "doc-2",
    type: "delivery_receipt" as const,
    name: "Delivery Receipt",
    required: true,
    status: "pending" as const,
    uploaded_at: "",
    uploaded_by: "",
  },
  {
    id: "doc-3",
    type: "inspection_report" as const,
    name: "Pre-delivery Inspection",
    required: true,
    status: "uploaded" as const,
    uploaded_at: new Date(Date.now() - 1800000).toISOString(),
    uploaded_by: "driver-123",
  },
  {
    id: "doc-4",
    type: "photo" as const,
    name: "Delivery Photos",
    required: true,
    status: "pending" as const,
    uploaded_at: "",
    uploaded_by: "",
  },
];

const mockPendingDocuments = mockDocuments.map((doc) => ({
  ...doc,
  status: "pending" as const,
  uploaded_at: "",
  uploaded_by: "",
}));

const mockApprovedDocuments = mockDocuments.map((doc) => ({
  ...doc,
  status: "approved" as const,
  uploaded_at: new Date(Date.now() - Math.random() * 3600000).toISOString(),
  uploaded_by: "driver-123",
}));

const mockUpdates = [
  {
    id: "update-1",
    type: "status_change" as const,
    title: "Status updated to In Progress",
    description: "Driver has arrived at destination and checked in",
    timestamp: new Date(Date.now() - 1800000).toISOString(),
  },
  {
    id: "update-2",
    type: "location_update" as const,
    title: "Location updated",
    description: "Driver is now at Walmart Distribution Center",
    timestamp: new Date(Date.now() - 1500000).toISOString(),
  },
  {
    id: "update-3",
    type: "document_upload" as const,
    title: "Document uploaded",
    description: "Pre-delivery inspection report uploaded",
    timestamp: new Date(Date.now() - 1800000).toISOString(),
  },
];

const mockBaseEngagement = {
  id: "eng-001",
  shipment_id: "ship-123",
  title: "Delivery to Walmart Distribution Center",
  description:
    "Deliver electronics shipment to Walmart DC - Building 3, Dock 15",
  status: "in_progress" as const,
  priority: "high" as const,
  type: "delivery" as const,

  location: {
    address: "1234 Distribution Way, Dallas, TX 75201",
    coordinates: { lat: 32.7767, lng: -96.797 },
    contact_person: "Mike Johnson",
    contact_phone: "(*************",
    special_instructions:
      "Use loading dock 15. Security checkpoint required. Present BOL at gate.",
  },

  scheduled_start: new Date(Date.now() + 3600000).toISOString(),
  scheduled_end: new Date(Date.now() + 7200000).toISOString(),
  actual_start: new Date(Date.now() - 1800000).toISOString(),

  progress_percentage: 65,
  current_step: "Document verification",
  estimated_completion: new Date(Date.now() + 3600000).toISOString(),

  participants: mockParticipants,
  messages: mockMessages,
  required_documents: mockDocuments,
  completed_documents: [mockDocuments[0]],
  workflow_steps: mockWorkflowSteps,
  updates: mockUpdates,

  issues: [
    {
      id: "issue-1",
      title: "Item count verification required",
      description:
        "Customer requested additional verification due to previous shipment discrepancy",
      severity: "medium" as const,
      status: "open" as const,
      reported_at: new Date(Date.now() - 120000).toISOString(),
    },
  ],

  notes:
    "Customer is very particular about item verification. Take extra time to double-check counts.",
  created_at: new Date(Date.now() - 14400000).toISOString(),
  updated_at: new Date(Date.now() - 120000).toISOString(),
};

export const Default: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: mockBaseEngagement,
    isLoadingEngagement: false,
    engagementError: null,
    messages: mockMessages,
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    lastUpdateReceived: new Date().toISOString(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const Loading: Story = {
  args: {
    driver: mockDriver,
    isLoading: true,
    engagement: null,
    isLoadingEngagement: true,
    engagementError: null,
    messages: [],
    isLoadingMessages: true,
    messagesError: null,
    newMessage: "",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const ActiveEngagement: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: {
      ...mockBaseEngagement,
      status: "in_progress",
      progress_percentage: 75,
      current_step: "Unloading and verification",
      workflow_steps: mockWorkflowSteps.map((step, index) =>
        index <= 3
          ? {
              ...step,
              status: "completed" as const,
              completed_at: new Date(
                Date.now() - (4 - index) * 1800000,
              ).toISOString(),
            }
          : step,
      ),
    },
    isLoadingEngagement: false,
    engagementError: null,
    messages: mockUrgentMessages,
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "Confirmed. Starting item count verification now.",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    lastUpdateReceived: new Date(Date.now() - 30000).toISOString(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const CompletedEngagement: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: {
      ...mockBaseEngagement,
      status: "completed",
      progress_percentage: 100,
      actual_end: new Date(Date.now() - 3600000).toISOString(),
      workflow_steps: mockCompletedWorkflowSteps,
      required_documents: mockApprovedDocuments,
      completed_documents: mockApprovedDocuments,
      issues: [],
      notes:
        "Delivery completed successfully. Customer satisfied with condition and count.",
    },
    isLoadingEngagement: false,
    engagementError: null,
    messages: [
      ...mockMessages,
      {
        id: "msg-final",
        content:
          "Delivery completed successfully. All documents signed and photos uploaded.",
        sender_id: "driver-123",
        sender_name: "John Smith",
        sender_role: "driver" as const,
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        urgent: false,
      },
    ],
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const PendingActions: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: {
      ...mockBaseEngagement,
      status: "assigned",
      progress_percentage: 25,
      current_step: "Arrival and check-in",
      workflow_steps: mockWorkflowSteps.map((step, index) =>
        index <= 1
          ? {
              ...step,
              status: "completed" as const,
              completed_at: new Date(
                Date.now() - (2 - index) * 1800000,
              ).toISOString(),
            }
          : step,
      ),
      required_documents: mockPendingDocuments,
      issues: [
        {
          id: "issue-pending",
          title: "Action Required: Complete check-in process",
          description:
            "Driver needs to check in at the facility and obtain gate pass",
          severity: "high" as const,
          status: "open" as const,
          reported_at: new Date(Date.now() - 300000).toISOString(),
        },
      ],
    },
    isLoadingEngagement: false,
    engagementError: null,
    messages: [
      {
        id: "msg-action",
        content:
          "Please proceed to security gate for check-in. Present your driver's license and BOL.",
        sender_id: "participant-1",
        sender_name: "Sarah Martinez",
        sender_role: "dispatcher" as const,
        timestamp: new Date(Date.now() - 600000).toISOString(),
        urgent: true,
      },
    ],
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const CommunicationActive: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: mockBaseEngagement,
    isLoadingEngagement: false,
    engagementError: null,
    messages: [
      ...mockUrgentMessages,
      {
        id: "msg-active-1",
        content:
          "I'm ready to start verification. Where should I position the truck?",
        sender_id: "driver-123",
        sender_name: "John Smith",
        sender_role: "driver" as const,
        timestamp: new Date(Date.now() - 60000).toISOString(),
        urgent: false,
      },
      {
        id: "msg-active-2",
        content:
          "Perfect! Use bay 3 on the east side. Someone will meet you there.",
        sender_id: "participant-2",
        sender_name: "Mike Johnson",
        sender_role: "customer" as const,
        timestamp: new Date(Date.now() - 30000).toISOString(),
        urgent: false,
      },
    ],
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "Thanks! Heading to bay 3 now.",
    isSendingMessage: true,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    lastUpdateReceived: new Date(Date.now() - 30000).toISOString(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const DocumentsRequired: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: {
      ...mockBaseEngagement,
      required_documents: [
        ...mockPendingDocuments,
        {
          id: "doc-urgent",
          type: "signature" as const,
          name: "Customer Signature Required",
          required: true,
          status: "pending" as const,
          uploaded_at: "",
          uploaded_by: "",
        },
      ],
      issues: [
        {
          id: "issue-docs",
          title: "Missing required documents",
          description:
            "Several documents are pending upload before delivery can be completed",
          severity: "high" as const,
          status: "open" as const,
          reported_at: new Date(Date.now() - 180000).toISOString(),
        },
      ],
    },
    isLoadingEngagement: false,
    engagementError: null,
    messages: [
      {
        id: "msg-docs",
        content:
          "Please upload the delivery receipt and inspection report before proceeding with unloading.",
        sender_id: "participant-1",
        sender_name: "Sarah Martinez",
        sender_role: "dispatcher" as const,
        timestamp: new Date(Date.now() - 180000).toISOString(),
        urgent: true,
      },
    ],
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "",
    isSendingMessage: false,
    uploadingDocuments: ["doc-2", "doc-3"],
    documentUploadProgress: {
      "doc-2": 45,
      "doc-3": 78,
    },
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const PhotoVerification: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: {
      ...mockBaseEngagement,
      workflow_steps: mockWorkflowSteps.map((step, index) =>
        index <= 5
          ? {
              ...step,
              status: "completed" as const,
              completed_at: new Date(
                Date.now() - (6 - index) * 600000,
              ).toISOString(),
            }
          : step,
      ),
      progress_percentage: 90,
      current_step: "Photo documentation",
    },
    isLoadingEngagement: false,
    engagementError: null,
    messages: mockMessages,
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [
      {
        id: "photo-1",
        type: "delivery_photo",
        file: new File([""], "delivery.jpg", { type: "image/jpeg" }),
        preview_url:
          "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAZABkAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiUpLTE1OT1BRUlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigD/9k=",
      },
      {
        id: "photo-2",
        type: "truck_condition",
        file: new File([""], "truck.jpg", { type: "image/jpeg" }),
        preview_url:
          "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAZABkAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiUpLTE1OT1BRUlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigD/9k=",
      },
    ],
    isCapturingPhoto: false,
    isOnline: true,
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const IssueReported: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: {
      ...mockBaseEngagement,
      status: "delayed",
      issues: [
        {
          id: "issue-critical",
          title: "Truck mechanical issue",
          description:
            "Engine warning light came on during transit. Need roadside assistance.",
          severity: "critical" as const,
          status: "investigating" as const,
          reported_at: new Date(Date.now() - 900000).toISOString(),
        },
        {
          id: "issue-medium",
          title: "Delivery address access issue",
          description:
            "Security gate is closed and no one is responding to calls",
          severity: "medium" as const,
          status: "open" as const,
          reported_at: new Date(Date.now() - 600000).toISOString(),
        },
        {
          id: "issue-resolved",
          title: "Customer contact delay",
          description: "Initial difficulty reaching customer contact person",
          severity: "low" as const,
          status: "resolved" as const,
          reported_at: new Date(Date.now() - 1800000).toISOString(),
          resolved_at: new Date(Date.now() - 1200000).toISOString(),
        },
      ],
      notes:
        "Multiple issues encountered. Waiting for roadside assistance and customer contact.",
    },
    isLoadingEngagement: false,
    engagementError: null,
    messages: [
      ...mockMessages,
      {
        id: "msg-issue",
        content:
          "Roadside assistance has been notified. ETA 30 minutes. Stay with the vehicle.",
        sender_id: "participant-1",
        sender_name: "Sarah Martinez",
        sender_role: "dispatcher" as const,
        timestamp: new Date(Date.now() - 600000).toISOString(),
        urgent: true,
      },
    ],
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};

export const HighPriority: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    engagement: {
      ...mockBaseEngagement,
      priority: "urgent",
      title: "URGENT: Medical Supply Delivery",
      description:
        "Time-critical medical supplies for hospital emergency department",
      type: "delivery",
      location: {
        ...mockBaseEngagement.location,
        address:
          "Dallas Memorial Hospital, 789 Medical Center Dr, Dallas, TX 75235",
        contact_person: "Dr. Sarah Williams",
        contact_phone: "(*************",
        special_instructions:
          "URGENT DELIVERY - Go directly to Emergency Department receiving dock. Call ahead.",
      },
      scheduled_end: new Date(Date.now() + 1800000).toISOString(), // 30 minutes from now
      estimated_completion: new Date(Date.now() + 1200000).toISOString(), // 20 minutes from now
      issues: [
        {
          id: "issue-time",
          title: "Time-critical delivery",
          description:
            "Medical supplies needed urgently for emergency patient care",
          severity: "critical" as const,
          status: "open" as const,
          reported_at: new Date(Date.now() - 3600000).toISOString(),
        },
      ],
    },
    isLoadingEngagement: false,
    engagementError: null,
    messages: [
      {
        id: "msg-urgent-1",
        content:
          "PRIORITY: This is a time-critical medical delivery. Hospital is expecting you ASAP.",
        sender_id: "participant-1",
        sender_name: "Sarah Martinez",
        sender_role: "dispatcher" as const,
        timestamp: new Date(Date.now() - 300000).toISOString(),
        urgent: true,
      },
      {
        id: "msg-urgent-2",
        content:
          "ETA 15 minutes. Taking fastest route. Will call hospital on arrival.",
        sender_id: "driver-123",
        sender_name: "John Smith",
        sender_role: "driver" as const,
        timestamp: new Date(Date.now() - 180000).toISOString(),
        urgent: false,
      },
      {
        id: "msg-urgent-3",
        content:
          "Perfect. Dr. Williams will meet you at the emergency dock. Use entrance C.",
        sender_id: "participant-2",
        sender_name: "Dr. Sarah Williams",
        sender_role: "customer" as const,
        timestamp: new Date(Date.now() - 120000).toISOString(),
        urgent: true,
      },
    ],
    isLoadingMessages: false,
    messagesError: null,
    newMessage: "Arriving at entrance C now.",
    isSendingMessage: false,
    uploadingDocuments: [],
    documentUploadProgress: {},
    capturedPhotos: [],
    isCapturingPhoto: false,
    isOnline: true,
    lastUpdateReceived: new Date(Date.now() - 60000).toISOString(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onUploadDocument: fn(),
    onCapturePhoto: fn(),
    onRetakePhoto: fn(),
    onSubmitPhotos: fn(),
    onUpdateStatus: fn(),
    onCompleteStep: fn(),
    onReportIssue: fn(),
    onCallParticipant: fn(),
    onUpdateNotes: fn(),
    onRequestHelp: fn(),
    onMarkMessageRead: fn(),
  },
};
