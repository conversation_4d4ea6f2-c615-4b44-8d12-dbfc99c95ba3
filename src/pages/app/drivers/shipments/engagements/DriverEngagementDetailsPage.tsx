import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Image,
  MapPin,
  MessageCircle,
  Package,
  Phone,
  Truck,
  Upload,
  User,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { UserContextType } from "@/contexts/User";

export interface EngagementParticipant {
  id: string;
  name: string;
  role: "driver" | "dispatcher" | "customer" | "shipper";
  avatar?: string;
  phone?: string;
  email?: string;
  company?: string;
}

export interface EngagementDocument {
  id: string;
  type:
    | "bill_of_lading"
    | "delivery_receipt"
    | "inspection_report"
    | "photo"
    | "signature";
  name: string;
  file_url?: string;
  uploaded_at: string;
  uploaded_by: string;
  required: boolean;
  status: "pending" | "uploaded" | "approved" | "rejected";
}

export interface EngagementMessage {
  id: string;
  content: string;
  sender_id: string;
  sender_name: string;
  sender_role: "driver" | "dispatcher" | "customer" | "shipper";
  timestamp: string;
  urgent: boolean;
  attachments?: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
  }>;
}

export interface EngagementStep {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in_progress" | "completed" | "skipped";
  required: boolean;
  estimated_duration?: number;
  completed_at?: string;
  notes?: string;
}

export interface EngagementUpdate {
  id: string;
  type:
    | "status_change"
    | "location_update"
    | "document_upload"
    | "message"
    | "issue_reported";
  title: string;
  description: string;
  timestamp: string;
  data?: Record<string, any>;
}

export interface EngagementDetails {
  id: string;
  shipment_id: string;
  title: string;
  description: string;
  status: "assigned" | "in_progress" | "completed" | "delayed" | "cancelled";
  priority: "low" | "normal" | "high" | "urgent";
  type: "pickup" | "delivery" | "inspection" | "maintenance";

  // Location and timing
  location: {
    address: string;
    coordinates?: { lat: number; lng: number };
    contact_person?: string;
    contact_phone?: string;
    special_instructions?: string;
  };
  scheduled_start: string;
  scheduled_end: string;
  actual_start?: string;
  actual_end?: string;

  // Progress tracking
  progress_percentage: number;
  current_step?: string;
  estimated_completion?: string;

  // Participants and communication
  participants: EngagementParticipant[];
  messages: EngagementMessage[];

  // Documents and verification
  required_documents: EngagementDocument[];
  completed_documents: EngagementDocument[];

  // Workflow steps
  workflow_steps: EngagementStep[];

  // Updates and history
  updates: EngagementUpdate[];

  // Issues and notes
  issues: Array<{
    id: string;
    title: string;
    description: string;
    severity: "low" | "medium" | "high" | "critical";
    status: "open" | "investigating" | "resolved";
    reported_at: string;
    resolved_at?: string;
  }>;
  notes: string;

  created_at: string;
  updated_at: string;
}

export interface DriverEngagementDetailsPageProps {
  // User data
  driver: UserContextType["driver"];
  isLoading: UserContextType["isLoading"];

  // Engagement data
  engagement: EngagementDetails | null;
  isLoadingEngagement: boolean;
  engagementError: Error | null;

  // Communication data
  messages: EngagementMessage[];
  isLoadingMessages: boolean;
  messagesError: Error | null;
  newMessage: string;
  isSendingMessage: boolean;

  // Document management
  uploadingDocuments: string[];
  documentUploadProgress: Record<string, number>;

  // Photo verification
  capturedPhotos: Array<{
    id: string;
    type: string;
    file: File;
    preview_url: string;
  }>;
  isCapturingPhoto: boolean;

  // Real-time updates
  isOnline: boolean;
  lastUpdateReceived?: string;

  // Function handlers
  onSendMessage: () => void;
  onUpdateMessage: (message: string) => void;
  onUploadDocument: (type: string, file: File) => void;
  onCapturePhoto: (type: string) => void;
  onRetakePhoto: (photoId: string) => void;
  onSubmitPhotos: () => void;
  onUpdateStatus: (status: EngagementDetails["status"]) => void;
  onCompleteStep: (stepId: string, notes?: string) => void;
  onReportIssue: (issue: {
    title: string;
    description: string;
    severity: string;
  }) => void;
  onCallParticipant: (participantId: string) => void;
  onUpdateNotes: (notes: string) => void;
  onRequestHelp: () => void;
  onMarkMessageRead: (messageId: string) => void;
}

export const DriverEngagementDetailsPage = ({
  driver,
  isLoading,
  engagement,
  isLoadingEngagement,
  engagementError,
  messages,
  isLoadingMessages,
  messagesError,
  newMessage,
  isSendingMessage,
  uploadingDocuments,
  documentUploadProgress,
  capturedPhotos,
  isCapturingPhoto,
  isOnline,
  lastUpdateReceived,
  onSendMessage,
  onUpdateMessage,
  onUploadDocument,
  onCapturePhoto,
  onRetakePhoto,
  onSubmitPhotos,
  onUpdateStatus,
  onCompleteStep,
  onReportIssue,
  onCallParticipant,
  onUpdateNotes,
  onRequestHelp,
  onMarkMessageRead,
}: DriverEngagementDetailsPageProps) => {
  if (isLoading || isLoadingEngagement) {
    return (
      <div className="container py-8">
        <div className="space-y-6">
          {/* Header skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>

          {/* Main content skeleton */}
          <div className="grid gap-6 lg:grid-cols-[2fr_1fr]">
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-24 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="space-y-6">
              {[...Array(2)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-6 w-24" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-32 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!driver) {
    return (
      <div className="container py-8">
        <Card className="mx-auto max-w-2xl">
          <CardContent className="flex flex-col items-center px-8 pt-6 pb-8 text-center">
            <User className="text-muted-foreground mb-4 h-12 w-12" />
            <h2 className="mb-2 text-2xl font-semibold">
              Driver Profile Required
            </h2>
            <p className="text-muted-foreground mb-6">
              You need a driver profile to access engagement details.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (engagementError || !engagement) {
    return (
      <div className="container py-8">
        <Card className="mx-auto max-w-2xl">
          <CardContent className="flex flex-col items-center px-8 pt-6 pb-8 text-center">
            <AlertTriangle className="text-destructive mb-4 h-12 w-12" />
            <h2 className="mb-2 text-2xl font-semibold">
              {engagementError
                ? "Failed to Load Engagement"
                : "Engagement Not Found"}
            </h2>
            <p className="text-muted-foreground mb-6">
              {engagementError?.message ||
                "The requested engagement could not be found."}
            </p>
            <Button variant="outline" onClick={() => window.history.back()}>
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500";
      case "in_progress":
        return "bg-blue-500";
      case "assigned":
        return "bg-yellow-500";
      case "delayed":
        return "bg-orange-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "destructive";
      case "high":
        return "secondary";
      case "normal":
        return "outline";
      case "low":
        return "outline";
      default:
        return "outline";
    }
  };

  const getStepIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "in_progress":
        return <Clock className="h-5 w-5 text-blue-600" />;
      case "pending":
        return <Clock className="h-5 w-5 text-gray-400" />;
      case "skipped":
        return <AlertTriangle className="h-5 w-5 text-orange-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-3">
            <h1 className="text-3xl font-bold">{engagement.title}</h1>
            <Badge
              variant={getPriorityColor(engagement.priority)}
              className="capitalize"
            >
              {engagement.priority}
            </Badge>
            <div
              className={`h-2 w-2 rounded-full ${getStatusColor(engagement.status)}`}
            />
            <span className="text-sm font-medium capitalize">
              {engagement.status.replace("_", " ")}
            </span>
          </div>
          <p className="text-muted-foreground mt-1">{engagement.description}</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <div
              className={`h-2 w-2 rounded-full ${isOnline ? "bg-green-500" : "bg-red-500"}`}
            />
            <span className="text-sm font-medium">
              {isOnline ? "Online" : "Offline"}
            </span>
          </div>
          <Button onClick={onRequestHelp} variant="outline" size="sm">
            <AlertTriangle className="mr-2 h-4 w-4" />
            Request Help
          </Button>
        </div>
      </div>

      {/* Progress Bar */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="mb-2 flex items-center justify-between">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-muted-foreground text-sm">
              {engagement.progress_percentage}%
            </span>
          </div>
          <Progress value={engagement.progress_percentage} className="h-2" />
          {engagement.estimated_completion && (
            <p className="text-muted-foreground mt-2 text-xs">
              Estimated completion:{" "}
              {new Date(engagement.estimated_completion).toLocaleString()}
            </p>
          )}
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-[2fr_1fr]">
        {/* Left Column - Main Content */}
        <div className="space-y-6">
          {/* Location and Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="mr-2 h-5 w-5" />
                Location & Schedule
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="font-medium">{engagement.location.address}</p>
                  {engagement.location.contact_person && (
                    <p className="text-muted-foreground text-sm">
                      Contact: {engagement.location.contact_person}
                      {engagement.location.contact_phone && (
                        <span> • {engagement.location.contact_phone}</span>
                      )}
                    </p>
                  )}
                  {engagement.location.special_instructions && (
                    <p className="mt-2 text-sm text-orange-600">
                      <AlertTriangle className="mr-1 inline h-4 w-4" />
                      {engagement.location.special_instructions}
                    </p>
                  )}
                </div>
                <Separator />
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <div className="mb-1 flex items-center text-sm font-medium">
                      <Calendar className="mr-2 h-4 w-4" />
                      Scheduled Start
                    </div>
                    <p className="text-muted-foreground text-sm">
                      {new Date(engagement.scheduled_start).toLocaleString()}
                    </p>
                    {engagement.actual_start && (
                      <p className="mt-1 text-xs text-green-600">
                        Started:{" "}
                        {new Date(engagement.actual_start).toLocaleString()}
                      </p>
                    )}
                  </div>
                  <div>
                    <div className="mb-1 flex items-center text-sm font-medium">
                      <Clock className="mr-2 h-4 w-4" />
                      Scheduled End
                    </div>
                    <p className="text-muted-foreground text-sm">
                      {new Date(engagement.scheduled_end).toLocaleString()}
                    </p>
                    {engagement.actual_end && (
                      <p className="mt-1 text-xs text-green-600">
                        Completed:{" "}
                        {new Date(engagement.actual_end).toLocaleString()}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Workflow Steps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="mr-2 h-5 w-5" />
                Workflow Steps
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {engagement.workflow_steps.map((step, index) => (
                  <div key={step.id} className="flex items-start space-x-3">
                    <div className="mt-1 flex-shrink-0">
                      {getStepIcon(step.status)}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <p
                          className={`font-medium ${step.status === "completed" ? "text-green-600" : ""}`}
                        >
                          {step.title}
                          {step.required && (
                            <span className="ml-1 text-red-500">*</span>
                          )}
                        </p>
                        {step.status === "pending" && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onCompleteStep(step.id)}
                          >
                            Mark Complete
                          </Button>
                        )}
                      </div>
                      <p className="text-muted-foreground mt-1 text-sm">
                        {step.description}
                      </p>
                      {step.estimated_duration && (
                        <p className="text-muted-foreground mt-1 text-xs">
                          Estimated duration: {step.estimated_duration} minutes
                        </p>
                      )}
                      {step.completed_at && (
                        <p className="mt-1 text-xs text-green-600">
                          Completed:{" "}
                          {new Date(step.completed_at).toLocaleString()}
                        </p>
                      )}
                      {step.notes && (
                        <p className="mt-2 rounded bg-gray-50 p-2 text-sm">
                          Notes: {step.notes}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Required Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {engagement.required_documents.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center justify-between rounded-lg border p-3"
                  >
                    <div className="flex items-center space-x-3">
                      <FileText className="text-muted-foreground h-5 w-5" />
                      <div>
                        <p className="font-medium">{doc.name}</p>
                        <p className="text-muted-foreground text-sm capitalize">
                          {doc.type.replace("_", " ")}
                          {doc.required && (
                            <span className="ml-1 text-red-500">*</span>
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={
                          doc.status === "approved"
                            ? "default"
                            : doc.status === "uploaded"
                              ? "secondary"
                              : doc.status === "rejected"
                                ? "destructive"
                                : "outline"
                        }
                        className="capitalize"
                      >
                        {doc.status}
                      </Badge>
                      {doc.status === "pending" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const input = document.createElement("input");
                            input.type = "file";
                            input.accept = "image/*,application/pdf";
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement)
                                .files?.[0];
                              if (file) {
                                onUploadDocument(doc.type, file);
                              }
                            };
                            input.click();
                          }}
                          disabled={uploadingDocuments.includes(doc.id)}
                        >
                          {uploadingDocuments.includes(doc.id) ? (
                            <>
                              <Clock className="mr-2 h-4 w-4 animate-spin" />
                              Uploading...
                            </>
                          ) : (
                            <>
                              <Upload className="mr-2 h-4 w-4" />
                              Upload
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Photo Verification */}
          {capturedPhotos.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Image className="mr-2 h-5 w-5" />
                  Photo Verification
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {capturedPhotos.map((photo) => (
                    <div key={photo.id} className="space-y-2">
                      <img
                        src={photo.preview_url}
                        alt={photo.type}
                        className="h-32 w-full rounded-lg object-cover"
                      />
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium capitalize">
                          {photo.type.replace("_", " ")}
                        </p>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onRetakePhoto(photo.id)}
                        >
                          Retake
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 flex space-x-2">
                  <Button onClick={onSubmitPhotos} className="flex-1">
                    Submit Photos
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => onCapturePhoto("delivery_photo")}
                    disabled={isCapturingPhoto}
                  >
                    <Image className="mr-2 h-4 w-4" />
                    Add Photo
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Communication and Participants */}
        <div className="space-y-6">
          {/* Participants */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Participants
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {engagement.participants.map((participant) => (
                  <div
                    key={participant.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={participant.avatar} />
                        <AvatarFallback>
                          {participant.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">
                          {participant.name}
                        </p>
                        <p className="text-muted-foreground text-xs capitalize">
                          {participant.role}
                          {participant.company && ` • ${participant.company}`}
                        </p>
                      </div>
                    </div>
                    {participant.phone && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onCallParticipant(participant.id)}
                      >
                        <Phone className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Messages */}
          <Card className="flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageCircle className="mr-2 h-5 w-5" />
                Messages
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1">
              <ScrollArea className="h-64 w-full pr-4">
                {isLoadingMessages ? (
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-12 w-full" />
                      </div>
                    ))}
                  </div>
                ) : messagesError ? (
                  <div className="py-4 text-center">
                    <AlertTriangle className="text-destructive mx-auto mb-2 h-8 w-8" />
                    <p className="text-destructive text-sm">
                      Failed to load messages
                    </p>
                  </div>
                ) : messages.length === 0 ? (
                  <div className="py-8 text-center">
                    <MessageCircle className="text-muted-foreground mx-auto mb-4 h-8 w-8" />
                    <p className="text-muted-foreground text-sm">
                      No messages yet
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          message.sender_role === "driver"
                            ? "justify-end"
                            : "justify-start"
                        }`}
                      >
                        <div
                          className={`max-w-xs rounded-lg px-3 py-2 ${
                            message.sender_role === "driver"
                              ? "bg-primary text-primary-foreground"
                              : "bg-accent text-accent-foreground"
                          } ${message.urgent ? "border-2 border-red-500" : ""}`}
                        >
                          <div className="mb-1 flex items-center justify-between">
                            <p className="text-xs font-medium">
                              {message.sender_name}
                            </p>
                            {message.urgent && (
                              <AlertTriangle className="h-3 w-3 text-red-500" />
                            )}
                          </div>
                          <p className="text-sm">{message.content}</p>
                          <p className="mt-1 text-xs opacity-70">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
              <Separator className="my-4" />
              <div className="flex space-x-2">
                <Input
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => onUpdateMessage(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      onSendMessage();
                    }
                  }}
                  disabled={isSendingMessage}
                />
                <Button
                  onClick={onSendMessage}
                  disabled={!newMessage.trim() || isSendingMessage}
                  size="sm"
                >
                  <MessageCircle className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Issues */}
          {engagement.issues.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="mr-2 h-5 w-5" />
                  Reported Issues
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {engagement.issues.map((issue) => (
                    <div key={issue.id} className="rounded-lg border p-3">
                      <div className="mb-2 flex items-center justify-between">
                        <p className="text-sm font-medium">{issue.title}</p>
                        <Badge
                          variant={
                            issue.severity === "critical"
                              ? "destructive"
                              : issue.severity === "high"
                                ? "secondary"
                                : "outline"
                          }
                          className="capitalize"
                        >
                          {issue.severity}
                        </Badge>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        {issue.description}
                      </p>
                      <p className="text-muted-foreground mt-2 text-xs">
                        Reported: {new Date(issue.reported_at).toLocaleString()}
                      </p>
                      {issue.status === "resolved" && issue.resolved_at && (
                        <p className="mt-1 text-xs text-green-600">
                          Resolved:{" "}
                          {new Date(issue.resolved_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Add notes about this engagement..."
                value={engagement.notes}
                onChange={(e) => onUpdateNotes(e.target.value)}
                rows={4}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Updates Timeline */}
      {engagement.updates.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              Recent Updates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {engagement.updates.slice(0, 5).map((update) => (
                <div key={update.id} className="flex items-start space-x-3">
                  <div className="mt-1 flex-shrink-0">
                    <div className="h-2 w-2 rounded-full bg-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{update.title}</p>
                    <p className="text-muted-foreground text-sm">
                      {update.description}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      {new Date(update.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
