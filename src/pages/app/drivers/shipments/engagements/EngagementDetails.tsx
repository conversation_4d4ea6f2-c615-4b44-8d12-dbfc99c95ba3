import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router";
import { toast } from "sonner";

import { useUser } from "@/contexts/User";
import {
  DriverEngagementDetailsPage,
  EngagementDetails as EngagementDetailsType,
  EngagementDocument,
  EngagementMessage,
  EngagementParticipant,
  EngagementStep,
  EngagementUpdate,
} from "./DriverEngagementDetailsPage";

// Mock data for development
const mockEngagement: EngagementDetailsType = {
  id: "eng-001",
  shipment_id: "ship-123",
  title: "Delivery to Walmart Distribution Center",
  description:
    "Deliver electronics shipment to Walmart DC - Building 3, Dock 15",
  status: "in_progress",
  priority: "high",
  type: "delivery",

  location: {
    address: "1234 Distribution Way, Dallas, TX 75201",
    coordinates: { lat: 32.7767, lng: -96.797 },
    contact_person: "<PERSON>",
    contact_phone: "(*************",
    special_instructions:
      "Use loading dock 15. Security checkpoint required. Present BOL at gate.",
  },

  scheduled_start: new Date(Date.now() + 3600000).toISOString(),
  scheduled_end: new Date(Date.now() + 7200000).toISOString(),
  actual_start: new Date(Date.now() - 1800000).toISOString(),

  progress_percentage: 65,
  current_step: "Document verification",
  estimated_completion: new Date(Date.now() + 3600000).toISOString(),

  participants: [
    {
      id: "participant-1",
      name: "Sarah Martinez",
      role: "dispatcher",
      avatar: undefined,
      phone: "(*************",
      email: "<EMAIL>",
      company: "QuikSkope Logistics",
    },
    {
      id: "participant-2",
      name: "Mike Johnson",
      role: "customer",
      avatar: undefined,
      phone: "(*************",
      email: "<EMAIL>",
      company: "Walmart Distribution",
    },
    {
      id: "participant-3",
      name: "Alex Chen",
      role: "shipper",
      avatar: undefined,
      phone: "(*************",
      email: "<EMAIL>",
      company: "Electronics Inc",
    },
  ],

  messages: [
    {
      id: "msg-1",
      content: "Approaching the distribution center. ETA 15 minutes.",
      sender_id: "driver-123",
      sender_name: "John Smith",
      sender_role: "driver",
      timestamp: new Date(Date.now() - 900000).toISOString(),
      urgent: false,
    },
    {
      id: "msg-2",
      content:
        "Perfect timing! Dock 15 is ready for you. Present your BOL at the security gate.",
      sender_id: "participant-2",
      sender_name: "Mike Johnson",
      sender_role: "customer",
      timestamp: new Date(Date.now() - 600000).toISOString(),
      urgent: false,
    },
    {
      id: "msg-3",
      content: "At security checkpoint now. BOL presented.",
      sender_id: "driver-123",
      sender_name: "John Smith",
      sender_role: "driver",
      timestamp: new Date(Date.now() - 300000).toISOString(),
      urgent: false,
    },
    {
      id: "msg-4",
      content:
        "URGENT: Please verify item count before unloading. Customer reported discrepancy in previous shipment.",
      sender_id: "participant-1",
      sender_name: "Sarah Martinez",
      sender_role: "dispatcher",
      timestamp: new Date(Date.now() - 120000).toISOString(),
      urgent: true,
    },
  ],

  required_documents: [
    {
      id: "doc-1",
      type: "bill_of_lading",
      name: "Bill of Lading - Electronics Shipment",
      file_url: "/documents/bol-123.pdf",
      uploaded_at: new Date(Date.now() - 3600000).toISOString(),
      uploaded_by: "driver-123",
      required: true,
      status: "approved",
    },
    {
      id: "doc-2",
      type: "delivery_receipt",
      name: "Delivery Receipt",
      required: true,
      status: "pending",
      uploaded_at: "",
      uploaded_by: "",
    },
    {
      id: "doc-3",
      type: "inspection_report",
      name: "Pre-delivery Inspection",
      required: true,
      status: "uploaded",
      uploaded_at: new Date(Date.now() - 1800000).toISOString(),
      uploaded_by: "driver-123",
    },
    {
      id: "doc-4",
      type: "photo",
      name: "Delivery Photos",
      required: true,
      status: "pending",
      uploaded_at: "",
      uploaded_by: "",
    },
  ],

  completed_documents: [
    {
      id: "doc-1",
      type: "bill_of_lading",
      name: "Bill of Lading - Electronics Shipment",
      file_url: "/documents/bol-123.pdf",
      uploaded_at: new Date(Date.now() - 3600000).toISOString(),
      uploaded_by: "driver-123",
      required: true,
      status: "approved",
    },
  ],

  workflow_steps: [
    {
      id: "step-1",
      title: "Arrive at pickup location",
      description:
        "Check in at the pickup location and verify shipment details",
      status: "completed",
      required: true,
      estimated_duration: 15,
      completed_at: new Date(Date.now() - 7200000).toISOString(),
      notes: "Arrived on time. All items accounted for.",
    },
    {
      id: "step-2",
      title: "Load verification",
      description: "Verify item count and condition before departure",
      status: "completed",
      required: true,
      estimated_duration: 20,
      completed_at: new Date(Date.now() - 6300000).toISOString(),
      notes: "25 boxes electronics. All items in good condition.",
    },
    {
      id: "step-3",
      title: "Route to destination",
      description: "Travel to delivery location following optimal route",
      status: "completed",
      required: true,
      estimated_duration: 120,
      completed_at: new Date(Date.now() - 1800000).toISOString(),
    },
    {
      id: "step-4",
      title: "Arrival and check-in",
      description: "Check in at destination and present documentation",
      status: "in_progress",
      required: true,
      estimated_duration: 10,
    },
    {
      id: "step-5",
      title: "Unload and verify",
      description: "Unload shipment and verify with customer",
      status: "pending",
      required: true,
      estimated_duration: 30,
    },
    {
      id: "step-6",
      title: "Get delivery signature",
      description: "Obtain customer signature on delivery receipt",
      status: "pending",
      required: true,
      estimated_duration: 5,
    },
    {
      id: "step-7",
      title: "Photo documentation",
      description: "Take photos of completed delivery",
      status: "pending",
      required: true,
      estimated_duration: 5,
    },
  ],

  updates: [
    {
      id: "update-1",
      type: "status_change",
      title: "Status updated to In Progress",
      description: "Driver has arrived at destination and checked in",
      timestamp: new Date(Date.now() - 1800000).toISOString(),
    },
    {
      id: "update-2",
      type: "location_update",
      title: "Location updated",
      description: "Driver is now at Walmart Distribution Center",
      timestamp: new Date(Date.now() - 1500000).toISOString(),
    },
    {
      id: "update-3",
      type: "document_upload",
      title: "Document uploaded",
      description: "Pre-delivery inspection report uploaded",
      timestamp: new Date(Date.now() - 1800000).toISOString(),
    },
    {
      id: "update-4",
      type: "message",
      title: "New urgent message",
      description: "Dispatcher sent urgent verification request",
      timestamp: new Date(Date.now() - 120000).toISOString(),
    },
  ],

  issues: [
    {
      id: "issue-1",
      title: "Item count verification required",
      description:
        "Customer requested additional verification due to previous shipment discrepancy",
      severity: "medium",
      status: "open",
      reported_at: new Date(Date.now() - 120000).toISOString(),
    },
  ],

  notes:
    "Customer is very particular about item verification. Take extra time to double-check counts.",
  created_at: new Date(Date.now() - 14400000).toISOString(),
  updated_at: new Date(Date.now() - 120000).toISOString(),
};

const mockCompletedEngagement: EngagementDetailsType = {
  ...mockEngagement,
  id: "eng-002",
  title: "Completed Delivery - Electronics Plus",
  status: "completed",
  progress_percentage: 100,
  actual_end: new Date(Date.now() - 3600000).toISOString(),
  workflow_steps: mockEngagement.workflow_steps.map((step) => ({
    ...step,
    status: "completed" as const,
    completed_at: new Date(Date.now() - Math.random() * 7200000).toISOString(),
  })),
  required_documents: mockEngagement.required_documents.map((doc) => ({
    ...doc,
    status: "approved" as const,
    uploaded_at: new Date(Date.now() - Math.random() * 7200000).toISOString(),
    uploaded_by: "driver-123",
  })),
  issues: [],
};

export default function EngagementDetails() {
  const { engagementId } = useParams<{ engagementId: string }>();
  const { driver, isLoading: isUserLoading } = useUser();

  // State management
  const [engagement, setEngagement] = useState<EngagementDetailsType | null>(
    null,
  );
  const [isLoadingEngagement, setIsLoadingEngagement] = useState(true);
  const [engagementError, setEngagementError] = useState<Error | null>(null);

  const [messages, setMessages] = useState<EngagementMessage[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [messagesError, setMessagesError] = useState<Error | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [isSendingMessage, setIsSendingMessage] = useState(false);

  const [uploadingDocuments, setUploadingDocuments] = useState<string[]>([]);
  const [documentUploadProgress, setDocumentUploadProgress] = useState<
    Record<string, number>
  >({});

  const [capturedPhotos, setCapturedPhotos] = useState<
    Array<{
      id: string;
      type: string;
      file: File;
      preview_url: string;
    }>
  >([]);
  const [isCapturingPhoto, setIsCapturingPhoto] = useState(false);

  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastUpdateReceived, setLastUpdateReceived] = useState<string>();

  // Load engagement data
  useEffect(() => {
    if (!engagementId) {
      setEngagementError(new Error("Engagement ID is required"));
      setIsLoadingEngagement(false);
      return;
    }

    setIsLoadingEngagement(true);
    setEngagementError(null);

    // Simulate API call
    setTimeout(() => {
      try {
        // Mock different engagement scenarios based on ID
        let selectedEngagement = mockEngagement;
        if (engagementId === "completed") {
          selectedEngagement = mockCompletedEngagement;
        } else if (engagementId === "error") {
          throw new Error("Failed to load engagement");
        }

        setEngagement(selectedEngagement);
        setMessages(selectedEngagement.messages);
        setIsLoadingEngagement(false);
      } catch (error) {
        setEngagementError(
          error instanceof Error ? error : new Error("Unknown error"),
        );
        setIsLoadingEngagement(false);
      }
    }, 1000);
  }, [engagementId]);

  // Real-time updates simulation
  useEffect(() => {
    if (!engagement) return;

    const interval = setInterval(() => {
      if (Math.random() > 0.95) {
        // 5% chance of update
        const updateTypes = ["message", "location_update", "document_upload"];
        const randomType =
          updateTypes[Math.floor(Math.random() * updateTypes.length)];

        setLastUpdateReceived(new Date().toISOString());

        if (randomType === "message" && Math.random() > 0.7) {
          const newMsg: EngagementMessage = {
            id: `msg-${Date.now()}`,
            content: "Status update received",
            sender_id: "system",
            sender_name: "System",
            sender_role: "dispatcher",
            timestamp: new Date().toISOString(),
            urgent: false,
          };
          setMessages((prev) => [...prev, newMsg]);
        }
      }
    }, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, [engagement]);

  // Connection status monitoring
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Event handlers
  const handleSendMessage = useCallback(() => {
    if (!newMessage.trim() || isSendingMessage || !engagement) return;

    setIsSendingMessage(true);

    // Simulate sending message
    setTimeout(() => {
      const message: EngagementMessage = {
        id: `msg-${Date.now()}`,
        content: newMessage.trim(),
        sender_id: driver?.id || "driver-123",
        sender_name: driver
          ? `${driver.first_name} ${driver.last_name}`
          : "John Smith",
        sender_role: "driver",
        timestamp: new Date().toISOString(),
        urgent: false,
      };

      setMessages((prev) => [...prev, message]);
      setNewMessage("");
      setIsSendingMessage(false);
      toast.success("Message sent");
    }, 500);
  }, [newMessage, isSendingMessage, engagement, driver]);

  const handleUpdateMessage = useCallback((message: string) => {
    setNewMessage(message);
  }, []);

  const handleUploadDocument = useCallback(
    (type: string, file: File) => {
      if (!engagement) return;

      const docId = `upload-${Date.now()}`;
      setUploadingDocuments((prev) => [...prev, docId]);
      setDocumentUploadProgress((prev) => ({ ...prev, [docId]: 0 }));

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setDocumentUploadProgress((prev) => {
          const currentProgress = prev[docId] || 0;
          const newProgress = Math.min(
            currentProgress + Math.random() * 30,
            100,
          );

          if (newProgress >= 100) {
            clearInterval(progressInterval);
            setUploadingDocuments((prev) => prev.filter((id) => id !== docId));
            toast.success("Document uploaded successfully");

            // Update engagement documents
            setEngagement((prev) => {
              if (!prev) return prev;
              return {
                ...prev,
                required_documents: prev.required_documents.map((doc) =>
                  doc.type === type
                    ? {
                        ...doc,
                        status: "uploaded" as const,
                        uploaded_at: new Date().toISOString(),
                        uploaded_by: driver?.id || "driver-123",
                        file_url: URL.createObjectURL(file),
                      }
                    : doc,
                ),
              };
            });
          }

          return { ...prev, [docId]: newProgress };
        });
      }, 200);
    },
    [engagement, driver?.id],
  );

  const handleCapturePhoto = useCallback((type: string) => {
    setIsCapturingPhoto(true);

    // Simulate photo capture
    setTimeout(() => {
      const photoId = `photo-${Date.now()}`;
      const mockFile = new File([""], `${type}-${photoId}.jpg`, {
        type: "image/jpeg",
      });
      const previewUrl = `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=`;

      setCapturedPhotos((prev) => [
        ...prev,
        {
          id: photoId,
          type,
          file: mockFile,
          preview_url: previewUrl,
        },
      ]);

      setIsCapturingPhoto(false);
      toast.success("Photo captured");
    }, 1000);
  }, []);

  const handleRetakePhoto = useCallback((photoId: string) => {
    setCapturedPhotos((prev) => prev.filter((photo) => photo.id !== photoId));
    toast.info("Photo removed. Capture a new one.");
  }, []);

  const handleSubmitPhotos = useCallback(() => {
    if (capturedPhotos.length === 0) return;

    toast.success("Photos submitted successfully");

    // Update engagement documents
    setEngagement((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        required_documents: prev.required_documents.map((doc) =>
          doc.type === "photo"
            ? {
                ...doc,
                status: "uploaded" as const,
                uploaded_at: new Date().toISOString(),
                uploaded_by: driver?.id || "driver-123",
              }
            : doc,
        ),
      };
    });

    setCapturedPhotos([]);
  }, [capturedPhotos, driver?.id]);

  const handleUpdateStatus = useCallback(
    (status: EngagementDetailsType["status"]) => {
      if (!engagement) return;

      setEngagement((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          status,
          updated_at: new Date().toISOString(),
        };
      });

      toast.success(`Status updated to ${status.replace("_", " ")}`);
    },
    [engagement],
  );

  const handleCompleteStep = useCallback(
    (stepId: string, notes?: string) => {
      if (!engagement) return;

      setEngagement((prev) => {
        if (!prev) return prev;

        const updatedSteps = prev.workflow_steps.map((step) =>
          step.id === stepId
            ? {
                ...step,
                status: "completed" as const,
                completed_at: new Date().toISOString(),
                notes: notes || step.notes,
              }
            : step,
        );

        // Calculate new progress
        const completedSteps = updatedSteps.filter(
          (step) => step.status === "completed",
        ).length;
        const totalSteps = updatedSteps.length;
        const newProgress = Math.round((completedSteps / totalSteps) * 100);

        return {
          ...prev,
          workflow_steps: updatedSteps,
          progress_percentage: newProgress,
          updated_at: new Date().toISOString(),
        };
      });

      toast.success("Step completed");
    },
    [engagement],
  );

  const handleReportIssue = useCallback(
    (issue: { title: string; description: string; severity: string }) => {
      if (!engagement) return;

      const newIssue = {
        id: `issue-${Date.now()}`,
        title: issue.title,
        description: issue.description,
        severity: issue.severity as "low" | "medium" | "high" | "critical",
        status: "open" as const,
        reported_at: new Date().toISOString(),
      };

      setEngagement((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          issues: [...prev.issues, newIssue],
          updated_at: new Date().toISOString(),
        };
      });

      toast.success("Issue reported");
    },
    [engagement],
  );

  const handleCallParticipant = useCallback(
    (participantId: string) => {
      const participant = engagement?.participants.find(
        (p) => p.id === participantId,
      );
      if (participant?.phone) {
        toast.info(`Calling ${participant.name}...`);
        // In a real app, this would initiate a phone call
      } else {
        toast.error("No phone number available");
      }
    },
    [engagement],
  );

  const handleUpdateNotes = useCallback(
    (notes: string) => {
      if (!engagement) return;

      setEngagement((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          notes,
          updated_at: new Date().toISOString(),
        };
      });
    },
    [engagement],
  );

  const handleRequestHelp = useCallback(() => {
    toast.info("Help request sent to dispatcher");

    // Add system message
    const helpMessage: EngagementMessage = {
      id: `help-${Date.now()}`,
      content: "Driver has requested assistance with this engagement",
      sender_id: "system",
      sender_name: "System",
      sender_role: "dispatcher",
      timestamp: new Date().toISOString(),
      urgent: true,
    };

    setMessages((prev) => [...prev, helpMessage]);
  }, []);

  const handleMarkMessageRead = useCallback((messageId: string) => {
    setMessages((prev) =>
      prev.map((msg) => (msg.id === messageId ? { ...msg, read: true } : msg)),
    );
  }, []);

  return (
    <DriverEngagementDetailsPage
      driver={driver}
      isLoading={isUserLoading}
      engagement={engagement}
      isLoadingEngagement={isLoadingEngagement}
      engagementError={engagementError}
      messages={messages}
      isLoadingMessages={isLoadingMessages}
      messagesError={messagesError}
      newMessage={newMessage}
      isSendingMessage={isSendingMessage}
      uploadingDocuments={uploadingDocuments}
      documentUploadProgress={documentUploadProgress}
      capturedPhotos={capturedPhotos}
      isCapturingPhoto={isCapturingPhoto}
      isOnline={isOnline}
      lastUpdateReceived={lastUpdateReceived}
      onSendMessage={handleSendMessage}
      onUpdateMessage={handleUpdateMessage}
      onUploadDocument={handleUploadDocument}
      onCapturePhoto={handleCapturePhoto}
      onRetakePhoto={handleRetakePhoto}
      onSubmitPhotos={handleSubmitPhotos}
      onUpdateStatus={handleUpdateStatus}
      onCompleteStep={handleCompleteStep}
      onReportIssue={handleReportIssue}
      onCallParticipant={handleCallParticipant}
      onUpdateNotes={handleUpdateNotes}
      onRequestHelp={handleRequestHelp}
      onMarkMessageRead={handleMarkMessageRead}
    />
  );
}
