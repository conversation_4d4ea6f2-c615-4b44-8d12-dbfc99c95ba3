import { useState } from "react";
import { format } from "date-fns";
import {
  Calendar,
  ChevronLeft,
  FileText,
  Hash,
  Pencil,
  Plus,
  Shield<PERSON>heck,
  Trash,
  Truck,
} from "lucide-react";
import { Link } from "react-router";

import type { VehicleFormValues } from "@/components/forms/VehicleForm";

import {
  useCreateVehicle,
  useDeleteVehicle,
  useListVehicles,
  useUpdateVehicle,
} from "@/api/vehicles";
import VehicleForm from "@/components/forms/VehicleForm";
import DialogConfirmation from "@/components/shared/DialogConfirmation";
import DialogForm from "@/components/shared/DialogForm";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/User";
import { Database } from "@/supabase/types";

// Helper function to get vehicle status color
const getVehicleStatusColor = (isActive: boolean) => {
  return isActive ? "bg-green-500" : "bg-gray-500/20 text-gray-700";
};

// Helper to get appropriate icon based on vehicle type
const getVehicleIcon = (make: string) => {
  // Different icons could be used based on make/type
  return <Truck className="h-10 w-10 text-blue-500" />;
};

// Helper to get vehicle status
const getVehicleStatus = (vehicle: Vehicle): "active" | "inactive" => {
  // In a real app, this might be based on inspection dates, registration status, etc.
  // For now, we'll assume all vehicles are active
  return "active";
};

type Vehicle = Database["public"]["Tables"]["vehicles"]["Row"];

// Vehicle card component
const VehicleCard = ({
  vehicle,
  onEdit,
  onInspect,
  onDelete,
}: {
  vehicle: Vehicle;
  onEdit: (vehicle: Vehicle) => void;
  onInspect: (vehicle: Vehicle) => void;
  onDelete: (vehicle: Vehicle) => void;
}) => {
  const status = getVehicleStatus(vehicle);
  const isActive = status === "active";

  return (
    <Card className="relative overflow-hidden transition-all hover:shadow-md">
      <div className="absolute top-2 right-2 flex gap-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={(e) => {
            e.stopPropagation();
            onEdit(vehicle);
          }}
        >
          <Pencil className="h-4 w-4" />
          <span className="sr-only">Edit</span>
        </Button>

        <DialogConfirmation
          title="Delete Vehicle"
          description={`Are you sure you want to delete ${vehicle.make} ${vehicle.model}? This action cannot be undone.`}
          onClick={() => onDelete(vehicle)}
          variant="ghost"
          size="icon"
          className="text-destructive hover:text-destructive h-8 w-8"
        >
          <Button
            variant="ghost"
            size="icon"
            className="text-destructive hover:text-destructive h-8 w-8"
          >
            <Trash className="h-4 w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </DialogConfirmation>
      </div>

      <div className="flex items-center gap-4 border-b p-6">
        {getVehicleIcon(vehicle.make)}
        <div>
          <h3 className="text-xl font-semibold">
            {vehicle.make} {vehicle.model}
          </h3>
          <p className="text-muted-foreground text-sm">
            {vehicle.year} • {vehicle.license_plate}
          </p>
        </div>
      </div>
      <CardContent className="p-6">
        <div className="flex flex-col gap-y-4">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium">VIN</p>
              <p className="text-muted-foreground text-sm">{vehicle.vin}</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">DOT Number</p>
              <p className="text-muted-foreground text-sm">{vehicle.us_dot}</p>
            </div>
          </div>
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium">MC Number</p>
              <p className="text-muted-foreground text-sm">
                {vehicle.mc_number}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">Status</p>
              <Badge className={getVehicleStatusColor(isActive)}>
                {isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>

          <div className="mt-4 flex gap-2">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => onInspect(vehicle)}
            >
              <ShieldCheck className="mr-2 h-4 w-4" />
              Inspection
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Empty vehicle placeholder card
const EmptyVehicleCard = ({ onClick }: { onClick: () => void }) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <Card
      className="hover:bg-primary/5 focus-visible:bg-primary/5 focus-visible:ring-primary flex min-h-[280px] cursor-pointer flex-col items-center justify-center border-dashed transition-all focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden"
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label="Add new vehicle"
    >
      <div className="flex h-full flex-col items-center justify-center p-6 text-center">
        <div className="bg-primary/10 mb-4 flex items-center justify-center rounded-full p-3">
          <Truck className="h-10 w-10 text-blue-500" />
        </div>
        <h3 className="text-lg font-medium">Add Your Vehicle</h3>
        <p className="text-muted-foreground mt-2 mb-3 max-w-[80%] text-sm">
          Register your commercial vehicle to start accepting shipments and
          track your fleet information.
        </p>
        <Button variant="outline" size="sm" className="mt-2">
          <Plus className="mr-2 h-4 w-4" />
          Add Vehicle
        </Button>
      </div>
    </Card>
  );
};

// Vehicle inspection/documents card
const VehicleInspectionCard = ({ onClick }: { onClick: () => void }) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <Card
      className="hover:bg-primary/5 focus-visible:bg-primary/5 focus-visible:ring-primary flex min-h-[280px] cursor-pointer flex-col items-center justify-center border-dashed transition-all focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden"
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label="Manage vehicle documents"
    >
      <div className="flex h-full flex-col items-center justify-center p-6 text-center">
        <div className="bg-primary/10 mb-4 flex items-center justify-center rounded-full p-3">
          <FileText className="h-10 w-10 text-green-500" />
        </div>
        <h3 className="text-lg font-medium">Vehicle Documents</h3>
        <p className="text-muted-foreground mt-2 mb-3 max-w-[80%] text-sm">
          Upload and manage your vehicle registration, insurance, and inspection
          documents.
        </p>
        <Button variant="outline" size="sm" className="mt-2">
          <FileText className="mr-2 h-4 w-4" />
          Manage Documents
        </Button>
      </div>
    </Card>
  );
};

export default function Vehicle() {
  const { driver } = useUser();
  const [showVehicleForm, setShowVehicleForm] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<Vehicle | null>(null);

  const {
    data: vehiclesData,
    isLoading,
    error,
  } = useListVehicles(
    {
      driver_id: driver?.id,
      pageIndex: 0,
      pageSize: 50,
    },
    {
      enabled: !!driver?.id,
    },
  );

  const { mutate: createVehicle } = useCreateVehicle({
    onSuccess: () => {
      setShowVehicleForm(false);
      setEditingVehicle(null);

      toast({
        title: "Vehicle Added",
        description: "Your vehicle has been successfully registered.",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Registration Failed",
        description:
          error.message ||
          "There was an error registering your vehicle. Please try again.",
        variant: "destructive",
      });
    },
  });

  const { mutate: updateVehicle } = useUpdateVehicle({
    onSuccess: () => {
      setShowVehicleForm(false);
      setEditingVehicle(null);

      toast({
        title: "Vehicle Updated",
        description: "Your vehicle information has been updated.",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Update Failed",
        description:
          error.message ||
          "There was an error updating your vehicle. Please try again.",
        variant: "destructive",
      });
    },
  });

  const { mutate: deleteVehicle } = useDeleteVehicle({
    onSuccess: () => {
      toast({
        title: "Vehicle Deleted",
        description: "Your vehicle has been removed from your fleet.",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Deletion Failed",
        description:
          error.message ||
          "There was an error deleting your vehicle. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleCreateVehicle = (values: VehicleFormValues) => {
    if (!driver?.id) return;

    const vehicleData = {
      make: values.make,
      model: values.model,
      year: values.year,
      license_plate: values.license_plate,
      vin: values.vin,
      mc_number: values.mc_number,
      us_dot: values.us_dot,
      driver_id: driver.id,
    };

    if (editingVehicle) {
      updateVehicle({
        id: editingVehicle.id,
        ...vehicleData,
      });
    } else {
      createVehicle(vehicleData);
    }
  };

  const handleAddVehicle = () => {
    setEditingVehicle(null);
    setShowVehicleForm(true);
  };

  const handleEditVehicle = (vehicle: Vehicle) => {
    setEditingVehicle(vehicle);
    setShowVehicleForm(true);
  };

  const handleDeleteVehicle = (vehicle: Vehicle) => {
    if (!driver?.id) return;

    deleteVehicle({
      id: vehicle.id,
    });
  };

  const handleInspectVehicle = (vehicle: Vehicle) => {
    toast({
      title: "Vehicle Inspection",
      description: `Inspection details for ${vehicle.make} ${vehicle.model} would be displayed here.`,
      variant: "default",
    });
  };

  const handleManageDocuments = () => {
    toast({
      title: "Vehicle Documents",
      description: "Document management interface would be displayed here.",
      variant: "default",
    });
  };

  const vehicles = vehiclesData?.items || [];
  const hasVehicles = vehicles.length > 0;

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button variant="ghost" size="sm" asChild className="mb-4">
          <Link to="/app/drivers/profile">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Profile
          </Link>
        </Button>
      </div>

      <div className="mb-8 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="mb-2 text-4xl font-bold">My Vehicles</h1>
          <p className="text-muted-foreground">
            Manage your commercial vehicles, registrations, and documentation
          </p>
        </div>

        <Button onClick={handleAddVehicle}>
          <Plus className="mr-2 h-4 w-4" />
          Add Vehicle
        </Button>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error loading your vehicles. Please try again later.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {isLoading ? (
          // Loading state
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="bg-muted/10 h-64 animate-pulse"></Card>
          ))
        ) : (
          <>
            {/* Display existing vehicles */}
            {vehicles.map((vehicle) => (
              <VehicleCard
                key={vehicle.id}
                vehicle={vehicle}
                onEdit={handleEditVehicle}
                onInspect={handleInspectVehicle}
                onDelete={handleDeleteVehicle}
              />
            ))}

            {/* Show empty state if no vehicles */}
            {!hasVehicles && <EmptyVehicleCard onClick={handleAddVehicle} />}

            {/* Always show documents card */}
            <VehicleInspectionCard onClick={handleManageDocuments} />
          </>
        )}
      </div>

      <DialogForm
        Component={VehicleForm}
        defaultValues={
          editingVehicle
            ? {
                make: editingVehicle.make,
                model: editingVehicle.model,
                year: editingVehicle.year,
                license_plate: editingVehicle.license_plate,
                vin: editingVehicle.vin,
                mc_number: editingVehicle.mc_number,
                us_dot: editingVehicle.us_dot,
                driver_id: editingVehicle.driver_id || undefined,
              }
            : undefined
        }
        title={editingVehicle ? "Edit Vehicle" : "Add Vehicle"}
        onSubmit={handleCreateVehicle}
        useTrigger={false}
        onCancel={() => {
          setShowVehicleForm(false);
          setEditingVehicle(null);
        }}
        open={showVehicleForm}
        onOpenChange={(open) => {
          setShowVehicleForm(open);
          if (!open) {
            setEditingVehicle(null);
          }
        }}
        isEditing={!!editingVehicle}
      />
    </div>
  );
}
