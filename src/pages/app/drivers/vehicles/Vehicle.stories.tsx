import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import type { Database } from "@/supabase/types";

import Vehicle from "./Vehicle";

// Create mock data types
type Vehicle = Database["public"]["Tables"]["vehicles"]["Row"];
type Driver = Database["public"]["Tables"]["drivers"]["Row"];

const meta: Meta<typeof Vehicle> = {
  title: "Pages/Drivers/Vehicles/Vehicle",
  component: Vehicle,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/app/drivers/vehicles" },
    }),
    docs: {
      description: {
        component:
          "Driver vehicle management component for registering, managing, and tracking commercial vehicles with documentation and inspection capabilities.",
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock driver data
const mockDriver: Driver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "Do<PERSON>",
  email: "<EMAIL>",
  phone_number: "******-0123",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active",
  tier: "professional",
  created_at: "2024-01-15T08:00:00Z",
  verified_at: "2024-01-20T10:00:00Z",
};

// Mock vehicle data
const mockSemiTruck: Vehicle = {
  id: "vehicle-001",
  driver_id: "driver-123",
  make: "Peterbilt",
  model: "579",
  year: 2022,
  license_plate: "CA-TRK-001",
  vin: "1NP5DB9X9ND123456",
  mc_number: "MC-123456",
  us_dot: "DOT-789012",
  created_at: "2024-01-15T08:00:00Z",
};

const mockFreightliner: Vehicle = {
  id: "vehicle-002",
  driver_id: "driver-123",
  make: "Freightliner",
  model: "Cascadia",
  year: 2021,
  license_plate: "TX-FRT-002",
  vin: "3AKJHHDR5MSKS1234",
  mc_number: "MC-234567",
  us_dot: "DOT-890123",
  created_at: "2024-02-01T10:00:00Z",
};

const mockVolvoTruck: Vehicle = {
  id: "vehicle-003",
  driver_id: "driver-123",
  make: "Volvo",
  model: "VNL 860",
  year: 2023,
  license_plate: "NV-VLV-003",
  vin: "4V4NC9EH5KN123456",
  mc_number: "MC-345678",
  us_dot: "DOT-901234",
  created_at: "2024-03-01T12:00:00Z",
};

const mockOlderTruck: Vehicle = {
  id: "vehicle-004",
  driver_id: "driver-123",
  make: "Kenworth",
  model: "T680",
  year: 2018,
  license_plate: "AZ-KEN-004",
  vin: "1XKWDB0X9JJ123456",
  mc_number: "MC-456789",
  us_dot: "DOT-012345",
  created_at: "2024-01-10T14:00:00Z",
};

const mockMackTruck: Vehicle = {
  id: "vehicle-005",
  driver_id: "driver-123",
  make: "Mack",
  model: "Anthem",
  year: 2020,
  license_plate: "FL-MCK-005",
  vin: "1M1AX07Y0KM123456",
  mc_number: "MC-567890",
  us_dot: "DOT-123456",
  created_at: "2024-04-01T16:00:00Z",
};

const mockInternationalTruck: Vehicle = {
  id: "vehicle-006",
  driver_id: "driver-123",
  make: "International",
  model: "LT Series",
  year: 2019,
  license_plate: "OH-INT-006",
  vin: "3HSDJSJR9KN123456",
  mc_number: "MC-678901",
  us_dot: "DOT-234567",
  created_at: "2024-05-01T18:00:00Z",
};

// Mock API responses
const createMockListVehiclesResponse = (vehicles: Vehicle[]) => ({
  items: vehicles,
  total: vehicles.length,
});

// Mock functions
const mockUseUser = () => ({ driver: mockDriver });
const mockUseListVehicles = (vehicles: Vehicle[] = []) =>
  fn().mockReturnValue({
    data: createMockListVehiclesResponse(vehicles),
    isLoading: false,
    error: null,
  });
const mockUseCreateVehicle = () => fn().mockReturnValue({ mutate: fn() });
const mockUseUpdateVehicle = () => fn().mockReturnValue({ mutate: fn() });
const mockUseDeleteVehicle = () => fn().mockReturnValue({ mutate: fn() });

// Default story - single vehicle
export const Default: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([mockSemiTruck]),
      },
    ],
  },
};

// Loading state
export const Loading: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([]),
        delay: 2000,
      },
    ],
  },
};

// Error state
export const ErrorState: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 500,
        response: { error: "Failed to load vehicles" },
      },
    ],
  },
};

// Empty state - no vehicles registered
export const EmptyState: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([]),
      },
    ],
  },
};

// Single vehicle
export const SingleVehicle: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([mockSemiTruck]),
      },
    ],
  },
};

// Multiple vehicles - small fleet
export const SmallFleet: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          mockSemiTruck,
          mockFreightliner,
        ]),
      },
    ],
  },
};

// Medium fleet
export const MediumFleet: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          mockSemiTruck,
          mockFreightliner,
          mockVolvoTruck,
          mockOlderTruck,
        ]),
      },
    ],
  },
};

// Large fleet
export const LargeFleet: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          mockSemiTruck,
          mockFreightliner,
          mockVolvoTruck,
          mockOlderTruck,
          mockMackTruck,
          mockInternationalTruck,
        ]),
      },
    ],
  },
};

// Different truck makes and models
export const DifferentTruckMakes: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          mockSemiTruck, // Peterbilt
          mockFreightliner, // Freightliner
          mockVolvoTruck, // Volvo
          mockMackTruck, // Mack
        ]),
      },
    ],
  },
};

// Different years and ages
export const MixedVehicleAges: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          { ...mockVolvoTruck, year: 2023 }, // New
          { ...mockSemiTruck, year: 2022 }, // Recent
          { ...mockFreightliner, year: 2021 }, // Moderate
          { ...mockOlderTruck, year: 2018 }, // Older
          {
            ...mockMackTruck,
            year: 2015,
            make: "Kenworth",
            model: "T800",
            license_plate: "OLD-TRK-001",
          }, // Very old
        ]),
      },
    ],
  },
};

// Different states registration
export const MultiStateRegistration: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          { ...mockSemiTruck, license_plate: "CA-TRK-001" }, // California
          { ...mockFreightliner, license_plate: "TX-FRT-002" }, // Texas
          { ...mockVolvoTruck, license_plate: "NV-VLV-003" }, // Nevada
          { ...mockOlderTruck, license_plate: "AZ-KEN-004" }, // Arizona
        ]),
      },
    ],
  },
};

// Owner operator vehicles
export const OwnerOperatorFleet: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "389",
            year: 2020,
            license_plate: "OO-PTB-001",
          },
          {
            ...mockFreightliner,
            make: "Kenworth",
            model: "W900",
            year: 2019,
            license_plate: "OO-KEN-002",
          },
        ]),
      },
    ],
  },
};

// Company fleet vehicles
export const CompanyFleet: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia",
            license_plate: "COM-FLT-001",
            mc_number: "MC-COMPANY1",
            us_dot: "DOT-COMP001",
          },
          {
            ...mockVolvoTruck,
            make: "Volvo",
            model: "VNL 760",
            license_plate: "COM-FLT-002",
            mc_number: "MC-COMPANY1",
            us_dot: "DOT-COMP001",
          },
          {
            ...mockMackTruck,
            make: "Mack",
            model: "Pinnacle",
            license_plate: "COM-FLT-003",
            mc_number: "MC-COMPANY1",
            us_dot: "DOT-COMP001",
          },
        ]),
      },
    ],
  },
};

// Specialized vehicle types
export const SpecializedVehicles: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "367 (Tanker)",
            license_plate: "TNK-001",
            vin: "1NP5DB9X9ND987654",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "114SD (Flatbed)",
            license_plate: "FLT-002",
            vin: "3AKJHHDR5MSKS5678",
          },
          {
            ...mockVolvoTruck,
            make: "Volvo",
            model: "VHD (Refrigerated)",
            license_plate: "REF-003",
            vin: "4V4NC9EH5KN987654",
          },
        ]),
      },
    ],
  },
};

// Long VIN numbers and plate edge cases
export const EdgeCaseLongIdentifiers: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            license_plate: "VERY-LONG-PLATE-123",
            vin: "1XKWDB0X9JJ1234567890ABCDEFG", // Longer than typical
            mc_number: "MC-VERY-LONG-NUMBER-123456",
            us_dot: "DOT-EXTENDED-ID-789012345",
          },
        ]),
      },
    ],
  },
};

// Interstate trucking operations
export const InterstateOperations: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "579 (Cross-Country)",
            license_plate: "INT-CA-001",
            us_dot: "DOT-INTERSTATE-001",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia (Regional)",
            license_plate: "INT-TX-002",
            us_dot: "DOT-INTERSTATE-002",
          },
        ]),
      },
    ],
  },
};

// Lease and financing scenarios
export const LeaseFinanceVehicles: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "579 (Leased)",
            license_plate: "LSE-001",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia (Financed)",
            license_plate: "FIN-002",
          },
          {
            ...mockVolvoTruck,
            make: "Volvo",
            model: "VNL 860 (Owned)",
            license_plate: "OWN-003",
          },
        ]),
      },
    ],
  },
};

// New driver with no vehicles (shows add vehicle prompt)
export const NewDriverNoVehicles: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([]),
      },
    ],
  },
};

// Vehicle registration scenarios
export const RegistrationScenarios: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "579 (Current Registration)",
            license_plate: "CUR-REG-001",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia (Renewal Due)",
            license_plate: "REN-DUE-002",
          },
          {
            ...mockVolvoTruck,
            make: "Volvo",
            model: "VNL 760 (Temporary Permit)",
            license_plate: "TEMP-003",
          },
        ]),
      },
    ],
  },
};

// DOT inspection statuses
export const InspectionStatuses: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "579 (Passed DOT)",
            license_plate: "DOT-PASS-001",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia (Inspection Due)",
            license_plate: "INS-DUE-002",
          },
          {
            ...mockVolvoTruck,
            make: "Volvo",
            model: "VNL 760 (Maintenance)",
            license_plate: "MAINT-003",
          },
        ]),
      },
    ],
  },
};

// Insurance coverage scenarios
export const InsuranceScenarios: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "579 (Full Coverage)",
            license_plate: "INS-FULL-001",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia (Liability Only)",
            license_plate: "INS-LIA-002",
          },
          {
            ...mockVolvoTruck,
            make: "Volvo",
            model: "VNL 760 (Renewal Due)",
            license_plate: "INS-REN-003",
          },
        ]),
      },
    ],
  },
};

// Form validation scenarios
export const FormValidationStates: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([mockSemiTruck]),
      },
    ],
  },
};

// Document upload scenarios
export const DocumentUploadStates: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "579 (Docs Complete)",
            license_plate: "DOC-COMP-001",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia (Docs Pending)",
            license_plate: "DOC-PEND-002",
          },
        ]),
      },
    ],
  },
};

// Mobile responsive view simulation
export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          mockSemiTruck,
          mockFreightliner,
        ]),
      },
    ],
  },
};

// Tablet view simulation
export const TabletView: Story = {
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          mockSemiTruck,
          mockFreightliner,
          mockVolvoTruck,
        ]),
      },
    ],
  },
};

// Maintenance and service scenarios
export const MaintenanceScenarios: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockSemiTruck,
            make: "Peterbilt",
            model: "579 (Service Current)",
            license_plate: "SVC-CURR-001",
          },
          {
            ...mockFreightliner,
            make: "Freightliner",
            model: "Cascadia (Service Due)",
            license_plate: "SVC-DUE-002",
          },
          {
            ...mockVolvoTruck,
            make: "Volvo",
            model: "VNL 760 (In Shop)",
            license_plate: "IN-SHOP-003",
          },
        ]),
      },
    ],
  },
};

// High mileage vehicles
export const HighMileageVehicles: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/vehicles",
        method: "GET",
        status: 200,
        response: createMockListVehiclesResponse([
          {
            ...mockOlderTruck,
            make: "Kenworth",
            model: "T680 (500K+ miles)",
            year: 2017,
            license_plate: "HI-MILE-001",
          },
          {
            ...mockMackTruck,
            make: "Mack",
            model: "Anthem (750K+ miles)",
            year: 2016,
            license_plate: "HI-MILE-002",
          },
        ]),
      },
    ],
  },
};
