import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  with<PERSON><PERSON><PERSON>,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { DriverDispatcherPage } from "./DriverDispatcherPage";

const meta: Meta<typeof DriverDispatcherPage> = {
  title: "Pages/Drivers/Dispatcher",
  component: DriverDispatcherPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockDriver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  phone_number: "+**********",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active" as const,
  tier: "professional",
  created_at: new Date().toISOString(),
  verified_at: new Date().toISOString(),
};

const mockSessions = [
  {
    id: "session-1",
    dispatcher_id: "dispatcher-1",
    dispatcher_name: "<PERSON>",
    dispatcher_avatar: undefined,
    status: "active" as const,
    unread_count: 2,
    created_at: new Date().toISOString(),
    priority: "high" as const,
    last_message: {
      id: "msg-1",
      content: "Your next pickup is ready at Location A",
      sender_id: "dispatcher-1",
      sender_type: "dispatcher" as const,
      timestamp: new Date(Date.now() - 300000).toISOString(),
      read: false,
      urgent: true,
    },
  },
  {
    id: "session-2",
    dispatcher_id: "dispatcher-2",
    dispatcher_name: "Mike Rodriguez",
    dispatcher_avatar: undefined,
    status: "active" as const,
    unread_count: 0,
    created_at: new Date(Date.now() - 86400000).toISOString(),
    priority: "normal" as const,
    last_message: {
      id: "msg-2",
      content: "Great job on the last delivery!",
      sender_id: "dispatcher-2",
      sender_type: "dispatcher" as const,
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      read: true,
      urgent: false,
    },
  },
  {
    id: "session-3",
    dispatcher_id: "dispatcher-3",
    dispatcher_name: "Emily Chen",
    dispatcher_avatar: undefined,
    status: "pending" as const,
    unread_count: 1,
    created_at: new Date(Date.now() - 172800000).toISOString(),
    priority: "urgent" as const,
    last_message: {
      id: "msg-3",
      content: "Emergency route change needed",
      sender_id: "dispatcher-3",
      sender_type: "dispatcher" as const,
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      read: false,
      urgent: true,
    },
  },
];

const mockMessages = [
  {
    id: "msg-1-1",
    content: "Good morning! I have your route ready for today.",
    sender_id: "dispatcher-1",
    sender_type: "dispatcher" as const,
    timestamp: new Date(Date.now() - 1800000).toISOString(),
    read: true,
    urgent: false,
  },
  {
    id: "msg-1-2",
    content: "Perfect, heading to the pickup location now.",
    sender_id: "driver-123",
    sender_type: "driver" as const,
    timestamp: new Date(Date.now() - 1500000).toISOString(),
    read: true,
    urgent: false,
  },
  {
    id: "msg-1-3",
    content: "Your next pickup is ready at Location A",
    sender_id: "dispatcher-1",
    sender_type: "dispatcher" as const,
    timestamp: new Date(Date.now() - 300000).toISOString(),
    read: false,
    urgent: true,
  },
  {
    id: "msg-1-4",
    content: "Traffic update: Take Highway 95 instead of I-10",
    sender_id: "dispatcher-1",
    sender_type: "dispatcher" as const,
    timestamp: new Date(Date.now() - 120000).toISOString(),
    read: false,
    urgent: true,
  },
];

const mockTasks = [
  {
    id: "task-1",
    task_id: "TK-001",
    title: "Pickup at Warehouse District",
    description: "Collect shipment #12345 from Building A, Dock 3",
    status: "assigned" as const,
    priority: "high" as const,
    due_date: new Date(Date.now() + 3600000).toISOString(),
    created_at: new Date(Date.now() - 1800000).toISOString(),
    location: "1234 Industrial Blvd, Dallas, TX",
  },
  {
    id: "task-2",
    task_id: "TK-002",
    title: "Delivery to Distribution Center",
    description: "Deliver shipment #12345 to receiving dock",
    status: "in_progress" as const,
    priority: "normal" as const,
    due_date: new Date(Date.now() + 7200000).toISOString(),
    created_at: new Date(Date.now() - 3600000).toISOString(),
    location: "5678 Commerce Ave, Austin, TX",
  },
  {
    id: "task-3",
    task_id: "TK-003",
    title: "Vehicle Inspection",
    description: "Complete daily vehicle safety inspection",
    status: "assigned" as const,
    priority: "urgent" as const,
    due_date: new Date(Date.now() + 1800000).toISOString(),
    created_at: new Date().toISOString(),
    location: "Current Location",
  },
];

const mockNotifications = [
  {
    id: "notif-1",
    type: "message" as const,
    title: "New Message",
    content: "You have a new urgent message from Sarah Johnson",
    timestamp: new Date(Date.now() - 300000).toISOString(),
    read: false,
  },
  {
    id: "notif-2",
    type: "task" as const,
    title: "Task Updated",
    content: "Vehicle inspection is now due in 30 minutes",
    timestamp: new Date(Date.now() - 600000).toISOString(),
    read: false,
  },
];

export const Default: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    sessions: mockSessions,
    activeSession: mockSessions[0],
    messages: mockMessages,
    isLoadingMessages: false,
    messagesError: null,
    tasks: mockTasks,
    isLoadingTasks: false,
    tasksError: null,
    notifications: mockNotifications,
    newMessage: "",
    isSendingMessage: false,
    isOnline: true,
    connectionStatus: "connected",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};

export const Loading: Story = {
  args: {
    driver: mockDriver,
    isLoading: true,
    sessions: [],
    activeSession: null,
    messages: [],
    isLoadingMessages: true,
    messagesError: null,
    tasks: [],
    isLoadingTasks: true,
    tasksError: null,
    notifications: [],
    newMessage: "",
    isSendingMessage: false,
    isOnline: true,
    connectionStatus: "connecting",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};

export const NewMessages: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    sessions: mockSessions.map((session) => ({
      ...session,
      unread_count: session.id === "session-1" ? 5 : session.unread_count,
    })),
    activeSession: mockSessions[0],
    messages: [
      ...mockMessages,
      {
        id: "msg-urgent",
        content: "URGENT: Route change required immediately!",
        sender_id: "dispatcher-1",
        sender_type: "dispatcher" as const,
        timestamp: new Date(Date.now() - 30000).toISOString(),
        read: false,
        urgent: true,
      },
    ],
    isLoadingMessages: false,
    messagesError: null,
    tasks: mockTasks,
    isLoadingTasks: false,
    tasksError: null,
    notifications: [
      {
        id: "urgent-notif",
        type: "message" as const,
        title: "Urgent Message",
        content: "You have urgent messages from dispatch",
        timestamp: new Date(Date.now() - 30000).toISOString(),
        read: false,
      },
      ...mockNotifications,
    ],
    newMessage: "",
    isSendingMessage: false,
    isOnline: true,
    connectionStatus: "connected",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};

export const ActiveCommunication: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    sessions: mockSessions,
    activeSession: mockSessions[0],
    messages: mockMessages,
    isLoadingMessages: false,
    messagesError: null,
    tasks: mockTasks.map((task) => ({
      ...task,
      status: task.id === "task-1" ? ("in_progress" as const) : task.status,
    })),
    isLoadingTasks: false,
    tasksError: null,
    notifications: mockNotifications,
    newMessage: "Understood, heading to the new location now",
    isSendingMessage: false,
    isOnline: true,
    connectionStatus: "connected",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};

export const NoMessages: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    sessions: [],
    activeSession: null,
    messages: [],
    isLoadingMessages: false,
    messagesError: null,
    tasks: [],
    isLoadingTasks: false,
    tasksError: null,
    notifications: [],
    newMessage: "",
    isSendingMessage: false,
    isOnline: true,
    connectionStatus: "connected",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};

export const CommunicationError: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    sessions: mockSessions,
    activeSession: mockSessions[0],
    messages: [],
    isLoadingMessages: false,
    messagesError: new globalThis.Error("Failed to load messages"),
    tasks: mockTasks,
    isLoadingTasks: false,
    tasksError: new globalThis.Error("Failed to load tasks"),
    notifications: mockNotifications,
    newMessage: "",
    isSendingMessage: false,
    isOnline: false,
    connectionStatus: "disconnected",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};

export const Notifications: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    sessions: mockSessions,
    activeSession: mockSessions[1],
    messages: mockMessages.slice(0, 2),
    isLoadingMessages: false,
    messagesError: null,
    tasks: mockTasks,
    isLoadingTasks: false,
    tasksError: null,
    notifications: [
      {
        id: "alert-weather",
        type: "alert" as const,
        title: "Weather Alert",
        content: "Heavy rain expected on your route",
        timestamp: new Date(Date.now() - 120000).toISOString(),
        read: false,
      },
      {
        id: "task-deadline",
        type: "task" as const,
        title: "Task Deadline",
        content: "Vehicle inspection due in 15 minutes",
        timestamp: new Date(Date.now() - 300000).toISOString(),
        read: false,
      },
      ...mockNotifications,
    ],
    newMessage: "",
    isSendingMessage: false,
    isOnline: true,
    connectionStatus: "connected",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};

export const BusyDispatcher: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    sessions: [
      ...mockSessions,
      {
        id: "session-4",
        dispatcher_id: "dispatcher-4",
        dispatcher_name: "Alex Thompson",
        dispatcher_avatar: undefined,
        status: "active" as const,
        unread_count: 3,
        created_at: new Date(Date.now() - 43200000).toISOString(),
        priority: "high" as const,
        last_message: {
          id: "msg-4",
          content: "Multiple stops assigned for today",
          sender_id: "dispatcher-4",
          sender_type: "dispatcher" as const,
          timestamp: new Date(Date.now() - 600000).toISOString(),
          read: false,
          urgent: false,
        },
      },
      {
        id: "session-5",
        dispatcher_id: "dispatcher-5",
        dispatcher_name: "Lisa Park",
        dispatcher_avatar: undefined,
        status: "active" as const,
        unread_count: 1,
        created_at: new Date(Date.now() - 21600000).toISOString(),
        priority: "normal" as const,
        last_message: {
          id: "msg-5",
          content: "Fuel station locations updated",
          sender_id: "dispatcher-5",
          sender_type: "dispatcher" as const,
          timestamp: new Date(Date.now() - 900000).toISOString(),
          read: false,
          urgent: false,
        },
      },
    ],
    activeSession: mockSessions[0],
    messages: mockMessages,
    isLoadingMessages: false,
    messagesError: null,
    tasks: [
      ...mockTasks,
      {
        id: "task-4",
        task_id: "TK-004",
        title: "Fuel Stop Required",
        description: "Stop at designated fuel station before next pickup",
        status: "assigned" as const,
        priority: "normal" as const,
        due_date: new Date(Date.now() + 5400000).toISOString(),
        created_at: new Date(Date.now() - 900000).toISOString(),
        location: "Shell Station, Highway 35",
      },
      {
        id: "task-5",
        task_id: "TK-005",
        title: "Customer Contact Required",
        description: "Call customer to confirm delivery time window",
        status: "assigned" as const,
        priority: "high" as const,
        due_date: new Date(Date.now() + 2700000).toISOString(),
        created_at: new Date(Date.now() - 600000).toISOString(),
        location: "Phone call required",
      },
    ],
    isLoadingTasks: false,
    tasksError: null,
    notifications: [
      {
        id: "busy-notif-1",
        type: "task" as const,
        title: "High Activity",
        content: "5 active tasks require attention",
        timestamp: new Date(Date.now() - 180000).toISOString(),
        read: false,
      },
      {
        id: "busy-notif-2",
        type: "message" as const,
        title: "Multiple Dispatchers",
        content: "Messages from 3 different dispatchers",
        timestamp: new Date(Date.now() - 420000).toISOString(),
        read: false,
      },
      ...mockNotifications,
    ],
    newMessage: "",
    isSendingMessage: false,
    isOnline: true,
    connectionStatus: "connected",
    onSelectSession: fn(),
    onSendMessage: fn(),
    onUpdateMessage: fn(),
    onMarkMessageRead: fn(),
    onMarkTaskComplete: fn(),
    onCallDispatcher: fn(),
    onRequestHelp: fn(),
    onUpdateTaskStatus: fn(),
  },
};
