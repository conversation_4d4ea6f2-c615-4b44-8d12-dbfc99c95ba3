import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

import { useUser } from "@/contexts/User";
import {
  CommunicationSession,
  DriverDispatcherPage,
  Message,
  TaskUpdate,
} from "./DriverDispatcherPage";

// Mock data for development
const mockSessions: CommunicationSession[] = [
  {
    id: "session-1",
    dispatcher_id: "dispatcher-1",
    dispatcher_name: "<PERSON>",
    dispatcher_avatar: undefined,
    status: "active",
    unread_count: 2,
    created_at: new Date().toISOString(),
    priority: "high",
    last_message: {
      id: "msg-1",
      content: "Your next pickup is ready at Location A",
      sender_id: "dispatcher-1",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 300000).toISOString(),
      read: false,
      urgent: true,
    },
  },
  {
    id: "session-2",
    dispatcher_id: "dispatcher-2",
    dispatcher_name: "<PERSON>",
    dispatcher_avatar: undefined,
    status: "active",
    unread_count: 0,
    created_at: new Date(Date.now() - 86400000).toISOString(),
    priority: "normal",
    last_message: {
      id: "msg-2",
      content: "Great job on the last delivery!",
      sender_id: "dispatcher-2",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      read: true,
      urgent: false,
    },
  },
  {
    id: "session-3",
    dispatcher_id: "dispatcher-3",
    dispatcher_name: "Emily Chen",
    dispatcher_avatar: undefined,
    status: "pending",
    unread_count: 1,
    created_at: new Date(Date.now() - 172800000).toISOString(),
    priority: "urgent",
    last_message: {
      id: "msg-3",
      content: "Emergency route change needed",
      sender_id: "dispatcher-3",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      read: false,
      urgent: true,
    },
  },
];

const mockMessages: Record<string, Message[]> = {
  "session-1": [
    {
      id: "msg-1-1",
      content: "Good morning! I have your route ready for today.",
      sender_id: "dispatcher-1",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      read: true,
      urgent: false,
    },
    {
      id: "msg-1-2",
      content: "Perfect, heading to the pickup location now.",
      sender_id: "driver-123",
      sender_type: "driver",
      timestamp: new Date(Date.now() - 1500000).toISOString(),
      read: true,
      urgent: false,
    },
    {
      id: "msg-1-3",
      content: "Your next pickup is ready at Location A",
      sender_id: "dispatcher-1",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 300000).toISOString(),
      read: false,
      urgent: true,
    },
    {
      id: "msg-1-4",
      content: "Traffic update: Take Highway 95 instead of I-10",
      sender_id: "dispatcher-1",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 120000).toISOString(),
      read: false,
      urgent: true,
    },
  ],
  "session-2": [
    {
      id: "msg-2-1",
      content: "How's the delivery going?",
      sender_id: "dispatcher-2",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      read: true,
      urgent: false,
    },
    {
      id: "msg-2-2",
      content: "All good! Just delivered the package.",
      sender_id: "driver-123",
      sender_type: "driver",
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      read: true,
      urgent: false,
    },
    {
      id: "msg-2-3",
      content: "Great job on the last delivery!",
      sender_id: "dispatcher-2",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      read: true,
      urgent: false,
    },
  ],
  "session-3": [
    {
      id: "msg-3-1",
      content: "Emergency route change needed",
      sender_id: "dispatcher-3",
      sender_type: "dispatcher",
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      read: false,
      urgent: true,
    },
  ],
};

const mockTasks: TaskUpdate[] = [
  {
    id: "task-1",
    task_id: "TK-001",
    title: "Pickup at Warehouse District",
    description: "Collect shipment #12345 from Building A, Dock 3",
    status: "assigned",
    priority: "high",
    due_date: new Date(Date.now() + 3600000).toISOString(),
    created_at: new Date(Date.now() - 1800000).toISOString(),
    location: "1234 Industrial Blvd, Dallas, TX",
  },
  {
    id: "task-2",
    task_id: "TK-002",
    title: "Delivery to Distribution Center",
    description: "Deliver shipment #12345 to receiving dock",
    status: "in_progress",
    priority: "normal",
    due_date: new Date(Date.now() + 7200000).toISOString(),
    created_at: new Date(Date.now() - 3600000).toISOString(),
    location: "5678 Commerce Ave, Austin, TX",
  },
  {
    id: "task-3",
    task_id: "TK-003",
    title: "Vehicle Inspection",
    description: "Complete daily vehicle safety inspection",
    status: "assigned",
    priority: "urgent",
    due_date: new Date(Date.now() + 1800000).toISOString(),
    created_at: new Date().toISOString(),
    location: "Current Location",
  },
];

const mockNotifications = [
  {
    id: "notif-1",
    type: "message" as const,
    title: "New Message",
    content: "You have a new urgent message from Sarah Johnson",
    timestamp: new Date(Date.now() - 300000).toISOString(),
    read: false,
  },
  {
    id: "notif-2",
    type: "task" as const,
    title: "Task Updated",
    content: "Vehicle inspection is now due in 30 minutes",
    timestamp: new Date(Date.now() - 600000).toISOString(),
    read: false,
  },
  {
    id: "notif-3",
    type: "alert" as const,
    title: "System Alert",
    content: "Weather advisory for your route",
    timestamp: new Date(Date.now() - 900000).toISOString(),
    read: false,
  },
];

export default function Dispatcher() {
  const { driver, isLoading } = useUser();

  // Local state
  const [sessions] = useState<CommunicationSession[]>(mockSessions);
  const [activeSession, setActiveSession] =
    useState<CommunicationSession | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [tasks, setTasks] = useState<TaskUpdate[]>(mockTasks);
  const [newMessage, setNewMessage] = useState("");
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionStatus, setConnectionStatus] = useState<
    "connected" | "connecting" | "disconnected"
  >("connected");
  const [notifications, setNotifications] = useState(mockNotifications);

  // Simulated loading states
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isLoadingTasks] = useState(false);
  const [messagesError] = useState<Error | null>(null);
  const [tasksError] = useState<Error | null>(null);

  // Event Handlers
  const handleSelectSession = useCallback(
    (session: CommunicationSession) => {
      setActiveSession(session);
      setIsLoadingMessages(true);

      // Simulate loading messages
      setTimeout(() => {
        const sessionMessages = mockMessages[session.id] || [];
        setMessages(sessionMessages);
        setIsLoadingMessages(false);

        // Mark messages as read
        if (session.unread_count > 0) {
          // Update session to mark as read
          const updatedSessions = sessions.map((s) =>
            s.id === session.id ? { ...s, unread_count: 0 } : s,
          );
          // In a real app, this would update the sessions state
          console.log("Messages marked as read for session:", session.id);
        }
      }, 500);
    },
    [sessions],
  );

  const handleSendMessage = useCallback(() => {
    if (!newMessage.trim() || !activeSession || isSendingMessage) return;

    setIsSendingMessage(true);

    // Simulate sending message
    setTimeout(() => {
      const newMsg: Message = {
        id: `msg-${Date.now()}`,
        content: newMessage.trim(),
        sender_id: driver?.id || "driver-123",
        sender_type: "driver",
        timestamp: new Date().toISOString(),
        read: true,
        urgent: false,
      };

      setMessages((prev) => [...prev, newMsg]);
      setNewMessage("");
      setIsSendingMessage(false);
      toast.success("Message sent");
    }, 500);
  }, [newMessage, activeSession, isSendingMessage, driver?.id]);

  const handleUpdateMessage = useCallback((message: string) => {
    setNewMessage(message);
  }, []);

  const handleMarkMessageRead = useCallback((messageId: string) => {
    setMessages((prev) =>
      prev.map((msg) => (msg.id === messageId ? { ...msg, read: true } : msg)),
    );
  }, []);

  const handleMarkTaskComplete = useCallback((taskId: string) => {
    setTasks((prev) =>
      prev.map((task) =>
        task.id === taskId ? { ...task, status: "completed" as const } : task,
      ),
    );
    toast.success("Task marked as completed");
  }, []);

  const handleCallDispatcher = useCallback((dispatcherId: string) => {
    toast.info("Call feature coming soon");
    console.log("Call dispatcher:", dispatcherId);
  }, []);

  const handleRequestHelp = useCallback(() => {
    toast.info("Emergency assistance requested");
    setNotifications((prev) => [
      {
        id: `help-${Date.now()}`,
        type: "alert" as const,
        title: "Help Requested",
        content: "Emergency assistance request has been sent to dispatch",
        timestamp: new Date().toISOString(),
        read: false,
      },
      ...prev,
    ]);
  }, []);

  const handleUpdateTaskStatus = useCallback(
    (taskId: string, status: TaskUpdate["status"]) => {
      setTasks((prev) =>
        prev.map((task) => (task.id === taskId ? { ...task, status } : task)),
      );
      toast.success(`Task status updated to ${status}`);
    },
    [],
  );

  // Connection status monitoring
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setConnectionStatus("connected");
      toast.success("Connection restored");
    };

    const handleOffline = () => {
      setIsOnline(false);
      setConnectionStatus("disconnected");
      toast.error("Connection lost");
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Real-time notifications simulation
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.9) {
        // 10% chance of notification
        const notification = {
          id: `notification-${Date.now()}`,
          type: Math.random() > 0.5 ? ("message" as const) : ("task" as const),
          title: "New Update",
          content: "You have a new communication update",
          timestamp: new Date().toISOString(),
          read: false,
        };
        setNotifications((prev) => [notification, ...prev.slice(0, 4)]); // Keep max 5 notifications
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Auto-select first session if none selected
  useEffect(() => {
    if (!activeSession && sessions.length > 0) {
      handleSelectSession(sessions[0]);
    }
  }, [sessions, activeSession, handleSelectSession]);

  return (
    <DriverDispatcherPage
      driver={driver}
      isLoading={isLoading}
      sessions={sessions}
      activeSession={activeSession}
      messages={messages}
      isLoadingMessages={isLoadingMessages}
      messagesError={messagesError}
      tasks={tasks}
      isLoadingTasks={isLoadingTasks}
      tasksError={tasksError}
      notifications={notifications}
      newMessage={newMessage}
      isSendingMessage={isSendingMessage}
      isOnline={isOnline}
      connectionStatus={connectionStatus}
      onSelectSession={handleSelectSession}
      onSendMessage={handleSendMessage}
      onUpdateMessage={handleUpdateMessage}
      onMarkMessageRead={handleMarkMessageRead}
      onMarkTaskComplete={handleMarkTaskComplete}
      onCallDispatcher={handleCallDispatcher}
      onRequestHelp={handleRequestHelp}
      onUpdateTaskStatus={handleUpdateTaskStatus}
    />
  );
}
