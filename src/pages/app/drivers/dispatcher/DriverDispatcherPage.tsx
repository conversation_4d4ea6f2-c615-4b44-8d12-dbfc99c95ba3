import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>C<PERSON>cle,
  Clock,
  MapPin,
  MessageCircle,
  Phone,
  Send,
  Truck,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { UserContextType } from "@/contexts/User";

export interface Message {
  id: string;
  content: string;
  sender_id: string;
  sender_type: "driver" | "dispatcher";
  timestamp: string;
  read: boolean;
  urgent: boolean;
}

export interface CommunicationSession {
  id: string;
  dispatcher_id: string;
  dispatcher_name: string;
  dispatcher_avatar?: string;
  status: "active" | "pending" | "closed";
  last_message?: Message;
  unread_count: number;
  created_at: string;
  priority: "low" | "normal" | "high" | "urgent";
}

export interface TaskUpdate {
  id: string;
  task_id: string;
  title: string;
  description: string;
  status: "assigned" | "in_progress" | "completed" | "delayed";
  priority: "low" | "normal" | "high" | "urgent";
  due_date?: string;
  created_at: string;
  location?: string;
}

export interface DriverDispatcherPageProps {
  // User data
  driver: UserContextType["driver"];
  isLoading: UserContextType["isLoading"];

  // Communication data
  sessions: CommunicationSession[];
  activeSession: CommunicationSession | null;
  messages: Message[];
  isLoadingMessages: boolean;
  messagesError: Error | null;

  // Task data
  tasks: TaskUpdate[];
  isLoadingTasks: boolean;
  tasksError: Error | null;

  // Real-time updates
  notifications: Array<{
    id: string;
    type: "message" | "task" | "alert";
    title: string;
    content: string;
    timestamp: string;
    read: boolean;
  }>;

  // Communication state
  newMessage: string;
  isSendingMessage: boolean;
  isOnline: boolean;
  connectionStatus: "connected" | "connecting" | "disconnected";

  // Function handlers
  onSelectSession: (session: CommunicationSession) => void;
  onSendMessage: () => void;
  onUpdateMessage: (message: string) => void;
  onMarkMessageRead: (messageId: string) => void;
  onMarkTaskComplete: (taskId: string) => void;
  onCallDispatcher: (dispatcherId: string) => void;
  onRequestHelp: () => void;
  onUpdateTaskStatus: (taskId: string, status: TaskUpdate["status"]) => void;
}

export const DriverDispatcherPage = ({
  driver,
  isLoading,
  sessions,
  activeSession,
  messages,
  isLoadingMessages,
  messagesError,
  tasks,
  isLoadingTasks,
  tasksError,
  notifications,
  newMessage,
  isSendingMessage,
  isOnline,
  connectionStatus,
  onSelectSession,
  onSendMessage,
  onUpdateMessage,
  onMarkMessageRead,
  onMarkTaskComplete,
  onCallDispatcher,
  onRequestHelp,
  onUpdateTaskStatus,
}: DriverDispatcherPageProps) => {
  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="grid gap-6 lg:grid-cols-[1fr_2fr]">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!driver) {
    return (
      <div className="container py-8">
        <Card className="mx-auto max-w-2xl">
          <CardContent className="flex flex-col items-center px-8 pt-6 pb-8 text-center">
            <MessageCircle className="text-muted-foreground mb-4 h-12 w-12" />
            <h2 className="mb-2 text-2xl font-semibold">
              Driver Profile Required
            </h2>
            <p className="text-muted-foreground mb-6">
              You need a driver profile to communicate with dispatchers.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      case "closed":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "destructive";
      case "high":
        return "secondary";
      case "normal":
        return "outline";
      case "low":
        return "outline";
      default:
        return "outline";
    }
  };

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dispatcher Communication</h1>
          <p className="text-muted-foreground">
            Stay connected with your dispatch team
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div
            className={`h-2 w-2 rounded-full ${connectionStatus === "connected" ? "bg-green-500" : connectionStatus === "connecting" ? "bg-yellow-500" : "bg-red-500"}`}
          />
          <span className="text-sm font-medium capitalize">
            {connectionStatus}
          </span>
          <Button onClick={onRequestHelp} variant="outline" size="sm">
            <AlertTriangle className="mr-2 h-4 w-4" />
            Request Help
          </Button>
        </div>
      </div>

      {/* Notifications Banner */}
      {notifications.filter((n) => !n.read).length > 0 && (
        <Card className="mb-6 border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <span className="font-medium text-orange-800">
                {notifications.filter((n) => !n.read).length} unread
                notifications
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 lg:grid-cols-[1fr_2fr]">
        {/* Left Column - Sessions and Tasks */}
        <div className="space-y-6">
          {/* Communication Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageCircle className="mr-2 h-5 w-5" />
                Active Communications
              </CardTitle>
            </CardHeader>
            <CardContent>
              {sessions.length === 0 ? (
                <div className="py-8 text-center">
                  <MessageCircle className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                  <p className="text-muted-foreground">
                    No active communications
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {sessions.map((session) => (
                    <div
                      key={session.id}
                      className={`hover:bg-accent cursor-pointer rounded-lg border p-3 transition-colors ${
                        activeSession?.id === session.id
                          ? "border-primary bg-accent"
                          : ""
                      }`}
                      onClick={() => onSelectSession(session)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={session.dispatcher_avatar} />
                              <AvatarFallback>
                                {session.dispatcher_name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div
                              className={`absolute -right-1 -bottom-1 h-3 w-3 rounded-full border-2 border-white ${getStatusColor(session.status)}`}
                            />
                          </div>
                          <div>
                            <p className="font-medium">
                              {session.dispatcher_name}
                            </p>
                            <p className="text-muted-foreground truncate text-sm">
                              {session.last_message?.content || "No messages"}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          {session.unread_count > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {session.unread_count}
                            </Badge>
                          )}
                          <Badge
                            variant={getPriorityColor(session.priority)}
                            className="text-xs"
                          >
                            {session.priority}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tasks */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Truck className="mr-2 h-5 w-5" />
                Active Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingTasks ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-3 w-3/4" />
                    </div>
                  ))}
                </div>
              ) : tasksError ? (
                <div className="py-4 text-center">
                  <AlertTriangle className="text-destructive mx-auto mb-2 h-8 w-8" />
                  <p className="text-destructive text-sm">
                    Failed to load tasks
                  </p>
                </div>
              ) : tasks.length === 0 ? (
                <div className="py-8 text-center">
                  <CheckCircle className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                  <p className="text-muted-foreground">No active tasks</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {tasks.map((task) => (
                    <div key={task.id} className="rounded-lg border p-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <p className="font-medium">{task.title}</p>
                          <p className="text-muted-foreground text-sm">
                            {task.description}
                          </p>
                          {task.location && (
                            <div className="text-muted-foreground flex items-center text-xs">
                              <MapPin className="mr-1 h-3 w-3" />
                              {task.location}
                            </div>
                          )}
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <Badge
                            variant={getPriorityColor(task.priority)}
                            className="text-xs"
                          >
                            {task.priority}
                          </Badge>
                          <select
                            value={task.status}
                            onChange={(e) =>
                              onUpdateTaskStatus(
                                task.id,
                                e.target.value as TaskUpdate["status"],
                              )
                            }
                            className="rounded border px-2 py-1 text-xs"
                          >
                            <option value="assigned">Assigned</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="delayed">Delayed</option>
                          </select>
                        </div>
                      </div>
                      {task.due_date && (
                        <div className="text-muted-foreground mt-2 flex items-center text-xs">
                          <Clock className="mr-1 h-3 w-3" />
                          Due: {new Date(task.due_date).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Chat Interface */}
        <Card className="flex flex-col">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                {activeSession ? (
                  <>
                    <Avatar className="mr-3 h-8 w-8">
                      <AvatarImage src={activeSession.dispatcher_avatar} />
                      <AvatarFallback>
                        {activeSession.dispatcher_name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    {activeSession.dispatcher_name}
                  </>
                ) : (
                  "Select a conversation"
                )}
              </CardTitle>
              {activeSession && (
                <Button
                  onClick={() => onCallDispatcher(activeSession.dispatcher_id)}
                  variant="outline"
                  size="sm"
                >
                  <Phone className="mr-2 h-4 w-4" />
                  Call
                </Button>
              )}
            </div>
          </CardHeader>

          {activeSession ? (
            <>
              <CardContent className="flex-1">
                <ScrollArea className="h-96 w-full pr-4">
                  {isLoadingMessages ? (
                    <div className="space-y-3">
                      {[...Array(5)].map((_, i) => (
                        <div
                          key={i}
                          className={`flex ${i % 2 === 0 ? "justify-start" : "justify-end"}`}
                        >
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-32" />
                            <Skeleton className="h-8 w-48" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : messagesError ? (
                    <div className="py-8 text-center">
                      <AlertTriangle className="text-destructive mx-auto mb-2 h-8 w-8" />
                      <p className="text-destructive text-sm">
                        Failed to load messages
                      </p>
                    </div>
                  ) : messages.length === 0 ? (
                    <div className="py-8 text-center">
                      <MessageCircle className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                      <p className="text-muted-foreground">No messages yet</p>
                      <p className="text-muted-foreground text-sm">
                        Start a conversation with your dispatcher
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${
                            message.sender_type === "driver"
                              ? "justify-end"
                              : "justify-start"
                          }`}
                        >
                          <div
                            className={`max-w-xs rounded-lg px-3 py-2 ${
                              message.sender_type === "driver"
                                ? "bg-primary text-primary-foreground"
                                : "bg-accent text-accent-foreground"
                            } ${message.urgent ? "border-2 border-red-500" : ""}`}
                          >
                            <p className="text-sm">{message.content}</p>
                            <div className="mt-1 flex items-center justify-between">
                              <span className="text-xs opacity-70">
                                {new Date(
                                  message.timestamp,
                                ).toLocaleTimeString()}
                              </span>
                              {message.urgent && (
                                <AlertTriangle className="h-3 w-3 text-red-500" />
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>

              <Separator />

              <CardContent className="pt-4">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Type your message..."
                    value={newMessage}
                    onChange={(e) => onUpdateMessage(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        onSendMessage();
                      }
                    }}
                    disabled={isSendingMessage || !isOnline}
                  />
                  <Button
                    onClick={onSendMessage}
                    disabled={
                      !newMessage.trim() || isSendingMessage || !isOnline
                    }
                    size="sm"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
                {!isOnline && (
                  <p className="text-destructive mt-2 text-sm">
                    Connection lost. Messages will be sent when reconnected.
                  </p>
                )}
              </CardContent>
            </>
          ) : (
            <CardContent className="flex flex-1 items-center justify-center">
              <div className="text-center">
                <MessageCircle className="text-muted-foreground mx-auto mb-4 h-16 w-16" />
                <h3 className="mb-2 text-lg font-medium">
                  Select a conversation
                </h3>
                <p className="text-muted-foreground">
                  Choose a dispatcher from the left to start communicating
                </p>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
};
