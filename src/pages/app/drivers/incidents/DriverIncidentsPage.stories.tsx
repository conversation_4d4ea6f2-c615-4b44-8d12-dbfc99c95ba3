import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import { AlertTriangle } from "lucide-react";
import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { Badge } from "@/components/ui/badge";
import { DriverIncidentsPage } from "./DriverIncidentsPage";

const meta: Meta<typeof DriverIncidentsPage> = {
  title: "Pages/Drivers/Incidents",
  component: DriverIncidentsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockDriver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  phone_number: "+**********",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active" as const,
  tier: "professional",
  created_at: new Date().toISOString(),
  verified_at: new Date().toISOString(),
};

const mockActiveIncidents = [
  {
    id: "incident-1",
    title: "Vehicle Breakdown on Highway 95",
    description: "Engine overheating, unable to continue route",
    type: "mechanical" as const,
    severity: "high" as const,
    status: "reported" as const,
    created_at: new Date(Date.now() - 1800000).toISOString(),
    updated_at: new Date(Date.now() - 900000).toISOString(),
    driver_id: "driver-123",
    shipment_id: "shipment-456",
    location: "Highway 95, Mile Marker 127",
  },
  {
    id: "incident-2",
    title: "Weather Delay - Heavy Rain",
    description: "Severe thunderstorm making travel unsafe",
    type: "weather" as const,
    severity: "medium" as const,
    status: "reported" as const,
    created_at: new Date(Date.now() - 3600000).toISOString(),
    updated_at: new Date(Date.now() - 1800000).toISOString(),
    driver_id: "driver-123",
    shipment_id: "shipment-789",
    location: "Interstate 40, Tennessee",
  },
  {
    id: "incident-3",
    title: "Minor Collision at Loading Dock",
    description: "Small scrape while backing into dock position",
    type: "accident" as const,
    severity: "low" as const,
    status: "investigating" as const,
    created_at: new Date(Date.now() - 7200000).toISOString(),
    updated_at: new Date(Date.now() - 3600000).toISOString(),
    driver_id: "driver-123",
    shipment_id: "shipment-101",
    location: "Warehouse District, Dallas TX",
  },
];

const mockResolvedIncidents = [
  {
    id: "incident-4",
    title: "Traffic Delay - Construction Zone",
    description: "Extended delay due to highway construction",
    type: "delay" as const,
    severity: "low" as const,
    status: "resolved" as const,
    created_at: new Date(Date.now() - 86400000).toISOString(),
    updated_at: new Date(Date.now() - 43200000).toISOString(),
    driver_id: "driver-123",
    shipment_id: "shipment-202",
    location: "I-35 Construction Zone",
  },
  {
    id: "incident-5",
    title: "Customer Address Incorrect",
    description:
      "Delivery address was outdated, customer provided new location",
    type: "other" as const,
    severity: "medium" as const,
    status: "resolved" as const,
    created_at: new Date(Date.now() - *********).toISOString(),
    updated_at: new Date(Date.now() - 86400000).toISOString(),
    driver_id: "driver-123",
    shipment_id: "shipment-303",
    location: "Houston TX",
  },
];

const mockCriticalIncidents = [
  {
    id: "incident-6",
    title: "URGENT: Cargo Theft Attempt",
    description:
      "Attempted break-in at truck stop, cargo secure but police involved",
    type: "theft" as const,
    severity: "critical" as const,
    status: "reported" as const,
    created_at: new Date(Date.now() - 600000).toISOString(),
    updated_at: new Date(Date.now() - 300000).toISOString(),
    driver_id: "driver-123",
    shipment_id: "shipment-999",
    location: "Rest Stop, I-10 Louisiana",
  },
  {
    id: "incident-7",
    title: "Major Accident - Highway Closure",
    description: "Multi-vehicle accident ahead, highway completely closed",
    type: "accident" as const,
    severity: "critical" as const,
    status: "investigating" as const,
    created_at: new Date(Date.now() - 1200000).toISOString(),
    updated_at: new Date(Date.now() - 600000).toISOString(),
    driver_id: "driver-123",
    shipment_id: "shipment-888",
    location: "I-75, Atlanta GA",
  },
];

const filterGroups = [
  {
    id: "type",
    label: "Type",
    options: [
      { value: null, label: "All Types" },
      { value: "accident", label: "Accident" },
      { value: "delay", label: "Delay" },
      { value: "damage", label: "Damage" },
      { value: "theft", label: "Theft" },
      { value: "weather", label: "Weather" },
      { value: "mechanical", label: "Mechanical" },
      { value: "other", label: "Other" },
    ],
  },
  {
    id: "severity",
    label: "Severity",
    options: [
      { value: null, label: "All Severities" },
      { value: "low", label: "Low" },
      { value: "medium", label: "Medium" },
      { value: "high", label: "High" },
      { value: "critical", label: "Critical" },
    ],
  },
  {
    id: "status",
    label: "Status",
    options: [
      { value: null, label: "All Statuses" },
      { value: "reported", label: "Reported" },
      { value: "investigating", label: "Investigating" },
      { value: "resolved", label: "Resolved" },
      { value: "closed", label: "Closed" },
    ],
  },
];

// Mock badge functions that match the actual implementation
const mockGetSeverityBadge = (severity: string) => {
  switch (severity) {
    case "critical":
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          <span>Critical</span>
        </Badge>
      );
    case "high":
      return (
        <Badge
          variant="destructive"
          className="flex items-center gap-1 bg-orange-500"
        >
          <AlertTriangle className="h-3 w-3" />
          <span>High</span>
        </Badge>
      );
    case "medium":
      return (
        <Badge
          variant="default"
          className="flex items-center gap-1 bg-yellow-500"
        >
          <AlertTriangle className="h-3 w-3" />
          <span>Medium</span>
        </Badge>
      );
    case "low":
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          <span>Low</span>
        </Badge>
      );
    default:
      return <Badge variant="outline">{severity || "Unknown"}</Badge>;
  }
};

const mockGetStatusBadge = (status: string) => {
  switch (status) {
    case "reported":
      return <Badge variant="destructive">Reported</Badge>;
    case "investigating":
      return <Badge variant="default">Investigating</Badge>;
    case "resolved":
      return (
        <Badge
          variant="outline"
          className="bg-green-100 text-green-800 hover:bg-green-200"
        >
          Resolved
        </Badge>
      );
    case "closed":
      return <Badge variant="secondary">Closed</Badge>;
    default:
      return <Badge variant="outline">{status || "Unknown"}</Badge>;
  }
};

export const Default: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: mockActiveIncidents,
    resolvedIncidents: mockResolvedIncidents,
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};

export const Loading: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: [],
    resolvedIncidents: [],
    isLoadingActive: true,
    isLoadingResolved: true,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};

export const CreateIncident: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: mockActiveIncidents,
    resolvedIncidents: mockResolvedIncidents,
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
  parameters: {
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
        state: { showCreateForm: true },
      },
      routing: { path: "/incidents/create" },
    }),
  },
};

export const EditIncident: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: mockActiveIncidents,
    resolvedIncidents: mockResolvedIncidents,
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
  parameters: {
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "incident-1" },
        state: { editMode: true },
      },
      routing: { path: "/incidents/:id/edit" },
    }),
  },
};

export const NoIncidents: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: [],
    resolvedIncidents: [],
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};

export const FilteredView: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: mockActiveIncidents.filter(
      (incident) => incident.type === "mechanical",
    ),
    resolvedIncidents: mockResolvedIncidents.filter(
      (incident) => incident.severity === "medium",
    ),
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};

export const CriticalIncidents: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: mockCriticalIncidents,
    resolvedIncidents: mockResolvedIncidents,
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};

export const RecentIncidents: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: [
      {
        ...mockActiveIncidents[0],
        created_at: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        title: "Just Reported: Tire Blowout",
        description: "Front right tire blew out, pulled over safely",
        type: "mechanical" as const,
        severity: "high" as const,
      },
      {
        ...mockActiveIncidents[1],
        created_at: new Date(Date.now() - 600000).toISOString(), // 10 minutes ago
        title: "Emergency: Medical Incident",
        description: "Driver feeling unwell, need immediate assistance",
        type: "other" as const,
        severity: "critical" as const,
      },
    ],
    resolvedIncidents: mockResolvedIncidents,
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};

export const IncidentDetails: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: mockActiveIncidents,
    resolvedIncidents: mockResolvedIncidents,
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
  parameters: {
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "incident-1" },
      },
      routing: { path: "/incidents/:id" },
    }),
  },
};

export const ResolvedView: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: mockActiveIncidents,
    resolvedIncidents: [
      ...mockResolvedIncidents,
      {
        id: "incident-resolved-1",
        title: "Successfully Resolved: Route Optimization",
        description: "Found alternate route, delivered on time",
        type: "delay" as const,
        severity: "low" as const,
        status: "resolved" as const,
        created_at: new Date(Date.now() - *********).toISOString(),
        updated_at: new Date(Date.now() - *********).toISOString(),
        driver_id: "driver-123",
        shipment_id: "shipment-444",
        location: "Phoenix AZ to Los Angeles CA",
      },
    ],
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: null,
    resolvedError: null,
    activeTab: "resolved",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};

export const ErrorState: Story = {
  args: {
    driver: mockDriver,
    activeIncidents: [],
    resolvedIncidents: [],
    isLoadingActive: false,
    isLoadingResolved: false,
    activeError: new globalThis.Error("Failed to load active incidents"),
    resolvedError: new globalThis.Error("Failed to load resolved incidents"),
    activeTab: "active",
    filterGroups,
    onTabChange: fn(),
    getSeverityBadge: mockGetSeverityBadge,
    getStatusBadge: mockGetStatusBadge,
  },
};
