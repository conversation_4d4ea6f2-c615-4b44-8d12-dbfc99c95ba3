import { useState } from "react";
import { AlertTriangle } from "lucide-react";

import { useListIncidents } from "@/api/incidents/use-list-incidents";
import { Badge } from "@/components/ui/badge";
import { useUser } from "@/contexts/User";
import { DriverIncidentsPage } from "./DriverIncidentsPage";

const filterGroups = [
  {
    id: "type",
    label: "Type",
    options: [
      { value: null, label: "All Types" },
      { value: "accident", label: "Accident" },
      { value: "delay", label: "Delay" },
      { value: "damage", label: "Damage" },
      { value: "theft", label: "Theft" },
      { value: "weather", label: "Weather" },
      { value: "mechanical", label: "Mechanical" },
      { value: "other", label: "Other" },
    ],
  },
  {
    id: "severity",
    label: "Severity",
    options: [
      { value: null, label: "All Severities" },
      { value: "low", label: "Low" },
      { value: "medium", label: "Medium" },
      { value: "high", label: "High" },
      { value: "critical", label: "Critical" },
    ],
  },
  {
    id: "status",
    label: "Status",
    options: [
      { value: null, label: "All Statuses" },
      { value: "reported", label: "Reported" },
      { value: "investigating", label: "Investigating" },
      { value: "resolved", label: "Resolved" },
      { value: "closed", label: "Closed" },
    ],
  },
];

export default function Incidents() {
  const { driver } = useUser();
  const [activeTab, setActiveTab] = useState("active");

  // For active incidents - use "reported" status for the query
  const {
    data: activeIncidents,
    isLoading: isLoadingActive,
    error: activeError,
  } = useListIncidents({
    driver_id: driver?.id,
    status: "reported", // Changed from array to single value
    pageIndex: 0,
    pageSize: 10,
  });

  // For resolved incidents - use "resolved" status for the query
  const {
    data: resolvedIncidents,
    isLoading: isLoadingResolved,
    error: resolvedError,
  } = useListIncidents({
    driver_id: driver?.id,
    status: "resolved", // Changed from array to single value
    pageIndex: 0,
    pageSize: 10,
  });

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "critical":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            <span>Critical</span>
          </Badge>
        );
      case "high":
        return (
          <Badge
            variant="destructive"
            className="flex items-center gap-1 bg-orange-500"
          >
            <AlertTriangle className="h-3 w-3" />
            <span>High</span>
          </Badge>
        );
      case "medium":
        return (
          <Badge
            variant="default"
            className="flex items-center gap-1 bg-yellow-500"
          >
            <AlertTriangle className="h-3 w-3" />
            <span>Medium</span>
          </Badge>
        );
      case "low":
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            <span>Low</span>
          </Badge>
        );
      default:
        return <Badge variant="outline">{severity || "Unknown"}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "reported":
        return <Badge variant="destructive">Reported</Badge>;
      case "investigating":
        return <Badge variant="default">Investigating</Badge>;
      case "resolved":
        return (
          <Badge
            variant="outline"
            className="bg-green-100 text-green-800 hover:bg-green-200"
          >
            Resolved
          </Badge>
        );
      case "closed":
        return <Badge variant="secondary">Closed</Badge>;
      default:
        return <Badge variant="outline">{status || "Unknown"}</Badge>;
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <DriverIncidentsPage
      driver={driver}
      activeIncidents={activeIncidents?.items || []}
      resolvedIncidents={resolvedIncidents?.items || []}
      isLoadingActive={isLoadingActive}
      isLoadingResolved={isLoadingResolved}
      activeError={activeError}
      resolvedError={resolvedError}
      activeTab={activeTab}
      filterGroups={filterGroups}
      onTabChange={handleTabChange}
      getSeverityBadge={getSeverityBadge}
      getStatusBadge={getStatusBadge}
    />
  );
}
