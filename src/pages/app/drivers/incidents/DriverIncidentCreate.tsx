import { useState } from "react";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router";

import { useCreateIncident } from "@/api/incidents/use-create-incident";
import IncidentForm, {
  IncidentFormValues,
} from "@/components/forms/IncidentForm";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useUser } from "@/contexts/User";
import { useToast } from "@/hooks/use-toast";

export default function DriverIncidentCreate() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { driver } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createIncident = useCreateIncident({
    onSuccess: (data) => {
      toast({
        title: "Incident Reported",
        description: "Your incident has been successfully reported.",
      });
      navigate(`/app/drivers/incidents/${data.id}`);
    },
    onError: (error) => {
      setIsSubmitting(false);
      toast({
        title: "Error Reporting Incident",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: IncidentFormValues) => {
    if (!driver) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in as a driver to report incidents.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    // Map form values to the API expected format
    const incidentData = {
      title: values.title,
      summary: values.description,
      type: values.type,
      severity: values.severity,
      status: "reported" as
        | "reported"
        | "investigating"
        | "resolved"
        | "closed",
      driver_id: driver.id,
      shipment_id: "", // Empty string as default
    };

    createIncident.mutate(incidentData);
  };

  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate("/app/drivers")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Report Incident</h1>
          <p className="text-muted-foreground">
            Report a new incident related to your delivery
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Incident Details</CardTitle>
              <CardDescription>
                Provide information about the incident you need to report
              </CardDescription>
            </CardHeader>
            <CardContent>
              <IncidentForm
                onSubmit={handleSubmit}
                defaultValues={{
                  title: "",
                  description: "",
                  type: "other",
                  severity: "medium",
                }}
              >
                <div className="flex w-full justify-center">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="min-w-40"
                  >
                    {isSubmitting ? "Submitting..." : "Submit Report"}
                  </Button>
                </div>
              </IncidentForm>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Reporting Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">When to Report</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>Accidents or collisions involving your vehicle</li>
                  <li>Damage to cargo or shipping containers</li>
                  <li>Significant delays due to road conditions</li>
                  <li>Mechanical failures affecting delivery</li>
                  <li>Weather events impacting your route</li>
                  <li>Security concerns or theft</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Severity Levels</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>
                    <strong>Low:</strong> Minor issues with minimal impact
                  </li>
                  <li>
                    <strong>Medium:</strong> Issues affecting delivery time or
                    requiring attention
                  </li>
                  <li>
                    <strong>High:</strong> Serious problems requiring immediate
                    response
                  </li>
                  <li>
                    <strong>Critical:</strong> Emergency situations involving
                    safety or major cargo loss
                  </li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Next Steps</h3>
                <p className="text-muted-foreground text-sm">
                  After submitting your report, our team will review the details
                  and may contact you for additional information. Critical
                  incidents will receive priority response.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
