import { ReactElement } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FileWarning, Plus } from "lucide-react";
import { <PERSON> } from "react-router";

import TimeAgo from "@/components/shared/TimeAgo";
import ListTable from "@/components/tables/ListTable";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { UserContextType } from "@/contexts/User";

export interface Incident {
  id: string;
  title: string;
  description?: string;
  type:
    | "accident"
    | "delay"
    | "damage"
    | "theft"
    | "weather"
    | "mechanical"
    | "other";
  severity: "low" | "medium" | "high" | "critical";
  status: "reported" | "investigating" | "resolved" | "closed";
  created_at: string;
  updated_at?: string;
  driver_id: string;
  shipment_id?: string;
  location?: string;
}

export interface DriverIncidentsPageProps {
  // User data
  driver: UserContextType["driver"];

  // Incidents data
  activeIncidents: Incident[];
  resolvedIncidents: Incident[];
  isLoadingActive: boolean;
  isLoadingResolved: boolean;
  activeError: Error | null;
  resolvedError: Error | null;

  // UI state
  activeTab: string;

  // Filter configuration
  filterGroups: Array<{
    id: string;
    label: string;
    options: Array<{
      value: string | null;
      label: string;
    }>;
  }>;

  // Event handlers
  onTabChange: (tab: string) => void;
  getSeverityBadge: (severity: string) => ReactElement;
  getStatusBadge: (status: string) => ReactElement;
}

export const DriverIncidentsPage = ({
  driver,
  activeIncidents,
  resolvedIncidents,
  isLoadingActive,
  isLoadingResolved,
  activeError,
  resolvedError,
  activeTab,
  filterGroups,
  onTabChange,
  getSeverityBadge,
  getStatusBadge,
}: DriverIncidentsPageProps) => {
  return (
    <div className="container py-8">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/app/drivers">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="mb-2 text-4xl font-bold">Incidents</h1>
            <p className="text-muted-foreground">
              View and report incidents related to your shipments
            </p>
          </div>
        </div>

        <Button asChild>
          <Link to="/app/drivers/incidents/create">
            <Plus className="mr-2 h-4 w-4" />
            Report Incident
          </Link>
        </Button>
      </div>

      {(activeError || resolvedError) && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error loading your incidents. Please try again later.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="active" value={activeTab} onValueChange={onTabChange}>
        <TabsList className="mb-6 grid w-full grid-cols-2">
          <TabsTrigger value="active">Active Incidents</TabsTrigger>
          <TabsTrigger value="resolved">Resolved Incidents</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <ListTable
            loading={isLoadingActive}
            data={activeIncidents}
            defaultPageSize={10}
            filterGroups={filterGroups}
            groupName="active-incidents"
            i18n={{
              emptyText: "No active incidents found",
              selection: "Selected",
              actions: {
                tableSettings: "Table settings",
                tableActions: "Table actions",
                search: "Search active incidents...",
              },
            }}
            columns={({ i18n, TableActions }) => [
              {
                id: "title",
                header: "Title",
                accessorKey: "title",
                cell: ({ row }) => {
                  return (
                    <Link
                      to={`/app/drivers/incidents/${row.original.id}`}
                      className="font-medium hover:underline"
                    >
                      {row.original.title}
                    </Link>
                  );
                },
              },
              {
                id: "severity",
                header: "Severity",
                accessorKey: "severity",
                cell: ({ row }) => getSeverityBadge(row.original.severity),
              },
              {
                id: "type",
                header: "Type",
                accessorKey: "type",
                cell: ({ row }) => (
                  <Badge variant="outline" className="capitalize">
                    {row.original.type}
                  </Badge>
                ),
              },
              {
                id: "status",
                header: "Status",
                accessorKey: "status",
                cell: ({ row }) => getStatusBadge(row.original.status),
              },
              {
                id: "created_at",
                header: "Reported At",
                accessorKey: "created_at",
                cell: ({ row }) => (
                  <TimeAgo
                    date={new Date(row.original.created_at)}
                    className="text-muted-foreground"
                  />
                ),
              },
              {
                id: "actions",
                header: "",
                cell: ({ row }) => {
                  return (
                    <div className="flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link to={`/app/drivers/incidents/${row.original.id}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  );
                },
              },
            ]}
          >
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileWarning className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="text-xl font-semibold">No active incidents</h3>
              <p className="text-muted-foreground mt-2 mb-6">
                You don't have any active incidents for your shipments
              </p>
              <Button asChild>
                <Link to="/app/drivers/incidents/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Report Incident
                </Link>
              </Button>
            </div>
          </ListTable>
        </TabsContent>

        <TabsContent value="resolved">
          <ListTable
            loading={isLoadingResolved}
            data={resolvedIncidents}
            defaultPageSize={10}
            filterGroups={filterGroups}
            groupName="resolved-incidents"
            i18n={{
              emptyText: "No resolved incidents found",
              selection: "Selected",
              actions: {
                tableSettings: "Table settings",
                tableActions: "Table actions",
                search: "Search resolved incidents...",
              },
            }}
            columns={({ i18n, TableActions }) => [
              {
                id: "title",
                header: "Title",
                accessorKey: "title",
                cell: ({ row }) => {
                  return (
                    <Link
                      to={`/app/drivers/incidents/${row.original.id}`}
                      className="font-medium hover:underline"
                    >
                      {row.original.title}
                    </Link>
                  );
                },
              },
              {
                id: "severity",
                header: "Severity",
                accessorKey: "severity",
                cell: ({ row }) => getSeverityBadge(row.original.severity),
              },
              {
                id: "type",
                header: "Type",
                accessorKey: "type",
                cell: ({ row }) => (
                  <Badge variant="outline" className="capitalize">
                    {row.original.type}
                  </Badge>
                ),
              },
              {
                id: "status",
                header: "Status",
                accessorKey: "status",
                cell: ({ row }) => getStatusBadge(row.original.status),
              },
              {
                id: "created_at",
                header: "Reported At",
                accessorKey: "created_at",
                cell: ({ row }) => (
                  <TimeAgo
                    date={new Date(row.original.created_at)}
                    className="text-muted-foreground"
                  />
                ),
              },
              {
                id: "actions",
                header: "",
                cell: ({ row }) => {
                  return (
                    <div className="flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link to={`/app/drivers/incidents/${row.original.id}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  );
                },
              },
            ]}
          >
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileWarning className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="text-xl font-semibold">No resolved incidents</h3>
              <p className="text-muted-foreground mt-2 mb-6">
                You don't have any resolved incidents for your shipments
              </p>
              <Button asChild>
                <Link to="/app/drivers/incidents/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Report Incident
                </Link>
              </Button>
            </div>
          </ListTable>
        </TabsContent>
      </Tabs>
    </div>
  );
};
