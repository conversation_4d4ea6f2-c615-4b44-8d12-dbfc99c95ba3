import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, FileWarning, Plus } from "lucide-react";
import { Link } from "react-router";

import { useListIncidents } from "@/api/incidents/use-list-incidents";
import TimeAgo from "@/components/shared/TimeAgo";
import ListTable from "@/components/tables/ListTable";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useUser } from "@/contexts/User";

const filterGroups = [
  {
    id: "type",
    label: "Type",
    options: [
      { value: null, label: "All Types" },
      { value: "accident", label: "Accident" },
      { value: "delay", label: "Delay" },
      { value: "damage", label: "Damage" },
      { value: "theft", label: "Theft" },
      { value: "weather", label: "Weather" },
      { value: "mechanical", label: "Mechanical" },
      { value: "other", label: "Other" },
    ],
  },
  {
    id: "severity",
    label: "Severity",
    options: [
      { value: null, label: "All Severities" },
      { value: "low", label: "Low" },
      { value: "medium", label: "Medium" },
      { value: "high", label: "High" },
      { value: "critical", label: "Critical" },
    ],
  },
  {
    id: "status",
    label: "Status",
    options: [
      { value: null, label: "All Statuses" },
      { value: "reported", label: "Reported" },
      { value: "investigating", label: "Investigating" },
      { value: "resolved", label: "Resolved" },
      { value: "closed", label: "Closed" },
    ],
  },
];

export default function DriverIncidents() {
  const { driver } = useUser();
  const [activeTab, setActiveTab] = useState("active");

  // For active incidents - use "reported" status for the query
  const {
    data: activeIncidents,
    isLoading: isLoadingActive,
    error: activeError,
  } = useListIncidents({
    driver_id: driver?.id,
    status: "reported", // Changed from array to single value
    pageIndex: 0,
    pageSize: 10,
  });

  // For resolved incidents - use "resolved" status for the query
  const {
    data: resolvedIncidents,
    isLoading: isLoadingResolved,
    error: resolvedError,
  } = useListIncidents({
    driver_id: driver?.id,
    status: "resolved", // Changed from array to single value
    pageIndex: 0,
    pageSize: 10,
  });

  const getSeverityBadge = (severity) => {
    switch (severity) {
      case "critical":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            <span>Critical</span>
          </Badge>
        );
      case "high":
        return (
          <Badge
            variant="destructive"
            className="flex items-center gap-1 bg-orange-500"
          >
            <AlertTriangle className="h-3 w-3" />
            <span>High</span>
          </Badge>
        );
      case "medium":
        return (
          <Badge
            variant="default"
            className="flex items-center gap-1 bg-yellow-500"
          >
            <AlertTriangle className="h-3 w-3" />
            <span>Medium</span>
          </Badge>
        );
      case "low":
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            <span>Low</span>
          </Badge>
        );
      default:
        return <Badge variant="outline">{severity || "Unknown"}</Badge>;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "reported":
        return <Badge variant="destructive">Reported</Badge>;
      case "investigating":
        return <Badge variant="default">Investigating</Badge>;
      case "resolved":
        return (
          <Badge
            variant="outline"
            className="bg-green-100 text-green-800 hover:bg-green-200"
          >
            Resolved
          </Badge>
        );
      case "closed":
        return <Badge variant="secondary">Closed</Badge>;
      default:
        return <Badge variant="outline">{status || "Unknown"}</Badge>;
    }
  };

  return (
    <div className="container py-8">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/app/drivers">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="mb-2 text-4xl font-bold">Incidents</h1>
            <p className="text-muted-foreground">
              View and report incidents related to your shipments
            </p>
          </div>
        </div>

        <Button asChild>
          <Link to="/app/drivers/incidents/create">
            <Plus className="mr-2 h-4 w-4" />
            Report Incident
          </Link>
        </Button>
      </div>

      {(activeError || resolvedError) && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error loading your incidents. Please try again later.
          </AlertDescription>
        </Alert>
      )}

      <Tabs
        defaultValue="active"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="mb-6 grid w-full grid-cols-2">
          <TabsTrigger value="active">Active Incidents</TabsTrigger>
          <TabsTrigger value="resolved">Resolved Incidents</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <ListTable
            loading={isLoadingActive}
            data={activeIncidents}
            defaultPageSize={10}
            filterGroups={filterGroups}
            groupName="active-incidents"
            i18n={{
              emptyText: "No active incidents found",
              selection: "Selected",
              actions: {
                tableSettings: "Table settings",
                tableActions: "Table actions",
                search: "Search active incidents...",
              },
            }}
            columns={({ i18n, TableActions }) => [
              {
                id: "title",
                header: "Title",
                accessorKey: "title",
                cell: ({ row }) => {
                  return (
                    <Link
                      to={`/app/drivers/incidents/${row.original.id}`}
                      className="font-medium hover:underline"
                    >
                      {row.original.title}
                    </Link>
                  );
                },
              },
              {
                id: "severity",
                header: "Severity",
                accessorKey: "severity",
                cell: ({ row }) => getSeverityBadge(row.original.severity),
              },
              {
                id: "type",
                header: "Type",
                accessorKey: "type",
                cell: ({ row }) => (
                  <Badge variant="outline" className="capitalize">
                    {row.original.type}
                  </Badge>
                ),
              },
              {
                id: "status",
                header: "Status",
                accessorKey: "status",
                cell: ({ row }) => getStatusBadge(row.original.status),
              },
              {
                id: "created_at",
                header: "Reported At",
                accessorKey: "created_at",
                cell: ({ row }) => (
                  <TimeAgo
                    date={new Date(row.original.created_at)}
                    className="text-muted-foreground"
                  />
                ),
              },
              {
                id: "actions",
                header: "",
                cell: ({ row }) => {
                  return (
                    <div className="flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link to={`/app/drivers/incidents/${row.original.id}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  );
                },
              },
            ]}
          >
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileWarning className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="text-xl font-semibold">No active incidents</h3>
              <p className="text-muted-foreground mt-2 mb-6">
                You don't have any active incidents for your shipments
              </p>
              <Button asChild>
                <Link to="/app/drivers/incidents/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Report Incident
                </Link>
              </Button>
            </div>
          </ListTable>
        </TabsContent>

        <TabsContent value="resolved">
          <ListTable
            loading={isLoadingResolved}
            data={resolvedIncidents}
            defaultPageSize={10}
            filterGroups={filterGroups}
            groupName="resolved-incidents"
            i18n={{
              emptyText: "No resolved incidents found",
              selection: "Selected",
              actions: {
                tableSettings: "Table settings",
                tableActions: "Table actions",
                search: "Search resolved incidents...",
              },
            }}
            columns={({ i18n, TableActions }) => [
              {
                id: "title",
                header: "Title",
                accessorKey: "title",
                cell: ({ row }) => {
                  return (
                    <Link
                      to={`/app/drivers/incidents/${row.original.id}`}
                      className="font-medium hover:underline"
                    >
                      {row.original.title}
                    </Link>
                  );
                },
              },
              {
                id: "severity",
                header: "Severity",
                accessorKey: "severity",
                cell: ({ row }) => getSeverityBadge(row.original.severity),
              },
              {
                id: "type",
                header: "Type",
                accessorKey: "type",
                cell: ({ row }) => (
                  <Badge variant="outline" className="capitalize">
                    {row.original.type}
                  </Badge>
                ),
              },
              {
                id: "status",
                header: "Status",
                accessorKey: "status",
                cell: ({ row }) => getStatusBadge(row.original.status),
              },
              {
                id: "created_at",
                header: "Reported At",
                accessorKey: "created_at",
                cell: ({ row }) => (
                  <TimeAgo
                    date={new Date(row.original.created_at)}
                    className="text-muted-foreground"
                  />
                ),
              },
              {
                id: "actions",
                header: "",
                cell: ({ row }) => {
                  return (
                    <div className="flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link to={`/app/drivers/incidents/${row.original.id}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  );
                },
              },
            ]}
          >
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileWarning className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="text-xl font-semibold">No resolved incidents</h3>
              <p className="text-muted-foreground mt-2 mb-6">
                You don't have any resolved incidents for your shipments
              </p>
              <Button asChild>
                <Link to="/app/drivers/incidents/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Report Incident
                </Link>
              </Button>
            </div>
          </ListTable>
        </TabsContent>
      </Tabs>
    </div>
  );
}
