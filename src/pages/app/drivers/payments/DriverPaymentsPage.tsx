import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  DollarSign,
  Download,
  FileText,
  PlusIcon,
  TrendingUp,
} from "lucide-react";
import { useNavigate } from "react-router";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { UserContextType } from "@/contexts/User";

export interface PaymentTransaction {
  id: string;
  amount: number;
  type: "payment" | "adjustment" | "bonus" | "deduction";
  status: "pending" | "completed" | "failed" | "processing";
  description: string;
  date: string;
  reference_number?: string;
  shipment_id?: string;
  payment_method?: string;
}

export interface EarningsSummary {
  currentWeek: number;
  currentMonth: number;
  lastWeek: number;
  lastMonth: number;
  totalEarnings: number;
  pendingPayments: number;
  weeklyGrowth: number;
  monthlyGrowth: number;
}

export interface PaymentMethod {
  id: string;
  type: "bank_account" | "debit_card" | "digital_wallet";
  last_four: string;
  bank_name?: string;
  account_type?: string;
  is_primary: boolean;
  is_verified: boolean;
  created_at: string;
}

export interface TaxDocument {
  id: string;
  type: "1099" | "earnings_statement" | "tax_summary";
  year: number;
  period?: string;
  amount: number;
  file_url?: string;
  generated_at: string;
  downloaded_at?: string;
}

export interface DriverPaymentsPageProps {
  // User data
  driver: UserContextType["driver"];
  isLoading: boolean;

  // Payment data
  transactions: PaymentTransaction[];
  isLoadingTransactions: boolean;
  transactionsError: Error | null;

  // Earnings data
  earnings: EarningsSummary;
  isLoadingEarnings: boolean;
  earningsError: Error | null;

  // Payment methods
  paymentMethods: PaymentMethod[];
  isLoadingPaymentMethods: boolean;
  paymentMethodsError: Error | null;

  // Tax documents
  taxDocuments: TaxDocument[];
  isLoadingTaxDocuments: boolean;
  taxDocumentsError: Error | null;

  // Filter and period state
  selectedPeriod: "week" | "month" | "quarter" | "year";
  selectedTransactionType: string | null;

  // Function handlers
  onPeriodChange: (period: "week" | "month" | "quarter" | "year") => void;
  onTransactionTypeChange: (type: string | null) => void;
  onDownloadTaxDocument: (documentId: string) => void;
  onSetupBanking: () => void;
  onAddPaymentMethod: () => void;
  onSetPrimaryPaymentMethod: (methodId: string) => void;
  onRequestPayment: () => void;
}

export const DriverPaymentsPage = ({
  driver,
  isLoading,
  transactions,
  isLoadingTransactions,
  transactionsError,
  earnings,
  isLoadingEarnings,
  earningsError,
  paymentMethods,
  isLoadingPaymentMethods,
  paymentMethodsError,
  taxDocuments,
  isLoadingTaxDocuments,
  taxDocumentsError,
  selectedPeriod,
  selectedTransactionType,
  onPeriodChange,
  onTransactionTypeChange,
  onDownloadTaxDocument,
  onSetupBanking,
  onAddPaymentMethod,
  onSetPrimaryPaymentMethod,
  onRequestPayment,
}: DriverPaymentsPageProps) => {
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="mb-8 space-y-4">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-6 w-96" />
        </div>
        <div className="mb-8 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-3 w-16" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid gap-6 lg:grid-cols-[2fr_1fr]">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-28" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!driver) {
    return (
      <div className="container py-8">
        <Card className="mx-auto max-w-2xl">
          <CardContent className="flex flex-col items-center px-8 pt-6 pb-8 text-center">
            <DollarSign className="text-muted-foreground mb-4 h-12 w-12" />
            <h2 className="mb-2 text-2xl font-semibold">
              Driver Profile Required
            </h2>
            <p className="text-muted-foreground mb-6">
              You need a driver profile to access payment information and manage
              your earnings.
            </p>
            <Button
              onClick={() => navigate("/app/onboarding/driver")}
              size="lg"
            >
              Create Driver Profile
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getTransactionStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="default" className="bg-green-500">
            Completed
          </Badge>
        );
      case "pending":
        return <Badge variant="secondary">Pending</Badge>;
      case "processing":
        return (
          <Badge variant="default" className="bg-blue-500">
            Processing
          </Badge>
        );
      case "failed":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTransactionTypeBadge = (type: string) => {
    switch (type) {
      case "payment":
        return (
          <Badge variant="default" className="bg-green-500">
            Payment
          </Badge>
        );
      case "bonus":
        return (
          <Badge variant="default" className="bg-blue-500">
            Bonus
          </Badge>
        );
      case "adjustment":
        return <Badge variant="secondary">Adjustment</Badge>;
      case "deduction":
        return <Badge variant="destructive">Deduction</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? "+" : "";
    return `${sign}${value.toFixed(1)}%`;
  };

  return (
    <div className="container py-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="mb-2 text-4xl font-bold">Payments</h1>
          <p className="text-muted-foreground text-lg">
            View and manage your payment information and earnings
          </p>
        </div>
        <div className="flex gap-2">
          {paymentMethods.length === 0 ? (
            <Button onClick={onSetupBanking} className="gap-2">
              <PlusIcon className="h-4 w-4" />
              Setup Banking
            </Button>
          ) : (
            <>
              <Button
                onClick={onAddPaymentMethod}
                variant="outline"
                className="gap-2"
              >
                <CreditCard className="h-4 w-4" />
                Add Payment Method
              </Button>
              <Button onClick={onRequestPayment} className="gap-2">
                <DollarSign className="h-4 w-4" />
                Request Payment
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Earnings Summary Cards */}
      <div className="mb-8 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  This Week
                </p>
                <p className="text-2xl font-bold">
                  {isLoadingEarnings ? (
                    <Skeleton className="h-8 w-20" />
                  ) : (
                    formatCurrency(earnings.currentWeek)
                  )}
                </p>
                <p
                  className={`text-xs ${earnings.weeklyGrowth >= 0 ? "text-green-600" : "text-red-600"}`}
                >
                  {isLoadingEarnings ? (
                    <Skeleton className="h-3 w-16" />
                  ) : (
                    formatPercentage(earnings.weeklyGrowth)
                  )}
                </p>
              </div>
              <TrendingUp className="text-muted-foreground h-8 w-8" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  This Month
                </p>
                <p className="text-2xl font-bold">
                  {isLoadingEarnings ? (
                    <Skeleton className="h-8 w-20" />
                  ) : (
                    formatCurrency(earnings.currentMonth)
                  )}
                </p>
                <p
                  className={`text-xs ${earnings.monthlyGrowth >= 0 ? "text-green-600" : "text-red-600"}`}
                >
                  {isLoadingEarnings ? (
                    <Skeleton className="h-3 w-16" />
                  ) : (
                    formatPercentage(earnings.monthlyGrowth)
                  )}
                </p>
              </div>
              <Calendar className="text-muted-foreground h-8 w-8" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Total Earnings
                </p>
                <p className="text-2xl font-bold">
                  {isLoadingEarnings ? (
                    <Skeleton className="h-8 w-20" />
                  ) : (
                    formatCurrency(earnings.totalEarnings)
                  )}
                </p>
                <p className="text-muted-foreground text-xs">All time</p>
              </div>
              <DollarSign className="text-muted-foreground h-8 w-8" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Pending
                </p>
                <p className="text-2xl font-bold">
                  {isLoadingEarnings ? (
                    <Skeleton className="h-8 w-20" />
                  ) : (
                    formatCurrency(earnings.pendingPayments)
                  )}
                </p>
                <p className="text-muted-foreground text-xs">Processing</p>
              </div>
              <Clock className="text-muted-foreground h-8 w-8" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-[2fr_1fr]">
        {/* Payment History */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Payment History</CardTitle>
              <div className="flex gap-2">
                <select
                  value={selectedPeriod}
                  onChange={(e) =>
                    onPeriodChange(
                      e.target.value as "week" | "month" | "quarter" | "year",
                    )
                  }
                  className="rounded border px-3 py-1 text-sm"
                >
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                </select>
                <select
                  value={selectedTransactionType || "all"}
                  onChange={(e) =>
                    onTransactionTypeChange(
                      e.target.value === "all" ? null : e.target.value,
                    )
                  }
                  className="rounded border px-3 py-1 text-sm"
                >
                  <option value="all">All Types</option>
                  <option value="payment">Payments</option>
                  <option value="bonus">Bonuses</option>
                  <option value="adjustment">Adjustments</option>
                  <option value="deduction">Deductions</option>
                </select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoadingTransactions ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-4 w-4 rounded" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                ))}
              </div>
            ) : transactionsError ? (
              <div className="py-8 text-center">
                <AlertCircle className="text-destructive mx-auto mb-2 h-8 w-8" />
                <p className="text-destructive text-sm">
                  Failed to load payment history
                </p>
              </div>
            ) : transactions.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {transaction.description}
                          </p>
                          {transaction.reference_number && (
                            <p className="text-muted-foreground text-sm">
                              Ref: {transaction.reference_number}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getTransactionTypeBadge(transaction.type)}
                      </TableCell>
                      <TableCell
                        className={`font-medium ${
                          transaction.type === "deduction"
                            ? "text-red-600"
                            : "text-green-600"
                        }`}
                      >
                        {transaction.type === "deduction" ? "-" : "+"}
                        {formatCurrency(Math.abs(transaction.amount))}
                      </TableCell>
                      <TableCell>
                        {getTransactionStatusBadge(transaction.status)}
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {new Date(transaction.date).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="py-12 text-center">
                <DollarSign className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="text-xl font-semibold">No payment history</h3>
                <p className="text-muted-foreground mt-2 mb-6">
                  You haven't received any payments yet. Complete deliveries to
                  start earning.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sidebar - Payment Methods and Tax Documents */}
        <div className="space-y-6">
          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Payment Methods
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingPaymentMethods ? (
                <div className="space-y-3">
                  {[...Array(2)].map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  ))}
                </div>
              ) : paymentMethodsError ? (
                <div className="py-4 text-center">
                  <AlertCircle className="text-destructive mx-auto mb-2 h-6 w-6" />
                  <p className="text-destructive text-sm">
                    Failed to load payment methods
                  </p>
                </div>
              ) : paymentMethods.length > 0 ? (
                <div className="space-y-3">
                  {paymentMethods.map((method) => (
                    <div key={method.id} className="rounded-lg border p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium capitalize">
                            {method.type.replace("_", " ")}
                          </p>
                          <p className="text-muted-foreground text-sm">
                            ****{method.last_four}
                          </p>
                          {method.bank_name && (
                            <p className="text-muted-foreground text-xs">
                              {method.bank_name}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          {method.is_primary && (
                            <Badge variant="default" className="text-xs">
                              Primary
                            </Badge>
                          )}
                          {method.is_verified ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-orange-500" />
                          )}
                        </div>
                      </div>
                      {!method.is_primary && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-2 w-full"
                          onClick={() => onSetPrimaryPaymentMethod(method.id)}
                        >
                          Set as Primary
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={onAddPaymentMethod}
                  >
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Add Payment Method
                  </Button>
                </div>
              ) : (
                <div className="py-6 text-center">
                  <CreditCard className="text-muted-foreground mx-auto mb-3 h-8 w-8" />
                  <p className="text-muted-foreground mb-3 text-sm">
                    No payment methods setup
                  </p>
                  <Button size="sm" onClick={onSetupBanking}>
                    Setup Banking
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tax Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Tax Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingTaxDocuments ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-4 w-28" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                  ))}
                </div>
              ) : taxDocumentsError ? (
                <div className="py-4 text-center">
                  <AlertCircle className="text-destructive mx-auto mb-2 h-6 w-6" />
                  <p className="text-destructive text-sm">
                    Failed to load tax documents
                  </p>
                </div>
              ) : taxDocuments.length > 0 ? (
                <div className="space-y-3">
                  {taxDocuments.map((document) => (
                    <div key={document.id} className="rounded-lg border p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">
                            {document.type.toUpperCase()} {document.year}
                          </p>
                          {document.period && (
                            <p className="text-muted-foreground text-sm">
                              {document.period}
                            </p>
                          )}
                          <p className="text-muted-foreground text-xs">
                            {formatCurrency(document.amount)}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDownloadTaxDocument(document.id)}
                          disabled={!document.file_url}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center">
                  <FileText className="text-muted-foreground mx-auto mb-3 h-8 w-8" />
                  <p className="text-muted-foreground text-sm">
                    No tax documents available
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
