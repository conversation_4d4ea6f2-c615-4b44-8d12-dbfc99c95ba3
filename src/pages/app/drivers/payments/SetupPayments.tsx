import { useState } from "react";
import {
  ArrowLeft,
  Building,
  Check,
  CreditCard,
  DollarSign,
  HelpCircle,
  LockIcon,
} from "lucide-react";
import { <PERSON> } from "react-router";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";

const bankAccountSchema = z.object({
  accountType: z.enum(["checking", "savings"]),
  accountNumber: z.string().min(6, {
    message: "Account number must be at least 6 characters",
  }),
  routingNumber: z
    .string()
    .min(9, {
      message: "Routing number must be 9 digits",
    })
    .max(9, {
      message: "Routing number must be 9 digits",
    }),
  name: z.string().min(2, {
    message: "Name must be at least 2 characters",
  }),
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms and conditions to continue",
  }),
});

const cardSchema = z.object({
  cardNumber: z
    .string()
    .min(16, {
      message: "Card number must be at least 16 digits",
    })
    .max(19, {
      message: "Card number must be at most 19 digits",
    }),
  cardName: z.string().min(2, {
    message: "Name must be at least 2 characters",
  }),
  expiryDate: z.string().regex(/^(0[1-9]|1[0-2])\/\d{2}$/, {
    message: "Expiry date must be in the format MM/YY",
  }),
  cvv: z
    .string()
    .min(3, {
      message: "CVV must be at least 3 digits",
    })
    .max(4, {
      message: "CVV must be at most 4 digits",
    }),
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms and conditions to continue",
  }),
});

export default function SetupPayments() {
  const [activeTab, setActiveTab] = useState("bank");
  const { toast } = useToast();

  const bankForm = useForm({
    schema: bankAccountSchema,
    defaultValues: {
      accountType: "checking",
      accountNumber: "",
      routingNumber: "",
      name: "",
      acceptTerms: false,
    },
  });

  const cardForm = useForm({
    schema: cardSchema,
    defaultValues: {
      cardNumber: "",
      cardName: "",
      expiryDate: "",
      cvv: "",
      acceptTerms: false,
    },
  });

  const onBankSubmit = (values) => {
    console.log(values);
    toast({
      title: "Banking information submitted",
      description: "We'll connect your account and notify you when it's ready.",
    });
  };

  const onCardSubmit = (values) => {
    console.log(values);
    toast({
      title: "Card information submitted",
      description: "We'll connect your card and notify you when it's ready.",
    });
  };

  return (
    <div className="container py-8">
      <div className="mb-8 flex items-center gap-4">
        <Link to="/app/drivers/payments">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="mb-2 text-4xl font-bold">Setup Payments</h1>
          <p className="text-muted-foreground">
            Connect your bank account to receive payments and tips directly
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Connect Payment Method</CardTitle>
              <CardDescription>
                Choose how you want to receive payments from deliveries and tips
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs
                defaultValue="bank"
                value={activeTab}
                onValueChange={setActiveTab}
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="bank" className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Bank Account
                  </TabsTrigger>
                  <TabsTrigger value="card" className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Debit Card
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="bank" className="mt-6">
                  <Form {...bankForm}>
                    <form
                      onSubmit={bankForm.handleSubmit(onBankSubmit)}
                      className="space-y-6"
                    >
                      <FormField
                        control={bankForm.control}
                        name="accountType"
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <FormLabel>Account Type</FormLabel>
                            <FormDescription>
                              Select the type of bank account
                            </FormDescription>
                            <FormControl>
                              <RadioGroup
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                className="flex flex-col space-y-1"
                              >
                                <FormItem className="flex items-center space-y-0 space-x-3">
                                  <FormControl>
                                    <RadioGroupItem value="checking" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    Checking Account
                                  </FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-y-0 space-x-3">
                                  <FormControl>
                                    <RadioGroupItem value="savings" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    Savings Account
                                  </FormLabel>
                                </FormItem>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                          control={bankForm.control}
                          name="routingNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Routing Number</FormLabel>
                              <FormDescription>
                                Your bank's 9-digit routing number
                              </FormDescription>
                              <FormControl>
                                <Input placeholder="*********" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={bankForm.control}
                          name="accountNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Account Number</FormLabel>
                              <FormDescription>
                                Your bank account number
                              </FormDescription>
                              <FormControl>
                                <Input
                                  placeholder="**************"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={bankForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Account Holder Name</FormLabel>
                            <FormDescription>
                              Name as it appears on your bank account
                            </FormDescription>
                            <FormControl>
                              <Input placeholder="John Doe" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={bankForm.control}
                        name="acceptTerms"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Accept Terms and Conditions</FormLabel>
                              <FormDescription>
                                I agree to the{" "}
                                <a
                                  href="#"
                                  className="text-primary hover:underline"
                                >
                                  terms and conditions
                                </a>{" "}
                                and authorize FleetFlow to verify my bank
                                account information.
                              </FormDescription>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button type="submit" className="w-full">
                        Connect Bank Account
                      </Button>
                    </form>
                  </Form>
                </TabsContent>

                <TabsContent value="card" className="mt-6">
                  <Form {...cardForm}>
                    <form
                      onSubmit={cardForm.handleSubmit(onCardSubmit)}
                      className="space-y-6"
                    >
                      <FormField
                        control={cardForm.control}
                        name="cardNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Card Number</FormLabel>
                            <FormDescription>
                              The long number on the front of your card
                            </FormDescription>
                            <FormControl>
                              <Input
                                placeholder="1234 5678 9012 3456"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={cardForm.control}
                        name="cardName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name on Card</FormLabel>
                            <FormDescription>
                              The name as it appears on your card
                            </FormDescription>
                            <FormControl>
                              <Input placeholder="John Doe" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                          control={cardForm.control}
                          name="expiryDate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Expiry Date</FormLabel>
                              <FormDescription>
                                The expiry date on your card (MM/YY)
                              </FormDescription>
                              <FormControl>
                                <Input placeholder="06/25" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={cardForm.control}
                          name="cvv"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>CVV</FormLabel>
                              <FormDescription>
                                The 3 or 4 digit security code on your card
                              </FormDescription>
                              <FormControl>
                                <Input placeholder="123" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={cardForm.control}
                        name="acceptTerms"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Accept Terms and Conditions</FormLabel>
                              <FormDescription>
                                I agree to the{" "}
                                <a
                                  href="#"
                                  className="text-primary hover:underline"
                                >
                                  terms and conditions
                                </a>{" "}
                                and authorize FleetFlow to make deposits to this
                                card.
                              </FormDescription>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button type="submit" className="w-full">
                        Connect Debit Card
                      </Button>
                    </form>
                  </Form>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="text-primary h-5 w-5" />
                Benefits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-4">
                <li className="flex items-start gap-2">
                  <Check className="mt-1 h-5 w-5 shrink-0 text-green-500" />
                  <span>Get paid instantly after deliveries are complete</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="mt-1 h-5 w-5 shrink-0 text-green-500" />
                  <span>Receive tips directly from customers</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="mt-1 h-5 w-5 shrink-0 text-green-500" />
                  <span>Track all your earnings in one place</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="mt-1 h-5 w-5 shrink-0 text-green-500" />
                  <span>No hidden fees or minimum balance requirements</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LockIcon className="text-primary h-5 w-5" />
                Secure Payments
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4 text-sm">
                All payment information is securely processed through Stripe, a
                PCI-compliant payment processor. Your banking details are never
                stored on our servers.
              </p>
              <div className="bg-muted rounded-md p-3 text-center">
                <div className="flex items-center justify-center gap-2">
                  <LockIcon className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">
                    256-bit encryption
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HelpCircle className="text-primary h-5 w-5" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4 text-sm">
                If you're having trouble connecting your payment method or have
                questions, our support team is here to help.
              </p>
              <Button variant="outline" className="w-full">
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
