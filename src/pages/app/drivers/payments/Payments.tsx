import { useCallback, useState } from "react";
import { useNavigate } from "react-router";
import { toast } from "sonner";

import { useUser } from "@/contexts/User";
import {
  DriverPaymentsPage,
  EarningsSummary,
  PaymentMethod,
  PaymentTransaction,
  TaxDocument,
} from "./DriverPaymentsPage";

// Mock data for development
const mockTransactions: PaymentTransaction[] = [
  {
    id: "PMT-001",
    amount: 325.5,
    type: "payment",
    status: "completed",
    description: "Weekly payment for deliveries",
    date: new Date(Date.now() - *********).toISOString(), // 2 days ago
    reference_number: "REF-2024-001",
    shipment_id: "shipment-456",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-002",
    amount: 275.0,
    type: "payment",
    status: "completed",
    description: "Route completion bonus",
    date: new Date(Date.now() - *********).toISOString(), // 3 days ago
    reference_number: "REF-2024-002",
    shipment_id: "shipment-789",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-003",
    amount: 150.75,
    type: "payment",
    status: "pending",
    description: "Express delivery payment",
    date: new Date(Date.now() - ********).toISOString(), // 1 day ago
    reference_number: "REF-2024-003",
    shipment_id: "shipment-101",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-004",
    amount: 50.0,
    type: "bonus",
    status: "completed",
    description: "On-time delivery bonus",
    date: new Date(Date.now() - *********).toISOString(), // 4 days ago
    reference_number: "BON-2024-001",
    shipment_id: "shipment-202",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-005",
    amount: 25.0,
    type: "deduction",
    status: "completed",
    description: "Fuel surcharge adjustment",
    date: new Date(Date.now() - *********).toISOString(), // 5 days ago
    reference_number: "ADJ-2024-001",
    shipment_id: "shipment-303",
    payment_method: "Bank Transfer",
  },
];

const mockEarnings: EarningsSummary = {
  currentWeek: 751.25,
  currentMonth: 2847.5,
  lastWeek: 692.0,
  lastMonth: 2654.75,
  totalEarnings: 18945.3,
  pendingPayments: 150.75,
  weeklyGrowth: 8.6,
  monthlyGrowth: 7.3,
};

const mockPaymentMethods: PaymentMethod[] = [
  {
    id: "method-1",
    type: "bank_account",
    last_four: "4521",
    bank_name: "Chase Bank",
    account_type: "Checking",
    is_primary: true,
    is_verified: true,
    created_at: new Date(Date.now() - *********0).toISOString(), // 30 days ago
  },
  {
    id: "method-2",
    type: "debit_card",
    last_four: "9876",
    bank_name: "Wells Fargo",
    account_type: "Debit Card",
    is_primary: false,
    is_verified: true,
    created_at: new Date(Date.now() - **********).toISOString(), // 15 days ago
  },
];

const mockTaxDocuments: TaxDocument[] = [
  {
    id: "tax-1",
    type: "1099",
    year: 2024,
    amount: 18945.3,
    file_url: "/documents/1099-2024.pdf",
    generated_at: new Date(Date.now() - *********0).toISOString(),
    downloaded_at: new Date(Date.now() - **********).toISOString(),
  },
  {
    id: "tax-2",
    type: "earnings_statement",
    year: 2024,
    period: "Q4 2024",
    amount: 4523.75,
    file_url: "/documents/earnings-q4-2024.pdf",
    generated_at: new Date(Date.now() - *********).toISOString(),
  },
  {
    id: "tax-3",
    type: "tax_summary",
    year: 2023,
    amount: 15432.8,
    file_url: "/documents/tax-summary-2023.pdf",
    generated_at: new Date(Date.now() - **********).toISOString(),
    downloaded_at: new Date(Date.now() - **********).toISOString(),
  },
];

const DriverPayments = () => {
  const navigate = useNavigate();
  const { driver } = useUser();

  // Local state
  const [transactions] = useState<PaymentTransaction[]>(mockTransactions);
  const [earnings] = useState<EarningsSummary>(mockEarnings);
  const [paymentMethods, setPaymentMethods] =
    useState<PaymentMethod[]>(mockPaymentMethods);
  const [taxDocuments] = useState<TaxDocument[]>(mockTaxDocuments);

  // Filter and period state
  const [selectedPeriod, setSelectedPeriod] = useState<
    "week" | "month" | "quarter" | "year"
  >("month");
  const [selectedTransactionType, setSelectedTransactionType] = useState<
    string | null
  >(null);

  // Loading states
  const [isLoading] = useState(false);
  const [isLoadingTransactions] = useState(false);
  const [isLoadingEarnings] = useState(false);
  const [isLoadingPaymentMethods] = useState(false);
  const [isLoadingTaxDocuments] = useState(false);

  // Error states
  const [transactionsError] = useState<Error | null>(null);
  const [earningsError] = useState<Error | null>(null);
  const [paymentMethodsError] = useState<Error | null>(null);
  const [taxDocumentsError] = useState<Error | null>(null);

  // Event handlers
  const handlePeriodChange = useCallback(
    (period: "week" | "month" | "quarter" | "year") => {
      setSelectedPeriod(period);
      toast.info(`Viewing ${period} payments`);
    },
    [],
  );

  const handleTransactionTypeChange = useCallback((type: string | null) => {
    setSelectedTransactionType(type);
    toast.info(type ? `Filtering by ${type}` : "Showing all transactions");
  }, []);

  const handleDownloadTaxDocument = useCallback(
    (documentId: string) => {
      const document = taxDocuments.find((doc) => doc.id === documentId);
      if (document?.file_url) {
        toast.success(
          `Downloading ${document.type.toUpperCase()} for ${document.year}`,
        );
        // In a real app, this would trigger a file download
        console.log("Download document:", document);
      } else {
        toast.error("Document not available for download");
      }
    },
    [taxDocuments],
  );

  const handleSetupBanking = useCallback(() => {
    toast.info("Redirecting to banking setup");
    navigate("/app/drivers/payments/setup");
  }, [navigate]);

  const handleAddPaymentMethod = useCallback(() => {
    toast.info("Opening payment method setup");
    navigate("/app/drivers/payments/methods/add");
  }, [navigate]);

  const handleSetPrimaryPaymentMethod = useCallback((methodId: string) => {
    setPaymentMethods((prev) =>
      prev.map((method) => ({
        ...method,
        is_primary: method.id === methodId,
      })),
    );
    toast.success("Primary payment method updated");
  }, []);

  const handleRequestPayment = useCallback(() => {
    if (earnings.pendingPayments > 0) {
      toast.success(
        `Payment request submitted for ${new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(earnings.pendingPayments)}`,
      );
    } else {
      toast.info("No pending payments to request");
    }
  }, [earnings.pendingPayments]);

  // Filter transactions based on selected criteria
  const filteredTransactions = transactions.filter((transaction) => {
    if (
      selectedTransactionType &&
      transaction.type !== selectedTransactionType
    ) {
      return false;
    }

    // Filter by period (simplified for demo)
    const transactionDate = new Date(transaction.date);
    const now = new Date();

    switch (selectedPeriod) {
      case "week": {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return transactionDate >= weekAgo;
      }
      case "month": {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return transactionDate >= monthAgo;
      }
      case "quarter": {
        const quarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        return transactionDate >= quarterAgo;
      }
      case "year": {
        const yearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        return transactionDate >= yearAgo;
      }
      default:
        return true;
    }
  });

  return (
    <DriverPaymentsPage
      driver={driver}
      isLoading={isLoading}
      transactions={filteredTransactions}
      isLoadingTransactions={isLoadingTransactions}
      transactionsError={transactionsError}
      earnings={earnings}
      isLoadingEarnings={isLoadingEarnings}
      earningsError={earningsError}
      paymentMethods={paymentMethods}
      isLoadingPaymentMethods={isLoadingPaymentMethods}
      paymentMethodsError={paymentMethodsError}
      taxDocuments={taxDocuments}
      isLoadingTaxDocuments={isLoadingTaxDocuments}
      taxDocumentsError={taxDocumentsError}
      selectedPeriod={selectedPeriod}
      selectedTransactionType={selectedTransactionType}
      onPeriodChange={handlePeriodChange}
      onTransactionTypeChange={handleTransactionTypeChange}
      onDownloadTaxDocument={handleDownloadTaxDocument}
      onSetupBanking={handleSetupBanking}
      onAddPaymentMethod={handleAddPaymentMethod}
      onSetPrimaryPaymentMethod={handleSetPrimaryPaymentMethod}
      onRequestPayment={handleRequestPayment}
    />
  );
};

export default DriverPayments;
