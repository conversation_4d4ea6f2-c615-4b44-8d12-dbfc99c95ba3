import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";

import { CreditCard, DollarSign, FileText, TrendingUp } from "lucide-react";
import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { DriverPaymentsPage } from "./DriverPaymentsPage";

const meta: Meta<typeof DriverPaymentsPage> = {
  title: "Pages/Drivers/Payments",
  component: DriverPaymentsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockDriver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  phone_number: "+**********",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active" as const,
  tier: "professional",
  created_at: new Date().toISOString(),
  verified_at: new Date().toISOString(),
};

const mockTransactions = [
  {
    id: "PMT-001",
    amount: 325.5,
    type: "payment" as const,
    status: "completed" as const,
    description: "Weekly payment for deliveries",
    date: new Date(Date.now() - *********).toISOString(),
    reference_number: "REF-2024-001",
    shipment_id: "shipment-456",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-002",
    amount: 275.0,
    type: "payment" as const,
    status: "completed" as const,
    description: "Route completion bonus",
    date: new Date(Date.now() - *********).toISOString(),
    reference_number: "REF-2024-002",
    shipment_id: "shipment-789",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-003",
    amount: 150.75,
    type: "payment" as const,
    status: "pending" as const,
    description: "Express delivery payment",
    date: new Date(Date.now() - ********).toISOString(),
    reference_number: "REF-2024-003",
    shipment_id: "shipment-101",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-004",
    amount: 50.0,
    type: "bonus" as const,
    status: "completed" as const,
    description: "On-time delivery bonus",
    date: new Date(Date.now() - *********).toISOString(),
    reference_number: "BON-2024-001",
    shipment_id: "shipment-202",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-005",
    amount: 25.0,
    type: "deduction" as const,
    status: "completed" as const,
    description: "Fuel surcharge adjustment",
    date: new Date(Date.now() - *********).toISOString(),
    reference_number: "ADJ-2024-001",
    shipment_id: "shipment-303",
    payment_method: "Bank Transfer",
  },
];

const mockHighEarningsTransactions = [
  {
    id: "PMT-H001",
    amount: 1250.0,
    type: "payment" as const,
    status: "completed" as const,
    description: "Long-haul cross-country delivery",
    date: new Date(Date.now() - ********).toISOString(),
    reference_number: "REF-2024-H001",
    shipment_id: "shipment-999",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-H002",
    amount: 850.75,
    type: "payment" as const,
    status: "completed" as const,
    description: "Express freight delivery",
    date: new Date(Date.now() - *********).toISOString(),
    reference_number: "REF-2024-H002",
    shipment_id: "shipment-888",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-H003",
    amount: 500.0,
    type: "bonus" as const,
    status: "completed" as const,
    description: "Perfect safety record bonus",
    date: new Date(Date.now() - *********).toISOString(),
    reference_number: "BON-2024-H001",
    shipment_id: "shipment-777",
    payment_method: "Bank Transfer",
  },
];

const mockPendingTransactions = [
  {
    id: "PMT-P001",
    amount: 425.3,
    type: "payment" as const,
    status: "pending" as const,
    description: "Weekly delivery payment",
    date: new Date(Date.now() - ********).toISOString(),
    reference_number: "REF-2024-P001",
    shipment_id: "shipment-P01",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-P002",
    amount: 187.5,
    type: "payment" as const,
    status: "processing" as const,
    description: "Express route completion",
    date: new Date(Date.now() - ********).toISOString(),
    reference_number: "REF-2024-P002",
    shipment_id: "shipment-P02",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-P003",
    amount: 75.0,
    type: "bonus" as const,
    status: "pending" as const,
    description: "Customer service excellence bonus",
    date: new Date(Date.now() - ********).toISOString(),
    reference_number: "BON-2024-P001",
    shipment_id: "shipment-P03",
    payment_method: "Bank Transfer",
  },
];

const mockRecentTransactions = [
  {
    id: "PMT-R001",
    amount: 215.75,
    type: "payment" as const,
    status: "completed" as const,
    description: "Local delivery payment",
    date: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    reference_number: "REF-2024-R001",
    shipment_id: "shipment-R01",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-R002",
    amount: 125.0,
    type: "payment" as const,
    status: "completed" as const,
    description: "Same-day delivery payment",
    date: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    reference_number: "REF-2024-R002",
    shipment_id: "shipment-R02",
    payment_method: "Bank Transfer",
  },
  {
    id: "PMT-R003",
    amount: 35.0,
    type: "bonus" as const,
    status: "completed" as const,
    description: "Early delivery bonus",
    date: new Date(Date.now() - ********).toISOString(), // 3 hours ago
    reference_number: "BON-2024-R001",
    shipment_id: "shipment-R03",
    payment_method: "Bank Transfer",
  },
];

const mockEarnings = {
  currentWeek: 751.25,
  currentMonth: 2847.5,
  lastWeek: 692.0,
  lastMonth: 2654.75,
  totalEarnings: 18945.3,
  pendingPayments: 150.75,
  weeklyGrowth: 8.6,
  monthlyGrowth: 7.3,
};

const mockHighEarnings = {
  currentWeek: 2850.5,
  currentMonth: 12475.25,
  lastWeek: 2654.75,
  lastMonth: 11230.8,
  totalEarnings: 125430.75,
  pendingPayments: 687.8,
  weeklyGrowth: 7.4,
  monthlyGrowth: 11.1,
};

const mockLowEarnings = {
  currentWeek: 125.0,
  currentMonth: 458.75,
  lastWeek: 98.5,
  lastMonth: 412.3,
  totalEarnings: 2847.5,
  pendingPayments: 687.8,
  weeklyGrowth: 26.9,
  monthlyGrowth: 11.3,
};

const mockPaymentMethods = [
  {
    id: "method-1",
    type: "bank_account" as const,
    last_four: "4521",
    bank_name: "Chase Bank",
    account_type: "Checking",
    is_primary: true,
    is_verified: true,
    created_at: new Date(Date.now() - **********).toISOString(),
  },
  {
    id: "method-2",
    type: "debit_card" as const,
    last_four: "9876",
    bank_name: "Wells Fargo",
    account_type: "Debit Card",
    is_primary: false,
    is_verified: true,
    created_at: new Date(Date.now() - **********).toISOString(),
  },
];

const mockUnverifiedPaymentMethods = [
  {
    id: "method-unverified",
    type: "bank_account" as const,
    last_four: "7890",
    bank_name: "Bank of America",
    account_type: "Checking",
    is_primary: true,
    is_verified: false,
    created_at: new Date(Date.now() - *********).toISOString(),
  },
];

const mockTaxDocuments = [
  {
    id: "tax-1",
    type: "1099" as const,
    year: 2024,
    amount: 18945.3,
    file_url: "/documents/1099-2024.pdf",
    generated_at: new Date(Date.now() - **********).toISOString(),
    downloaded_at: new Date(Date.now() - **********).toISOString(),
  },
  {
    id: "tax-2",
    type: "earnings_statement" as const,
    year: 2024,
    period: "Q4 2024",
    amount: 4523.75,
    file_url: "/documents/earnings-q4-2024.pdf",
    generated_at: new Date(Date.now() - *********).toISOString(),
  },
  {
    id: "tax-3",
    type: "tax_summary" as const,
    year: 2023,
    amount: 15432.8,
    file_url: "/documents/tax-summary-2023.pdf",
    generated_at: new Date(Date.now() - **********).toISOString(),
    downloaded_at: new Date(Date.now() - **********).toISOString(),
  },
];

export const Default: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: mockTransactions,
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: mockEarnings,
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: mockTaxDocuments,
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "month",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const Loading: Story = {
  args: {
    driver: mockDriver,
    isLoading: true,
    transactions: [],
    isLoadingTransactions: true,
    transactionsError: null,
    earnings: mockEarnings,
    isLoadingEarnings: true,
    earningsError: null,
    paymentMethods: [],
    isLoadingPaymentMethods: true,
    paymentMethodsError: null,
    taxDocuments: [],
    isLoadingTaxDocuments: true,
    taxDocumentsError: null,
    selectedPeriod: "month",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const PaymentHistory: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: [
      ...mockTransactions,
      ...mockHighEarningsTransactions.slice(0, 2),
      ...mockPendingTransactions.slice(0, 1),
    ],
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: mockEarnings,
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: mockTaxDocuments,
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "quarter",
    selectedTransactionType: "payment",
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const EarningsSummary: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: mockTransactions,
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: {
      ...mockEarnings,
      currentWeek: 1250.75,
      currentMonth: 4823.5,
      weeklyGrowth: 18.2,
      monthlyGrowth: 15.7,
    },
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: mockTaxDocuments,
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "week",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const NoPayments: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: [],
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: {
      ...mockLowEarnings,
      currentWeek: 0,
      currentMonth: 0,
      totalEarnings: 0,
      pendingPayments: 0,
      weeklyGrowth: 0,
      monthlyGrowth: 0,
    },
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: [],
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: [],
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "month",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const PendingPayments: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: mockPendingTransactions,
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: {
      ...mockEarnings,
      pendingPayments: 687.8,
      currentWeek: 425.3,
      currentMonth: 1247.85,
    },
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: mockTaxDocuments,
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "week",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const PaymentMethods: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: mockTransactions,
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: mockEarnings,
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: [
      ...mockPaymentMethods,
      {
        id: "method-3",
        type: "digital_wallet" as const,
        last_four: "1234",
        bank_name: "PayPal",
        account_type: "Digital Wallet",
        is_primary: false,
        is_verified: true,
        created_at: new Date(Date.now() - *********).toISOString(),
      },
      ...mockUnverifiedPaymentMethods,
    ],
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: mockTaxDocuments,
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "month",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const TaxDocuments: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: mockTransactions,
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: mockEarnings,
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: [
      ...mockTaxDocuments,
      {
        id: "tax-4",
        type: "earnings_statement" as const,
        year: 2024,
        period: "Q3 2024",
        amount: 3842.75,
        file_url: "/documents/earnings-q3-2024.pdf",
        generated_at: new Date(Date.now() - **********).toISOString(),
      },
      {
        id: "tax-5",
        type: "earnings_statement" as const,
        year: 2024,
        period: "Q2 2024",
        amount: 4127.9,
        file_url: "/documents/earnings-q2-2024.pdf",
        generated_at: new Date(Date.now() - **********).toISOString(),
        downloaded_at: new Date(Date.now() - **********).toISOString(),
      },
    ],
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "year",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const HighEarnings: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: mockHighEarningsTransactions,
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: mockHighEarnings,
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: [
      {
        id: "tax-high-1",
        type: "1099" as const,
        year: 2024,
        amount: 125430.75,
        file_url: "/documents/1099-2024-high.pdf",
        generated_at: new Date(Date.now() - **********).toISOString(),
        downloaded_at: new Date(Date.now() - **********).toISOString(),
      },
      ...mockTaxDocuments.slice(1),
    ],
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "month",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};

export const RecentActivity: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    transactions: mockRecentTransactions,
    isLoadingTransactions: false,
    transactionsError: null,
    earnings: {
      ...mockEarnings,
      currentWeek: 375.75,
      pendingPayments: 0,
      weeklyGrowth: 12.8,
    },
    isLoadingEarnings: false,
    earningsError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    taxDocuments: mockTaxDocuments,
    isLoadingTaxDocuments: false,
    taxDocumentsError: null,
    selectedPeriod: "week",
    selectedTransactionType: null,
    onPeriodChange: fn(),
    onTransactionTypeChange: fn(),
    onDownloadTaxDocument: fn(),
    onSetupBanking: fn(),
    onAddPaymentMethod: fn(),
    onSetPrimaryPaymentMethod: fn(),
    onRequestPayment: fn(),
  },
};
