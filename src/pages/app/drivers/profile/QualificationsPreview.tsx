import { useCallback, useState } from "react";
import { format } from "date-fns";
import {
  Award,
  Car,
  FileText,
  Flame,
  HeartPulse,
  Plus,
  Shield,
  ShieldAlert,
  Truck,
} from "lucide-react";
import { Link } from "react-router";

import type { QualificationFormValues } from "@/components/forms/QualificationForm";

import { useCreateQualification, useGetDriver } from "@/api";
import QualificationForm from "@/components/forms/QualificationForm";
import DialogForm from "@/components/shared/DialogForm";
import EmptyList from "@/components/shared/EmptyList";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useUser } from "@/contexts/User";

// Helper to get appropriate icon based on qualification type
const getQualificationIcon = (type: string) => {
  switch (type) {
    case "commercial_drivers_license":
      return <Car className="h-8 w-8 text-blue-500" />;
    case "hazmat_endorsement":
      return <Flame className="h-8 w-8 text-red-500" />;
    case "medical_certificate":
      return <HeartPulse className="h-8 w-8 text-green-500" />;
    case "defensive_driving_certificate":
      return <ShieldAlert className="h-8 w-8 text-yellow-500" />;
    case "tanker_endorsement":
      return <FileText className="h-8 w-8 text-purple-500" />;
    case "doubles_triples_endorsement":
      return <Truck className="h-8 w-8 text-indigo-500" />;
    case "other":
      return <Award className="text-primary h-8 w-8" />;
    default:
      return <Shield className="text-primary h-8 w-8" />;
  }
};

// Helper to properly format qualification type for display
const formatQualificationType = (type: string): string => {
  return type
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export default function QualificationsPreview() {
  const { driver } = useUser();
  const [showQualificationForm, setShowQualificationForm] = useState(false);

  const { data: driverData } = useGetDriver(driver?.id ?? "");

  const { mutate: createQualification } = useCreateQualification({
    onSuccess: () => {
      setShowQualificationForm(false);
    },
  });

  const handleCreateQualification = useCallback(
    (values: QualificationFormValues) => {
      if (!driver?.id) return;

      createQualification({
        driver_id: driver.id,
        status: "pending",
        issued_at: format(values.issued_at, "yyyy-MM-dd"),
        expires_at: format(values.expires_at, "yyyy-MM-dd"),
        issuing_state: values.issuing_state,
        type: values.type,
      });
    },
    [driver?.id, createQualification],
  );

  // Get the first qualification or null if none exist
  const primaryQualification = driverData?.qualifications?.[0] || null;

  return (
    <Card className="flex h-full flex-col">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Credentials & Certifications</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowQualificationForm(true)}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="grow space-y-4">
        {!primaryQualification ? (
          <EmptyList
            title="No Credentials"
            description="Add your first credential to get started"
          />
        ) : (
          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="flex items-center gap-4">
              {getQualificationIcon(primaryQualification.type)}
              <div>
                <p className="font-medium">
                  {formatQualificationType(primaryQualification.type)}
                </p>
                <p className="text-muted-foreground text-sm">
                  Expires:{" "}
                  {format(
                    new Date(primaryQualification.expires_at),
                    "MMM dd, yyyy",
                  )}
                </p>
              </div>
            </div>
            <Badge
              variant={
                primaryQualification.status === "verified"
                  ? "default"
                  : "secondary"
              }
            >
              {primaryQualification.status}
            </Badge>
          </div>
        )}
        {driverData?.qualifications?.length > 1 && (
          <p className="text-muted-foreground text-center text-sm">
            +{driverData.qualifications.length - 1} more credential
            {driverData.qualifications.length > 2 ? "s" : ""}
          </p>
        )}
      </CardContent>
      <CardFooter className="justify-center pt-0">
        <Button variant="ghost" size="sm" asChild>
          <Link to="/app/drivers/profile/qualifications">
            View All Credentials
          </Link>
        </Button>
      </CardFooter>
      <DialogForm
        Component={QualificationForm}
        onSubmit={handleCreateQualification}
        useTrigger={false}
        onCancel={() => setShowQualificationForm(false)}
        open={showQualificationForm}
        onOpenChange={setShowQualificationForm}
      />
    </Card>
  );
}
