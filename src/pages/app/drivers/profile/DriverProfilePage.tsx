import { Package, Route, Truck } from "lucide-react";

import { DriverScore } from "@/components/common/DriverScore";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import QualificationsPreview from "./QualificationsPreview";
import DriverRecentActivity from "./RecentActivity";

export interface DriverProfilePageProps {
  // User data
  driver: {
    id: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    score?: number;
  } | null;

  // Driver data from API
  driverData:
    | {
        id: string;
        first_name?: string;
        last_name?: string;
        email?: string;
        phone_number?: string;
        avatar?: string | null;
        score?: number;
        status?: string;
        tier?: string;
      }
    | undefined;

  // Metrics data
  metrics:
    | {
        totalDeliveries: number;
        totalMiles: number;
        totalTonnage: number;
      }
    | undefined;

  // Loading states
  isDriverLoading: boolean;
  isMetricsLoading: boolean;
  isLoading: boolean;
}

export const DriverProfilePage = ({
  driver,
  driverData,
  metrics,
  isDriverLoading,
  isMetricsLoading,
  isLoading,
}: DriverProfilePageProps) => {
  if (!driver) {
    return null;
  }

  return (
    <div className="container space-y-8 py-8">
      <Card className="overflow-hidden">
        <div className="from-primary/10 to-primary/5 relative h-32 bg-linear-to-r">
          <div className="absolute -bottom-12 left-6">
            <Avatar className="border-background h-24 w-24 border-4">
              <AvatarFallback className="text-2xl">
                {driverData?.first_name?.[0] || ""}
                {driverData?.last_name?.[0] || ""}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
        <CardContent className="mt-14 space-y-4">
          <div>
            <h1 className="text-2xl font-bold">
              {driverData?.first_name || "Loading"}{" "}
              {driverData?.last_name || "..."}
            </h1>
            <p className="text-muted-foreground">
              {driverData?.email || "Loading..."}
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
        <Card className="overflow-hidden">
          <div className="bg-linear-to-br from-blue-500/10 to-blue-600/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Total Deliveries
                </p>
                <p className="text-2xl font-bold">
                  {isLoading ? "..." : metrics?.totalDeliveries}
                </p>
              </div>
              <Truck className="h-8 w-8 text-blue-500" />
            </div>
          </div>
        </Card>

        <Card className="overflow-hidden">
          <div className="bg-linear-to-br from-green-500/10 to-green-600/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Total Miles
                </p>
                <p className="text-2xl font-bold">
                  {isLoading
                    ? "..."
                    : Math.round(metrics?.totalMiles || 0).toLocaleString()}
                </p>
              </div>
              <Route className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </Card>

        <Card className="overflow-hidden">
          <div className="bg-linear-to-br from-purple-500/10 to-purple-600/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Total Tonnage
                </p>
                <p className="text-2xl font-bold">
                  {isLoading
                    ? "..."
                    : Math.round(metrics?.totalTonnage || 0).toLocaleString()}
                </p>
              </div>
              <Package className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <Card className="flex flex-col">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle>Performance</CardTitle>
            <DriverScore score={driverData?.score || 0} isLoading={isLoading} />
          </CardHeader>
          <CardContent className="flex grow flex-col justify-end pt-4">
            <div className="space-y-4">
              <div>
                <div className="mb-1 flex justify-between text-sm">
                  <span>On-time Delivery</span>
                  {isLoading ? (
                    <Skeleton className="h-4 w-8" />
                  ) : (
                    <span className="font-medium">99%</span>
                  )}
                </div>
                <div className="bg-muted h-2 rounded-full">
                  {isLoading ? (
                    <Skeleton className="h-2 w-full" />
                  ) : (
                    <div
                      className="bg-primary h-2 rounded-full"
                      style={{ width: "99%" }}
                    />
                  )}
                </div>
              </div>
              <div>
                <div className="mb-1 flex justify-between text-sm">
                  <span>Customer Satisfaction</span>
                  {isLoading ? (
                    <Skeleton className="h-4 w-8" />
                  ) : (
                    <span className="font-medium">97%</span>
                  )}
                </div>
                <div className="bg-muted h-2 rounded-full">
                  {isLoading ? (
                    <Skeleton className="h-2 w-full" />
                  ) : (
                    <div
                      className="bg-primary h-2 rounded-full"
                      style={{ width: "97%" }}
                    />
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <QualificationsPreview />
      </div>

      <DriverRecentActivity />
    </div>
  );
};
