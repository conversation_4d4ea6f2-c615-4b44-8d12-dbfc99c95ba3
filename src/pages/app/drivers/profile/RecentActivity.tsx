import { format } from "date-fns";
import { Clock, MapPin } from "lucide-react";

import { useListShipments } from "@/api";
import EmptyList from "@/components/shared/EmptyList";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function DriverRecentActivity() {
  const { data: completedShipments } = useListShipments({
    status: "completed",
    pageSize: 10,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        {!completedShipments?.items?.length ? (
          <EmptyList
            title="No Recent Activity"
            description="Your completed shipments will appear here"
          />
        ) : (
          <div className="space-y-4">
            {completedShipments?.items?.map((shipment) => {
              const stops =
                shipment.stops?.sort(
                  (a, b) => (a.sequence_number || 0) - (b.sequence_number || 0),
                ) || [];
              const origin = stops[0]?.location?.formatted;
              const destination = stops[stops.length - 1]?.location?.formatted;

              return (
                <div
                  key={shipment.id}
                  className="flex items-center justify-between rounded-lg border p-4"
                >
                  <div className="flex items-center gap-4">
                    <Clock className="text-muted-foreground h-5 w-5" />
                    <div>
                      <div className="flex items-center gap-2">
                        <MapPin className="text-muted-foreground h-4 w-4" />
                        <span>
                          {origin || "Unknown"} to {destination || "Unknown"}
                        </span>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        {shipment.completed_at
                          ? `Completed ${format(new Date(shipment.completed_at), "MMM dd, yyyy")}`
                          : "Completion date not available"}
                      </p>
                    </div>
                  </div>
                  <Badge>Completed</Badge>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
