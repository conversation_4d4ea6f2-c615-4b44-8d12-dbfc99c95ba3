import { useState } from "react";
import { format } from "date-fns";
import {
  Award,
  Car,
  CheckCircle2,
  ChevronLeft,
  FileText,
  Flame,
  HeartPulse,
  Pencil,
  Plus,
  Shield,
  ShieldAlert,
  Trash,
  Truck,
} from "lucide-react";
import { Link } from "react-router";

import type { QualificationFormValues } from "@/components/forms/QualificationForm";

import {
  useCreateQualification,
  useDeleteQualification,
  useListQualifications,
  useUpdateQualification,
} from "@/api";
import { QUALIFICATION_TYPES } from "@/components/forms/fields/types/QualificationType";
import QualificationForm from "@/components/forms/QualificationForm";
import DialogConfirmation from "@/components/shared/DialogConfirmation";
import DialogForm from "@/components/shared/DialogForm";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/User";
import { Database, Enums } from "@/supabase/types";

// Helper function to get the color class based on status
const getStatusColor = (status: string) => {
  switch (status) {
    case "verified":
      return "bg-green-500";
    case "pending":
      return "bg-yellow-500/20 text-yellow-700";
    case "rejected":
      return "bg-red-500/20 text-red-700";
    default:
      return "bg-gray-500/20 text-gray-700";
  }
};

// Helper to get appropriate icon based on qualification type
const getQualificationIcon = (type: string) => {
  switch (type) {
    case "commercial_drivers_license":
      return <Car className="h-10 w-10 text-blue-500" />;
    case "hazmat_endorsement":
      return <Flame className="h-10 w-10 text-red-500" />;
    case "medical_certificate":
      return <HeartPulse className="h-10 w-10 text-green-500" />;
    case "defensive_driving_certificate":
      return <ShieldAlert className="h-10 w-10 text-yellow-500" />;
    case "tanker_endorsement":
      return <FileText className="h-10 w-10 text-purple-500" />;
    case "doubles_triples_endorsement":
      return <Truck className="h-10 w-10 text-indigo-500" />;
    case "other":
      return <Award className="text-primary h-10 w-10" />;
    default:
      return <Shield className="text-primary h-10 w-10" />;
  }
};

// Helper to get qualification descriptions
const getQualificationDescription = (type: string) => {
  switch (type) {
    case "commercial_drivers_license":
      return "A commercial driver's license (CDL) is required for operating large, heavy, or placarded hazardous material vehicles in commerce.";
    case "hazmat_endorsement":
      return "The hazardous materials endorsement allows drivers to transport materials that have been deemed hazardous by the Department of Transportation.";
    case "medical_certificate":
      return "A medical examiner's certificate verifies that you meet the medical standards to safely operate a commercial motor vehicle.";
    case "defensive_driving_certificate":
      return "This certification demonstrates your training in defensive driving techniques and accident prevention strategies.";
    case "tanker_endorsement":
      return "Required for drivers who operate tank vehicles that carry liquids or gases in bulk.";
    case "doubles_triples_endorsement":
      return "Allows drivers to pull double or triple trailers, increasing the vehicle's cargo capacity.";
    default:
      return "A required professional credential for operating commercial vehicles.";
  }
};

// Helper to properly format qualification type for display
const formatQualificationType = (type: string): string => {
  return type
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

type Qualification = Database["public"]["Tables"]["qualifications"]["Row"];

// Qualification card component
const QualificationCard = ({
  qualification,
  onEdit,
  onVerify,
  onDelete,
}: {
  qualification: Qualification;
  onEdit: (qualification: Qualification) => void;
  onVerify: (qualification: Qualification) => void;
  onDelete: (qualification: Qualification) => void;
}) => {
  const needsVerification = qualification.status !== "verified";

  return (
    <Card className="relative overflow-hidden transition-all hover:shadow-md">
      <div className="absolute top-2 right-2 flex gap-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={(e) => {
            e.stopPropagation();
            onEdit(qualification);
          }}
        >
          <Pencil className="h-4 w-4" />
          <span className="sr-only">Edit</span>
        </Button>

        <DialogConfirmation
          title="Delete Qualification"
          description={`Are you sure you want to delete this ${formatQualificationType(qualification.type)}? This action cannot be undone.`}
          onClick={() => onDelete(qualification)}
          variant="ghost"
          size="icon"
          className="text-destructive hover:text-destructive h-8 w-8"
        >
          <Button
            variant="ghost"
            size="icon"
            className="text-destructive hover:text-destructive h-8 w-8"
          >
            <Trash className="h-4 w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </DialogConfirmation>
      </div>

      <div className="flex items-center gap-4 border-b p-6">
        {getQualificationIcon(qualification.type)}
        <div>
          <h3 className="text-xl font-semibold">
            {formatQualificationType(qualification.type)}
          </h3>
          <p className="text-muted-foreground text-sm">
            {qualification.issuing_state}
          </p>
        </div>
      </div>
      <CardContent className="p-6">
        <div className="flex flex-col gap-y-4">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium">Issued</p>
              <p className="text-muted-foreground text-sm">
                {format(new Date(qualification.issued_at), "MMM dd, yyyy")}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">Expires</p>
              <p className="text-muted-foreground text-sm">
                {format(new Date(qualification.expires_at), "MMM dd, yyyy")}
              </p>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium">Status</p>
            <Badge className={getStatusColor(qualification.status)}>
              {qualification.status}
            </Badge>
          </div>

          {needsVerification && (
            <Button
              variant="outline"
              className="mt-2 w-full"
              onClick={() => onVerify(qualification)}
            >
              <CheckCircle2 className="mr-2 h-4 w-4" />
              Verify Document
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Empty qualification placeholder card
const EmptyQualificationCard = ({
  type,
  onClick,
}: {
  type: Enums<"qualification_type">;
  onClick: () => void;
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <Card
      className="hover:bg-primary/5 focus-visible:bg-primary/5 focus-visible:ring-primary flex min-h-[250px] cursor-pointer flex-col items-center justify-center border-dashed transition-all focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden"
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Add ${formatQualificationType(type)} credential`}
    >
      <div className="flex h-full flex-col items-center justify-center p-6 text-center">
        <div className="bg-primary/10 mb-4 flex items-center justify-center rounded-full p-3">
          {getQualificationIcon(type)}
        </div>
        <h3 className="text-lg font-medium capitalize">
          {formatQualificationType(type)}
        </h3>
        <p className="text-muted-foreground mt-2 mb-3 max-w-[80%] text-sm">
          {getQualificationDescription(type)}
        </p>
        <Button variant="outline" size="sm" className="mt-2">
          <Plus className="mr-2 h-4 w-4" />
          Add Credential
        </Button>
      </div>
    </Card>
  );
};

export default function Qualifications() {
  const { driver } = useUser();
  const [showQualificationForm, setShowQualificationForm] = useState(false);
  const [selectedQualificationType, setSelectedQualificationType] =
    useState<Enums<"qualification_type"> | null>(null);
  const [editingQualification, setEditingQualification] =
    useState<Qualification | null>(null);

  const {
    data: qualificationsData,
    isLoading,
    error,
  } = useListQualifications(
    {
      driver_id: driver?.id,
      pageIndex: 0,
      pageSize: 50,
    },
    {
      enabled: !!driver?.id,
    },
  );

  const { mutate: createQualification } = useCreateQualification({
    onSuccess: () => {
      setShowQualificationForm(false);
      setSelectedQualificationType(null);
      setEditingQualification(null);

      toast({
        title: "Qualification Added",
        description:
          "Your qualification has been added and is pending verification.",
        variant: "default",
      });
    },
  });

  const { mutate: updateQualification } = useUpdateQualification({
    onSuccess: () => {
      setShowQualificationForm(false);
      setSelectedQualificationType(null);
      setEditingQualification(null);
    },
  });

  const { mutate: deleteQualification } = useDeleteQualification({
    onSuccess: () => {
      toast({
        title: "Qualification Deleted",
        description: "Your qualification has been removed.",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Deletion Failed",
        description:
          error.message ||
          "There was an error deleting your qualification. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleCreateQualification = (values: QualificationFormValues) => {
    if (!driver?.id) return;

    if (editingQualification) {
      updateQualification(
        {
          id: editingQualification.id,
          driver_id: driver.id,
          status: "pending", // Always set status to pending when editing
          issued_at: format(values.issued_at, "yyyy-MM-dd"),
          expires_at: format(values.expires_at, "yyyy-MM-dd"),
          issuing_state: values.issuing_state,
          type: values.type,
        },
        {
          onSuccess: () => {
            toast({
              title: "Qualification Updated",
              description:
                "Your qualification has been updated and requires verification.",
              variant: "default",
            });
          },
        },
      );
    } else {
      createQualification({
        driver_id: driver.id,
        status: "pending",
        issued_at: format(values.issued_at, "yyyy-MM-dd"),
        expires_at: format(values.expires_at, "yyyy-MM-dd"),
        issuing_state: values.issuing_state,
        type: values.type,
      });
    }
  };

  const handleAddQualification = (type: Enums<"qualification_type">) => {
    setSelectedQualificationType(type);
    setEditingQualification(null);
    setShowQualificationForm(true);
  };

  const handleEditQualification = (qualification: Qualification) => {
    // Set the editing qualification
    setEditingQualification(qualification);
    setSelectedQualificationType(
      qualification.type as Enums<"qualification_type">,
    );
    setShowQualificationForm(true);

    // Show toast to inform user that editing will reset verification status
    if (qualification.status === "verified") {
      toast({
        title: "Verification Notice",
        description:
          "Editing this qualification will reset its verification status.",
        variant: "default",
      });
    }
  };

  const handleDeleteQualification = (qualification: Qualification) => {
    if (!driver?.id) return;

    deleteQualification({
      id: qualification.id,
      driver_id: driver.id,
    });
  };

  const handleVerifyQualification = (qualification: Qualification) => {
    if (!driver?.id) return;

    // Update the qualification with "verified" status
    updateQualification(
      {
        id: qualification.id,
        driver_id: driver.id,
        status: "verified",
        issued_at: qualification.issued_at,
        expires_at: qualification.expires_at,
        issuing_state: qualification.issuing_state,
        type: qualification.type,
      },
      {
        onSuccess: () => {
          toast({
            title: "Document Verified",
            description: `Your ${formatQualificationType(qualification.type)} has been verified.`,
            variant: "default",
          });
        },
        onError: (error) => {
          toast({
            title: "Verification failed",
            description:
              error.message ||
              "There was an error verifying your document. Please try again.",
            variant: "destructive",
          });
        },
      },
    );
  };

  // Get unique qualification types that the driver already has
  const existingQualificationTypes =
    qualificationsData?.items.map((q) => q.type) || [];

  // Filter qualification types to those that haven't been added yet
  const missingQualificationTypes = QUALIFICATION_TYPES.filter(
    (type) => !existingQualificationTypes.includes(type),
  );

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button variant="ghost" size="sm" asChild className="mb-4">
          <Link to="/app/drivers/profile">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Profile
          </Link>
        </Button>
      </div>

      <div className="mb-8 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="mb-2 text-4xl font-bold">
            Digital Credentials Wallet
          </h1>
          <p className="text-muted-foreground">
            Manage your professional qualifications and certifications in one
            secure place
          </p>
        </div>

        <Button onClick={() => setShowQualificationForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Credential
        </Button>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error loading your qualifications. Please try again
            later.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {isLoading ? (
          // Loading state
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="bg-muted/10 h-64 animate-pulse"></Card>
          ))
        ) : (
          <>
            {/* Display existing qualifications */}
            {qualificationsData?.items.map((qualification) => (
              <QualificationCard
                key={qualification.id}
                qualification={qualification}
                onEdit={handleEditQualification}
                onVerify={handleVerifyQualification}
                onDelete={handleDeleteQualification}
              />
            ))}

            {/* Add placeholder cards for missing qualification types */}
            {missingQualificationTypes.map((type) => (
              <EmptyQualificationCard
                key={type}
                type={type}
                onClick={() => handleAddQualification(type)}
              />
            ))}
          </>
        )}
      </div>

      <DialogForm
        Component={QualificationForm}
        defaultValues={
          editingQualification
            ? {
                type: editingQualification.type,
                issuing_state: editingQualification.issuing_state,
                issued_at: new Date(editingQualification.issued_at),
                expires_at: new Date(editingQualification.expires_at),
              }
            : selectedQualificationType
              ? { type: selectedQualificationType }
              : undefined
        }
        title={editingQualification ? "Edit Credential" : "Add Credential"}
        onSubmit={handleCreateQualification}
        useTrigger={false}
        onCancel={() => {
          setShowQualificationForm(false);
          setSelectedQualificationType(null);
          setEditingQualification(null);
        }}
        open={showQualificationForm}
        onOpenChange={(open) => {
          setShowQualificationForm(open);
          if (!open) {
            setEditingQualification(null);
          }
        }}
        isEditing={!!editingQualification}
      />
    </div>
  );
}
