import { useState } from "react";
import {
  BadgeCheck,
  CheckCircle2,
  Clock,
  FileCheck,
  Globe,
  Info,
  Lock,
  ShieldCheck,
  Truck,
  UserCheck,
  X,
} from "lucide-react";
import { useNavigate } from "react-router";

import Currency from "@/components/shared/Currency";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/User";

// Define a type for verification status
type VerificationStatus = "verified" | "pending" | "unverified";

export default function DriverVerification() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { driver } = useUser();
  const [isLoading, setIsLoading] = useState(false);

  // For demonstration purposes, we'll add a state to simulate the pending status
  // In a real app, this would come from an API or the driver object
  const [pendingStatus, setPendingStatus] = useState<boolean>(false);

  // Determine verification status based on driver object and pending state
  const verificationStatus: VerificationStatus = pendingStatus
    ? "pending"
    : driver?.verified_at
      ? "verified"
      : "unverified";

  // Dummy verification details
  const verificationDetails = {
    status: verificationStatus,
    verifiedAt:
      verificationStatus === "verified"
        ? driver?.verified_at || new Date("2023-06-15").toLocaleDateString()
        : null,
    verifiedBy: verificationStatus === "verified" ? "FleetFlow Admin" : null,
    expiresAt:
      verificationStatus === "verified"
        ? new Date("2024-06-15").toLocaleDateString()
        : null,
    progress:
      verificationStatus === "pending"
        ? 60
        : verificationStatus === "verified"
          ? 100
          : 0,
  };

  const handleStartVerification = () => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setPendingStatus(true); // Set status to pending after payment
      toast({
        title: "Verification process started",
        description:
          "We've received your request and will begin processing your verification.",
      });
      // In a real implementation, we would update the driver's verification status
    }, 1500);
  };

  return (
    <div className="container py-8">
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <div className="mb-8">
            <h1 className="mb-2 text-4xl font-bold">Driver Verification</h1>
            <p className="text-muted-foreground text-lg">
              Get verified to unlock all platform features and access more
              shipments
            </p>
          </div>

          {verificationStatus === "unverified" && (
            <Alert className="mb-6 border-blue-200 bg-blue-50">
              <Info className="h-5 w-5 text-blue-500" />
              <AlertTitle className="text-blue-700">
                Verification Required
              </AlertTitle>
              <AlertDescription className="text-blue-600">
                Complete the verification process to access new shipments and
                special features.
              </AlertDescription>
            </Alert>
          )}

          {verificationStatus === "pending" && (
            <Alert className="mb-6 border-amber-200 bg-amber-50">
              <Clock className="h-5 w-5 text-amber-500" />
              <AlertTitle className="text-amber-700">
                Verification In Progress
              </AlertTitle>
              <AlertDescription className="text-amber-600">
                Your verification is being processed. This typically takes 1-3
                business days.
              </AlertDescription>
            </Alert>
          )}

          {verificationStatus === "verified" && (
            <Alert className="mb-6 border-green-200 bg-green-50">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              <AlertTitle className="text-green-700">
                Verification Complete
              </AlertTitle>
              <AlertDescription className="text-green-600">
                You are fully verified! You can now access all shipments and
                platform features.
              </AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="benefits" className="mb-8">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="benefits">Benefits</TabsTrigger>
              <TabsTrigger value="process">Process</TabsTrigger>
              <TabsTrigger value="requirements">Requirements</TabsTrigger>
            </TabsList>

            <TabsContent value="benefits" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BadgeCheck className="text-primary h-5 w-5" />
                    Verification Benefits
                  </CardTitle>
                  <CardDescription>
                    Why becoming a verified driver is worth it
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="bg-card rounded-lg border p-4">
                      <div className="mb-3 flex items-center gap-2">
                        <Globe className="text-primary h-5 w-5" />
                        <h3 className="font-semibold">Access More Shipments</h3>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        Get exclusive access to premium shipments with higher
                        pay rates and preferred routes.
                      </p>
                    </div>

                    <div className="bg-card rounded-lg border p-4">
                      <div className="mb-3 flex items-center gap-2">
                        <BadgeCheck className="text-primary h-5 w-5" />
                        <h3 className="font-semibold">Verified Badge</h3>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        Earn a verified badge on your profile that builds trust
                        with customers and partners.
                      </p>
                    </div>

                    <div className="bg-card rounded-lg border p-4">
                      <div className="mb-3 flex items-center gap-2">
                        <UserCheck className="text-primary h-5 w-5" />
                        <h3 className="font-semibold">Priority Support</h3>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        Get priority access to customer support and dedicated
                        assistance for any issues.
                      </p>
                    </div>

                    <div className="bg-card rounded-lg border p-4">
                      <div className="mb-3 flex items-center gap-2">
                        <Truck className="text-primary h-5 w-5" />
                        <h3 className="font-semibold">Higher Earnings</h3>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        Verified drivers earn up to 30% more on average due to
                        access to premium shipments.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="process" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileCheck className="text-primary h-5 w-5" />
                    Verification Process
                  </CardTitle>
                  <CardDescription>
                    What to expect during the verification process
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ol className="border-muted-foreground/20 relative space-y-8 border-l pl-6">
                    <li className="relative">
                      <div className="bg-primary/10 text-primary absolute -left-[25px] flex h-12 w-12 items-center justify-center rounded-full">
                        1
                      </div>
                      <div className="ml-6">
                        <h3 className="font-semibold">
                          Application Submission
                        </h3>
                        <p className="text-muted-foreground text-sm">
                          Submit your application by paying the one-time $25
                          verification fee.
                        </p>
                      </div>
                    </li>

                    <li className="relative">
                      <div className="bg-primary/10 text-primary absolute -left-[25px] flex h-12 w-12 items-center justify-center rounded-full">
                        2
                      </div>
                      <div className="ml-6">
                        <h3 className="font-semibold">Document Verification</h3>
                        <p className="text-muted-foreground text-sm">
                          Our team will verify your identity, driver's license,
                          insurance, and vehicle information.
                        </p>
                      </div>
                    </li>

                    <li className="relative">
                      <div className="bg-primary/10 text-primary absolute -left-[25px] flex h-12 w-12 items-center justify-center rounded-full">
                        3
                      </div>
                      <div className="ml-6">
                        <h3 className="font-semibold">Background Check</h3>
                        <p className="text-muted-foreground text-sm">
                          We'll conduct a background check to ensure safety and
                          reliability.
                        </p>
                      </div>
                    </li>

                    <li className="relative">
                      <div className="bg-primary/10 text-primary absolute -left-[25px] flex h-12 w-12 items-center justify-center rounded-full">
                        4
                      </div>
                      <div className="ml-6">
                        <h3 className="font-semibold">Approval & Badge</h3>
                        <p className="text-muted-foreground text-sm">
                          Upon approval, you'll receive your verified badge and
                          access to all features.
                        </p>
                      </div>
                    </li>
                  </ol>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="requirements" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShieldCheck className="text-primary h-5 w-5" />
                    Verification Requirements
                  </CardTitle>
                  <CardDescription>
                    What you'll need to complete the verification process
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-2">
                    <div className="mt-1 rounded-full bg-green-100 p-1">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Valid Driver's License</h3>
                      <p className="text-muted-foreground text-sm">
                        Must be current and not expired
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <div className="mt-1 rounded-full bg-green-100 p-1">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Vehicle Registration</h3>
                      <p className="text-muted-foreground text-sm">
                        Current registration for the vehicle you'll be using
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <div className="mt-1 rounded-full bg-green-100 p-1">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Proof of Insurance</h3>
                      <p className="text-muted-foreground text-sm">
                        Valid insurance policy with appropriate coverage levels
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <div className="mt-1 rounded-full bg-green-100 p-1">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Clean Driving Record</h3>
                      <p className="text-muted-foreground text-sm">
                        Minimal traffic violations in the past 3 years
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <div className="mt-1 rounded-full bg-green-100 p-1">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">Clear Background Check</h3>
                      <p className="text-muted-foreground text-sm">
                        No serious criminal history
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <div className="mt-1 rounded-full bg-green-100 p-1">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">One-time $25 Fee</h3>
                      <p className="text-muted-foreground text-sm">
                        Processing fee for verification (non-refundable)
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {verificationStatus === "unverified" && (
            <Card className="border-primary/20 bg-primary/5 mt-8">
              <CardHeader>
                <CardTitle>Start Verification Process</CardTitle>
                <CardDescription>
                  Pay the one-time fee of $25 to begin your verification process
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-background flex items-center justify-between rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <Lock className="text-primary h-5 w-5" />
                    <div>
                      <p className="font-medium">Verification Fee</p>
                      <p className="text-muted-foreground text-sm">
                        One-time payment
                      </p>
                    </div>
                  </div>
                  <Currency amount={25} />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleStartVerification}
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "Processing..." : "Pay & Start Verification"}
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>

        {/* Verification status card (always visible) */}
        <div>
          <Card className="sticky top-6">
            <CardHeader
              className={`${
                verificationStatus === "verified"
                  ? "bg-green-50"
                  : verificationStatus === "pending"
                    ? "bg-amber-50"
                    : "bg-muted"
              }`}
            >
              <CardTitle className="flex items-center gap-2 text-lg">
                {verificationStatus === "verified" ? (
                  <BadgeCheck className="h-5 w-5 text-green-500" />
                ) : verificationStatus === "pending" ? (
                  <Clock className="h-5 w-5 text-amber-500" />
                ) : (
                  <X className="text-muted-foreground h-5 w-5" />
                )}
                Verification Status
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="mb-6 rounded-lg border p-4">
                <div className="mb-3 flex items-center justify-between">
                  <span className="text-muted-foreground text-sm font-medium">
                    Status
                  </span>
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      verificationStatus === "verified"
                        ? "bg-green-100 text-green-800"
                        : verificationStatus === "pending"
                          ? "bg-amber-100 text-amber-800"
                          : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {verificationStatus === "verified"
                      ? "Verified"
                      : verificationStatus === "pending"
                        ? "Pending"
                        : "Not Verified"}
                  </span>
                </div>

                {verificationStatus === "pending" && (
                  <div className="mb-3 space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span>Verification Progress</span>
                      <span>{verificationDetails.progress}%</span>
                    </div>
                    <Progress
                      value={verificationDetails.progress}
                      className="h-2"
                    />
                  </div>
                )}

                {verificationStatus === "verified" && (
                  <>
                    <div className="mb-3 flex items-center justify-between">
                      <span className="text-muted-foreground text-sm font-medium">
                        Verified On
                      </span>
                      <span className="text-sm">
                        {verificationDetails.verifiedAt}
                      </span>
                    </div>

                    <div className="mb-3 flex items-center justify-between">
                      <span className="text-muted-foreground text-sm font-medium">
                        Verified By
                      </span>
                      <span className="text-sm">
                        {verificationDetails.verifiedBy}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground text-sm font-medium">
                        Expires On
                      </span>
                      <span className="text-sm">
                        {verificationDetails.expiresAt}
                      </span>
                    </div>
                  </>
                )}

                {verificationStatus === "unverified" && (
                  <div className="flex flex-col items-center justify-center py-4">
                    <BadgeCheck className="text-muted-foreground/30 mb-2 h-12 w-12" />
                    <p className="text-muted-foreground text-center text-sm">
                      Complete verification to unlock all features
                    </p>
                  </div>
                )}
              </div>

              {verificationStatus === "verified" && (
                <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                  <div className="flex items-center gap-2 text-green-700">
                    <CheckCircle2 className="h-5 w-5" />
                    <span className="font-medium">All Features Unlocked</span>
                  </div>
                  <p className="mt-2 text-sm text-green-600">
                    You have full access to all premium shipments and platform
                    features.
                  </p>
                </div>
              )}

              {verificationStatus === "pending" && (
                <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                  <div className="flex items-center gap-2 text-amber-700">
                    <Clock className="h-5 w-5" />
                    <span className="font-medium">
                      Verification in Progress
                    </span>
                  </div>
                  <p className="mt-2 text-sm text-amber-600">
                    We're reviewing your information. This typically takes 1-3
                    business days.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
