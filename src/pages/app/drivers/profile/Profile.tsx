import { useQuery } from "@tanstack/react-query";

import { useGetDriver } from "@/api/drivers/use-get-driver";
import { useUser } from "@/contexts/User";
import { supabase } from "@/supabase/client";
import { DriverProfilePage } from "./DriverProfilePage";

const DriverProfile = () => {
  const { driver } = useUser();
  const { data: driverData, isLoading: isDriverLoading } = useGetDriver(
    driver?.id ?? "",
  );

  const { data: metrics, isLoading: isMetricsLoading } = useQuery({
    queryKey: ["driver-metrics", driver?.id],
    queryFn: async () => {
      if (!driver?.id) return null;

      const { data: shipments, error: shipmentsError } = await supabase
        .from("shipments")
        .select(
          `
          id,
          status,
          completed_at,
          load:loads (
            id,
            weight
          ),
          stops (
            id,
            sequence_number,
            location:locations (
              id,
              formatted
            )
          )
        `,
        )
        .eq("driver_id", driver.id)
        .eq("status", "completed");

      if (shipmentsError) throw shipmentsError;

      const { data: positions, error: positionsError } = await supabase
        .from("positions")
        .select("latitude, longitude")
        .eq("driver_id", driver.id)
        .order("recorded_at", { ascending: true });

      if (positionsError) throw positionsError;

      const totalTonnage =
        shipments?.reduce(
          (sum, shipment) => sum + (Number(shipment.load?.weight) || 0),
          0,
        ) || 0;

      const totalMiles = calculateTotalDistance(positions);

      return {
        totalDeliveries: shipments?.length || 0,
        totalMiles: Math.round(totalMiles),
        totalTonnage: Math.round(totalTonnage),
      };
    },
    enabled: !!driver?.id,
  });

  const isLoading = isDriverLoading || isMetricsLoading;

  return (
    <DriverProfilePage
      driver={driver}
      driverData={driverData}
      metrics={metrics}
      isDriverLoading={isDriverLoading}
      isMetricsLoading={isMetricsLoading}
      isLoading={isLoading}
    />
  );
};

const calculateTotalDistance = (
  positions: { latitude: number; longitude: number }[],
) => {
  let totalDistance = 0;
  for (let i = 1; i < positions.length; i++) {
    const prev = positions[i - 1];
    const curr = positions[i];
    totalDistance += getDistanceFromLatLonInKm(
      prev.latitude,
      prev.longitude,
      curr.latitude,
      curr.longitude,
    );
  }
  return totalDistance;
};

const getDistanceFromLatLonInKm = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in km
};

const deg2rad = (deg: number) => deg * (Math.PI / 180);

export default DriverProfile;
