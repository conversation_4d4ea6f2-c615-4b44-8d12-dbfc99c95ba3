import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import type { Database } from "@/supabase/types";

import Qualifications from "./Qualifications";

// Create mock data types
type Qualification = Database["public"]["Tables"]["qualifications"]["Row"];
type Driver = Database["public"]["Tables"]["drivers"]["Row"];

const meta: Meta<typeof Qualifications> = {
  title: "Pages/Drivers/Profile/Qualifications",
  component: Qualifications,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/app/drivers/profile/qualifications" },
    }),
    docs: {
      description: {
        component:
          "Driver qualifications management component showing digital credentials wallet with full CRUD functionality for professional qualifications and certifications.",
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock driver data
const mockDriver: Driver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone_number: "******-0123",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active",
  tier: "professional",
  created_at: "2024-01-15T08:00:00Z",
  verified_at: "2024-01-20T10:00:00Z",
};

// Mock qualifications data
const mockCDLQualification: Qualification = {
  id: "qual-cdl-001",
  driver_id: "driver-123",
  type: "commercial_drivers_license",
  status: "verified",
  issuing_state: "CA",
  issued_at: "2023-01-15",
  expires_at: "2026-01-15",
  created_at: "2024-01-20T08:00:00Z",
  document_id: "doc-cdl-001",
  verified_at: "2024-01-20T10:00:00Z",
};

const mockHazmatQualification: Qualification = {
  id: "qual-hazmat-001",
  driver_id: "driver-123",
  type: "hazmat_endorsement",
  status: "pending",
  issuing_state: "CA",
  issued_at: "2024-03-01",
  expires_at: "2027-03-01",
  created_at: "2024-03-15T09:00:00Z",
  document_id: null,
  verified_at: null,
};

const mockMedicalQualification: Qualification = {
  id: "qual-medical-001",
  driver_id: "driver-123",
  type: "medical_certificate",
  status: "verified",
  issuing_state: "CA",
  issued_at: "2024-01-01",
  expires_at: "2025-01-01",
  created_at: "2024-01-15T11:00:00Z",
  document_id: "doc-medical-001",
  verified_at: "2024-01-15T12:00:00Z",
};

const mockExpiredQualification: Qualification = {
  id: "qual-expired-001",
  driver_id: "driver-123",
  type: "defensive_driving_certificate",
  status: "expired",
  issuing_state: "CA",
  issued_at: "2022-06-01",
  expires_at: "2024-06-01",
  created_at: "2022-06-15T10:00:00Z",
  document_id: "doc-expired-001",
  verified_at: "2022-06-15T11:00:00Z",
};

const mockTankerQualification: Qualification = {
  id: "qual-tanker-001",
  driver_id: "driver-123",
  type: "tanker_endorsement",
  status: "verified",
  issuing_state: "TX",
  issued_at: "2023-08-15",
  expires_at: "2026-08-15",
  created_at: "2023-09-01T14:00:00Z",
  document_id: "doc-tanker-001",
  verified_at: "2023-09-01T15:00:00Z",
};

const mockDoublesQualification: Qualification = {
  id: "qual-doubles-001",
  driver_id: "driver-123",
  type: "doubles_triples_endorsement",
  status: "pending",
  issuing_state: "NV",
  issued_at: "2024-05-01",
  expires_at: "2027-05-01",
  created_at: "2024-05-15T16:00:00Z",
  document_id: null,
  verified_at: null,
};

const mockExpiringSoonQualification: Qualification = {
  id: "qual-expiring-001",
  driver_id: "driver-123",
  type: "medical_certificate",
  status: "verified",
  issuing_state: "CA",
  issued_at: "2023-01-01",
  expires_at: "2025-02-01", // Expires soon
  created_at: "2023-01-15T08:00:00Z",
  document_id: "doc-expiring-001",
  verified_at: "2023-01-15T09:00:00Z",
};

// Mock API responses
const createMockListQualificationsResponse = (
  qualifications: Qualification[],
) => ({
  items: qualifications,
  total: qualifications.length,
});

// Mock functions
const mockUseUser = () => ({ driver: mockDriver });
const mockUseListQualifications = (qualifications: Qualification[] = []) =>
  fn().mockReturnValue({
    data: createMockListQualificationsResponse(qualifications),
    isLoading: false,
    error: null,
  });
const mockUseCreateQualification = () => fn().mockReturnValue({ mutate: fn() });
const mockUseUpdateQualification = () => fn().mockReturnValue({ mutate: fn() });
const mockUseDeleteQualification = () => fn().mockReturnValue({ mutate: fn() });

// Default story
export const Default: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification,
          mockMedicalQualification,
        ]),
      },
    ],
  },
};

// Loading state
export const Loading: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([]),
        delay: 2000,
      },
    ],
  },
};

// Error state
export const ErrorState: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 500,
        response: { error: "Failed to load qualifications" },
      },
    ],
  },
};

// Empty state - no qualifications
export const EmptyState: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([]),
      },
    ],
  },
};

// Single qualification
export const SingleQualification: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([mockCDLQualification]),
      },
    ],
  },
};

// Multiple qualifications with different statuses
export const MultipleQualifications: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification,
          mockHazmatQualification,
          mockMedicalQualification,
          mockTankerQualification,
        ]),
      },
    ],
  },
};

// All qualification types
export const AllQualificationTypes: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification,
          mockHazmatQualification,
          mockMedicalQualification,
          mockExpiredQualification,
          mockTankerQualification,
          mockDoublesQualification,
          {
            id: "qual-other-001",
            driver_id: "driver-123",
            type: "other",
            status: "verified",
            issuing_state: "CA",
            issued_at: "2024-01-01",
            expires_at: "2026-01-01",
            created_at: "2024-01-15T10:00:00Z",
            document_id: "doc-other-001",
            verified_at: "2024-01-15T11:00:00Z",
          },
        ]),
      },
    ],
  },
};

// Mixed status qualifications
export const MixedStatusQualifications: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification, // verified
          mockHazmatQualification, // pending
          mockExpiredQualification, // expired
          {
            id: "qual-revoked-001",
            driver_id: "driver-123",
            type: "tanker_endorsement",
            status: "revoked",
            issuing_state: "TX",
            issued_at: "2022-01-01",
            expires_at: "2025-01-01",
            created_at: "2022-01-15T08:00:00Z",
            document_id: "doc-revoked-001",
            verified_at: null,
          },
        ]),
      },
    ],
  },
};

// Expired qualifications
export const ExpiredQualifications: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockExpiredQualification,
          {
            id: "qual-expired-002",
            driver_id: "driver-123",
            type: "hazmat_endorsement",
            status: "expired",
            issuing_state: "CA",
            issued_at: "2021-01-01",
            expires_at: "2024-01-01",
            created_at: "2021-01-15T08:00:00Z",
            document_id: "doc-expired-002",
            verified_at: "2021-01-15T09:00:00Z",
          },
        ]),
      },
    ],
  },
};

// Qualifications expiring soon
export const ExpiringSoonQualifications: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification,
          mockExpiringSoonQualification,
          {
            id: "qual-expiring-002",
            driver_id: "driver-123",
            type: "hazmat_endorsement",
            status: "verified",
            issuing_state: "CA",
            issued_at: "2023-01-01",
            expires_at: "2025-01-30", // Expires very soon
            created_at: "2023-01-15T08:00:00Z",
            document_id: "doc-expiring-002",
            verified_at: "2023-01-15T09:00:00Z",
          },
        ]),
      },
    ],
  },
};

// Pending verifications
export const PendingVerifications: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockHazmatQualification,
          mockDoublesQualification,
          {
            id: "qual-pending-001",
            driver_id: "driver-123",
            type: "defensive_driving_certificate",
            status: "pending",
            issuing_state: "NV",
            issued_at: "2024-04-01",
            expires_at: "2027-04-01",
            created_at: "2024-04-15T10:00:00Z",
            document_id: null,
            verified_at: null,
          },
        ]),
      },
    ],
  },
};

// Different issuing states
export const DifferentIssuingStates: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          { ...mockCDLQualification, issuing_state: "CA" },
          { ...mockHazmatQualification, issuing_state: "TX" },
          { ...mockMedicalQualification, issuing_state: "NV" },
          { ...mockTankerQualification, issuing_state: "AZ" },
        ]),
      },
    ],
  },
};

// New driver with no qualifications (shows all placeholder cards)
export const NewDriverEmptyWallet: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([]),
      },
    ],
  },
};

// Professional driver with comprehensive qualifications
export const ProfessionalDriverComplete: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          {
            ...mockCDLQualification,
            type: "commercial_drivers_license",
            status: "verified",
            issuing_state: "CA",
          },
          {
            ...mockHazmatQualification,
            status: "verified",
            issuing_state: "CA",
          },
          {
            ...mockMedicalQualification,
            status: "verified",
            issuing_state: "CA",
          },
          {
            ...mockTankerQualification,
            status: "verified",
            issuing_state: "CA",
          },
          {
            ...mockDoublesQualification,
            status: "verified",
            issuing_state: "CA",
          },
          {
            id: "qual-defensive-001",
            driver_id: "driver-123",
            type: "defensive_driving_certificate",
            status: "verified",
            issuing_state: "CA",
            issued_at: "2024-01-01",
            expires_at: "2026-01-01",
            created_at: "2024-01-15T08:00:00Z",
            document_id: "doc-defensive-001",
            verified_at: "2024-01-15T09:00:00Z",
          },
        ]),
      },
    ],
  },
};

// Interstate driver with multiple state licenses
export const InterstateDriver: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          { ...mockCDLQualification, issuing_state: "CA" },
          {
            ...mockHazmatQualification,
            issuing_state: "TX",
            status: "verified",
          },
          { ...mockMedicalQualification, issuing_state: "NV" },
          { ...mockTankerQualification, issuing_state: "AZ" },
        ]),
      },
    ],
  },
};

// Edge case: Very long state names and qualification details
export const EdgeCaseLongDetails: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          {
            ...mockCDLQualification,
            issuing_state: "CALIFORNIA_VERY_LONG_STATE_NAME",
          },
          {
            ...mockHazmatQualification,
            issuing_state: "TEXAS_DEPARTMENT_OF_TRANSPORTATION",
          },
        ]),
      },
    ],
  },
};

// Qualifications with documents
export const QualificationsWithDocuments: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: {
          items: [
            {
              ...mockCDLQualification,
              document: {
                id: "doc-cdl-001",
                name: "CDL_License_JohnDoe.pdf",
                url: "https://example.com/documents/cdl.pdf",
              },
            },
            {
              ...mockMedicalQualification,
              document: {
                id: "doc-medical-001",
                name: "Medical_Certificate_2024.pdf",
                url: "https://example.com/documents/medical.pdf",
              },
            },
          ],
          total: 2,
        },
      },
    ],
  },
};

// High-volume driver with many qualifications
export const HighVolumeDriver: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification,
          mockHazmatQualification,
          mockMedicalQualification,
          mockTankerQualification,
          mockDoublesQualification,
          mockExpiredQualification,
          {
            id: "qual-defensive-001",
            driver_id: "driver-123",
            type: "defensive_driving_certificate",
            status: "verified",
            issuing_state: "CA",
            issued_at: "2024-01-01",
            expires_at: "2026-01-01",
            created_at: "2024-01-15T08:00:00Z",
            document_id: "doc-defensive-001",
            verified_at: "2024-01-15T09:00:00Z",
          },
          {
            id: "qual-other-001",
            driver_id: "driver-123",
            type: "other",
            status: "pending",
            issuing_state: "FL",
            issued_at: "2024-02-01",
            expires_at: "2026-02-01",
            created_at: "2024-02-15T08:00:00Z",
            document_id: null,
            verified_at: null,
          },
        ]),
      },
    ],
  },
};

// Qualification verification workflow
export const VerificationWorkflow: Story = {
  parameters: {
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          {
            ...mockHazmatQualification,
            status: "pending",
            document_id: null,
            verified_at: null,
          },
          {
            ...mockDoublesQualification,
            status: "pending",
            document_id: null,
            verified_at: null,
          },
        ]),
      },
    ],
  },
};

// Mobile responsive view simulation
export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification,
          mockHazmatQualification,
        ]),
      },
    ],
  },
};

// Tablet view simulation
export const TabletView: Story = {
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
    mockData: [
      {
        url: "/api/qualifications",
        method: "GET",
        status: 200,
        response: createMockListQualificationsResponse([
          mockCDLQualification,
          mockMedicalQualification,
          mockHazmatQualification,
        ]),
      },
    ],
  },
};
