import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { DriverProfilePage } from "./DriverProfilePage";

const meta: Meta<typeof DriverProfilePage> = {
  title: "Pages/Drivers/Profile",
  component: DriverProfilePage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockDriver = {
  id: "driver-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  score: 4.8,
};

const mockDriverData = {
  id: "driver-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  phone_number: "+**********",
  avatar: null,
  score: 4.8,
  status: "active",
  tier: "professional",
};

const mockMetrics = {
  totalDeliveries: 127,
  totalMiles: 12450,
  totalTonnage: 850,
};

const mockIncompleteDriverData = {
  id: "driver-456",
  first_name: "Jane",
  last_name: "",
  email: "",
  phone_number: "",
  avatar: null,
  score: 0,
  status: "pending",
  tier: "basic",
};

const mockLowMetrics = {
  totalDeliveries: 5,
  totalMiles: 250,
  totalTonnage: 15,
};

export const Default: Story = {
  args: {
    driver: mockDriver,
    driverData: mockDriverData,
    metrics: mockMetrics,
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    driver: mockDriver,
    driverData: undefined,
    metrics: undefined,
    isDriverLoading: true,
    isMetricsLoading: true,
    isLoading: true,
  },
};

export const LoadingDriverData: Story = {
  args: {
    driver: mockDriver,
    driverData: undefined,
    metrics: mockMetrics,
    isDriverLoading: true,
    isMetricsLoading: false,
    isLoading: true,
  },
};

export const LoadingMetrics: Story = {
  args: {
    driver: mockDriver,
    driverData: mockDriverData,
    metrics: undefined,
    isDriverLoading: false,
    isMetricsLoading: true,
    isLoading: true,
  },
};

export const NewDriver: Story = {
  args: {
    driver: {
      id: "driver-new",
      first_name: "Alex",
      last_name: "Johnson",
      email: "<EMAIL>",
      score: 0,
    },
    driverData: {
      id: "driver-new",
      first_name: "Alex",
      last_name: "Johnson",
      email: "<EMAIL>",
      phone_number: "+1987654321",
      avatar: null,
      score: 0,
      status: "pending",
      tier: "basic",
    },
    metrics: {
      totalDeliveries: 0,
      totalMiles: 0,
      totalTonnage: 0,
    },
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};

export const HighPerformer: Story = {
  args: {
    driver: {
      id: "driver-pro",
      first_name: "Sarah",
      last_name: "Williams",
      email: "<EMAIL>",
      score: 4.95,
    },
    driverData: {
      id: "driver-pro",
      first_name: "Sarah",
      last_name: "Williams",
      email: "<EMAIL>",
      phone_number: "+1555123456",
      avatar: null,
      score: 4.95,
      status: "active",
      tier: "premium",
    },
    metrics: {
      totalDeliveries: 450,
      totalMiles: 45000,
      totalTonnage: 2850,
    },
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};

export const Incomplete: Story = {
  args: {
    driver: {
      id: "driver-456",
      first_name: "Jane",
      last_name: "",
      email: "",
      score: 0,
    },
    driverData: mockIncompleteDriverData,
    metrics: mockLowMetrics,
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};

export const NoMetrics: Story = {
  args: {
    driver: mockDriver,
    driverData: mockDriverData,
    metrics: undefined,
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};

export const EmptyMetrics: Story = {
  args: {
    driver: mockDriver,
    driverData: mockDriverData,
    metrics: {
      totalDeliveries: 0,
      totalMiles: 0,
      totalTonnage: 0,
    },
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};

export const LowScore: Story = {
  args: {
    driver: {
      id: "driver-low",
      first_name: "Mike",
      last_name: "Brown",
      email: "<EMAIL>",
      score: 2.1,
    },
    driverData: {
      id: "driver-low",
      first_name: "Mike",
      last_name: "Brown",
      email: "<EMAIL>",
      phone_number: "+1444555666",
      avatar: null,
      score: 2.1,
      status: "active",
      tier: "basic",
    },
    metrics: {
      totalDeliveries: 25,
      totalMiles: 1200,
      totalTonnage: 75,
    },
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};

export const NoDriver: Story = {
  args: {
    driver: null,
    driverData: undefined,
    metrics: undefined,
    isDriverLoading: false,
    isMetricsLoading: false,
    isLoading: false,
  },
};
