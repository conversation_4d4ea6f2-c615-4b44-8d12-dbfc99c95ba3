import { ArrowRight, Navigation, Shield } from "lucide-react";
import { <PERSON> } from "react-router";

import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/User";

const VerificationBanner = () => {
  const { driver } = useUser();
  const { toast } = useToast();

  // Don't show banner if driver is already verified
  if (driver?.tier === "verified") {
    return null;
  }

  return (
    <div className="bg-background relative mb-8 overflow-hidden rounded-lg border p-8">
      <div className="from-accent/10 absolute top-0 right-0 h-full w-1/2 bg-linear-to-l to-transparent" />

      <div className="relative z-10 flex items-center justify-between">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Shield className="text-accent h-6 w-6 animate-pulse" />
            <h3 className="text-2xl font-bold">Get Verified</h3>
          </div>
          <p className="text-muted-foreground max-w-[40ch]">
            Unlock premium routes and faster verifications. Join our network of
            trusted drivers today.
          </p>
          <Button variant="accent" className="gap-2" asChild>
            <Link to="/app/drivers/profile/verification">
              Start Verification
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>

        <div className="hidden md:block">
          <Navigation className="text-accent/20 h-32 w-32" />
        </div>
      </div>
    </div>
  );
};

export default VerificationBanner;
