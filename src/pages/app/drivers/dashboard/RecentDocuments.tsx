import { format } from "date-fns";
import { CalendarDays, FileText } from "lucide-react";
import { Link } from "react-router";

import {
  DocumentType,
  DocumentTypeBadge,
} from "@/components/common/types/DocumentType";
import EmptyList from "@/components/shared/EmptyList";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface RecentDocumentsProps {
  documents: any[];
}

const RecentDocuments = ({ documents }: RecentDocumentsProps) => {
  if (!documents || documents.length === 0) {
    return (
      <EmptyList
        title="No documents"
        description="You haven't uploaded any documents yet."
      >
        <Link to="/app/drivers/documents/create">
          <Button variant="default">Upload a Document</Button>
        </Link>
      </EmptyList>
    );
  }

  return (
    <div className="space-y-3">
      {documents.map((document) => (
        <Link
          key={document.id}
          to={`/app/drivers/documents/${document.id}`}
          className="block"
        >
          <Card className="hover:bg-accent transition-colors">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-medium">
                    {document.name || "Untitled Document"}
                  </h3>
                  <div className="text-muted-foreground flex items-center text-sm">
                    <FileText className="mr-1 h-3.5 w-3.5" />
                    <DocumentTypeBadge
                      type={document.type}
                      metadata={document.metadata}
                    />
                  </div>
                </div>
                <div className="text-muted-foreground flex items-center text-sm">
                  <CalendarDays className="mr-1 h-3.5 w-3.5" />
                  {format(new Date(document.created_at), "MMM d, yyyy")}
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
};

export default RecentDocuments;
