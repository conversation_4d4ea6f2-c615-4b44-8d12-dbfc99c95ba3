import { DriverScore, getScoreColor } from "@/components/common/DriverScore";
import { Badge } from "@/components/ui/badge";
import { useUser } from "@/contexts/User";

const ProfileOverview = () => {
  const { driver } = useUser();

  if (!driver) {
    return null;
  }

  return (
    <div className="flex items-center justify-between">
      <div>
        <h3 className="text-lg font-semibold">Driver Score</h3>
        <p className="text-muted-foreground text-sm">
          Based on performance metrics
        </p>
        <div className="mt-2 flex gap-2">
          <Badge variant={driver.tier === "pro" ? "default" : "secondary"}>
            {driver.tier.toUpperCase()}
          </Badge>
        </div>
      </div>
      <div className="text-right">
        <DriverScore score={driver.score} size="lg" showLabel={false} />
        <p className="text-muted-foreground text-sm">out of 500</p>
      </div>
    </div>
  );
};

export default ProfileOverview;
