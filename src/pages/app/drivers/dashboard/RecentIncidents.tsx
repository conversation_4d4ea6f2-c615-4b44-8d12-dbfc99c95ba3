import { formatDistanceToNow } from "date-fns";
import { CalendarClock } from "lucide-react";
import { Link } from "react-router";

import {
  IncidentSeverity,
  IncidentSeverityBadge,
} from "@/components/common/types/IncidentSeverity";
import {
  IncidentType,
  IncidentTypeBadge,
} from "@/components/common/types/IncidentType";
import EmptyList from "@/components/shared/EmptyList";
import { Card, CardContent } from "@/components/ui/card";

interface RecentIncidentsProps {
  incidents: any[];
}

const RecentIncidents = ({ incidents }: RecentIncidentsProps) => {
  if (!incidents.length) {
    return (
      <p className="text-muted-foreground">No recent incidents reported</p>
    );
  }

  return (
    <div className="space-y-3">
      {incidents.map((incident) => (
        <Link
          key={incident.id}
          to={`/app/drivers/incidents/${incident.id}`}
          className="block"
        >
          <Card className="hover:bg-accent transition-colors">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-medium">{incident.title}</h3>
                  <div className="text-muted-foreground flex items-center gap-2 text-sm">
                    <IncidentTypeBadge type={incident.type} />
                    <span>•</span>
                    <IncidentSeverityBadge severity={incident.severity} />
                  </div>
                </div>
                <div className="text-muted-foreground flex items-center text-sm">
                  <CalendarClock className="mr-1 h-3.5 w-3.5" />
                  {formatDistanceToNow(new Date(incident.created_at), {
                    addSuffix: true,
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
};

export default RecentIncidents;
