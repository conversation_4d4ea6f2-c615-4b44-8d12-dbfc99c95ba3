import {
  AlertOctagon,
  Car,
  DollarSign,
  Flag,
  Map,
  Scale,
  Truck,
} from "lucide-react";

import Currency from "@/components/shared/Currency";
import { Card, CardContent } from "@/components/ui/card";

interface MetricsOverviewProps {
  metrics: {
    score?: number;
    totalShipments: number;
    weeklyTonnage: number;
    weeklyEarnings: number;
    totalMileage?: number;
    activeIncidents?: number;
    routesCompleted?: number;
  };
}

const MetricsOverview = ({ metrics }: MetricsOverviewProps) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3">
      {/* Total Shipments */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <Truck className="h-8 w-8 text-blue-500" />
            <div className="space-y-1 text-right">
              <h3 className="text-muted-foreground text-lg font-medium">
                Total Shipments
              </h3>
              <p className="text-3xl font-bold">{metrics.totalShipments}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total Mileage */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <Map className="h-8 w-8 text-orange-500" />
            <div className="space-y-1 text-right">
              <h3 className="text-muted-foreground text-lg font-medium">
                Total Mileage
              </h3>
              <p className="text-3xl font-bold">
                {metrics.totalMileage || 0} mi
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Weekly Tonnage */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <Scale className="h-8 w-8 text-purple-500" />
            <div className="space-y-1 text-right">
              <h3 className="text-muted-foreground text-lg font-medium">
                Weekly Tonnage
              </h3>
              <p className="text-3xl font-bold">{metrics.weeklyTonnage}t</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Weekly Earnings */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <DollarSign className="h-8 w-8 text-green-500" />
            <div className="space-y-1 text-right">
              <h3 className="text-muted-foreground text-lg font-medium">
                Weekly Earnings
              </h3>
              <p className="text-3xl font-bold">
                <Currency amount={metrics.weeklyEarnings} />
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Active Incidents */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <AlertOctagon className="h-8 w-8 text-red-500" />
            <div className="space-y-1 text-right">
              <h3 className="text-muted-foreground text-lg font-medium">
                Active Incidents
              </h3>
              <p className="text-3xl font-bold">
                {metrics.activeIncidents || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Routes Completed */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <Car className="h-8 w-8 text-teal-500" />
            <div className="space-y-1 text-right">
              <h3 className="text-muted-foreground text-lg font-medium">
                Routes Completed
              </h3>
              <p className="text-3xl font-bold">
                {metrics.routesCompleted || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MetricsOverview;
