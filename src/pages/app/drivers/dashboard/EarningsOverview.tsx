import { format } from "date-fns";
import { BanknoteIcon, CalendarIcon, PiggyBankIcon } from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";

const EarningsOverview = () => {
  // For demo purposes, let's set the next payment date to 7 days from now
  const nextPaymentDate = new Date();
  nextPaymentDate.setDate(nextPaymentDate.getDate() + 7);

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">$0.00</p>
                <p className="text-muted-foreground text-sm">Current Balance</p>
              </div>
              <div className="text-right">
                <p className="text-muted-foreground text-sm">Next Payment</p>
                <p className="font-medium">
                  <span className="hidden lg:inline">
                    {format(nextPaymentDate, "MMMM d")}
                  </span>
                  <span className="lg:hidden">
                    {format(nextPaymentDate, "MM/dd")}
                  </span>
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardContent className="p-6">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CalendarIcon className="text-muted-foreground h-5 w-5" />
                <h3 className="font-semibold">This Week</h3>
              </div>
            </div>
            <p className="mb-1 text-2xl font-bold">$0.00</p>
            <p className="text-muted-foreground text-sm">
              Week of {format(new Date(), "MMM d")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BanknoteIcon className="text-muted-foreground h-5 w-5" />
                <h3 className="font-semibold">This Month</h3>
              </div>
            </div>
            <p className="mb-1 text-2xl font-bold">$0.00</p>
            <p className="text-muted-foreground text-sm">
              {format(new Date(), "MMMM yyyy")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <PiggyBankIcon className="text-muted-foreground h-5 w-5" />
                <h3 className="font-semibold">Tips</h3>
              </div>
            </div>
            <p className="mb-1 text-2xl font-bold">$0.00</p>
            <p className="text-muted-foreground text-sm">This month's tips</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EarningsOverview;
