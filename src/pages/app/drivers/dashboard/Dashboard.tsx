import { useState } from "react";
import { useNavigate } from "react-router";
import { toast } from "sonner";

import { useDriversCurrentShipment } from "@/api";
import { useDriverAnalytics } from "@/api/drivers/use-driver-analytics";
import { useDriverShipment } from "@/contexts/DriverShipmentContext";
import { useUser } from "@/contexts/User";
import { DriverDashboardPage } from "./DriverDashboardPage";

export default function DriversApp() {
  const navigate = useNavigate();
  const { driver, isLoading } = useUser();
  const [isTracking, setIsTracking] = useState(false);
  const { activeShipment } = useDriverShipment();

  const {
    data: currentShipment,
    isLoading: isLoadingCurrent,
    error: currentError,
  } = useDriversCurrentShipment();

  const { data: analytics, isLoading: isLoadingAnalytics } = useDriverAnalytics(
    driver?.id,
  );

  const hasActiveShipment = Boolean(activeShipment || currentShipment);

  const handleToggleTracking = () => {
    setIsTracking(!isTracking);
    toast.success(isTracking ? "Tracking stopped" : "Tracking started");
  };

  const handleScanDocument = () => {
    navigate("/app/drivers/documents/scan");
  };

  const handleCreateDocument = () => {
    navigate("/app/drivers/documents/create");
  };

  const handleNavigateToDriverOnboarding = () => {
    navigate("/app/onboarding/driver");
  };

  return (
    <DriverDashboardPage
      driver={driver}
      isLoading={isLoading}
      currentShipment={currentShipment}
      isLoadingCurrent={isLoadingCurrent}
      currentError={currentError}
      analytics={analytics}
      isLoadingAnalytics={isLoadingAnalytics}
      activeShipment={activeShipment}
      isTracking={isTracking}
      hasActiveShipment={hasActiveShipment}
      onToggleTracking={handleToggleTracking}
      onScanDocument={handleScanDocument}
      onCreateDocument={handleCreateDocument}
      onNavigateToDriverOnboarding={handleNavigateToDriverOnboarding}
    />
  );
}
