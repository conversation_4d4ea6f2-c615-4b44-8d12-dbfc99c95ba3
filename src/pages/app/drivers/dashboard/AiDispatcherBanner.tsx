import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { <PERSON> } from "react-router";

import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/User";

const AiDispatcherBanner = () => {
  const { driver } = useUser();
  const { toast } = useToast();

  // Don't show banner if driver already has AI Dispatcher
  // @ts-expect-error - driver.add_ons is not typed
  if ((driver?.add_ons || [])?.includes("ai_dispatcher")) {
    return null;
  }

  return (
    <div className="bg-background relative mb-8 overflow-hidden rounded-lg border p-8">
      <div className="from-primary/10 absolute top-0 right-0 h-full w-1/2 bg-linear-to-l to-transparent" />

      <div className="relative z-10 flex items-center justify-between">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Bot className="text-primary h-6 w-6 animate-pulse" />
            <h3 className="text-2xl font-bold">AI Dispatcher</h3>
          </div>
          <p className="text-muted-foreground max-w-[40ch]">
            Upgrade your driving experience with AI-powered route optimization
            and real-time assistance.
          </p>
          <Button className="gap-2" asChild>
            <Link to="/app/drivers/dispatcher">
              Add AI Dispatcher
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>

        <div className="hidden md:block">
          <Bot className="text-primary/20 h-32 w-32" />
        </div>
      </div>
    </div>
  );
};

export default AiDispatcherBanner;
