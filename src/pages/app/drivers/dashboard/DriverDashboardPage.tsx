import { Loader2, TruckIcon } from "lucide-react";

import { DriverAnalytics } from "@/api/drivers/use-driver-analytics";
import { queryFn as currentShipmentQueryFn } from "@/api/drivers/use-drivers-current-shipment";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { UserContextType } from "@/contexts/User";
import AiDispatcherBanner from "@/pages/app/drivers/dashboard/AiDispatcherBanner";
import AnalyticsCard from "@/pages/app/drivers/dashboard/components/AnalyticsCard";
import DocumentsSection from "@/pages/app/drivers/dashboard/components/DocumentsSection";
import EarningsSection from "@/pages/app/drivers/dashboard/components/EarningsSection";
import IncidentsSection from "@/pages/app/drivers/dashboard/components/IncidentsSection";
import ProfileCard from "@/pages/app/drivers/dashboard/components/ProfileCard";
import ShipmentsSection from "@/pages/app/drivers/dashboard/components/ShipmentsSection";
import WelcomeSection from "@/pages/app/drivers/dashboard/components/WelcomeSection";
import VerificationBanner from "@/pages/app/drivers/dashboard/VerificationBanner";

export interface DriverDashboardPageProps {
  driver: UserContextType["driver"];
  isLoading: UserContextType["isLoading"];
  currentShipment: Awaited<ReturnType<typeof currentShipmentQueryFn>>;
  isLoadingCurrent: boolean;
  currentError: Error | null;
  analytics: DriverAnalytics | undefined;
  isLoadingAnalytics: boolean;
  activeShipment: Awaited<ReturnType<typeof currentShipmentQueryFn>>;
  isTracking: boolean;
  hasActiveShipment: boolean;
  onToggleTracking: () => void;
  onScanDocument: () => void;
  onCreateDocument: () => void;
  onNavigateToDriverOnboarding: () => void;
}

export function DriverDashboardPage({
  driver,
  isLoading,
  currentShipment,
  isLoadingCurrent,
  currentError,
  analytics,
  isLoadingAnalytics,
  activeShipment,
  isTracking,
  hasActiveShipment,
  onToggleTracking,
  onScanDocument,
  onCreateDocument,
  onNavigateToDriverOnboarding,
}: DriverDashboardPageProps) {
  const loading = isLoading || isLoadingCurrent || isLoadingAnalytics;

  if (loading) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!driver) {
    return (
      <div className="container py-8">
        <Card className="mx-auto max-w-2xl">
          <CardContent className="flex flex-col items-center px-8 pt-6 pb-8 text-center">
            <TruckIcon className="text-muted-foreground mb-4 h-12 w-12" />
            <h2 className="mb-2 text-2xl font-semibold">
              No Driver Profile Found
            </h2>
            <p className="text-muted-foreground mb-6">
              You need to create a driver profile to start managing shipments
              and deliveries.
            </p>
            <Button onClick={onNavigateToDriverOnboarding} size="lg">
              Create Driver Profile
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <WelcomeSection
        isTracking={isTracking}
        hasActiveShipment={hasActiveShipment}
        onToggleTracking={onToggleTracking}
        onScanDocument={onScanDocument}
        onCreateDocument={onCreateDocument}
      />

      <div className="grid gap-6 lg:grid-cols-[2fr_1fr]">
        {/* Left Column - Stacked Cards */}
        <div className="space-y-6">
          {/* Verification Banner */}
          <VerificationBanner />

          {/* Shipments Section */}
          <ShipmentsSection
            currentShipment={currentShipment}
            error={currentError}
          />

          {/* AI Dispatcher Banner */}
          <AiDispatcherBanner />

          {/* Earnings Section */}
          <EarningsSection />

          {/* Documents Section */}
          <DocumentsSection />

          {/* Incidents Section */}
          <IncidentsSection />
        </div>

        {/* Right Column - Profile Card and Stats */}
        <div className="space-y-6">
          {/* Profile Card */}
          <ProfileCard />

          {/* Metrics Overview */}
          <AnalyticsCard metrics={analytics} />
        </div>
      </div>
    </div>
  );
}
