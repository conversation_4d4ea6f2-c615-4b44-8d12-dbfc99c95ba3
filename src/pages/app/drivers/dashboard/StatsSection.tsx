import { Loader2 } from "lucide-react";

import { useDriverAnalytics } from "@/api/drivers/use-driver-analytics";
import { DriverScore } from "@/components/common/DriverScore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useUser } from "@/contexts/User";
import MetricsOverview from "./MetricsOverview";

const StatsSection = () => {
  const { driver } = useUser();
  const { data: analytics, isLoading } = useDriverAnalytics(driver?.id);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="text-primary h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">No analytics data available</p>
      </div>
    );
  }

  const metrics = {
    score: analytics.score,
    totalShipments: analytics.totalShipments,
    weeklyTonnage: analytics.weeklyTonnage,
    weeklyEarnings: analytics.weeklyEarnings,
    totalMileage: analytics.totalMiles,
    activeIncidents: analytics.activeIncidents,
    routesCompleted: analytics.routesCompleted,
  };

  return (
    <div className="grid gap-6 lg:grid-cols-3">
      {/* Performance Score */}
      <Card className="lg:col-span-1">
        <CardHeader>
          <CardTitle className="text-xl font-medium">Driver Score</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center space-y-4">
            <DriverScore score={analytics.score} size="xl" showLabel={true} />
            <p className="text-muted-foreground text-center text-sm">
              Based on delivery times, customer feedback, and safety record
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Overview */}
      <div className="lg:col-span-2">
        <MetricsOverview metrics={metrics} />
      </div>
    </div>
  );
};

export default StatsSection;
