import { Loader2 } from "lucide-react";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { type Database } from "@/supabase/types";

type Shipment = Database["public"]["Tables"]["shipments"]["Row"];
type Stop = Database["public"]["Tables"]["stops"]["Row"];
type Location = Database["public"]["Tables"]["locations"]["Row"];
type Verification = Database["public"]["Tables"]["verifications"]["Row"];

type RecentShipment = Shipment & {
  stops?: (Stop & {
    location?: Location;
    verifications?: Verification[];
  })[];
};

interface RecentShipmentsProps {
  recentShipments: RecentShipment[];
  isLoading: boolean;
}

const RecentShipments = ({
  recentShipments,
  isLoading,
}: RecentShipmentsProps) => {
  return (
    <div className="mt-8">
      <h3 className="mb-4 text-lg font-semibold">Recent Shipments</h3>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : recentShipments?.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground text-sm">
              No recent shipments found
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {recentShipments?.map((shipment) => (
            <Card key={shipment.id}>
              <CardHeader className="p-4">
                <CardTitle className="flex justify-between text-base">
                  <span>Shipment #{shipment.id.slice(0, 8)}</span>
                  <span className="text-sm font-normal capitalize">
                    {shipment.status}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="space-y-2">
                  {shipment.stops?.map((stop) => (
                    <div key={stop.id} className="text-sm">
                      <span className="font-medium">
                        Stop {stop.sequence_number}:
                      </span>{" "}
                      {stop.location?.formatted}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecentShipments;
