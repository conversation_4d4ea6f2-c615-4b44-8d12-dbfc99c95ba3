import { <PERSON>R<PERSON>, BanknoteIcon, PiggyBank } from "lucide-react";
import { Link } from "react-router";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/User";

const BankingBanner = () => {
  const { driver } = useUser();
  const { toast } = useToast();

  return (
    <div className="bg-background relative mb-8 overflow-hidden rounded-lg border p-8">
      <div className="from-primary/10 absolute top-0 right-0 h-full w-1/2 bg-linear-to-l to-transparent" />

      <div className="relative z-10 flex items-center justify-between">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <BanknoteIcon className="text-primary h-6 w-6 animate-pulse" />
            <h3 className="text-2xl font-bold">Setup Banking</h3>
          </div>
          <p className="text-muted-foreground max-w-[40ch]">
            Get paid instantly for your deliveries and tips. Set up direct
            deposit today.
          </p>
          <Button className="gap-2" asChild>
            <Link to="/app/drivers/payments/setup">
              Setup Banking
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>

        <div className="hidden md:block">
          <PiggyBank className="text-primary/20 h-32 w-32" />
        </div>
      </div>
    </div>
  );
};

export default BankingBanner;
