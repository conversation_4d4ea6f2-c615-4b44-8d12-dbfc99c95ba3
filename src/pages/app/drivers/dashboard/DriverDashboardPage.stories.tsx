import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { DriverDashboardPage } from "./DriverDashboardPage";

const meta: Meta<typeof DriverDashboardPage> = {
  title: "Pages/Drivers/Dashboard",
  component: DriverDashboardPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockDriver = {
  id: "driver-123",
  user_id: "user-123",
  first_name: "<PERSON>",
  last_name: "<PERSON>",
  email: "<EMAIL>",
  phone_number: "+**********",
  avatar: null,
  location_id: null,
  score: 4.8,
  status: "active" as const,
  tier: "professional",
  created_at: new Date().toISOString(),
  verified_at: new Date().toISOString(),
};

const mockCurrentShipment = {
  id: "shipment-456",
  cancelled_at: null,
  completed_at: null,
  created_at: new Date().toISOString(),
  distance: 250,
  driver_id: "driver-123",
  duration: null,
  load_id: "load-789",
  mode: "closed" as const,
  organization_id: "org-123",
  source: "organization" as const,
  started_at: new Date().toISOString(),
  status: "in_progress" as const,
  type: "ground" as const,
  valuation: 50000,
  weight: 15000,
  load: {
    id: "load-789",
    created_at: new Date().toISOString(),
    destination_id: "location-2",
    label: "Electronics Shipment",
    notes: "Handle with care",
    organization_id: "org-123",
    origin_id: "location-1",
    perishable: false,
    start_date: new Date().toISOString(),
    status: "in_transit" as const,
    type: "general" as const,
    valuation: 50000,
    weight: 15000,
  },
  verifications: [
    {
      verified_at: new Date().toISOString(),
    },
  ],
  stops: [
    {
      id: "stop-1",
      arrived_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      departed_at: new Date().toISOString(),
      label: "Pickup Location",
      latitude: 40.7128,
      load_id: "load-789",
      location_id: "location-1",
      longitude: -74.006,
      next_stop_id: "stop-2",
      previous_stop_id: null,
      sequence_number: 1,
      shipment_id: "shipment-456",
      tags: ["pickup"],
      type: "pickup" as const,
      location: {
        id: "location-1",
        city: "New York",
        country: "USA",
        created_at: new Date().toISOString(),
        driver_id: null,
        formatted: "123 Industrial Blvd, New York, NY 10001",
        latitude: 40.7128,
        longitude: -74.006,
        neighborhood: "Manhattan",
        organization_id: "org-123",
        postal: "10001",
        state: "NY",
        street: "123 Industrial Blvd",
        suite: null,
        type: "warehouse" as const,
      },
    },
    {
      id: "stop-2",
      arrived_at: null,
      created_at: new Date().toISOString(),
      departed_at: null,
      label: "Delivery Location",
      latitude: 39.9526,
      load_id: "load-789",
      location_id: "location-2",
      longitude: -75.1652,
      next_stop_id: null,
      previous_stop_id: "stop-1",
      sequence_number: 2,
      shipment_id: "shipment-456",
      tags: ["delivery"],
      type: "destination" as const,
      location: {
        id: "location-2",
        city: "Philadelphia",
        country: "USA",
        created_at: new Date().toISOString(),
        driver_id: null,
        formatted: "456 Commerce Ave, Philadelphia, PA 19103",
        latitude: 39.9526,
        longitude: -75.1652,
        neighborhood: "Center City",
        organization_id: "org-123",
        postal: "19103",
        state: "PA",
        street: "456 Commerce Ave",
        suite: null,
        type: "distribution_center" as const,
      },
    },
  ],
};

const mockAnalytics = {
  totalShipments: 127,
  totalMiles: 12450,
  totalTonnage: 850,
  weeklyEarnings: 2850,
  weeklyMiles: 1200,
  weeklyTonnage: 45,
  routesCompleted: 127,
  activeIncidents: 0,
  score: 4.8,
};

export const Default: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    currentShipment: mockCurrentShipment,
    isLoadingCurrent: false,
    currentError: null,
    analytics: mockAnalytics,
    isLoadingAnalytics: false,
    activeShipment: mockCurrentShipment,
    isTracking: false,
    hasActiveShipment: true,
    onToggleTracking: fn(),
    onScanDocument: fn(),
    onCreateDocument: fn(),
    onNavigateToDriverOnboarding: fn(),
  },
};

export const Loading: Story = {
  args: {
    driver: mockDriver,
    isLoading: true,
    currentShipment: null,
    isLoadingCurrent: true,
    currentError: null,
    analytics: undefined,
    isLoadingAnalytics: true,
    activeShipment: null,
    isTracking: false,
    hasActiveShipment: false,
    onToggleTracking: fn(),
    onScanDocument: fn(),
    onCreateDocument: fn(),
    onNavigateToDriverOnboarding: fn(),
  },
};

export const NoShipment: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    currentShipment: null,
    isLoadingCurrent: false,
    currentError: null,
    analytics: {
      ...mockAnalytics,
      totalShipments: 25,
      routesCompleted: 25,
      weeklyEarnings: 0,
      weeklyMiles: 0,
      weeklyTonnage: 0,
    },
    isLoadingAnalytics: false,
    activeShipment: null,
    isTracking: false,
    hasActiveShipment: false,
    onToggleTracking: fn(),
    onScanDocument: fn(),
    onCreateDocument: fn(),
    onNavigateToDriverOnboarding: fn(),
  },
};

export const TrackingActive: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    currentShipment: mockCurrentShipment,
    isLoadingCurrent: false,
    currentError: null,
    analytics: mockAnalytics,
    isLoadingAnalytics: false,
    activeShipment: mockCurrentShipment,
    isTracking: true,
    hasActiveShipment: true,
    onToggleTracking: fn(),
    onScanDocument: fn(),
    onCreateDocument: fn(),
    onNavigateToDriverOnboarding: fn(),
  },
};

export const Error: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    currentShipment: null,
    isLoadingCurrent: false,
    currentError: new globalThis.Error("Failed to load current shipment"),
    analytics: undefined,
    isLoadingAnalytics: false,
    activeShipment: null,
    isTracking: false,
    hasActiveShipment: false,
    onToggleTracking: fn(),
    onScanDocument: fn(),
    onCreateDocument: fn(),
    onNavigateToDriverOnboarding: fn(),
  },
};

export const NoDriverProfile: Story = {
  args: {
    driver: null,
    isLoading: false,
    currentShipment: null,
    isLoadingCurrent: false,
    currentError: null,
    analytics: undefined,
    isLoadingAnalytics: false,
    activeShipment: null,
    isTracking: false,
    hasActiveShipment: false,
    onToggleTracking: fn(),
    onScanDocument: fn(),
    onCreateDocument: fn(),
    onNavigateToDriverOnboarding: fn(),
  },
};

export const LoadingAnalytics: Story = {
  args: {
    driver: mockDriver,
    isLoading: false,
    currentShipment: mockCurrentShipment,
    isLoadingCurrent: false,
    currentError: null,
    analytics: undefined,
    isLoadingAnalytics: true,
    activeShipment: mockCurrentShipment,
    isTracking: false,
    hasActiveShipment: true,
    onToggleTracking: fn(),
    onScanDocument: fn(),
    onCreateDocument: fn(),
    onNavigateToDriverOnboarding: fn(),
  },
};
