import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON> } from "react-router";

import { But<PERSON> } from "@/components/ui/button";
import BankingBanner from "@/pages/app/drivers/dashboard/BankingBanner";
import EarningsOverview from "@/pages/app/drivers/dashboard/EarningsOverview";

const EarningsSection = () => {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Earnings</h2>
        <Link to="/app/drivers/payments">
          <Button variant="outline" size="sm" className="gap-2">
            View All Earnings
            <ArrowRight className="h-4 w-4" />
          </Button>
        </Link>
      </div>
      <EarningsOverview />
      {/* Banking Banner */}
      <BankingBanner />
    </div>
  );
};

export default EarningsSection;
