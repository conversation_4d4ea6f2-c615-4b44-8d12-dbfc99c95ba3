import { <PERSON>R<PERSON>, Plus, Shield } from "lucide-react";
import { <PERSON> } from "react-router";

import { useListIncidents } from "@/api/incidents/use-list-incidents";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useUser } from "@/contexts/User";
import RecentIncidents from "@/pages/app/drivers/dashboard/RecentIncidents";

const IncidentsSection = () => {
  const { driver } = useUser();
  const { data: incidents } = useListIncidents(
    { driver_id: driver?.id, pageSize: 3 },
    { enabled: !!driver?.id },
  );

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Incidents</h2>
        <Link to="/app/drivers/incidents">
          <Button variant="outline" size="sm" className="gap-2">
            View All Incidents
            <ArrowRight className="h-4 w-4" />
          </Button>
        </Link>
      </div>

      <div className="mb-4">
        <Link to="/app/drivers/incidents/create" className="block">
          <div className="rounded-lg border border-amber-200 bg-amber-50 p-3 transition-colors hover:bg-amber-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-amber-500" />
                <span className="font-medium text-amber-800">
                  Report a New Incident
                </span>
              </div>
              <Plus className="h-4 w-4 text-amber-500" />
            </div>
            <p className="mt-1 text-sm text-amber-700">
              Report accidents, damages, or safety concerns immediately
            </p>
          </div>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="text-muted-foreground h-5 w-5" />
            Recent Incidents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <RecentIncidents incidents={incidents?.items || []} />
        </CardContent>
      </Card>
    </div>
  );
};

export default IncidentsSection;
