import { ArrowR<PERSON> } from "lucide-react";
import { Link } from "react-router";

import { Button } from "@/components/ui/button";
import { useDriverShipment } from "@/contexts/DriverShipmentContext";
import CurrentShipment from "@/pages/app/drivers/dashboard/CurrentShipment";
import PastShipments from "@/pages/app/drivers/dashboard/PastShipments";

interface ShipmentsSectionProps {
  currentShipment: any;
  error: Error | null;
}

const ShipmentsSection = ({
  currentShipment,
  error,
}: ShipmentsSectionProps) => {
  const { pastShipments } = useDriverShipment();

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Shipments</h2>
        <Link to="/app/drivers/shipments">
          <Button variant="outline" size="sm" className="gap-2">
            View All Shipments
            <ArrowRight className="h-4 w-4" />
          </Button>
        </Link>
      </div>

      <CurrentShipment
        currentShipment={currentShipment ?? null}
        error={error}
      />

      {pastShipments && pastShipments.length > 0 && (
        <div className="mt-6">
          <h3 className="mb-3 text-lg font-medium">
            Recent Completed Shipments
          </h3>
          <PastShipments shipments={pastShipments.slice(0, 3)} />
        </div>
      )}
    </div>
  );
};

export default ShipmentsSection;
