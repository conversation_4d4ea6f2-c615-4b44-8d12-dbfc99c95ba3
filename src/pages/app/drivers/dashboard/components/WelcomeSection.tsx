import { ArrowR<PERSON>, Play, ScanSearch, StopCircle, Upload } from "lucide-react";
import { <PERSON> } from "react-router";

import { Button } from "@/components/ui/button";
import { useUser } from "@/contexts/User";

interface WelcomeSectionProps {
  isTracking: boolean;
  hasActiveShipment: boolean;
  onToggleTracking: () => void;
  onScanDocument: () => void;
  onCreateDocument: () => void;
}

const WelcomeSection = ({
  isTracking,
  hasActiveShipment,
  onToggleTracking,
  onScanDocument,
  onCreateDocument,
}: WelcomeSectionProps) => {
  const { driver } = useUser();

  return (
    <div className="mb-8">
      <h1 className="mb-2 text-4xl font-bold">
        Welcome back, {driver?.first_name}! 👋
      </h1>
      <p className="text-muted-foreground text-lg">
        Here's an overview of your current shipments and earnings.
      </p>

      {/* Quick Action Bar */}
      <div className="mt-8 mb-8 flex flex-wrap gap-4">
        <Button
          variant={isTracking ? "destructive" : "default"}
          onClick={onToggleTracking}
          className="gap-2"
          disabled={!hasActiveShipment}
        >
          {isTracking ? (
            <>
              <StopCircle className="h-4 w-4" />
              Stop Tracking
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              Start Tracking
            </>
          )}
        </Button>
        <Button variant="secondary" onClick={onScanDocument} className="gap-2">
          <ScanSearch className="h-4 w-4" />
          Scan Document
        </Button>
        <Button
          variant="secondary"
          onClick={onCreateDocument}
          className="gap-2"
        >
          <Upload className="h-4 w-4" />
          Create Document
        </Button>
      </div>
    </div>
  );
};

export default WelcomeSection;
