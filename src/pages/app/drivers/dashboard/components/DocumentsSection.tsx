import { ArrowRight } from "lucide-react";
import { <PERSON> } from "react-router";

import { useListDocuments } from "@/api/documents/use-list-documents";
import { Button } from "@/components/ui/button";
import { useUser } from "@/contexts/User";
import RecentDocuments from "@/pages/app/drivers/dashboard/RecentDocuments";

const DocumentsSection = () => {
  const { driver } = useUser();
  const { data: documents } = useListDocuments(
    { driver_id: driver?.id, pageSize: 3 },
    { enabled: !!driver?.id },
  );

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Documents</h2>
        <Link to="/app/drivers/documents">
          <Button variant="outline" size="sm" className="gap-2">
            View All Documents
            <ArrowRight className="h-4 w-4" />
          </Button>
        </Link>
      </div>
      <RecentDocuments documents={documents?.items || []} />
    </div>
  );
};

export default DocumentsSection;
