import { <PERSON>R<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ProfileOverview from "@/pages/app/drivers/dashboard/ProfileOverview";

const ProfileCard = () => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-2xl font-semibold">Profile</h2>
          <Link to="/app/drivers/profile">
            <Button variant="outline" size="sm" className="gap-2">
              View Profile
              <ArrowRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>
        <ProfileOverview />
      </CardContent>
    </Card>
  );
};

export default ProfileCard;
