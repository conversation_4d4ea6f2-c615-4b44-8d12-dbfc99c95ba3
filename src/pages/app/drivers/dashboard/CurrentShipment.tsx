import { TruckIcon } from "lucide-react";
import { <PERSON> } from "react-router";

import { useDriversCurrentShipment } from "@/api";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface CurrentShipmentProps {
  currentShipment:
    | Awaited<ReturnType<typeof useDriversCurrentShipment>>["data"]
    | null;
  error: Error | null;
}

const CurrentShipment = ({ currentShipment, error }: CurrentShipmentProps) => {
  if (error) {
    return (
      <Alert variant="destructive" className="mb-8">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          There was an error loading your shipment data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  if (!currentShipment) {
    return (
      <Card className="mb-8">
        <CardContent className="p-8 text-center">
          <TruckIcon className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
          <h3 className="mb-2 text-xl font-semibold">No Active Shipments</h3>
          <p className="text-muted-foreground mb-6">
            You don't have any active shipments at the moment. Check out
            available jobs in your area.
          </p>
          <Link to="/app/drivers/shipments/search">
            <Button size="lg">Browse Available Jobs</Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        <div className="space-y-4">
          {currentShipment.stops?.map((stop) => (
            <Card key={stop.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Stop #{stop.sequence_number}</p>
                    <p className="text-muted-foreground text-sm">
                      {stop.location.formatted}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div>
          {currentShipment.verifications?.[0]?.verified_at ? (
            <span className="text-green-500">Completed</span>
          ) : (
            <span className="text-yellow-500">Pending</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CurrentShipment;
