import { format } from "date-fns";
import { Calendar<PERSON>he<PERSON>, MapPin } from "lucide-react";
import { Link } from "react-router";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

interface PastShipmentsProps {
  shipments: any[];
}

const PastShipments = ({ shipments }: PastShipmentsProps) => {
  if (!shipments || shipments.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      {shipments.map((shipment) => {
        // Find pickup and delivery locations
        const pickupStop = shipment.stops?.find(
          (stop: any) => stop.type === "pickup",
        );
        const deliveryStop = shipment.stops?.find(
          (stop: any) => stop.type === "delivery",
        );

        return (
          <Link
            key={shipment.id}
            to={`/app/drivers/shipments/${shipment.id}`}
            className="block"
          >
            <Card className="hover:bg-accent transition-colors">
              <CardContent className="p-4">
                <div className="flex justify-between">
                  <div>
                    <h3 className="font-medium">
                      {shipment.reference_number ||
                        `Shipment #${shipment.id.substring(0, 8)}`}
                    </h3>
                    <div className="text-muted-foreground mt-1 flex flex-wrap items-center gap-1 text-sm">
                      {pickupStop && (
                        <div className="flex items-center">
                          <MapPin className="mr-1 h-3.5 w-3.5" />
                          {pickupStop.location?.formatted?.split(",")[0]}
                        </div>
                      )}
                      {pickupStop && deliveryStop && (
                        <span className="mx-1">→</span>
                      )}
                      {deliveryStop && (
                        <div className="flex items-center">
                          <MapPin className="mr-1 h-3.5 w-3.5" />
                          {deliveryStop.location?.formatted?.split(",")[0]}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <Badge variant="outline" className="mb-1">
                      Completed
                    </Badge>
                    <div className="text-muted-foreground flex items-center text-xs">
                      <CalendarCheck className="mr-1 h-3 w-3" />
                      {shipment.completed_at
                        ? format(new Date(shipment.completed_at), "MMM d, yyyy")
                        : "N/A"}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        );
      })}
    </div>
  );
};

export default PastShipments;
