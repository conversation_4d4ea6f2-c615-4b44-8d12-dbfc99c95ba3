import { <PERSON><PERSON>he<PERSON> } from "lucide-react";
import { Link, useParams } from "react-router";

import { But<PERSON> } from "@/components/ui/button";

export default function VerificationDetails() {
  const { id } = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">
            Verification Details
          </h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <ShieldCheck className="h-4 w-4" />
            <p>Verification #{id}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to={`/app/console/verifications/${id}/edit`}>
              Edit Verification
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/app/console/verifications">Back to Verifications</Link>
          </Button>
        </div>
      </div>
      <div className="flex min-h-[400px] items-center justify-center">
        <span className="text-muted-foreground">
          Verification details coming soon
        </span>
      </div>
    </div>
  );
}
