import { useQuery } from "@tanstack/react-query";
import { CheckSquare } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import type { VerificationFormValues } from "@/components/forms/VerificationForm";

import { useUpdateVerification } from "@/api/verifications/use-update-verification";
import VerificationForm from "@/components/forms/VerificationForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Verification",
    toasts: {
      success: "Verification updated successfully",
      error: "Failed to update verification",
    },
    loading: "Loading verification...",
    error: "Failed to load verification",
    notFound: "Verification not found",
    backButton: "Back to Verifications",
  },
};

// Hook to get a single verification
function useGetVerification(id: string) {
  return useQuery({
    queryKey: ["verifications", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("verifications")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditVerificationPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: verification, isLoading, error } = useGetVerification(id!);

  const updateVerification = useUpdateVerification({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/verifications/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: VerificationFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const verificationData = {
      id,
      stop_id: values.stop_id || null,
      driver_id: values.driver_id || null,
      vehicle_id: values.vehicle_id || null,
      document_id: values.document_id || null,
      verified: values.verified,
      notes: values.notes || null,
      verified_at: values.verified ? new Date().toISOString() : null,
    };

    updateVerification.mutate(verificationData);
  };

  const handleCancel = () => {
    navigate(`/app/console/verifications/${id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <CheckSquare className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <CheckSquare className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <ErrorAlert error={error} />
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/verifications")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  if (!verification) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <CheckSquare className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/verifications")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: VerificationFormValues = {
    stop_id: verification.stop_id || "",
    driver_id: verification.driver_id || "",
    vehicle_id: verification.vehicle_id || "",
    document_id: verification.document_id || "",
    verified: verification.verified_at ? true : false,
    notes: verification.notes || "",
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <CheckSquare className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <VerificationForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={updateVerification.isPending}
        />
      </div>
    </div>
  );
}
