import { CheckSquare } from "lucide-react";
import { useNavigate } from "react-router";

import type { VerificationFormValues } from "@/components/forms/VerificationForm";

import { useCreateVerification } from "@/api/verifications/use-create-verification";
import VerificationForm from "@/components/forms/VerificationForm";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "New Verification",
    toasts: {
      success: "Verification created successfully",
      error: "Failed to create verification",
    },
    backButton: "Back to Verifications",
  },
};

export default function CreateVerificationPage() {
  const navigate = useNavigate();

  const createVerification = useCreateVerification({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/verifications/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: VerificationFormValues) => {
    // Map form values to the API expected format
    const verificationData = {
      stop_id: values.stop_id || null,
      driver_id: values.driver_id || null,
      vehicle_id: values.vehicle_id || null,
      document_id: values.document_id || null,
      verified: values.verified,
      notes: values.notes || null,
      verified_at: values.verified ? new Date().toISOString() : null,
    };

    createVerification.mutate(verificationData);
  };

  const handleCancel = () => {
    navigate("/app/console/verifications");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <CheckSquare className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <VerificationForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={createVerification.isPending}
        />
      </div>
    </div>
  );
}
