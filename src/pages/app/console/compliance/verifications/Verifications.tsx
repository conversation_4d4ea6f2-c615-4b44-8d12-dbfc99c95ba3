"use client";

import { Package } from "lucide-react";
import { Link } from "react-router";

import {
  useDeleteVerification,
  useListVerifications,
} from "@/api/verifications";
import { useSearchFilterValue } from "@/components/search/filter";
import { useSearchPaginationValue } from "@/components/search/pagination";
import { useSearchTextValue } from "@/components/search/text";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import ListVerifications from "./ListVerifications";

const i18n = {
  en: {
    title: "Verifications",
    addButton: "Add Verification",
    toasts: {
      deleteSuccess: "Verification deleted successfully",
      deleteError: "Failed to delete verification",
    },
    delete: {
      title: "Delete verification",
      description: "Are you sure you want to delete this verification?",
      confirm: "Delete",
      cancel: "Cancel",
    },
  },
  links: {
    create: "/app/console/verifications/new",
  },
};

export function VerificationsView({
  loading,
  error,
  verifications,
  onDelete,
}: {
  loading?: boolean;
  error?: Error | null;
  verifications?: ReturnType<typeof useListVerifications>["data"];
  onDelete?: (id: string) => void;
}) {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Package className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <Button asChild disabled={loading}>
          <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
        </Button>
      </div>

      {error && <ErrorAlert error={error} />}

      <ListVerifications
        loading={loading}
        verifications={verifications}
        onDelete={onDelete}
      />
    </div>
  );
}

export default function VerificationsPage() {
  const pagination = useSearchPaginationValue("verification");
  const verificationQuery = useSearchTextValue("verification");
  const isVerified = useSearchFilterValue<string>(
    "is_verified",
    "verification",
  );
  const hasDocument = useSearchFilterValue<string>(
    "has_document",
    "verification",
  );

  const {
    data: verifications,
    isLoading,
    error,
    refetch,
  } = useListVerifications({
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
    search: verificationQuery,
    is_verified:
      isVerified === "true" ? true : isVerified === "false" ? false : undefined,
    has_document:
      hasDocument === "true"
        ? true
        : hasDocument === "false"
          ? false
          : undefined,
  });

  const deleteVerification = useDeleteVerification({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.deleteSuccess,
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.deleteError + " " + error.message,
        variant: "destructive",
      });
    },
  });

  const handleDelete = (id: string) => {
    if (confirm(i18n.en.delete.description)) {
      deleteVerification.mutate({ id });
    }
  };

  return (
    <VerificationsView
      loading={isLoading}
      error={error}
      verifications={verifications}
      onDelete={handleDelete}
    />
  );
}
