"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { Check, Pencil, Trash } from "lucide-react";
import { Link, useNavigate } from "react-router";

import type { useListVerifications } from "@/api/verifications";
import type { UseDataTableProps } from "@/components/tables";

import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type FilterOption = {
  value: string | undefined;
  label: string;
};

type FilterGroup = {
  id: string;
  label: string;
  options: FilterOption[];
};

const i18n = {
  en: {
    noVerification: "There are no verifications yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search verifications...",
    },
    headers: {
      id: "ID",
      stop: "Stop",
      driver: "Driver",
      document: "Document",
      verified: "Verified",
      notes: "Notes",
      created_at: "Created At",
      actions: "Actions",
    },
    filters: {
      is_verified: {
        label: "Verification Status",
        all: "All Status",
        verified: "Verified",
        not_verified: "Not Verified",
      },
      has_document: {
        label: "Document Status",
        all: "All Status",
        with_document: "With Document",
        without_document: "Without Document",
      },
    },
  },
  links: {
    verifications: "/app/console/verifications/[id]",
  },
};

const groupName = "verification";

// Filter groups for the verification table
const filterGroups: FilterGroup[] = [
  {
    id: "is_verified",
    label: i18n.en.filters.is_verified.label,
    options: [
      { value: "ALL", label: i18n.en.filters.is_verified.all },
      { value: "true", label: i18n.en.filters.is_verified.verified },
      { value: "false", label: i18n.en.filters.is_verified.not_verified },
    ] satisfies { value: "true" | "false" | "ALL"; label: string }[],
  },
  {
    id: "has_document",
    label: i18n.en.filters.has_document.label,
    options: [
      { value: "ALL", label: i18n.en.filters.has_document.all },
      { value: "true", label: i18n.en.filters.has_document.with_document },
      { value: "false", label: i18n.en.filters.has_document.without_document },
    ] satisfies { value: "true" | "false" | "ALL"; label: string }[],
  },
];

export type VerificationsQueryResult = Awaited<
  ReturnType<typeof useListVerifications>
>["data"];
export type VerificationsType = VerificationsQueryResult["items"];
export type VerificationType = VerificationsType[number];
export type TableProps = UseDataTableProps<VerificationType, VerificationsType>;

// Links object used in the table
const tableLinks = {
  verifications: "/app/console/verifications/[id]",
};

export default function ListVerifications({
  loading = false,
  verifications,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onDelete,
}: PropsWithChildren<{
  loading?: boolean;
  verifications?: VerificationsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onDelete?: (id: string) => void;
}>) {
  const navigate = useNavigate();

  return (
    <ListTable
      loading={loading}
      data={verifications}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      filterGroups={filterGroups}
      i18n={{
        emptyText: i18n.en.noVerification,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      columns={({ i18n, TableActions }) => [
        selectColumn as ColumnDef<VerificationType, VerificationType[]>,
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.id || "ID"}
            />
          ),
          cell: ({ row }) => (
            <Link
              to={tableLinks.verifications.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "stop",
          accessorFn: (row) => row.stop?.label || "—",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.stop || "Stop"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("stop")}</div>,
        },
        {
          id: "driver",
          accessorFn: (row) =>
            row.driver
              ? `${row.driver.first_name} ${row.driver.last_name}`
              : "—",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.driver || "Driver"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("driver")}</div>,
        },
        {
          id: "document",
          accessorFn: (row) => (row.document ? row.document.name : "—"),
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.document || "Document"}
            />
          ),
          cell: ({ row }) => {
            const doc = row.original.document;
            return doc ? (
              <a
                href={doc.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:underline"
              >
                {doc.name}
              </a>
            ) : (
              <span>—</span>
            );
          },
        },
        {
          id: "verified",
          accessorFn: (row) => !!row.verified_at,
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.verified || "Verified"}
            />
          ),
          cell: ({ row }) => {
            const isVerified = row.getValue("verified");
            return isVerified ? (
              <Badge
                variant="accent"
                className="flex items-center gap-1 bg-green-100 text-green-700 hover:bg-green-100"
              >
                <Check className="h-3 w-3" />
                <span>Verified</span>
              </Badge>
            ) : (
              <Badge variant="outline" className="text-muted-foreground">
                Not Verified
              </Badge>
            );
          },
        },
        {
          id: "notes",
          accessorKey: "notes",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.notes || "Notes"}
            />
          ),
          cell: ({ row }) => (
            <div className="max-w-[200px] truncate">
              {row.getValue("notes") || "—"}
            </div>
          ),
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.created_at || "Created At"}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "actions",
          meta: {
            className: "w-[80px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TableActions table={table} i18n={i18n} />
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex items-center justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <span className="sr-only">Open menu</span>
                    <svg
                      width="15"
                      height="15"
                      viewBox="0 0 15 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                    >
                      <path
                        d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z"
                        fill="currentColor"
                        fillRule="evenodd"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() =>
                      navigate(
                        `/app/console/verifications/${row.original.id}/edit`,
                      )
                    }
                  >
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  {onDelete && (
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => onDelete(row.original.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ),
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
