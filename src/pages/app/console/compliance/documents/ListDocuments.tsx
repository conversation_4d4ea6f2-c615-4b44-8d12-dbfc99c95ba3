"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import { CircleIcon, MenuIcon, Settings2Icon } from "lucide-react";
import { Link } from "react-router";

import type { useListDocuments } from "@/api/documents/use-list-documents";
import type { UseDataTableProps } from "@/components/tables";

import DocumentViewer from "@/components/common/DocumentViewer";
import {
  SearchFilter,
  SearchText,
  useSearchPagination,
} from "@/components/search";
// import PreviewOrganization from "@/components/common/PreviewOrganization";
import EmptyList from "@/components/shared/EmptyList";
import { DataTable, useDataTable } from "@/components/tables";
import { dataTableColumns, selectColumn } from "@/components/tables/columns";
import {
  DataTableColumnHeader,
  DataTableSettings,
  DataTableSimplePagination,
} from "@/components/tables/helpers";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// import { DocumentMenu } from "@/components/actions/document";

const i18n = {
  en: {
    noDocument: "There are no documents yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search documents...",
    },
    headers: {
      name: "Name",
      type: "Type",
      organization: "Organization",
    },
    filters: {
      type: "Type",
      options: {
        ALL: "All",
        MANIFEST: "Manifest",
        CONTRACT: "Contract",
        GENERAL: "General",
        OTHER: "Other",
      },
    },
  },
  links: {
    documents: "/app/console/documents/[id]",
    organizations: "/app/console/organizations/[id]",
  },
};

const groupName = "document";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: "manifest", label: i18n.en.filters.options.MANIFEST },
      { value: "contract", label: i18n.en.filters.options.CONTRACT },
      { value: "general", label: i18n.en.filters.options.GENERAL },
      { value: "other", label: i18n.en.filters.options.OTHER },
    ],
  },
];

export type DocumentsQueryResult = Awaited<
  ReturnType<typeof useListDocuments>
>["data"];
export type DocumentsType = DocumentsQueryResult["items"];
export type DocumentType = DocumentsType[number];
export type TableProps = UseDataTableProps<DocumentType, DocumentsType>;

export function DocumentsTableActions({
  table,
}: {
  table: ReturnType<typeof useDataTable<DocumentType, DocumentsType>>["table"];
}) {
  const selection = table.getSelectedRowModel();
  const selectionCount = selection.rows.length;
  const hasSelection = selectionCount > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={!hasSelection}>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label={i18n.en.actions.tableActions}
        >
          <MenuIcon size="20" color="currentColor" />
          <CircleIcon
            size="10"
            data-visible={hasSelection}
            className="absolute top-1.5 right-1.5 fill-red-400 text-red-400 opacity-0 transition-opacity data-[visible='true']:opacity-100"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          {i18n.en.selection} ({selectionCount})
        </DropdownMenuLabel>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default function ListDocuments({
  loading = false,
  documents,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: PropsWithChildren<{
  loading?: boolean;
  documents?: DocumentsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}>) {
  const { pagination, setPagination } = useSearchPagination({
    group: groupName,
    defaultPageSize,
    defaultPageIndex,
  });

  const { table } = useDataTable<DocumentType, DocumentsType>({
    data: documents?.items ?? [],
    rowCount: documents?.total,
    manualPagination: true,
    pagination,
    setPagination,
    columns: useMemo(
      () =>
        dataTableColumns<DocumentType, DocumentsType>([
          selectColumn as ColumnDef<DocumentType, DocumentsType>,
          {
            id: "preview",
            accessorKey: "preview",
            header: () => null,
            cell: ({ row }) => (
              <DocumentViewer
                document={{
                  // TODO: remove this once we have the actual data as columns in the table
                  type: "PDF",
                  url: "",
                  ...row.original,
                }}
              />
            ),
          },
          {
            id: "name",
            accessorKey: "name",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.name}
              />
            ),
            cell: ({ row }) => (
              <Link
                to={i18n.links.documents.replace("[id]", row.original.id)}
                className="h-fit"
              >
                <p className="font-semibold">{row.getValue("name")}</p>
              </Link>
            ),
            enableHiding: false,
          },
          {
            id: "type",
            accessorKey: "type",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.type}
              />
            ),
            cell: ({ row }) => <p>{row.getValue("type")}</p>,
          },
          // {
          //   id: "organization",
          //   accessorKey: "organization.name",
          //   header: ({ column }) => (
          //     <DataTableColumnHeader
          //       column={column}
          //       title={i18n.en.headers.organization}
          //     />
          //   ),
          //   cell: ({ row }) => (
          //     <Link
          //       to={i18n.links.organizations.replace(
          //         "[id]",
          //         row.original.organization?.id ?? "",
          //       )}
          //       className="transition-colors hover:text-primary"
          //     >
          //       <PreviewOrganization
          //         size="sm"
          //         organization={row.original.organization}
          //       />
          //     </Link>
          //   ),
          //   filterFn: (row, id, value: string) => {
          //     return value.includes(row.original.organization?.id ?? "");
          //   },
          // },
          {
            id: "actions",
            meta: {
              className: "w-[32px]",
            },
            header: ({ table }) => (
              <div className="flex size-full items-center justify-end">
                <DocumentsTableActions table={table} />
              </div>
            ),
            // cell: ({ row }) => (
            //   <div className="flex size-full items-center justify-end">
            //     <DocumentMenu document={row.original} variant="ghost" />
            //   </div>
            // ),
          },
        ]),
      [],
    ),
  });

  return (
    <Card>
      <CardHeader className="border-b p-2">
        <div className="flex items-center justify-between">
          <div className="border-r pr-2">
            <SearchFilter name={groupName} groups={filterGroups} />
          </div>

          <div className="flex h-fit flex-1 gap-2 overflow-scroll p-1 px-2">
            <SearchText
              name={groupName}
              group={groupName}
              loading={loading}
              placeholder={i18n.en.actions.search}
            />
            {filters}
          </div>

          <div className="border-l pl-2">
            <DataTableSettings
              table={table}
              variant="ghost"
              size="icon"
              className="ml-auto"
              aria-label={i18n.en.actions.tableSettings}
            >
              <Settings2Icon size="20" color="currentColor" />
            </DataTableSettings>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <DataTable loading={loading} table={table}>
          <EmptyList title={i18n.en.noDocument}>{children}</EmptyList>
        </DataTable>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 border-t pt-6">
        <DataTableSimplePagination table={table} />
      </CardFooter>
    </Card>
  );
}
