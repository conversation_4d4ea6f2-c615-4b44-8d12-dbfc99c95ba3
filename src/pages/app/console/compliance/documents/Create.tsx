import { useNavigate } from "react-router";

import { But<PERSON> } from "@/components/ui/button";

export default function CreateDocumentPage() {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Upload Document</h1>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/documents")}
        >
          Back to Documents
        </Button>
      </div>
      <div className="flex min-h-[400px] items-center justify-center">
        <span className="text-muted-foreground">
          Document upload form coming soon
        </span>
      </div>
    </div>
  );
}
