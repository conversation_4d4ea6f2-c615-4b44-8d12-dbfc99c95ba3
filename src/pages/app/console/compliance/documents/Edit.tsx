import { Link, useParams } from "react-router";

import { useGetDocument } from "@/api/documents/use-get-document";
import { Button } from "@/components/ui/button";

const i18n = {
  en: {
    title: "Edit Document",
    backButton: "Back to Documents",
  },
  links: {
    list: "/app/console/documents",
  },
};

export function EditDocumentView({
  loading,
  error,
  document,
}: {
  loading?: boolean;
  error?: Error;
  document: ReturnType<typeof useGetDocument>["data"];
}) {
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!document) return <div>Document not found</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        <Button variant="outline" asChild>
          <Link to={i18n.links.list}>{i18n.en.backButton}</Link>
        </Button>
      </div>
      <div className="flex min-h-[400px] items-center justify-center">
        <span className="text-muted-foreground">
          Document edit form coming soon
        </span>
      </div>
    </div>
  );
}

export default function DocumentEdit() {
  const { id } = useParams();
  const documentQuery = useGetDocument(id);

  return (
    <EditDocumentView
      loading={documentQuery.isLoading}
      error={documentQuery.error}
      document={documentQuery.data}
    />
  );
}
