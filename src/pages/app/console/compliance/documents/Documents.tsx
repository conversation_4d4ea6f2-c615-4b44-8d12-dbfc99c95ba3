import { useState } from "react";
import { FileText } from "lucide-react";

import { useListDocuments } from "@/api/documents/use-list-documents";
import CreateDocumentDialog from "@/components/documents/CreateDocumentDialog";
import { useSearchFilterValue } from "@/components/search/filter";
import { useSearchTextValue } from "@/components/search/text";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListDocuments from "@/pages/app/console/compliance/documents/ListDocuments";
import { Enums } from "@/supabase/types";

type DocumentType = Enums<"document_type">;

const i18n = {
  en: {
    title: "Documents",
    createButton: "Create Document",
  },
};

export function DocumentsView({
  loading,
  error,
  documents,
}: {
  loading?: boolean;
  error?: Error;
  documents?: ReturnType<typeof useListDocuments>["data"];
}) {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <CreateDocumentDialog>
          <Button disabled={loading}>
            <FileText className="mr-2 h-4 w-4" />
            {i18n.en.createButton}
          </Button>
        </CreateDocumentDialog>
      </div>

      {error && <ErrorAlert error={error} />}

      <ListDocuments loading={loading} documents={documents} />
    </div>
  );
}

export default function DocumentsPage() {
  const [pageIndex, setPageIndex] = useState(0);
  const documentsQuery = useSearchTextValue("document");
  const documentType = useSearchFilterValue<DocumentType>("type", "document");
  const { data: documents } = useListDocuments({
    pageIndex,
    pageSize: 10,
    search: documentsQuery,
    type: documentType,
  });

  return <DocumentsView documents={documents} />;
}
