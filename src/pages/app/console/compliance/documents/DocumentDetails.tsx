import { FileText } from "lucide-react";
import { Link, useParams } from "react-router";

import { useGetDocument } from "@/api/documents/use-get-document";
import DocumentPreviewPane from "@/components/documents/DocumentPreviewPane";
import { Button } from "@/components/ui/button";

const i18n = {
  en: {
    title: "Document Details",
    editButton: "Edit Document",
    backButton: "Back to Documents",
  },
  links: {
    edit: "/app/console/documents/:id/edit",
    list: "/app/console/documents",
  },
};

export function DocumentDetailsView({
  loading,
  error,
  document,
}: {
  loading?: boolean;
  error?: Error;
  document: ReturnType<typeof useGetDocument>["data"];
}) {
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!document) return <div>Document not found</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <p>
              {i18n.en.title} #{document.id}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to={i18n.links.edit.replace(":id", document.id)}>
              {i18n.en.editButton}
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to={i18n.links.list}>{i18n.en.backButton}</Link>
          </Button>
        </div>
      </div>
      <div className="flex min-h-[400px]">
        {document.content_type ? (
          <DocumentPreviewPane
            document={{
              name: document.name,
              url: document.url,
              content_type: document.content_type,
            }}
          />
        ) : (
          <div className="flex w-full items-center justify-center">
            <span className="text-muted-foreground">{document.name}</span>
          </div>
        )}
      </div>
    </div>
  );
}

export default function DocumentDetails() {
  const { id } = useParams();
  const documentQuery = useGetDocument(id);

  return (
    <DocumentDetailsView
      loading={documentQuery.isLoading}
      error={documentQuery.error}
      document={documentQuery.data}
    />
  );
}
