import { <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON> } from "react-router";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function CreateContractPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link to="/app/console/contracts">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Create Contract</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Contract Information</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Contract creation form will be implemented here.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
