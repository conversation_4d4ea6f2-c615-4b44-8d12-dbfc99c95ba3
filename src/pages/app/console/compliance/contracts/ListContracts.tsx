import { PropsWithChildren } from "react";
import { <PERSON> } from "react-router";

import TimeAgo from "@/components/shared/TimeAgo";
import ListTable from "@/components/tables/ListTable";
import { Badge } from "@/components/ui/badge";

type FilterOption = {
  value: string | undefined;
  label: string;
};

type FilterGroup = {
  id: string;
  label: string;
  options: FilterOption[];
};

// Define the contract type to fix type issues
interface Contract {
  id: string;
  name: string;
  type: string;
  status: string;
  created_at: string;
  organization?: string;
}

const i18n = {
  en: {
    noContract: "There are no contracts yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search contracts...",
    },
    headers: {
      id: "ID",
      name: "Name",
      type: "Type",
      status: "Status",
      organization: "Organization",
      created_at: "Created At",
      actions: "Actions",
    },
    filters: {
      type: {
        label: "Type",
        all: "All Types",
        service: "Service",
        employment: "Employment",
        partnership: "Partnership",
        vendor: "Vendor",
        customer: "Customer",
        consulting: "Consulting",
        other: "Other",
      },
      status: {
        label: "Status",
        all: "All Statuses",
        draft: "Draft",
        pending: "Pending",
        active: "Active",
        expired: "Expired",
        terminated: "Terminated",
      },
    },
  },
};

// Define the links separately as they're not part of the i18n structure expected by ListTable
const tableLinks = {
  contracts: "/app/console/contracts/[id]",
};

const groupName = "contract";

const filterGroups: FilterGroup[] = [
  {
    id: "type",
    label: i18n.en.filters.type.label,
    options: [
      { value: undefined, label: i18n.en.filters.type.all },
      { value: "service", label: i18n.en.filters.type.service },
      { value: "employment", label: i18n.en.filters.type.employment },
      { value: "partnership", label: i18n.en.filters.type.partnership },
      { value: "vendor", label: i18n.en.filters.type.vendor },
      { value: "customer", label: i18n.en.filters.type.customer },
      { value: "consulting", label: i18n.en.filters.type.consulting },
      { value: "other", label: i18n.en.filters.type.other },
    ],
  },
  {
    id: "status",
    label: i18n.en.filters.status.label,
    options: [
      { value: undefined, label: i18n.en.filters.status.all },
      { value: "draft", label: i18n.en.filters.status.draft },
      { value: "pending", label: i18n.en.filters.status.pending },
      { value: "active", label: i18n.en.filters.status.active },
      { value: "expired", label: i18n.en.filters.status.expired },
      { value: "terminated", label: i18n.en.filters.status.terminated },
    ],
  },
];

export default function ListContracts({
  loading = false,
  contracts,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: PropsWithChildren<{
  loading?: boolean;
  contracts?: {
    items: Contract[];
    total: number;
  };
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}>) {
  // Ensure we provide default data structure when contracts is undefined
  const contractsData = contracts || { items: [], total: 0 };

  return (
    <ListTable
      loading={loading}
      data={contractsData}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      filterGroups={filterGroups}
      i18n={{
        emptyText: i18n.en.noContract,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      columns={({ i18n }) => [
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => <div>{i18n.headers?.id || "ID"}</div>,
          cell: ({ row }) => (
            <Link
              to={tableLinks.contracts.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "name",
          accessorKey: "name",
          header: ({ column }) => <div>{i18n.headers?.name || "Name"}</div>,
          cell: ({ row }) => <div>{row.getValue("name")}</div>,
        },
        {
          id: "type",
          accessorKey: "type",
          header: ({ column }) => <div>{i18n.headers?.type || "Type"}</div>,
          cell: ({ row }) => (
            <Badge variant="outline" className="capitalize">
              {row.getValue("type")}
            </Badge>
          ),
        },
        {
          id: "status",
          accessorKey: "status",
          header: ({ column }) => <div>{i18n.headers?.status || "Status"}</div>,
          cell: ({ row }) => {
            const status = row.getValue("status") as string;
            const getVariant = (
              status: string,
            ):
              | "default"
              | "destructive"
              | "outline"
              | "secondary"
              | "accent" => {
              switch (status) {
                case "active":
                  return "outline"; // Changed from "success" to "outline"
                case "pending":
                  return "secondary"; // Changed from "warning" to "secondary"
                case "expired":
                case "terminated":
                  return "destructive";
                default:
                  return "default";
              }
            };

            return (
              <Badge variant={getVariant(status)} className="capitalize">
                {status}
              </Badge>
            );
          },
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <div>{i18n.headers?.created_at || "Created At"}</div>
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
