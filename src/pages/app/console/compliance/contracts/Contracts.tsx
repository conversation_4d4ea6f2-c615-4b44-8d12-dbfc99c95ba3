import { useState } from "react";
import { FileText } from "lucide-react";
import { Link } from "react-router";

import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListContracts from "@/pages/app/console/compliance/contracts/ListContracts";

// import { useListContracts } from "@/api/contracts/use-list-contracts";
const i18n = {
  en: {
    title: "Contracts",
    addButton: "Create Contract",
  },
  links: {
    create: "/app/console/contracts/create",
  },
};

export function ContractsView({
  loading,
  error,
  contracts,
}: {
  loading?: boolean;
  error?: Error;
  contracts?: any;
  // contracts?: ReturnType<typeof useListContracts>["data"];
}) {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <Button asChild disabled={loading}>
          <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
        </Button>
      </div>

      {error && <ErrorAlert error={error} />}

      <ListContracts loading={loading} contracts={contracts} />
    </div>
  );
}

export default function ContractsPage() {
  const [loading, setLoading] = useState(false);

  // Mock data for testing the contracts table
  const mockContracts = {
    items: [
      {
        id: "cnt-001",
        name: "Service Agreement",
        type: "service",
        status: "active",
        created_at: "2023-06-01T12:00:00Z",
      },
      {
        id: "cnt-002",
        name: "Employment Contract",
        type: "employment",
        status: "pending",
        created_at: "2023-07-15T09:30:00Z",
      },
      {
        id: "cnt-003",
        name: "Vendor Agreement",
        type: "vendor",
        status: "expired",
        created_at: "2023-05-20T14:15:00Z",
      },
    ],
    total: 3,
  };

  return <ContractsView loading={loading} contracts={mockContracts} />;
}
