import { Helmet } from "react-helmet-async";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

function CompliancePage() {
  return (
    <>
      <Helmet>
        <title>Compliance | QuikSkope</title>
      </Helmet>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Compliance</h1>
        <div className="flex gap-2">
          <Button variant="outline">Export Report</Button>
          <Button>New Compliance Check</Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="regulations">Regulations</TabsTrigger>
          <TabsTrigger value="audits">Audits</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <p>View your organization's compliance status and key metrics.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="regulations">
          <Card>
            <CardHeader>
              <CardTitle>Regulatory Requirements</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                Track and manage regulatory requirements applicable to your
                operations.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="audits">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Audits</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                Schedule, track, and review compliance audits and inspections.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                Generate and review compliance reports for internal and external
                stakeholders.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </>
  );
}

export default CompliancePage;
