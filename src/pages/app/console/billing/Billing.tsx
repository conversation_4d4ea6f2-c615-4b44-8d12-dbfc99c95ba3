import { useUser } from "@/contexts/User";
import { BillingData, ConsoleBillingPage } from "./ConsoleBillingPage";

const organizationPlans = [
  {
    name: "Courier",
    price: "$99",
    period: "per month",
    description: "Perfect for small businesses just getting started",
    features: [
      "Unlimited Users",
      "Up to 100 verifications per month",
      "Real-time tracking",
      "AI Documentation",
      "Basic analytics dashboard",
      "Email support",
    ],
  },
  {
    name: "Professional",
    price: "$599",
    period: "per month",
    description: "Ideal for growing businesses with higher volume needs",
    features: [
      "Everything in Courier",
      "Up to 1,000 verifications per month",
      "Advanced analytics and reporting",
      "Priority email & phone support",
    ],
    highlighted: true,
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "contact us",
    description: "For large organizations with specific requirements",
    features: [
      "Everything in Professional",
      "Unlimited verifications",
      "Custom analytics solutions",
      "24/7 dedicated support",
      "Advanced security features",
      "SLA guarantees",
    ],
  },
];

const getPricePerVerification = (planName: string) => {
  switch (planName.toLowerCase()) {
    case "courier":
      return "$0.99";
    case "professional":
      return "$0.59";
    case "enterprise":
      return "Custom";
    default:
      return "N/A";
  }
};

const integrationAddOns = [
  {
    name: "Samsara Integration",
    price: "$199",
    period: "per month",
    description: "Connect your Samsara fleet management system",
    features: [
      "Real-time vehicle tracking",
      "Automated driver logs",
      "Maintenance scheduling",
      "Fuel usage monitoring",
      "Temperature monitoring",
      "Custom reporting",
    ],
  },
  {
    name: "KeepTruckin Integration",
    price: "$149",
    period: "per month",
    description: "Integrate with KeepTruckin's ELD and fleet management",
    features: [
      "Electronic logging device (ELD) sync",
      "Driver performance monitoring",
      "Vehicle inspection reports",
      "GPS tracking integration",
      "Hours of service compliance",
    ],
  },
];

const ConsoleBilling = () => {
  // User and membership data from context
  const { memberships, isLoading: isLoadingUser } = useUser();

  // TODO: Add these hooks when API endpoints are available
  // const {
  //   data: subscriptionData,
  //   isLoading: isLoadingSubscription,
  //   error: subscriptionError,
  // } = useSubscription();

  // const {
  //   data: paymentMethods,
  //   isLoading: isLoadingPaymentMethods,
  //   error: paymentMethodsError,
  // } = usePaymentMethods();

  // const {
  //   data: billingHistory,
  //   isLoading: isLoadingBillingHistory,
  //   error: billingHistoryError,
  // } = useBillingHistory();

  // Simulate loading states for now
  const isLoadingSubscription = false;
  const subscriptionError = null;
  const isLoadingPaymentMethods = false;
  const paymentMethodsError = null;
  const isLoadingBillingHistory = false;
  const billingHistoryError = null;

  // Get current plan from membership data
  const currentOrganization = memberships?.[0]?.organization;
  const currentPlan = currentOrganization ? "Courier" : "No Plan"; // This should be fetched from the organization's data

  // Build billing data configuration
  const billingData: BillingData = {
    currentPlan,
    pricePerVerification: getPricePerVerification(currentPlan),
    organizationPlans,
    integrationAddOns,
  };

  // Mock subscription data - replace with real API data
  const mockSubscriptionData = currentOrganization
    ? {
        status: "active" as const,
        current_period_start: new Date(
          Date.now() - 7 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        current_period_end: new Date(
          Date.now() + 23 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        cancel_at_period_end: false,
        usage: {
          verifications_used: 87,
          verifications_limit: 100,
          overage_charges: 0,
        },
      }
    : null;

  // Mock payment methods - replace with real API data
  const mockPaymentMethods = currentOrganization
    ? [
        {
          id: "pm_1",
          type: "card",
          last4: "4242",
          brand: "visa",
          exp_month: 12,
          exp_year: 2025,
          is_default: true,
        },
      ]
    : null;

  // Mock billing history - replace with real API data
  const mockBillingHistory = currentOrganization
    ? [
        {
          id: "inv_1",
          amount: 9900, // $99.00 in cents
          currency: "usd",
          status: "paid",
          created_at: new Date(
            Date.now() - 30 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          description: "Courier Plan - Monthly Subscription",
          invoice_url: "https://invoice.stripe.com/example",
        },
        {
          id: "inv_2",
          amount: 9900,
          currency: "usd",
          status: "paid",
          created_at: new Date(
            Date.now() - 60 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          description: "Courier Plan - Monthly Subscription",
          invoice_url: "https://invoice.stripe.com/example2",
        },
      ]
    : null;

  // Action handlers - replace with real implementations
  const handleUpgradePlan = (planName: string) => {
    console.log("Upgrade to plan:", planName);
    // TODO: Implement plan upgrade logic
  };

  const handleDowngradePlan = (planName: string) => {
    console.log("Downgrade to plan:", planName);
    // TODO: Implement plan downgrade logic
  };

  const handleAddPaymentMethod = () => {
    console.log("Add payment method");
    // TODO: Implement add payment method logic
  };

  const handleRemovePaymentMethod = (paymentMethodId: string) => {
    console.log("Remove payment method:", paymentMethodId);
    // TODO: Implement remove payment method logic
  };

  const handleUpdateBillingInfo = () => {
    console.log("Update billing info");
    // TODO: Implement update billing info logic
  };

  const handleCancelSubscription = () => {
    console.log("Cancel subscription");
    // TODO: Implement cancel subscription logic
  };

  const handleViewInvoice = (invoiceUrl: string) => {
    window.open(invoiceUrl, "_blank");
  };

  const handleContactSales = () => {
    console.log("Contact sales");
    // TODO: Implement contact sales logic
  };

  // Navigation handlers
  const handleNavigateToPayments = () => {
    console.log("Navigate to payments");
    // TODO: Implement navigation to payments page
  };

  const handleNavigateToPayroll = () => {
    console.log("Navigate to payroll");
    // TODO: Implement navigation to payroll page
  };

  const handleNavigateToUsage = () => {
    console.log("Navigate to usage analytics");
    // TODO: Implement navigation to usage analytics page
  };

  return (
    <ConsoleBillingPage
      // User and membership data
      memberships={memberships || null}
      isLoadingUser={isLoadingUser}
      userError={null}
      // Billing configuration data
      billingData={billingData}
      isLoadingBilling={false}
      billingError={null}
      // Subscription and usage data
      subscriptionData={mockSubscriptionData}
      isLoadingSubscription={isLoadingSubscription}
      subscriptionError={subscriptionError}
      // Payment methods and billing history
      paymentMethods={mockPaymentMethods}
      isLoadingPaymentMethods={isLoadingPaymentMethods}
      paymentMethodsError={paymentMethodsError}
      billingHistory={mockBillingHistory}
      isLoadingBillingHistory={isLoadingBillingHistory}
      billingHistoryError={billingHistoryError}
      // Action handlers
      onUpgradePlan={handleUpgradePlan}
      onDowngradePlan={handleDowngradePlan}
      onAddPaymentMethod={handleAddPaymentMethod}
      onRemovePaymentMethod={handleRemovePaymentMethod}
      onUpdateBillingInfo={handleUpdateBillingInfo}
      onCancelSubscription={handleCancelSubscription}
      onViewInvoice={handleViewInvoice}
      onContactSales={handleContactSales}
      // Navigation handlers
      onNavigateToPayments={handleNavigateToPayments}
      onNavigateToPayroll={handleNavigateToPayroll}
      onNavigateToUsage={handleNavigateToUsage}
    />
  );
};

export default ConsoleBilling;
