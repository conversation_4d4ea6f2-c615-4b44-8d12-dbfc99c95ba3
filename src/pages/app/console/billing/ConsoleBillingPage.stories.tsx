import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleBillingPage } from "./ConsoleBillingPage";

const meta: Meta<typeof ConsoleBillingPage> = {
  title: "Pages/Console/Billing",
  component: ConsoleBillingPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/billing" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockOrganizationPlans = [
  {
    name: "Courier",
    price: "$99",
    period: "per month",
    description: "Perfect for small businesses just getting started",
    features: [
      "Unlimited Users",
      "Up to 100 verifications per month",
      "Real-time tracking",
      "AI Documentation",
      "Basic analytics dashboard",
      "Email support",
    ],
  },
  {
    name: "Professional",
    price: "$599",
    period: "per month",
    description: "Ideal for growing businesses with higher volume needs",
    features: [
      "Everything in Courier",
      "Up to 1,000 verifications per month",
      "Advanced analytics and reporting",
      "Priority email & phone support",
    ],
    highlighted: true,
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "contact us",
    description: "For large organizations with specific requirements",
    features: [
      "Everything in Professional",
      "Unlimited verifications",
      "Custom analytics solutions",
      "24/7 dedicated support",
      "Advanced security features",
      "SLA guarantees",
    ],
  },
];

const mockIntegrationAddOns = [
  {
    name: "Samsara Integration",
    price: "$199",
    period: "per month",
    description: "Connect your Samsara fleet management system",
    features: [
      "Real-time vehicle tracking",
      "Automated driver logs",
      "Maintenance scheduling",
      "Fuel usage monitoring",
      "Temperature monitoring",
      "Custom reporting",
    ],
  },
  {
    name: "KeepTruckin Integration",
    price: "$149",
    period: "per month",
    description: "Integrate with KeepTruckin's ELD and fleet management",
    features: [
      "Electronic logging device (ELD) sync",
      "Driver performance monitoring",
      "Vehicle inspection reports",
      "GPS tracking integration",
      "Hours of service compliance",
    ],
  },
];

const mockBillingData = {
  currentPlan: "Courier",
  pricePerVerification: "$0.99",
  organizationPlans: mockOrganizationPlans,
  integrationAddOns: mockIntegrationAddOns,
};

const mockMemberships = [
  {
    organization: {
      id: "org-123",
      name: "QuikSkope Logistics",
      plan: "courier",
      subscription_status: "active",
      billing_cycle: "monthly",
      current_usage: {
        verifications: 87,
        limit: 100,
      },
    },
  },
];

const mockPaymentMethods = [
  {
    id: "pm_1",
    type: "card",
    last4: "4242",
    brand: "visa",
    exp_month: 12,
    exp_year: 2025,
    is_default: true,
  },
  {
    id: "pm_2",
    type: "card",
    last4: "1234",
    brand: "mastercard",
    exp_month: 8,
    exp_year: 2026,
    is_default: false,
  },
];

const mockBillingHistory = [
  {
    id: "inv_1",
    amount: 9900,
    currency: "usd",
    status: "paid",
    created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    description: "Courier Plan - Monthly Subscription",
    invoice_url: "https://invoice.stripe.com/example1",
  },
  {
    id: "inv_2",
    amount: 9900,
    currency: "usd",
    status: "paid",
    created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    description: "Courier Plan - Monthly Subscription",
    invoice_url: "https://invoice.stripe.com/example2",
  },
  {
    id: "inv_3",
    amount: 9900,
    currency: "usd",
    status: "paid",
    created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
    description: "Courier Plan - Monthly Subscription",
    invoice_url: "https://invoice.stripe.com/example3",
  },
];

export const Default: Story = {
  args: {
    memberships: mockMemberships,
    isLoadingUser: false,
    userError: null,
    billingData: mockBillingData,
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: {
      status: "active",
      current_period_start: new Date(
        Date.now() - 7 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      current_period_end: new Date(
        Date.now() + 23 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      cancel_at_period_end: false,
      usage: {
        verifications_used: 87,
        verifications_limit: 100,
        overage_charges: 0,
      },
    },
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: mockBillingHistory,
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const Loading: Story = {
  args: {
    memberships: null,
    isLoadingUser: true,
    userError: null,
    billingData: mockBillingData,
    isLoadingBilling: true,
    billingError: null,
    subscriptionData: null,
    isLoadingSubscription: true,
    subscriptionError: null,
    paymentMethods: null,
    isLoadingPaymentMethods: true,
    paymentMethodsError: null,
    billingHistory: null,
    isLoadingBillingHistory: true,
    billingHistoryError: null,
  },
};

export const FreeTrialUser: Story = {
  args: {
    memberships: [
      {
        organization: {
          id: "org-trial",
          name: "Trial Organization",
        },
      },
    ],
    isLoadingUser: false,
    userError: null,
    billingData: {
      currentPlan: "No Plan",
      pricePerVerification: "N/A",
      organizationPlans: mockOrganizationPlans,
      integrationAddOns: mockIntegrationAddOns,
    },
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: null,
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: null,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: null,
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
  },
};

export const PaidSubscription: Story = {
  args: {
    memberships: [
      {
        organization: {
          id: "org-pro",
          name: "Professional Logistics Corp",
          plan: "professional",
          subscription_status: "active",
          billing_cycle: "monthly",
        },
      },
    ],
    isLoadingUser: false,
    userError: null,
    billingData: {
      currentPlan: "Professional",
      pricePerVerification: "$0.59",
      organizationPlans: mockOrganizationPlans,
      integrationAddOns: mockIntegrationAddOns,
    },
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: {
      status: "active",
      current_period_start: new Date(
        Date.now() - 15 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      current_period_end: new Date(
        Date.now() + 15 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      cancel_at_period_end: false,
      usage: {
        verifications_used: 456,
        verifications_limit: 1000,
        overage_charges: 0,
      },
    },
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: [
      {
        id: "inv_pro_1",
        amount: 59900,
        currency: "usd",
        status: "paid",
        created_at: new Date(
          Date.now() - 30 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "Professional Plan - Monthly Subscription",
        invoice_url: "https://invoice.stripe.com/pro1",
      },
      ...mockBillingHistory,
    ],
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const OverageWarning: Story = {
  args: {
    memberships: mockMemberships,
    isLoadingUser: false,
    userError: null,
    billingData: mockBillingData,
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: {
      status: "active",
      current_period_start: new Date(
        Date.now() - 20 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      current_period_end: new Date(
        Date.now() + 10 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      cancel_at_period_end: false,
      usage: {
        verifications_used: 127,
        verifications_limit: 100,
        overage_charges: 26.73,
      },
    },
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: [
      {
        id: "inv_overage_1",
        amount: 12573,
        currency: "usd",
        status: "paid",
        created_at: new Date(
          Date.now() - 30 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "Courier Plan + Overage Charges",
        invoice_url: "https://invoice.stripe.com/overage1",
      },
      ...mockBillingHistory,
    ],
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const PaymentIssue: Story = {
  args: {
    memberships: mockMemberships,
    isLoadingUser: false,
    userError: null,
    billingData: mockBillingData,
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: {
      status: "past_due",
      current_period_start: new Date(
        Date.now() - 35 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      current_period_end: new Date(
        Date.now() - 5 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      cancel_at_period_end: true,
      usage: {
        verifications_used: 45,
        verifications_limit: 100,
        overage_charges: 0,
      },
    },
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: [
      {
        id: "pm_expired",
        type: "card",
        last4: "4242",
        brand: "visa",
        exp_month: 11,
        exp_year: 2023,
        is_default: true,
      },
    ],
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: [
      {
        id: "inv_failed",
        amount: 9900,
        currency: "usd",
        status: "payment_failed",
        created_at: new Date(
          Date.now() - 5 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "Courier Plan - Monthly Subscription",
        invoice_url: "https://invoice.stripe.com/failed",
      },
      ...mockBillingHistory,
    ],
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const UpgradeFlow: Story = {
  args: {
    memberships: mockMemberships,
    isLoadingUser: false,
    userError: null,
    billingData: mockBillingData,
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: {
      status: "active",
      current_period_start: new Date(
        Date.now() - 25 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      current_period_end: new Date(
        Date.now() + 5 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      cancel_at_period_end: false,
      usage: {
        verifications_used: 95,
        verifications_limit: 100,
        overage_charges: 0,
      },
    },
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: mockBillingHistory,
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const UsageAnalytics: Story = {
  args: {
    memberships: [
      {
        organization: {
          id: "org-analytics",
          name: "Analytics Heavy User",
          plan: "professional",
          subscription_status: "active",
          billing_cycle: "monthly",
        },
      },
    ],
    isLoadingUser: false,
    userError: null,
    billingData: {
      currentPlan: "Professional",
      pricePerVerification: "$0.59",
      organizationPlans: mockOrganizationPlans,
      integrationAddOns: mockIntegrationAddOns,
    },
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: {
      status: "active",
      current_period_start: new Date(
        Date.now() - 10 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      current_period_end: new Date(
        Date.now() + 20 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      cancel_at_period_end: false,
      usage: {
        verifications_used: 234,
        verifications_limit: 1000,
        overage_charges: 0,
      },
    },
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: mockPaymentMethods,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: [
      {
        id: "inv_analytics_1",
        amount: 59900,
        currency: "usd",
        status: "paid",
        created_at: new Date(
          Date.now() - 30 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "Professional Plan - Monthly Subscription",
        invoice_url: "https://invoice.stripe.com/analytics1",
      },
      {
        id: "inv_analytics_2",
        amount: 19900,
        currency: "usd",
        status: "paid",
        created_at: new Date(
          Date.now() - 30 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "Samsara Integration Add-on",
        invoice_url: "https://invoice.stripe.com/analytics2",
      },
      ...mockBillingHistory,
    ],
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const NewOrganization: Story = {
  args: {
    memberships: [
      {
        organization: {
          id: "org-new",
          name: "New Startup Logistics",
        },
      },
    ],
    isLoadingUser: false,
    userError: null,
    billingData: {
      currentPlan: "No Plan",
      pricePerVerification: "N/A",
      organizationPlans: mockOrganizationPlans,
      integrationAddOns: mockIntegrationAddOns,
    },
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: null,
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: null,
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: null,
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const EnterpriseUser: Story = {
  args: {
    memberships: [
      {
        organization: {
          id: "org-enterprise",
          name: "Enterprise Transport Solutions",
          plan: "enterprise",
          subscription_status: "active",
          billing_cycle: "yearly",
        },
      },
    ],
    isLoadingUser: false,
    userError: null,
    billingData: {
      currentPlan: "Enterprise",
      pricePerVerification: "Custom",
      organizationPlans: mockOrganizationPlans,
      integrationAddOns: mockIntegrationAddOns,
    },
    isLoadingBilling: false,
    billingError: null,
    subscriptionData: {
      status: "active",
      current_period_start: new Date(
        Date.now() - 60 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      current_period_end: new Date(
        Date.now() + 305 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      cancel_at_period_end: false,
      usage: {
        verifications_used: 12847,
        verifications_limit: 999999,
        overage_charges: 0,
      },
    },
    isLoadingSubscription: false,
    subscriptionError: null,
    paymentMethods: [
      {
        id: "pm_enterprise",
        type: "ach",
        last4: "9876",
        brand: "bank_transfer",
        exp_month: 12,
        exp_year: 2025,
        is_default: true,
      },
    ],
    isLoadingPaymentMethods: false,
    paymentMethodsError: null,
    billingHistory: [
      {
        id: "inv_enterprise_1",
        amount: 5000000,
        currency: "usd",
        status: "paid",
        created_at: new Date(
          Date.now() - 60 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "Enterprise Plan - Annual Subscription",
        invoice_url: "https://invoice.stripe.com/enterprise1",
      },
      {
        id: "inv_enterprise_2",
        amount: 199000,
        currency: "usd",
        status: "paid",
        created_at: new Date(
          Date.now() - 60 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "Samsara Integration - Annual",
        invoice_url: "https://invoice.stripe.com/enterprise2",
      },
      {
        id: "inv_enterprise_3",
        amount: 149000,
        currency: "usd",
        status: "paid",
        created_at: new Date(
          Date.now() - 60 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        description: "KeepTruckin Integration - Annual",
        invoice_url: "https://invoice.stripe.com/enterprise3",
      },
    ],
    isLoadingBillingHistory: false,
    billingHistoryError: null,
    onUpgradePlan: fn(),
    onDowngradePlan: fn(),
    onAddPaymentMethod: fn(),
    onRemovePaymentMethod: fn(),
    onUpdateBillingInfo: fn(),
    onCancelSubscription: fn(),
    onViewInvoice: fn(),
    onContactSales: fn(),
    onNavigateToPayments: fn(),
    onNavigateToPayroll: fn(),
    onNavigateToUsage: fn(),
  },
};

export const ErrorState: Story = {
  args: {
    memberships: null,
    isLoadingUser: false,
    userError: new Error("Failed to load user data"),
    billingData: mockBillingData,
    isLoadingBilling: false,
    billingError: new Error("Billing service unavailable"),
    subscriptionData: null,
    isLoadingSubscription: false,
    subscriptionError: new Error("Unable to fetch subscription"),
    paymentMethods: null,
    isLoadingPaymentMethods: false,
    paymentMethodsError: new Error("Payment methods service error"),
    billingHistory: null,
    isLoadingBillingHistory: false,
    billingHistoryError: new Error("Billing history unavailable"),
  },
};
