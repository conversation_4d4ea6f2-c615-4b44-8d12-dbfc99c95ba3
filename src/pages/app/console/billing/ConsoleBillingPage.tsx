import { BadgeDollarSign, CreditCard } from "lucide-react";

import AddOnsSection from "@/components/common/pricing/AddOnsSection";
import PricingSection from "@/components/common/pricing/PricingSection";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";

export interface OrganizationPlan {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  highlighted?: boolean;
}

export interface IntegrationAddOn {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
}

export interface BillingData {
  currentPlan: string;
  pricePerVerification: string;
  organizationPlans: OrganizationPlan[];
  integrationAddOns: IntegrationAddOn[];
}

export interface UserMembership {
  organization?: {
    id: string;
    name: string;
    plan?: string;
    subscription_status?: string;
    billing_cycle?: string;
    current_usage?: {
      verifications: number;
      limit: number;
    };
  };
}

export interface ConsoleBillingPageProps {
  // User and membership data
  memberships: UserMembership[] | null;
  isLoadingUser: boolean;
  userError: Error | null;

  // Billing configuration data
  billingData: BillingData;
  isLoadingBilling: boolean;
  billingError: Error | null;

  // Subscription and usage data
  subscriptionData?: {
    status: string;
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    usage: {
      verifications_used: number;
      verifications_limit: number;
      overage_charges?: number;
    };
  } | null;
  isLoadingSubscription: boolean;
  subscriptionError: Error | null;

  // Payment methods and billing history
  paymentMethods?: Array<{
    id: string;
    type: string;
    last4: string;
    brand: string;
    exp_month: number;
    exp_year: number;
    is_default: boolean;
  }> | null;
  isLoadingPaymentMethods: boolean;
  paymentMethodsError: Error | null;

  // Billing history
  billingHistory?: Array<{
    id: string;
    amount: number;
    currency: string;
    status: string;
    created_at: string;
    description: string;
    invoice_url?: string;
  }> | null;
  isLoadingBillingHistory: boolean;
  billingHistoryError: Error | null;

  // Action handlers
  onUpgradePlan?: (planName: string) => void;
  onDowngradePlan?: (planName: string) => void;
  onAddPaymentMethod?: () => void;
  onRemovePaymentMethod?: (paymentMethodId: string) => void;
  onUpdateBillingInfo?: () => void;
  onCancelSubscription?: () => void;
  onViewInvoice?: (invoiceUrl: string) => void;
  onContactSales?: () => void;

  // Navigation handlers
  onNavigateToPayments?: () => void;
  onNavigateToPayroll?: () => void;
  onNavigateToUsage?: () => void;
}

export const ConsoleBillingPage = ({
  memberships,
  isLoadingUser,
  userError,
  billingData,
  isLoadingBilling,
  billingError,
  subscriptionData,
  isLoadingSubscription,
  subscriptionError,
  paymentMethods,
  isLoadingPaymentMethods,
  paymentMethodsError,
  billingHistory,
  isLoadingBillingHistory,
  billingHistoryError,
  onUpgradePlan,
  onDowngradePlan,
  onAddPaymentMethod,
  onRemovePaymentMethod,
  onUpdateBillingInfo,
  onCancelSubscription,
  onViewInvoice,
  onContactSales,
  onNavigateToPayments,
  onNavigateToPayroll,
  onNavigateToUsage,
}: ConsoleBillingPageProps) => {
  if (isLoadingUser || isLoadingBilling) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="text-center">
          <div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-t-transparent" />
          <p className="text-muted-foreground">
            Loading billing information...
          </p>
        </div>
      </div>
    );
  }

  if (userError || billingError) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-2">
            Error loading billing information
          </p>
          <p className="text-muted-foreground text-sm">
            {userError?.message || billingError?.message}
          </p>
        </div>
      </div>
    );
  }

  const currentOrganization = memberships?.[0]?.organization;
  const hasActiveSubscription = subscriptionData?.status === "active";
  const isTrialUser = !hasActiveSubscription && !currentOrganization?.plan;
  const usagePercentage = subscriptionData?.usage
    ? (subscriptionData.usage.verifications_used /
        subscriptionData.usage.verifications_limit) *
      100
    : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CreditCard className="h-8 w-8" />
          <h1 className="text-3xl font-bold tracking-tight">Billing</h1>
        </div>
        {onNavigateToPayments && (
          <div className="flex gap-2">
            <button
              onClick={onNavigateToPayments}
              className="text-muted-foreground hover:text-foreground px-4 py-2 text-sm font-medium transition-colors"
            >
              Payment Methods
            </button>
            {onNavigateToPayroll && (
              <button
                onClick={onNavigateToPayroll}
                className="text-muted-foreground hover:text-foreground px-4 py-2 text-sm font-medium transition-colors"
              >
                Payroll
              </button>
            )}
            {onNavigateToUsage && (
              <button
                onClick={onNavigateToUsage}
                className="text-muted-foreground hover:text-foreground px-4 py-2 text-sm font-medium transition-colors"
              >
                Usage Analytics
              </button>
            )}
          </div>
        )}
      </div>

      {/* Current Plan Status */}
      <Card className="bg-accent/10 p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-xl font-semibold">
              Current Plan: {billingData.currentPlan}
            </h2>
            <p className="text-muted-foreground">
              Price per verification: {billingData.pricePerVerification}
            </p>
            {subscriptionData?.usage && (
              <div className="mt-2">
                <div className="text-muted-foreground flex items-center gap-2 text-sm">
                  <span>
                    {subscriptionData.usage.verifications_used} /{" "}
                    {subscriptionData.usage.verifications_limit} verifications
                    used
                  </span>
                  <span className="text-xs">
                    ({usagePercentage.toFixed(1)}%)
                  </span>
                </div>
                <div className="bg-secondary mt-1 h-2 w-full rounded-full">
                  <div
                    className={`h-2 rounded-full transition-all ${
                      usagePercentage > 90
                        ? "bg-destructive"
                        : usagePercentage > 75
                          ? "bg-warning"
                          : "bg-primary"
                    }`}
                    style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                  />
                </div>
              </div>
            )}
          </div>
          <div className="flex items-center gap-3">
            {subscriptionData?.usage?.overage_charges &&
              subscriptionData.usage.overage_charges > 0 && (
                <Badge variant="destructive" className="px-3 py-1">
                  Overage: ${subscriptionData.usage.overage_charges}
                </Badge>
              )}
            <Badge
              variant={
                hasActiveSubscription
                  ? "default"
                  : isTrialUser
                    ? "secondary"
                    : "outline"
              }
              className="px-4 py-1 text-lg"
            >
              <BadgeDollarSign className="mr-1 h-5 w-5" />
              {hasActiveSubscription
                ? "Active"
                : isTrialUser
                  ? "Trial"
                  : "Inactive"}
            </Badge>
          </div>
        </div>

        {/* Subscription Details */}
        {subscriptionData && (
          <div className="border-border/50 mt-4 border-t pt-4">
            <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
              <div>
                <span className="text-muted-foreground">Billing Cycle:</span>
                <p className="font-medium">
                  {new Date(
                    subscriptionData.current_period_start,
                  ).toLocaleDateString()}{" "}
                  -{" "}
                  {new Date(
                    subscriptionData.current_period_end,
                  ).toLocaleDateString()}
                </p>
              </div>
              <div>
                <span className="text-muted-foreground">Status:</span>
                <p className="font-medium capitalize">
                  {subscriptionData.status}
                </p>
              </div>
              <div>
                <span className="text-muted-foreground">Auto-renewal:</span>
                <p className="font-medium">
                  {subscriptionData.cancel_at_period_end
                    ? "Canceled"
                    : "Active"}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-4 flex gap-2">
          {onUpdateBillingInfo && (
            <button
              onClick={onUpdateBillingInfo}
              className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium transition-colors"
            >
              Update Billing Info
            </button>
          )}
          {onCancelSubscription && hasActiveSubscription && (
            <button
              onClick={onCancelSubscription}
              className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground rounded-md border px-4 py-2 text-sm font-medium transition-colors"
            >
              Cancel Subscription
            </button>
          )}
          {onContactSales && (
            <button
              onClick={onContactSales}
              className="border-border hover:bg-accent rounded-md border px-4 py-2 text-sm font-medium transition-colors"
            >
              Contact Sales
            </button>
          )}
        </div>
      </Card>

      {/* Usage Warning */}
      {subscriptionData?.usage && usagePercentage > 75 && (
        <Card
          className={`p-4 ${usagePercentage > 90 ? "border-destructive bg-destructive/5" : "border-warning bg-warning/5"}`}
        >
          <div className="flex items-center gap-3">
            <div
              className={`h-2 w-2 rounded-full ${usagePercentage > 90 ? "bg-destructive" : "bg-warning"}`}
            />
            <div>
              <p className="font-medium">
                {usagePercentage > 90
                  ? "Usage Limit Exceeded"
                  : "Approaching Usage Limit"}
              </p>
              <p className="text-muted-foreground text-sm">
                {usagePercentage > 90
                  ? "You have exceeded your monthly verification limit. Additional charges may apply."
                  : "You are approaching your monthly verification limit. Consider upgrading your plan."}
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Pricing Plans */}
      <div className="mt-8">
        <PricingSection
          title="Available Plans"
          plans={billingData.organizationPlans}
        />
        <AddOnsSection
          title="Integration Add-ons"
          addOns={billingData.integrationAddOns}
        />
      </div>

      {/* Payment Methods */}
      {paymentMethods && paymentMethods.length > 0 && (
        <Card className="p-6">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold">Payment Methods</h3>
            {onAddPaymentMethod && (
              <button
                onClick={onAddPaymentMethod}
                className="border-border hover:bg-accent rounded-md border px-3 py-1 text-sm font-medium transition-colors"
              >
                Add New
              </button>
            )}
          </div>
          <div className="space-y-3">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className="border-border flex items-center justify-between rounded-md border p-3"
              >
                <div className="flex items-center gap-3">
                  <CreditCard className="text-muted-foreground h-4 w-4" />
                  <div>
                    <p className="font-medium">
                      {method.brand.toUpperCase()} ending in {method.last4}
                    </p>
                    <p className="text-muted-foreground text-sm">
                      Expires {method.exp_month}/{method.exp_year}
                    </p>
                  </div>
                  {method.is_default && (
                    <Badge variant="secondary" className="text-xs">
                      Default
                    </Badge>
                  )}
                </div>
                {onRemovePaymentMethod && !method.is_default && (
                  <button
                    onClick={() => onRemovePaymentMethod(method.id)}
                    className="text-destructive text-sm hover:underline"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Billing History */}
      {billingHistory && billingHistory.length > 0 && (
        <Card className="p-6">
          <h3 className="mb-4 text-lg font-semibold">Recent Invoices</h3>
          <div className="space-y-3">
            {billingHistory.slice(0, 5).map((invoice) => (
              <div
                key={invoice.id}
                className="border-border flex items-center justify-between rounded-md border p-3"
              >
                <div>
                  <p className="font-medium">{invoice.description}</p>
                  <p className="text-muted-foreground text-sm">
                    {new Date(invoice.created_at).toLocaleDateString()} •
                    <span
                      className={`ml-1 capitalize ${
                        invoice.status === "paid"
                          ? "text-green-600"
                          : invoice.status === "pending"
                            ? "text-yellow-600"
                            : "text-red-600"
                      }`}
                    >
                      {invoice.status}
                    </span>
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <span className="font-medium">
                    ${(invoice.amount / 100).toFixed(2)}{" "}
                    {invoice.currency.toUpperCase()}
                  </span>
                  {invoice.invoice_url && onViewInvoice && (
                    <button
                      onClick={() => onViewInvoice(invoice.invoice_url!)}
                      className="text-primary text-sm hover:underline"
                    >
                      View
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};
