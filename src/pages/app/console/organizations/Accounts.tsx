import { Users } from "lucide-react";
import { useNavigate } from "react-router";

import { Button } from "@/components/ui/button";

export default function AccountsPage() {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-8 w-8" />
          <h1 className="text-3xl font-bold tracking-tight">Accounts</h1>
        </div>
        <Button>Add Account</Button>
      </div>
      <div className="flex min-h-[400px] items-center justify-center">
        <span className="text-muted-foreground">
          Accounts management coming soon
        </span>
      </div>
    </div>
  );
}
