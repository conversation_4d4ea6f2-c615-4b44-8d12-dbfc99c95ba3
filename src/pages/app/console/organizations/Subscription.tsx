import { BadgeDollarSign, CreditCard } from "lucide-react";

import AddOnsSection from "@/components/common/pricing/AddOnsSection";
import PricingSection from "@/components/common/pricing/PricingSection";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { useUser } from "@/contexts/User";

const organizationPlans = [
  {
    name: "Courier",
    price: "$99",
    period: "per month",
    description: "Perfect for small businesses just getting started",
    features: [
      "Unlimited Users",
      "Up to 100 verifications per month",
      "Real-time tracking",
      "AI Documentation",
      "Basic analytics dashboard",
      "Email support",
    ],
  },
  {
    name: "Professional",
    price: "$599",
    period: "per month",
    description: "Ideal for growing businesses with higher volume needs",
    features: [
      "Everything in Courier",
      "Up to 1,000 verifications per month",
      "Advanced analytics and reporting",
      "Priority email & phone support",
    ],
    highlighted: true,
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "contact us",
    description: "For large organizations with specific requirements",
    features: [
      "Everything in Professional",
      "Unlimited verifications",
      "Custom analytics solutions",
      "24/7 dedicated support",
      "Advanced security features",
      "SLA guarantees",
    ],
  },
];

const getPricePerVerification = (planName: string) => {
  switch (planName.toLowerCase()) {
    case "courier":
      return "$0.99";
    case "professional":
      return "$0.59";
    case "enterprise":
      return "Custom";
    default:
      return "N/A";
  }
};

const integrationAddOns = [
  {
    name: "Samsara Integration",
    price: "$199",
    period: "per month",
    description: "Connect your Samsara fleet management system",
    features: [
      "Real-time vehicle tracking",
      "Automated driver logs",
      "Maintenance scheduling",
      "Fuel usage monitoring",
      "Temperature monitoring",
      "Custom reporting",
    ],
  },
  {
    name: "KeepTruckin Integration",
    price: "$149",
    period: "per month",
    description: "Integrate with KeepTruckin's ELD and fleet management",
    features: [
      "Electronic logging device (ELD) sync",
      "Driver performance monitoring",
      "Vehicle inspection reports",
      "GPS tracking integration",
      "Hours of service compliance",
    ],
  },
];

export default function BillingPage() {
  const { memberships } = useUser();
  const currentOrganization = memberships[0]?.organization;
  const currentPlan = currentOrganization ? "Courier" : "No Plan"; // This should be fetched from the organization's data

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CreditCard className="h-8 w-8" />
          <h1 className="text-3xl font-bold tracking-tight">Billing</h1>
        </div>
      </div>

      <Card className="bg-accent/10 p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-xl font-semibold">
              Current Plan: {currentPlan}
            </h2>
            <p className="text-muted-foreground">
              Price per verification: {getPricePerVerification(currentPlan)}
            </p>
          </div>
          <Badge variant="outline" className="px-4 py-1 text-lg">
            <BadgeDollarSign className="mr-1 h-5 w-5" />
            Active
          </Badge>
        </div>
      </Card>

      <div className="mt-8">
        <PricingSection title="Available Plans" plans={organizationPlans} />
        <AddOnsSection title="Integration Add-ons" addOns={integrationAddOns} />
      </div>
    </div>
  );
}
