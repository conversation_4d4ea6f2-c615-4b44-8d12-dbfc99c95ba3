import { useState } from "react";
import { format } from "date-fns";
import {
  Clock,
  Loader2,
  Mail,
  MoreHorizontal,
  Plus,
  Trash,
  UserCog,
  Users,
} from "lucide-react";
import { useNavigate } from "react-router";

import {
  useDeleteMember,
  useListInvitations,
  useListMembers,
  useRevokeInvitation,
  useUpdateMember,
} from "@/api/organizations";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { useOrganization } from "@/contexts/Organization";
import { Enums } from "@/supabase/types";

// Define types for team members and invitations
type MemberRole = Enums<"members_role">;

interface TeamMember {
  id: string;
  user_id?: string;
  organization_id: string;
  email: string;
  role: MemberRole;
  status: string;
  created_at: string;
  user?: {
    id?: string;
    email?: string;
    first_name?: string;
    last_name?: string;
  };
}

interface Invitation {
  id: string;
  organization_id: string;
  email: string;
  role: MemberRole;
  status: string;
  created_at: string;
  created_by: string;
  expires_at: string;
  organization?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

const i18n = {
  en: {
    title: "Team Members",
    inviteButton: "Invite Member",
    membersCard: {
      title: "Current Members",
      emptyState: "No team members found",
      columns: {
        user: "User",
        email: "Email",
        role: "Role",
        joined: "Joined",
        actions: "Actions",
      },
      actions: {
        changeRole: "Change Role",
        remove: "Remove",
      },
    },
    invitationsCard: {
      title: "Pending Invitations",
      emptyState: "No pending invitations",
      columns: {
        email: "Email",
        role: "Role",
        status: "Status",
        expires: "Expires",
        actions: "Actions",
      },
    },
    updateDialog: {
      title: "Update Team Member Role",
      description: "Change the role for",
      roleLabel: "Role",
      rolePlaceholder: "Select a role",
      cancel: "Cancel",
      update: "Update Role",
      updating: "Updating...",
    },
    toast: {
      memberRemoved: {
        title: "Member removed",
        description: "The team member has been removed successfully.",
      },
      invitationRevoked: {
        title: "Invitation revoked",
        description: "The invitation has been revoked successfully.",
      },
      memberUpdated: {
        title: "Member updated",
        description: "The team member has been updated successfully.",
      },
      error: {
        title: "Error",
        removeMember: "Failed to remove member: ",
        revokeInvitation: "Failed to revoke invitation: ",
        updateMember: "Failed to update member: ",
      },
    },
  },
  links: {
    invite: "/app/console/organization/invite",
  },
};

export function TeamView({
  members,
  invitations,
  isLoadingMembers,
  isLoadingInvitations,
  isUpdateMemberDialogOpen,
  selectedMember,
  selectedRole,
  isUpdatingMember,
  isDeletingMember,
  onNavigateToInvite,
  onDeleteMember,
  onRevokeInvitation,
  onOpenUpdateMemberDialog,
  onUpdateMember,
  onCloseUpdateMemberDialog,
  onRoleChange,
}: {
  members: TeamMember[];
  invitations: Invitation[];
  isLoadingMembers: boolean;
  isLoadingInvitations: boolean;
  isUpdateMemberDialogOpen: boolean;
  selectedMember: TeamMember | null;
  selectedRole: MemberRole;
  isUpdatingMember: boolean;
  isDeletingMember: boolean;
  onNavigateToInvite: () => void;
  onDeleteMember: (memberId: string) => void;
  onRevokeInvitation: (invitationId: string) => void;
  onOpenUpdateMemberDialog: (member: TeamMember) => void;
  onUpdateMember: () => void;
  onCloseUpdateMemberDialog: () => void;
  onRoleChange: (role: MemberRole) => void;
}) {
  if (isLoadingMembers || isLoadingInvitations) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-8 w-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <Button onClick={onNavigateToInvite}>
          <Plus className="mr-2 h-4 w-4" />
          {i18n.en.inviteButton}
        </Button>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {i18n.en.membersCard.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{i18n.en.membersCard.columns.user}</TableHead>
                  <TableHead>{i18n.en.membersCard.columns.email}</TableHead>
                  <TableHead>{i18n.en.membersCard.columns.role}</TableHead>
                  <TableHead>{i18n.en.membersCard.columns.joined}</TableHead>
                  <TableHead className="w-[80px]">
                    {i18n.en.membersCard.columns.actions}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={5}
                      className="text-muted-foreground text-center"
                    >
                      {i18n.en.membersCard.emptyState}
                    </TableCell>
                  </TableRow>
                ) : (
                  members.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 text-primary flex h-8 w-8 items-center justify-center rounded-full">
                            {member.user?.first_name?.[0] ||
                              member.user?.email?.[0] ||
                              "U"}
                          </div>
                          <div>
                            {member.user?.first_name &&
                            member.user?.last_name ? (
                              <div className="font-medium">
                                {member.user.first_name} {member.user.last_name}
                              </div>
                            ) : (
                              <div className="font-medium">User</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail className="text-muted-foreground h-4 w-4" />
                          {member.user?.email || member.email}
                        </div>
                      </TableCell>
                      <TableCell className="capitalize">
                        {member.role}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Clock className="text-muted-foreground h-4 w-4" />
                          {format(new Date(member.created_at), "MMM d, yyyy")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => onOpenUpdateMemberDialog(member)}
                            >
                              <UserCog className="mr-2 h-4 w-4" />
                              {i18n.en.membersCard.actions.changeRole}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => onDeleteMember(member.id)}
                              className="text-destructive"
                              disabled={isDeletingMember}
                            >
                              <Trash className="mr-2 h-4 w-4" />
                              {i18n.en.membersCard.actions.remove}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {i18n.en.invitationsCard.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{i18n.en.invitationsCard.columns.email}</TableHead>
                  <TableHead>{i18n.en.invitationsCard.columns.role}</TableHead>
                  <TableHead>
                    {i18n.en.invitationsCard.columns.status}
                  </TableHead>
                  <TableHead>
                    {i18n.en.invitationsCard.columns.expires}
                  </TableHead>
                  <TableHead className="w-[80px]">
                    {i18n.en.invitationsCard.columns.actions}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invitations.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={5}
                      className="text-muted-foreground text-center"
                    >
                      {i18n.en.invitationsCard.emptyState}
                    </TableCell>
                  </TableRow>
                ) : (
                  invitations.map((invitation) => (
                    <TableRow key={invitation.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail className="text-muted-foreground h-4 w-4" />
                          {invitation.email}
                        </div>
                      </TableCell>
                      <TableCell className="capitalize">
                        {invitation.role}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            invitation.status === "pending"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {invitation.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Clock className="text-muted-foreground h-4 w-4" />
                          {format(
                            new Date(invitation.expires_at),
                            "MMM d, yyyy",
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRevokeInvitation(invitation.id)}
                          disabled={invitation.status !== "pending"}
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Revoke</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <Dialog
        open={isUpdateMemberDialogOpen}
        onOpenChange={onCloseUpdateMemberDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{i18n.en.updateDialog.title}</DialogTitle>
            <DialogDescription>
              {i18n.en.updateDialog.description}{" "}
              {selectedMember?.user?.email || selectedMember?.email}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="role" className="text-sm font-medium">
                {i18n.en.updateDialog.roleLabel}
              </label>
              <Select
                value={selectedRole}
                onValueChange={(value) => onRoleChange(value as MemberRole)}
              >
                <SelectTrigger id="role">
                  <SelectValue
                    placeholder={i18n.en.updateDialog.rolePlaceholder}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="owner">Owner</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="member">Member</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                  <SelectItem value="billing">Billing</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={onCloseUpdateMemberDialog}>
              {i18n.en.updateDialog.cancel}
            </Button>
            <Button onClick={onUpdateMember} disabled={isUpdatingMember}>
              {isUpdatingMember ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {i18n.en.updateDialog.updating}
                </>
              ) : (
                i18n.en.updateDialog.update
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function TeamPage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentOrganization } = useOrganization();
  const organizationId = currentOrganization?.id;

  const [isUpdateMemberDialogOpen, setIsUpdateMemberDialogOpen] =
    useState(false);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [selectedRole, setSelectedRole] = useState<MemberRole>("member");

  const { data: membersData, isLoading: isLoadingMembers } = useListMembers(
    { organization_id: organizationId || "" },
    { enabled: !!organizationId },
  );

  const { data: invitationsData, isLoading: isLoadingInvitations } =
    useListInvitations(
      { organization_id: organizationId || "" },
      { enabled: !!organizationId },
    );

  const deleteMember = useDeleteMember({
    onSuccess: () => {
      toast({
        title: i18n.en.toast.memberRemoved.title,
        description: i18n.en.toast.memberRemoved.description,
      });
    },
    onError: (error) => {
      toast({
        title: i18n.en.toast.error.title,
        description: `${i18n.en.toast.error.removeMember}${error.message}`,
        variant: "destructive",
      });
    },
  });

  const revokeInvitation = useRevokeInvitation({
    onSuccess: () => {
      toast({
        title: i18n.en.toast.invitationRevoked.title,
        description: i18n.en.toast.invitationRevoked.description,
      });
    },
    onError: (error) => {
      toast({
        title: i18n.en.toast.error.title,
        description: `${i18n.en.toast.error.revokeInvitation}${error.message}`,
        variant: "destructive",
      });
    },
  });

  const updateMember = useUpdateMember({
    onSuccess: () => {
      toast({
        title: i18n.en.toast.memberUpdated.title,
        description: i18n.en.toast.memberUpdated.description,
      });
      setIsUpdateMemberDialogOpen(false);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toast.error.title,
        description: `${i18n.en.toast.error.updateMember}${error.message}`,
        variant: "destructive",
      });
    },
  });

  function handleNavigateToInvite() {
    navigate(i18n.links.invite);
  }

  function handleDeleteMember(memberId: string) {
    if (!organizationId) return;

    deleteMember.mutate({
      id: memberId,
      organization_id: organizationId,
    });
  }

  function handleRevokeInvitation(invitationId: string) {
    if (!organizationId) return;

    revokeInvitation.mutate({
      id: invitationId,
      organization_id: organizationId,
    });
  }

  function handleUpdateMember() {
    if (!selectedMember || !selectedRole || !organizationId) return;

    updateMember.mutate({
      id: selectedMember.id,
      role: selectedRole,
      organization_id: organizationId,
    });
  }

  function handleOpenUpdateMemberDialog(member: TeamMember) {
    setSelectedMember(member);
    setSelectedRole(member.role);
    setIsUpdateMemberDialogOpen(true);
  }

  function handleCloseUpdateMemberDialog() {
    setIsUpdateMemberDialogOpen(false);
  }

  function handleRoleChange(role: MemberRole) {
    setSelectedRole(role);
  }

  const members = Array.isArray(membersData)
    ? membersData
    : membersData && "items" in membersData
      ? membersData.items
      : [];

  const invitations = Array.isArray(invitationsData)
    ? invitationsData
    : invitationsData && "items" in invitationsData
      ? invitationsData.items
      : [];

  return (
    <TeamView
      members={members as TeamMember[]}
      invitations={invitations as Invitation[]}
      isLoadingMembers={isLoadingMembers}
      isLoadingInvitations={isLoadingInvitations}
      isUpdateMemberDialogOpen={isUpdateMemberDialogOpen}
      selectedMember={selectedMember}
      selectedRole={selectedRole}
      isUpdatingMember={updateMember.isPending}
      isDeletingMember={deleteMember.isPending}
      onNavigateToInvite={handleNavigateToInvite}
      onDeleteMember={handleDeleteMember}
      onRevokeInvitation={handleRevokeInvitation}
      onOpenUpdateMemberDialog={handleOpenUpdateMemberDialog}
      onUpdateMember={handleUpdateMember}
      onCloseUpdateMemberDialog={handleCloseUpdateMemberDialog}
      onRoleChange={handleRoleChange}
    />
  );
}
