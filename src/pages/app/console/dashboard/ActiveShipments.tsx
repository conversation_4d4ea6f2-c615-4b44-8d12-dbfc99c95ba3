import { format } from "date-fns";
import { Loader2, Truck } from "lucide-react";

import { useActiveShipments } from "@/api";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const ActiveShipments = () => {
  const { data: shipments, isLoading } = useActiveShipments();

  if (isLoading) {
    return (
      <div className="flex h-32 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium">Active Shipments</CardTitle>
        <Truck className="h-5 w-5 text-blue-500" />
      </CardHeader>
      <CardContent>
        {!shipments?.length ? (
          <p className="text-muted-foreground py-4 text-center text-sm">
            No active shipments
          </p>
        ) : (
          <div className="space-y-4">
            {shipments.map((shipment) => (
              <div
                key={shipment.id}
                className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
              >
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <p className="font-medium">
                      Shipment #{shipment.id.slice(0, 8)}
                    </p>
                    <Badge>{shipment.mode}</Badge>
                  </div>
                  <p className="text-sm">
                    Driver: {shipment.driver?.first_name}{" "}
                    {shipment.driver?.last_name}
                  </p>
                  {shipment.stops?.[0] && (
                    <p className="text-muted-foreground text-sm">
                      Next Stop: {shipment.stops[0].location.formatted}
                    </p>
                  )}
                  <p className="text-muted-foreground text-xs">
                    Started:{" "}
                    {format(new Date(shipment.started_at), "MMM d, h:mm a")}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActiveShipments;
