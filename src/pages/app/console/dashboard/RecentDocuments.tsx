import { formatDistanceToNow } from "date-fns";
import { FileText } from "lucide-react";
import { Link } from "react-router";

import { useListDocuments } from "@/api";
import DocumentType from "@/components/common/DocumentType";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

const RecentDocuments = () => {
  const { data: documents, isLoading } = useListDocuments({
    pageIndex: 0,
    pageSize: 3,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">Recent Documents</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading && (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        )}

        {!isLoading && (!documents?.items || documents.items.length === 0) && (
          <p className="text-muted-foreground">No recent documents found</p>
        )}

        {!isLoading && documents?.items && documents.items.length > 0 && (
          <div className="space-y-4">
            {documents.items.map((doc) => (
              <Link
                key={doc.id}
                to={`/app/console/documents/${doc.id}`}
                className="hover:bg-accent/5 flex items-center justify-between rounded-md p-2 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <DocumentType type={doc.type} size={20} />
                  <div>
                    <p className="max-w-[180px] truncate font-medium">
                      {doc.name}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      {doc.driver?.first_name
                        ? `${doc.driver.first_name} ${doc.driver.last_name}`
                        : doc.organization?.name || "Unknown"}
                    </p>
                  </div>
                </div>
                <div className="text-muted-foreground text-xs">
                  {formatDistanceToNow(new Date(doc.created_at), {
                    addSuffix: true,
                  })}
                </div>
              </Link>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RecentDocuments;
