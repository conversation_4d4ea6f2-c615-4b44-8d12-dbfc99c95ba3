import { Loader2 } from "lucide-react";

import ActiveShipments from "@/pages/app/console/dashboard/ActiveShipments";
import AnalyticsSection from "@/pages/app/console/dashboard/AnalyticsSection";
import PendingIncidents from "@/pages/app/console/dashboard/PendingIncidents";
import RecentDocuments from "@/pages/app/console/dashboard/RecentDocuments";

export interface DashboardMetrics {
  totalShipments: number;
  activeShipments: number;
  totalDrivers: number;
  activeDrivers: number;
  totalIncidents: number;
  openIncidents: number;
  totalTonnage: number;
  weeklyTonnage: number;
  totalEarnings: number;
  weeklyEarnings: number;
}

export interface ActiveShipment {
  id: string;
  mode: string;
  started_at: string;
  driver?: {
    first_name: string;
    last_name: string;
  };
  stops?: Array<{
    location: {
      formatted: string;
    };
  }>;
}

export interface AnalyticsSnapshot {
  total_shipments: number;
  active_shipments: number;
  completed_shipments: number;
  cancelled_shipments: number;
  total_drivers: number;
  active_drivers: number;
  total_organizations: number;
  active_organizations: number;
  total_incidents: number;
  open_incidents: number;
  total_revenue: number;
  average_shipment_value: number;
}

export interface PendingIncident {
  id: string;
  title: string;
  severity: string;
  created_at: string;
  driver?: {
    first_name: string;
    last_name: string;
  };
}

export interface RecentDocument {
  id: string;
  name: string;
  type: string;
  created_at: string;
  driver?: {
    first_name: string;
    last_name: string;
  };
  organization?: {
    name: string;
  };
}

export interface DocumentsResponse {
  items: RecentDocument[];
  total: number;
}

export interface ConsoleDashboardPageProps {
  // Dashboard metrics
  metrics: DashboardMetrics | null;
  isLoadingMetrics: boolean;
  metricsError: Error | null;

  // Active shipments data
  activeShipments: ActiveShipment[] | null;
  isLoadingActiveShipments: boolean;
  activeShipmentsError: Error | null;

  // Analytics data
  analyticsData: AnalyticsSnapshot | null;
  isLoadingAnalytics: boolean;
  analyticsError: Error | null;

  // Pending incidents data
  pendingIncidents: PendingIncident[] | null;
  isLoadingPendingIncidents: boolean;
  pendingIncidentsError: Error | null;

  // Recent documents data
  recentDocuments: DocumentsResponse | null;
  isLoadingDocuments: boolean;
  documentsError: Error | null;
}

export const ConsoleDashboardPage = ({
  metrics,
  isLoadingMetrics,
  metricsError,
  activeShipments,
  isLoadingActiveShipments,
  activeShipmentsError,
  analyticsData,
  isLoadingAnalytics,
  analyticsError,
  pendingIncidents,
  isLoadingPendingIncidents,
  pendingIncidentsError,
  recentDocuments,
  isLoadingDocuments,
  documentsError,
}: ConsoleDashboardPageProps) => {
  if (isLoadingMetrics) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Dashboard</h1>

      {/* TODO: Add metrics overview specific to organizations */}

      <div className="grid gap-6 md:grid-cols-2">
        <PendingIncidents />
        <RecentDocuments />
      </div>

      <div className="grid gap-6">
        <ActiveShipments />
        <AnalyticsSection />
      </div>
    </div>
  );
};
