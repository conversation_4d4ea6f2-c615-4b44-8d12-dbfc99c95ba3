import {
  useActiveShipments,
  useAnalyticsSnapshot,
  useDashboardMetrics,
  useListDocuments,
  usePendingIncidents,
} from "@/api";
import { ConsoleDashboardPage } from "./ConsoleDashboardPage";

const ConsoleApp = () => {
  // Dashboard metrics
  const {
    data: metrics,
    isLoading: isLoadingMetrics,
    error: metricsError,
  } = useDashboardMetrics();

  // Active shipments data
  const {
    data: activeShipments,
    isLoading: isLoadingActiveShipments,
    error: activeShipmentsError,
  } = useActiveShipments();

  // Analytics data
  const {
    data: analyticsData,
    isLoading: isLoadingAnalytics,
    error: analyticsError,
  } = useAnalyticsSnapshot();

  // Pending incidents data
  const {
    data: pendingIncidents,
    isLoading: isLoadingPendingIncidents,
    error: pendingIncidentsError,
  } = usePendingIncidents();

  // Recent documents data
  const {
    data: recentDocuments,
    isLoading: isLoadingDocuments,
    error: documentsError,
  } = useListDocuments({
    pageIndex: 0,
    pageSize: 3,
  });

  return (
    <ConsoleDashboardPage
      metrics={metrics || null}
      isLoadingMetrics={isLoadingMetrics}
      metricsError={metricsError}
      activeShipments={activeShipments || null}
      isLoadingActiveShipments={isLoadingActiveShipments}
      activeShipmentsError={activeShipmentsError}
      analyticsData={analyticsData || null}
      isLoadingAnalytics={isLoadingAnalytics}
      analyticsError={analyticsError}
      pendingIncidents={pendingIncidents || null}
      isLoadingPendingIncidents={isLoadingPendingIncidents}
      pendingIncidentsError={pendingIncidentsError}
      recentDocuments={recentDocuments || null}
      isLoadingDocuments={isLoadingDocuments}
      documentsError={documentsError}
    />
  );
};

export default ConsoleApp;
