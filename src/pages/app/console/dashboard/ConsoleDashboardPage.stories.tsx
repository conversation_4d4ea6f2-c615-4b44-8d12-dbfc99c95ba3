import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleDashboardPage } from "./ConsoleDashboardPage";

const meta: Meta<typeof ConsoleDashboardPage> = {
  title: "Pages/Console/Dashboard",
  component: ConsoleDashboardPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/dashboard" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

const mockDashboardMetrics = {
  totalShipments: 1247,
  activeShipments: 89,
  totalDrivers: 156,
  activeDrivers: 124,
  totalIncidents: 23,
  openIncidents: 7,
  totalTonnage: 15632,
  weeklyTonnage: 2847,
  totalEarnings: 847500,
  weeklyEarnings: 125300,
};

const mockActiveShipments = [
  {
    id: "ship-001",
    mode: "LTL",
    started_at: new Date(Date.now() - 3600000).toISOString(),
    driver: {
      first_name: "John",
      last_name: "Smith",
    },
    stops: [
      {
        location: {
          formatted: "Dallas Distribution Center, TX",
        },
      },
    ],
  },
  {
    id: "ship-002",
    mode: "FTL",
    started_at: new Date(Date.now() - 7200000).toISOString(),
    driver: {
      first_name: "Sarah",
      last_name: "Johnson",
    },
    stops: [
      {
        location: {
          formatted: "Houston Warehouse, TX",
        },
      },
    ],
  },
  {
    id: "ship-003",
    mode: "Expedite",
    started_at: new Date(Date.now() - 1800000).toISOString(),
    driver: {
      first_name: "Mike",
      last_name: "Rodriguez",
    },
    stops: [
      {
        location: {
          formatted: "Austin Medical Center, TX",
        },
      },
    ],
  },
];

const mockAnalyticsSnapshot = {
  total_shipments: 1247,
  active_shipments: 89,
  completed_shipments: 1134,
  cancelled_shipments: 24,
  total_drivers: 156,
  active_drivers: 124,
  total_organizations: 42,
  active_organizations: 38,
  total_incidents: 23,
  open_incidents: 7,
  total_revenue: 847500,
  average_shipment_value: 679.5,
};

const mockPendingIncidents = [
  {
    id: "incident-001",
    title: "Vehicle breakdown on I-35",
    severity: "high",
    created_at: new Date(Date.now() - 1800000).toISOString(),
    driver: {
      first_name: "Mike",
      last_name: "Wilson",
    },
  },
  {
    id: "incident-002",
    title: "Delivery delay due to traffic",
    severity: "medium",
    created_at: new Date(Date.now() - 3600000).toISOString(),
    driver: {
      first_name: "Lisa",
      last_name: "Chen",
    },
  },
  {
    id: "incident-003",
    title: "Customer contact issue",
    severity: "low",
    created_at: new Date(Date.now() - 5400000).toISOString(),
    driver: {
      first_name: "David",
      last_name: "Brown",
    },
  },
];

const mockRecentDocuments = {
  items: [
    {
      id: "doc-001",
      name: "Bill of Lading - Walmart Shipment",
      type: "bill_of_lading",
      created_at: new Date(Date.now() - 1800000).toISOString(),
      driver: {
        first_name: "John",
        last_name: "Smith",
      },
      organization: {
        name: "QuikSkope Logistics",
      },
    },
    {
      id: "doc-002",
      name: "Delivery Receipt - Medical Supplies",
      type: "delivery_receipt",
      created_at: new Date(Date.now() - 3600000).toISOString(),
      driver: {
        first_name: "Sarah",
        last_name: "Johnson",
      },
      organization: {
        name: "MedTrans Corp",
      },
    },
    {
      id: "doc-003",
      name: "Inspection Report - Electronics",
      type: "inspection_report",
      created_at: new Date(Date.now() - 7200000).toISOString(),
      driver: {
        first_name: "Mike",
        last_name: "Rodriguez",
      },
      organization: {
        name: "TechFlow Shipping",
      },
    },
  ],
  total: 89,
};

const mockHighTrafficMetrics = {
  totalShipments: 2847,
  activeShipments: 247,
  totalDrivers: 298,
  activeDrivers: 276,
  totalIncidents: 18,
  openIncidents: 12,
  totalTonnage: 38492,
  weeklyTonnage: 7284,
  totalEarnings: 2147800,
  weeklyEarnings: 389500,
};

const mockHighTrafficShipments = [
  ...mockActiveShipments,
  {
    id: "ship-004",
    mode: "Expedite",
    started_at: new Date(Date.now() - 900000).toISOString(),
    driver: {
      first_name: "Emma",
      last_name: "Davis",
    },
    stops: [
      {
        location: {
          formatted: "Emergency Hospital, TX",
        },
      },
    ],
  },
  {
    id: "ship-005",
    mode: "FTL",
    started_at: new Date(Date.now() - 2700000).toISOString(),
    driver: {
      first_name: "Alex",
      last_name: "Martinez",
    },
    stops: [
      {
        location: {
          formatted: "Manufacturing Plant, TX",
        },
      },
    ],
  },
];

const mockHighTrafficIncidents = [
  ...mockPendingIncidents,
  {
    id: "incident-004",
    title: "Equipment malfunction",
    severity: "high",
    created_at: new Date(Date.now() - 900000).toISOString(),
    driver: {
      first_name: "Tom",
      last_name: "Anderson",
    },
  },
  {
    id: "incident-005",
    title: "Weather delay",
    severity: "medium",
    created_at: new Date(Date.now() - 1200000).toISOString(),
    driver: {
      first_name: "Jennifer",
      last_name: "Taylor",
    },
  },
];

export const Default: Story = {
  args: {
    metrics: mockDashboardMetrics,
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: mockActiveShipments,
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: mockAnalyticsSnapshot,
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: mockPendingIncidents,
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: mockRecentDocuments,
    isLoadingDocuments: false,
    documentsError: null,
  },
};

export const Loading: Story = {
  args: {
    metrics: null,
    isLoadingMetrics: true,
    metricsError: null,
    activeShipments: null,
    isLoadingActiveShipments: true,
    activeShipmentsError: null,
    analyticsData: null,
    isLoadingAnalytics: true,
    analyticsError: null,
    pendingIncidents: null,
    isLoadingPendingIncidents: true,
    pendingIncidentsError: null,
    recentDocuments: null,
    isLoadingDocuments: true,
    documentsError: null,
  },
};

export const NoData: Story = {
  args: {
    metrics: {
      totalShipments: 0,
      activeShipments: 0,
      totalDrivers: 0,
      activeDrivers: 0,
      totalIncidents: 0,
      openIncidents: 0,
      totalTonnage: 0,
      weeklyTonnage: 0,
      totalEarnings: 0,
      weeklyEarnings: 0,
    },
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: [],
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: {
      total_shipments: 0,
      active_shipments: 0,
      completed_shipments: 0,
      cancelled_shipments: 0,
      total_drivers: 0,
      active_drivers: 0,
      total_organizations: 1,
      active_organizations: 1,
      total_incidents: 0,
      open_incidents: 0,
      total_revenue: 0,
      average_shipment_value: 0,
    },
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: [],
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: {
      items: [],
      total: 0,
    },
    isLoadingDocuments: false,
    documentsError: null,
  },
};

export const HighTraffic: Story = {
  args: {
    metrics: mockHighTrafficMetrics,
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: mockHighTrafficShipments,
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: {
      total_shipments: 2847,
      active_shipments: 247,
      completed_shipments: 2547,
      cancelled_shipments: 53,
      total_drivers: 298,
      active_drivers: 276,
      total_organizations: 67,
      active_organizations: 62,
      total_incidents: 18,
      open_incidents: 12,
      total_revenue: 2147800,
      average_shipment_value: 754.25,
    },
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: mockHighTrafficIncidents,
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: {
      items: [
        ...mockRecentDocuments.items,
        {
          id: "doc-004",
          name: "Emergency Medical Transport BOL",
          type: "bill_of_lading",
          created_at: new Date(Date.now() - 300000).toISOString(),
          driver: {
            first_name: "Emma",
            last_name: "Davis",
          },
          organization: {
            name: "EmergeMed Logistics",
          },
        },
      ],
      total: 156,
    },
    isLoadingDocuments: false,
    documentsError: null,
  },
};

export const Alerts: Story = {
  args: {
    metrics: {
      ...mockDashboardMetrics,
      openIncidents: 15,
      totalIncidents: 38,
    },
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: mockActiveShipments,
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: {
      ...mockAnalyticsSnapshot,
      open_incidents: 15,
      total_incidents: 38,
    },
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: [
      {
        id: "incident-critical-001",
        title: "CRITICAL: Multi-vehicle accident on I-45",
        severity: "critical",
        created_at: new Date(Date.now() - 600000).toISOString(),
        driver: {
          first_name: "Emergency",
          last_name: "Response",
        },
      },
      {
        id: "incident-high-001",
        title: "Vehicle fire reported",
        severity: "high",
        created_at: new Date(Date.now() - 900000).toISOString(),
        driver: {
          first_name: "Robert",
          last_name: "Johnson",
        },
      },
      {
        id: "incident-high-002",
        title: "Cargo theft attempt",
        severity: "high",
        created_at: new Date(Date.now() - 1200000).toISOString(),
        driver: {
          first_name: "Maria",
          last_name: "Garcia",
        },
      },
      ...mockPendingIncidents,
    ],
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: mockRecentDocuments,
    isLoadingDocuments: false,
    documentsError: null,
  },
};

export const Analytics: Story = {
  args: {
    metrics: mockDashboardMetrics,
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: mockActiveShipments,
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: {
      total_shipments: 1247,
      active_shipments: 89,
      completed_shipments: 1134,
      cancelled_shipments: 24,
      total_drivers: 156,
      active_drivers: 124,
      total_organizations: 42,
      active_organizations: 38,
      total_incidents: 23,
      open_incidents: 7,
      total_revenue: 847500,
      average_shipment_value: 679.5,
    },
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: mockPendingIncidents,
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: mockRecentDocuments,
    isLoadingDocuments: false,
    documentsError: null,
  },
};

export const NewUser: Story = {
  args: {
    metrics: {
      totalShipments: 5,
      activeShipments: 2,
      totalDrivers: 3,
      activeDrivers: 2,
      totalIncidents: 0,
      openIncidents: 0,
      totalTonnage: 127,
      weeklyTonnage: 89,
      totalEarnings: 3500,
      weeklyEarnings: 2100,
    },
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: [
      {
        id: "ship-new-001",
        mode: "LTL",
        started_at: new Date(Date.now() - 1800000).toISOString(),
        driver: {
          first_name: "John",
          last_name: "Starter",
        },
        stops: [
          {
            location: {
              formatted: "Local Distribution Hub, TX",
            },
          },
        ],
      },
      {
        id: "ship-new-002",
        mode: "FTL",
        started_at: new Date(Date.now() - 3600000).toISOString(),
        driver: {
          first_name: "Sarah",
          last_name: "New",
        },
        stops: [
          {
            location: {
              formatted: "Regional Warehouse, TX",
            },
          },
        ],
      },
    ],
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: {
      total_shipments: 5,
      active_shipments: 2,
      completed_shipments: 3,
      cancelled_shipments: 0,
      total_drivers: 3,
      active_drivers: 2,
      total_organizations: 1,
      active_organizations: 1,
      total_incidents: 0,
      open_incidents: 0,
      total_revenue: 3500,
      average_shipment_value: 700.0,
    },
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: [],
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: {
      items: [
        {
          id: "doc-new-001",
          name: "First Shipment BOL",
          type: "bill_of_lading",
          created_at: new Date(Date.now() - 1800000).toISOString(),
          driver: {
            first_name: "John",
            last_name: "Starter",
          },
          organization: {
            name: "New Logistics LLC",
          },
        },
      ],
      total: 3,
    },
    isLoadingDocuments: false,
    documentsError: null,
  },
};

export const OffPeak: Story = {
  args: {
    metrics: {
      totalShipments: 1247,
      activeShipments: 12,
      totalDrivers: 156,
      activeDrivers: 45,
      totalIncidents: 23,
      openIncidents: 2,
      totalTonnage: 15632,
      weeklyTonnage: 892,
      totalEarnings: 847500,
      weeklyEarnings: 34200,
    },
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: [
      {
        id: "ship-off-001",
        mode: "LTL",
        started_at: new Date(Date.now() - 10800000).toISOString(),
        driver: {
          first_name: "Night",
          last_name: "Driver",
        },
        stops: [
          {
            location: {
              formatted: "24hr Distribution Center, TX",
            },
          },
        ],
      },
    ],
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: {
      total_shipments: 1247,
      active_shipments: 12,
      completed_shipments: 1225,
      cancelled_shipments: 10,
      total_drivers: 156,
      active_drivers: 45,
      total_organizations: 42,
      active_organizations: 38,
      total_incidents: 23,
      open_incidents: 2,
      total_revenue: 847500,
      average_shipment_value: 679.5,
    },
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: [
      {
        id: "incident-minor-001",
        title: "Minor traffic delay",
        severity: "low",
        created_at: new Date(Date.now() - 7200000).toISOString(),
        driver: {
          first_name: "Late",
          last_name: "Night",
        },
      },
    ],
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: {
      items: [
        {
          id: "doc-off-001",
          name: "Night Delivery Receipt",
          type: "delivery_receipt",
          created_at: new Date(Date.now() - 3600000).toISOString(),
          driver: {
            first_name: "Night",
            last_name: "Driver",
          },
          organization: {
            name: "24/7 Logistics",
          },
        },
      ],
      total: 89,
    },
    isLoadingDocuments: false,
    documentsError: null,
  },
};

export const ErrorState: Story = {
  args: {
    metrics: mockDashboardMetrics,
    isLoadingMetrics: false,
    metricsError: new Error("Failed to load dashboard metrics"),
    activeShipments: null,
    isLoadingActiveShipments: false,
    activeShipmentsError: new Error("Unable to fetch active shipments"),
    analyticsData: null,
    isLoadingAnalytics: false,
    analyticsError: new Error("Analytics service unavailable"),
    pendingIncidents: mockPendingIncidents,
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: null,
    isLoadingDocuments: false,
    documentsError: new Error("Document service error"),
  },
};

export const ComprehensiveData: Story = {
  args: {
    metrics: {
      totalShipments: 5842,
      activeShipments: 347,
      totalDrivers: 512,
      activeDrivers: 398,
      totalIncidents: 45,
      openIncidents: 8,
      totalTonnage: 94753,
      weeklyTonnage: 12847,
      totalEarnings: 4729800,
      weeklyEarnings: 687400,
    },
    isLoadingMetrics: false,
    metricsError: null,
    activeShipments: [
      ...mockActiveShipments,
      {
        id: "ship-comp-001",
        mode: "Refrigerated",
        started_at: new Date(Date.now() - 5400000).toISOString(),
        driver: {
          first_name: "Cold",
          last_name: "Chain",
        },
        stops: [
          {
            location: {
              formatted: "Frozen Foods Warehouse, TX",
            },
          },
        ],
      },
      {
        id: "ship-comp-002",
        mode: "Hazmat",
        started_at: new Date(Date.now() - 7200000).toISOString(),
        driver: {
          first_name: "Safety",
          last_name: "First",
        },
        stops: [
          {
            location: {
              formatted: "Chemical Plant, TX",
            },
          },
        ],
      },
    ],
    isLoadingActiveShipments: false,
    activeShipmentsError: null,
    analyticsData: {
      total_shipments: 5842,
      active_shipments: 347,
      completed_shipments: 5442,
      cancelled_shipments: 53,
      total_drivers: 512,
      active_drivers: 398,
      total_organizations: 128,
      active_organizations: 115,
      total_incidents: 45,
      open_incidents: 8,
      total_revenue: 4729800,
      average_shipment_value: 809.75,
    },
    isLoadingAnalytics: false,
    analyticsError: null,
    pendingIncidents: [
      ...mockPendingIncidents,
      {
        id: "incident-comp-001",
        title: "Scheduled maintenance required",
        severity: "medium",
        created_at: new Date(Date.now() - 2700000).toISOString(),
        driver: {
          first_name: "Fleet",
          last_name: "Manager",
        },
      },
    ],
    isLoadingPendingIncidents: false,
    pendingIncidentsError: null,
    recentDocuments: {
      items: [
        ...mockRecentDocuments.items,
        {
          id: "doc-comp-001",
          name: "Hazmat Certificate",
          type: "inspection_report",
          created_at: new Date(Date.now() - 900000).toISOString(),
          driver: {
            first_name: "Safety",
            last_name: "First",
          },
          organization: {
            name: "HazTrans Solutions",
          },
        },
        {
          id: "doc-comp-002",
          name: "Temperature Log",
          type: "delivery_receipt",
          created_at: new Date(Date.now() - 1800000).toISOString(),
          driver: {
            first_name: "Cold",
            last_name: "Chain",
          },
          organization: {
            name: "RefrigTrans Corp",
          },
        },
      ],
      total: 247,
    },
    isLoadingDocuments: false,
    documentsError: null,
  },
};
