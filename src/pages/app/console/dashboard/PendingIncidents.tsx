import { format } from "date-fns";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2 } from "lucide-react";

import { usePendingIncidents } from "@/api";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const PendingIncidents = () => {
  const { data: incidents, isLoading } = usePendingIncidents();

  if (isLoading) {
    return (
      <div className="flex h-32 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium">Recent Incidents</CardTitle>
        <AlertTriangle className="h-5 w-5 text-yellow-500" />
      </CardHeader>
      <CardContent>
        {!incidents?.length ? (
          <p className="text-muted-foreground py-4 text-center text-sm">
            No pending incidents
          </p>
        ) : (
          <div className="space-y-4">
            {incidents.map((incident) => (
              <div
                key={incident.id}
                className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
              >
                <div>
                  <p className="font-medium">{incident.title}</p>
                  <p className="text-muted-foreground text-sm">
                    Reported by: {incident.driver?.first_name}{" "}
                    {incident.driver?.last_name}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {format(new Date(incident.created_at), "MMM d, h:mm a")}
                  </p>
                </div>
                <Badge
                  variant={
                    incident.severity === "high" ? "destructive" : "default"
                  }
                >
                  {incident.severity}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PendingIncidents;
