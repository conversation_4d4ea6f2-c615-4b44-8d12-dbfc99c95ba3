import { useQuery } from "@tanstack/react-query";
import { Users } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import { useUpdateDriver } from "@/api/drivers/use-update-driver";
import DriverForm, { DriverFormValues } from "@/components/forms/DriverForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Driver",
    toasts: {
      success: "Driver updated successfully",
      error: "Failed to update driver",
    },
    loading: "Loading driver...",
    error: "Failed to load driver",
    notFound: "Driver not found",
    backButton: "Back to Drivers",
  },
};

// Hook to get a single driver
function useGetDriver(id: string) {
  return useQuery({
    queryKey: ["drivers", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("drivers")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditDriverPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: driver, isLoading, error } = useGetDriver(id!);

  const updateDriver = useUpdateDriver({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/drivers/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (values: DriverFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const driverData = {
      id,
      first_name: values.first_name,
      last_name: values.last_name,
      email: values.email,
      phone_number: values.phone_number,
    };

    updateDriver.mutate(driverData);
  };

  const handleCancel = () => {
    navigate(`/app/console/drivers/${id}`);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-2">
          <Users className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-2">
          <Users className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <ErrorAlert error={error} />
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/drivers")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  if (!driver) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-2">
          <Users className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/drivers")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: DriverFormValues = {
    first_name: driver.first_name,
    last_name: driver.last_name,
    email: driver.email,
    phone_number: driver.phone_number,
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Users className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>

      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <DriverForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={updateDriver.isPending}
          defaultValues={defaultValues}
        />
      </div>
    </div>
  );
}
