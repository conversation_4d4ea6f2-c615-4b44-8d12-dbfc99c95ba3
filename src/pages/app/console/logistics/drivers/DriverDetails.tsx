import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, Loader2 } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/supabase/client";

const DriverDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const {
    data: driver,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["driver", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("drivers")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
  });

  if (isLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !driver) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center gap-4">
        <p className="text-destructive">Failed to load driver details</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/drivers")}
        >
          Back to Drivers
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate("/app/console/drivers")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">Driver Details</h1>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  First Name
                </p>
                <p className="text-lg">{driver.first_name}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Last Name
                </p>
                <p className="text-lg">{driver.last_name}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Email
                </p>
                <p className="text-lg">{driver.email}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Phone Number
                </p>
                <p className="text-lg">{driver.phone_number}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Driver Score
                </p>
                <p className="text-lg">{driver.score}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm font-medium">
                  Tier
                </p>
                <p className="text-lg capitalize">{driver.tier}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end">
        <Button onClick={() => navigate(`/app/console/drivers/${id}/edit`)}>
          Edit Driver
        </Button>
      </div>
    </div>
  );
};

export default DriverDetails;
