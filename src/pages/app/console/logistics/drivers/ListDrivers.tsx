"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { <PERSON>cil, Trash } from "lucide-react";
import { Link, useNavigate } from "react-router";

import type { UseDataTableProps } from "@/components/tables";
import type { BadgeProps } from "@/components/ui/badge";
import type { Tables } from "@/supabase/types";

import { DriverStatusBadge } from "@/components/common/types/DriverStatus";
import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const i18n = {
  en: {
    noDriver: "There are no drivers yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search drivers...",
    },
    headers: {
      id: "ID",
      avatar: "Avatar",
      name: "Name",
      email: "Email",
      phone: "Phone",
      score: "Score",
      tier: "Tier",
      status: "Status",
      created_at: "Created At",
      actions: "Actions",
    },
    filters: {
      status: "Status",
      options: {
        status: {
          ALL: "All",
          ACTIVE: "Active",
          INACTIVE: "Inactive",
          SUSPENDED: "Suspended",
        },
      },
    },
  },
  links: {
    drivers: "/app/console/drivers/[id]",
  },
};

const groupName = "driver";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.status.ALL },
      { value: "active", label: i18n.en.filters.options.status.ACTIVE },
      { value: "inactive", label: i18n.en.filters.options.status.INACTIVE },
      { value: "suspended", label: i18n.en.filters.options.status.SUSPENDED },
    ],
  },
];

export type DriverType = Tables<"drivers">;
export type DriversQueryResult = {
  items: DriverType[];
  total: number;
};
export type DriversType = DriversQueryResult["items"];
export type TableProps = UseDataTableProps<DriverType, DriversType>;

const tableLinks = {
  drivers: "/app/console/drivers/[id]",
};

export default function ListDrivers({
  loading = false,
  drivers,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onDelete,
}: PropsWithChildren<{
  loading?: boolean;
  drivers?: DriversQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onDelete?: (id: string) => void;
}>) {
  const navigate = useNavigate();

  return (
    <ListTable
      loading={loading}
      data={drivers}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      i18n={{
        emptyText: i18n.en.noDriver,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      filterGroups={filterGroups}
      columns={({ i18n, TableActions }) => [
        selectColumn as ColumnDef<DriverType, DriverType[]>,
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.id || "ID"}
            />
          ),
          cell: ({ row }) => (
            <Link
              to={tableLinks.drivers.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "avatar",
          accessorKey: "avatar",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.avatar || "Avatar"}
            />
          ),
          cell: ({ row }) => {
            const initials = `${row.original.first_name?.[0] || ""}${
              row.original.last_name?.[0] || ""
            }`;
            return (
              <Avatar>
                <AvatarImage src={row.original.avatar || ""} alt={initials} />
                <AvatarFallback>{initials}</AvatarFallback>
              </Avatar>
            );
          },
        },
        {
          id: "name",
          accessorFn: (row) => `${row.first_name} ${row.last_name}`,
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.name || "Name"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("name")}</div>,
        },
        {
          id: "email",
          accessorKey: "email",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.email || "Email"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("email")}</div>,
        },
        {
          id: "phone_number",
          accessorKey: "phone_number",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.phone || "Phone"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("phone_number")}</div>,
        },
        {
          id: "score",
          accessorKey: "score",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.score || "Score"}
            />
          ),
          cell: ({ row }) => {
            const score = Number(row.getValue("score") || 0);

            let textColor = "text-green-600";
            if (score < 50) {
              textColor = "text-red-600";
            } else if (score < 75) {
              textColor = "text-amber-600";
            }

            return (
              <div
                className={`font-mono font-medium tabular-nums ${textColor}`}
              >
                {score}
              </div>
            );
          },
        },
        {
          id: "tier",
          accessorKey: "tier",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.tier || "Tier"}
            />
          ),
          cell: ({ row }) => {
            const tier = row.getValue("tier") as string;

            let badgeVariant: BadgeProps["variant"] = "default";
            switch (tier) {
              case "bronze":
                badgeVariant = "secondary";
                break;
              case "silver":
                badgeVariant = "outline";
                break;
              case "gold":
                badgeVariant = "default";
                break;
              case "platinum":
                badgeVariant = "destructive";
                break;
              default:
                badgeVariant = "outline";
            }

            return (
              <Badge variant={badgeVariant} className="capitalize">
                {tier}
              </Badge>
            );
          },
        },
        {
          id: "status",
          accessorKey: "status",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.status || "Status"}
            />
          ),
          cell: ({ row }) => (
            <DriverStatusBadge status={row.getValue("status")} />
          ),
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.created_at || "Created At"}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "actions",
          meta: {
            className: "w-[80px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TableActions table={table} i18n={i18n} />
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex items-center justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <span className="sr-only">Open menu</span>
                    <svg
                      width="15"
                      height="15"
                      viewBox="0 0 15 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                    >
                      <path
                        d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z"
                        fill="currentColor"
                        fillRule="evenodd"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() =>
                      navigate(`/app/console/drivers/${row.original.id}/edit`)
                    }
                  >
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  {onDelete && (
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => onDelete(row.original.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              <Button variant="ghost" size="sm" asChild className="ml-2">
                <Link to={tableLinks.drivers.replace("[id]", row.original.id)}>
                  View
                </Link>
              </Button>
            </div>
          ),
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
