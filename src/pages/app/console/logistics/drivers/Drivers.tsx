"use client";

import { useState } from "react";

import { useListDrivers } from "@/api/drivers/use-list-drivers";
import {
  useSearchFilterValue,
  useSearchPagination,
  useSearchTextValue,
} from "@/components/search";
import { useToast } from "@/hooks/use-toast";
import { ConsoleDriversPage } from "@/pages/app/console/logistics/drivers/ConsoleDriversPage";
import { supabase } from "@/supabase/client";
import { Enums } from "@/supabase/types";

type DriverStatus = Enums<"driver_status">;

const i18n = {
  en: {
    toast: {
      deleteSuccess: "Driver deleted successfully.",
      deleteError: "Failed to delete driver. Please try again.",
    },
  },
};

export default function DriversPage() {
  const { toast } = useToast();
  const [deleteDriverId, setDeleteDriverId] = useState<string | null>(null);
  const [isDeletingDriver, setIsDeletingDriver] = useState(false);

  // Set up search hooks
  const pagination = useSearchPagination({
    group: "driver",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });
  const driversQuery = useSearchTextValue("driver");
  const driverStatus = useSearchFilterValue<DriverStatus>("status", "driver");

  // Use the hook with search parameters
  const {
    data: drivers,
    isLoading,
    error,
    refetch,
  } = useListDrivers({
    pageIndex: pagination.pagination.pageIndex,
    pageSize: pagination.pagination.pageSize,
    search: driversQuery,
    status: driverStatus,
  });

  const handleDelete = async (driverId: string) => {
    setIsDeletingDriver(true);
    try {
      const { error } = await supabase
        .from("drivers")
        .delete()
        .eq("id", driverId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.deleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.deleteSuccess,
        });
        setDeleteDriverId(null);
        refetch();
      }
    } finally {
      setIsDeletingDriver(false);
    }
  };

  // Calculate driver summary from the current data
  const driverSummary = drivers
    ? {
        totalDrivers: drivers.total,
        activeDrivers: drivers.items.filter((d) => d.status === "active")
          .length,
        inactiveDrivers: drivers.items.filter((d) => d.status === "inactive")
          .length,
        pendingDrivers: drivers.items.filter((d) => d.status === "suspended")
          .length,
        newThisMonth: drivers.items.filter((d) => {
          const createdAt = new Date(d.created_at);
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          return createdAt >= startOfMonth;
        }).length,
      }
    : undefined;

  // Handler functions for driver management
  const handleCreateDriver = () => {
    // Navigate to create driver page
    window.location.href = "/app/console/drivers/create";
  };

  const handleEditDriver = (driverId: string) => {
    window.location.href = `/app/console/drivers/${driverId}/edit`;
  };

  const handleViewDriver = (driverId: string) => {
    window.location.href = `/app/console/drivers/${driverId}`;
  };

  const handleToggleDriverStatus = async (
    driverId: string,
    newStatus: DriverStatus,
  ) => {
    try {
      const { error } = await supabase
        .from("drivers")
        .update({ status: newStatus })
        .eq("id", driverId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to update driver status. Please try again.",
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: "Driver status updated successfully.",
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating driver status:", error);
    }
  };

  const handleBulkDelete = async (driverIds: string[]) => {
    setIsDeletingDriver(true);
    try {
      const { error } = await supabase
        .from("drivers")
        .delete()
        .in("id", driverIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to delete drivers. Please try again.",
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: `${driverIds.length} driver(s) deleted successfully.`,
        });
        refetch();
      }
    } finally {
      setIsDeletingDriver(false);
    }
  };

  const handleBulkStatusChange = async (
    driverIds: string[],
    newStatus: DriverStatus,
  ) => {
    try {
      const { error } = await supabase
        .from("drivers")
        .update({ status: newStatus })
        .in("id", driverIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to update driver status. Please try again.",
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: `${driverIds.length} driver(s) status updated successfully.`,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating driver status:", error);
    }
  };

  return (
    <ConsoleDriversPage
      // Driver list data and loading states
      drivers={drivers || null}
      isLoadingDrivers={isLoading}
      driversError={error}
      // Search and filter state
      searchQuery={driversQuery}
      onSearchQueryChange={(query: string) => {
        // This would be handled by the search hook internally
      }}
      driverStatus={driverStatus}
      onDriverStatusChange={(status: DriverStatus | undefined) => {
        // This would be handled by the filter hook internally
      }}
      // Pagination state
      pagination={{
        pageIndex: pagination.pagination.pageIndex,
        pageSize: pagination.pagination.pageSize,
        setPageIndex: (pageIndex: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageIndex,
          }));
        },
        setPageSize: (pageSize: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageSize,
          }));
        },
        total: drivers?.total || 0,
      }}
      // Delete functionality
      deleteDriverId={deleteDriverId}
      setDeleteDriverId={setDeleteDriverId}
      onDeleteDriver={handleDelete}
      isDeletingDriver={isDeletingDriver}
      // Driver management actions
      onCreateDriver={handleCreateDriver}
      onEditDriver={handleEditDriver}
      onViewDriver={handleViewDriver}
      onToggleDriverStatus={handleToggleDriverStatus}
      // Driver analytics and summary
      driverSummary={driverSummary}
      // Bulk operations
      selectedDrivers={[]}
      onSelectDriver={(driverId: string) => {
        // TODO: Implement driver selection logic
      }}
      onSelectAllDrivers={(selected: boolean) => {
        // TODO: Implement select all logic
      }}
      onBulkDelete={handleBulkDelete}
      onBulkStatusChange={handleBulkStatusChange}
      // Organization context
      organizationId={undefined}
      canManageDrivers={true}
      canDeleteDrivers={true}
      canCreateDrivers={true}
    />
  );
}
