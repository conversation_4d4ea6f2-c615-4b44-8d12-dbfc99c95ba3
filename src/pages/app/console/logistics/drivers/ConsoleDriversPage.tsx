import { User } from "lucide-react";
import { Link } from "react-router";

import type { DriversQueryResult } from "@/pages/app/console/logistics/drivers/ListDrivers";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListDrivers from "@/pages/app/console/logistics/drivers/ListDrivers";
import { Enums } from "@/supabase/types";

type DriverStatus = Enums<"driver_status">;

export interface Driver {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  status: DriverStatus;
  license_number?: string;
  license_expiry?: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  avatar_url?: string;
}

export interface DriversListResponse {
  items: Driver[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface DriverSearchParams {
  pageIndex: number;
  pageSize: number;
  search?: string;
  status?: DriverStatus;
}

export interface DeleteDriverHandler {
  (driverId: string): Promise<void>;
}

export interface ConsoleDriversPageProps {
  // Driver list data and loading states
  drivers: DriversQueryResult | null;
  isLoadingDrivers: boolean;
  driversError: Error | null;

  // Search and filter state
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  driverStatus: DriverStatus | undefined;
  onDriverStatusChange: (status: DriverStatus | undefined) => void;

  // Pagination state
  pagination: {
    pageIndex: number;
    pageSize: number;
    setPageIndex: (pageIndex: number) => void;
    setPageSize: (pageSize: number) => void;
  };

  // Delete functionality
  deleteDriverId: string | null;
  setDeleteDriverId: (id: string | null) => void;
  onDeleteDriver: DeleteDriverHandler;
  isDeletingDriver: boolean;

  // Driver management actions
  onCreateDriver?: () => void;
  onEditDriver?: (driverId: string) => void;
  onViewDriver?: (driverId: string) => void;
  onToggleDriverStatus?: (driverId: string, newStatus: DriverStatus) => void;

  // Driver analytics and summary
  driverSummary?: {
    totalDrivers: number;
    activeDrivers: number;
    inactiveDrivers: number;
    pendingDrivers: number;
    newThisMonth: number;
  };

  // Bulk operations
  selectedDrivers?: string[];
  onSelectDriver?: (driverId: string) => void;
  onSelectAllDrivers?: (selected: boolean) => void;
  onBulkDelete?: (driverIds: string[]) => Promise<void>;
  onBulkStatusChange?: (
    driverIds: string[],
    newStatus: DriverStatus,
  ) => Promise<void>;

  // Organization context
  organizationId?: string;
  canManageDrivers?: boolean;
  canDeleteDrivers?: boolean;
  canCreateDrivers?: boolean;
}

const i18n = {
  en: {
    title: "Drivers",
    addButton: "Add Driver",
    deleteDialog: {
      title: "Are you sure?",
      description:
        "This action cannot be undone. This will permanently delete the driver.",
      cancel: "Cancel",
      confirm: "Delete",
    },
    toast: {
      deleteSuccess: "Driver deleted successfully.",
      deleteError: "Failed to delete driver. Please try again.",
    },
    summary: {
      total: "Total Drivers",
      active: "Active",
      inactive: "Inactive",
      pending: "Pending",
      newThisMonth: "New This Month",
    },
    search: {
      placeholder: "Search drivers...",
      noResults: "No drivers found",
      filtering: "Filtering by status",
    },
    actions: {
      view: "View Details",
      edit: "Edit Driver",
      delete: "Delete Driver",
      activate: "Activate",
      deactivate: "Deactivate",
      bulkDelete: "Delete Selected",
      bulkActivate: "Activate Selected",
      bulkDeactivate: "Deactivate Selected",
    },
    status: {
      active: "Active",
      inactive: "Inactive",
      pending: "Pending",
      suspended: "Suspended",
    },
  },
  links: {
    create: "/app/console/drivers/create",
    view: (id: string) => `/app/console/drivers/${id}`,
    edit: (id: string) => `/app/console/drivers/${id}/edit`,
  },
};

export const ConsoleDriversPage = ({
  drivers,
  isLoadingDrivers,
  driversError,
  searchQuery,
  onSearchQueryChange,
  driverStatus,
  onDriverStatusChange,
  pagination,
  deleteDriverId,
  setDeleteDriverId,
  onDeleteDriver,
  isDeletingDriver,
  onCreateDriver,
  onEditDriver,
  onViewDriver,
  onToggleDriverStatus,
  driverSummary,
  selectedDrivers = [],
  onSelectDriver,
  onSelectAllDrivers,
  onBulkDelete,
  onBulkStatusChange,
  organizationId,
  canManageDrivers = true,
  canDeleteDrivers = true,
  canCreateDrivers = true,
}: ConsoleDriversPageProps) => {
  const hasSelectedDrivers = selectedDrivers.length > 0;

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <User className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          {hasSelectedDrivers && (
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                {selectedDrivers.length} selected
              </span>
              {onBulkDelete && canDeleteDrivers && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onBulkDelete(selectedDrivers)}
                  disabled={isDeletingDriver}
                >
                  {i18n.en.actions.bulkDelete}
                </Button>
              )}
              {onBulkStatusChange && canManageDrivers && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      onBulkStatusChange(selectedDrivers, "active")
                    }
                    disabled={isDeletingDriver}
                  >
                    {i18n.en.actions.bulkActivate}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      onBulkStatusChange(selectedDrivers, "inactive")
                    }
                    disabled={isDeletingDriver}
                  >
                    {i18n.en.actions.bulkDeactivate}
                  </Button>
                </>
              )}
            </div>
          )}
          {canCreateDrivers && (
            <Button asChild disabled={isLoadingDrivers}>
              <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
            </Button>
          )}
        </div>
      </div>

      {/* Driver Summary Cards */}
      {driverSummary && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold">
              {driverSummary.totalDrivers}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.total}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-green-600">
              {driverSummary.activeDrivers}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.active}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-gray-600">
              {driverSummary.inactiveDrivers}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.inactive}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {driverSummary.pendingDrivers}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.pending}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-blue-600">
              {driverSummary.newThisMonth}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.newThisMonth}
            </div>
          </div>
        </div>
      )}

      {/* Error Alert */}
      {driversError && <ErrorAlert error={driversError} />}

      {/* Drivers List */}
      <ListDrivers
        loading={isLoadingDrivers}
        drivers={drivers}
        onDelete={setDeleteDriverId}
      />

      {/* Delete Confirmation Dialog */}
      <DialogConfirmation
        open={!!deleteDriverId}
        onOpenChange={(open) => {
          if (!open) setDeleteDriverId(null);
        }}
        onClick={() => {
          if (deleteDriverId) {
            return onDeleteDriver(deleteDriverId);
          }
          return Promise.resolve();
        }}
        title={i18n.en.deleteDialog.title}
        description={i18n.en.deleteDialog.description}
        action={i18n.en.deleteDialog.confirm}
        cancel={i18n.en.deleteDialog.cancel}
        useTrigger={false}
      />
    </div>
  );
};
