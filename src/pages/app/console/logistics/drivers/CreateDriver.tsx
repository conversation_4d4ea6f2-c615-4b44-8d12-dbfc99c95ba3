import { Users } from "lucide-react";
import { useNavigate } from "react-router";

import { useCreateDriver } from "@/api/drivers/use-create-driver";
import DriverForm, { DriverFormValues } from "@/components/forms/DriverForm";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "Create Driver",
    toasts: {
      success: "Driver created successfully",
      error: "Failed to create driver",
    },
  },
};

export default function CreateDriverPage() {
  const navigate = useNavigate();

  const createDriver = useCreateDriver({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/drivers/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (values: DriverFormValues) => {
    // Map form values to the API expected format
    const driverData = {
      first_name: values.first_name,
      last_name: values.last_name,
      email: values.email,
      phone_number: values.phone_number,
    };

    createDriver.mutate(driverData);
  };

  const handleCancel = () => {
    navigate("/app/console/drivers");
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Users className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>

      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <DriverForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={createDriver.isPending}
        />
      </div>
    </div>
  );
}
