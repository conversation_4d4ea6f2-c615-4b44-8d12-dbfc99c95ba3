import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleDriversPage } from "./ConsoleDriversPage";

const meta: Meta<typeof ConsoleDriversPage> = {
  title: "Pages/Console/Logistics/Drivers",
  component: ConsoleDriversPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/drivers" },
    }),
    docs: {
      description: {
        component: `
The ConsoleDriversPage component provides a comprehensive interface for managing drivers within the console application.

## Features
- **Driver List Management**: Display drivers with pagination, search, and filtering
- **CRUD Operations**: Create, read, update, and delete driver records
- **Status Management**: Activate, deactivate, and suspend drivers
- **Bulk Operations**: Select and manage multiple drivers simultaneously
- **Search & Filter**: Real-time search with status-based filtering
- **Analytics Dashboard**: Driver summary statistics and metrics
- **Delete Confirmation**: Safe deletion with confirmation dialogs

## Usage
This component follows the established console pattern with a presentation component that receives all data and handlers as props, ensuring clean separation of concerns between data management and UI rendering.
        `,
      },
    },
  },
  args: {
    // Default props
    isLoadingDrivers: false,
    driversError: null,
    searchQuery: "",
    onSearchQueryChange: fn(),
    driverStatus: undefined,
    onDriverStatusChange: fn(),
    pagination: {
      pageIndex: 0,
      pageSize: 10,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    deleteDriverId: null,
    setDeleteDriverId: fn(),
    onDeleteDriver: fn(),
    isDeletingDriver: false,
    onCreateDriver: fn(),
    onEditDriver: fn(),
    onViewDriver: fn(),
    onToggleDriverStatus: fn(),
    selectedDrivers: [],
    onSelectDriver: fn(),
    onSelectAllDrivers: fn(),
    onBulkDelete: fn(),
    onBulkStatusChange: fn(),
    organizationId: "org_1",
    canManageDrivers: true,
    canDeleteDrivers: true,
    canCreateDrivers: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock driver data for stories
const mockDrivers = {
  items: [
    {
      id: "drv_1",
      first_name: "John",
      last_name: "Smith",
      email: "<EMAIL>",
      phone_number: "******-0101",
      status: "active" as const,
      score: 92,
      tier: "gold",
      created_at: "2024-01-15T10:30:00Z",
      verified_at: "2024-01-15T14:30:00Z",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      location_id: "loc_1",
      user_id: "user_1",
    },
    {
      id: "drv_2",
      first_name: "Sarah",
      last_name: "Johnson",
      email: "<EMAIL>",
      phone_number: "******-0102",
      status: "active" as const,
      score: 87,
      tier: "silver",
      created_at: "2024-02-20T08:15:00Z",
      verified_at: "2024-02-20T12:15:00Z",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b332c5e0?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      location_id: "loc_2",
      user_id: "user_2",
    },
    {
      id: "drv_3",
      first_name: "Michael",
      last_name: "Brown",
      email: "<EMAIL>",
      phone_number: "******-0103",
      status: "inactive" as const,
      score: 74,
      tier: "bronze",
      created_at: "2024-03-10T16:45:00Z",
      verified_at: "2024-03-10T18:45:00Z",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      location_id: "loc_3",
      user_id: "user_3",
    },
    {
      id: "drv_4",
      first_name: "Emily",
      last_name: "Davis",
      email: "<EMAIL>",
      phone_number: "******-0104",
      status: "suspended" as const,
      score: 45,
      tier: "bronze",
      created_at: "2024-04-05T09:20:00Z",
      verified_at: "2024-04-05T11:20:00Z",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      location_id: "loc_4",
      user_id: "user_4",
    },
    {
      id: "drv_5",
      first_name: "David",
      last_name: "Wilson",
      email: "<EMAIL>",
      phone_number: "******-0105",
      status: "active" as const,
      score: 98,
      tier: "platinum",
      created_at: "2024-05-12T13:30:00Z",
      verified_at: "2024-05-12T15:30:00Z",
      avatar:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      location_id: "loc_5",
      user_id: "user_5",
    },
  ],
  total: 5,
};

const driverSummary = {
  totalDrivers: 5,
  activeDrivers: 3,
  inactiveDrivers: 1,
  pendingDrivers: 1,
  newThisMonth: 2,
};

export const Default: Story = {
  args: {
    drivers: mockDrivers,
    driverSummary,
  },
};

export const Loading: Story = {
  args: {
    isLoadingDrivers: true,
    drivers: null,
    driverSummary: undefined,
  },
};

export const EmptyState: Story = {
  args: {
    drivers: {
      items: [],
      total: 0,
    },
    driverSummary: {
      totalDrivers: 0,
      activeDrivers: 0,
      inactiveDrivers: 0,
      pendingDrivers: 0,
      newThisMonth: 0,
    },
  },
};

export const SearchResults: Story = {
  args: {
    drivers: {
      items: mockDrivers.items.filter((d) => d.first_name.includes("John")),
      total: 1,
    },
    searchQuery: "John",
    driverSummary: {
      totalDrivers: 1,
      activeDrivers: 1,
      inactiveDrivers: 0,
      pendingDrivers: 0,
      newThisMonth: 0,
    },
  },
};

export const FilteredByActiveStatus: Story = {
  args: {
    drivers: {
      items: mockDrivers.items.filter((d) => d.status === "active"),
      total: 3,
    },
    driverStatus: "active",
    driverSummary: {
      totalDrivers: 3,
      activeDrivers: 3,
      inactiveDrivers: 0,
      pendingDrivers: 0,
      newThisMonth: 2,
    },
  },
};

export const FilteredByInactiveStatus: Story = {
  args: {
    drivers: {
      items: mockDrivers.items.filter((d) => d.status === "inactive"),
      total: 1,
    },
    driverStatus: "inactive",
    driverSummary: {
      totalDrivers: 1,
      activeDrivers: 0,
      inactiveDrivers: 1,
      pendingDrivers: 0,
      newThisMonth: 0,
    },
  },
};

export const FilteredBySuspendedStatus: Story = {
  args: {
    drivers: {
      items: mockDrivers.items.filter((d) => d.status === "suspended"),
      total: 1,
    },
    driverStatus: "suspended",
    driverSummary: {
      totalDrivers: 1,
      activeDrivers: 0,
      inactiveDrivers: 0,
      pendingDrivers: 1,
      newThisMonth: 0,
    },
  },
};

export const PaginatedResults: Story = {
  args: {
    drivers: {
      items: mockDrivers.items.slice(0, 3),
      total: 25,
    },
    pagination: {
      pageIndex: 0,
      pageSize: 3,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    driverSummary: {
      totalDrivers: 25,
      activeDrivers: 18,
      inactiveDrivers: 5,
      pendingDrivers: 2,
      newThisMonth: 7,
    },
  },
};

export const DeleteConfirmation: Story = {
  args: {
    drivers: mockDrivers,
    deleteDriverId: "drv_3",
    driverSummary,
  },
};

export const DeletingDriver: Story = {
  args: {
    drivers: mockDrivers,
    deleteDriverId: "drv_3",
    isDeletingDriver: true,
    driverSummary,
  },
};

export const BulkOperationsActive: Story = {
  args: {
    drivers: mockDrivers,
    selectedDrivers: ["drv_1", "drv_2", "drv_3"],
    driverSummary,
  },
};

export const MixedStatusDrivers: Story = {
  args: {
    drivers: {
      items: [
        ...mockDrivers.items,
        {
          id: "drv_6",
          first_name: "Lisa",
          last_name: "Anderson",
          email: "<EMAIL>",
          phone_number: "******-0106",
          status: "active" as const,
          score: 89,
          tier: "gold",
          created_at: "2024-06-01T10:00:00Z",
          verified_at: "2024-06-01T12:00:00Z",
          avatar:
            "https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
          location_id: "loc_6",
          user_id: "user_6",
        },
        {
          id: "drv_7",
          first_name: "Robert",
          last_name: "Taylor",
          email: "<EMAIL>",
          phone_number: "******-0107",
          status: "inactive" as const,
          score: 67,
          tier: "bronze",
          created_at: "2024-01-30T14:15:00Z",
          verified_at: "2024-01-30T16:15:00Z",
          avatar:
            "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
          location_id: "loc_7",
          user_id: "user_7",
        },
      ],
      total: 7,
    },
    driverSummary: {
      totalDrivers: 7,
      activeDrivers: 4,
      inactiveDrivers: 2,
      pendingDrivers: 1,
      newThisMonth: 3,
    },
  },
};

export const NewOrganization: Story = {
  args: {
    drivers: {
      items: [
        {
          id: "drv_new_1",
          first_name: "Alex",
          last_name: "Martinez",
          email: "<EMAIL>",
          phone_number: "******-0201",
          status: "active" as const,
          score: 85,
          tier: "silver",
          created_at: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 2 days ago
          verified_at: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000,
          ).toISOString(),
          avatar:
            "https://images.unsplash.com/photo-1502764613149-7f1d229e230f?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
          location_id: "loc_new_1",
          user_id: "user_new_1",
        },
        {
          id: "drv_new_2",
          first_name: "Jessica",
          last_name: "Lee",
          email: "<EMAIL>",
          phone_number: "******-0202",
          status: "active" as const,
          score: 91,
          tier: "gold",
          created_at: new Date(
            Date.now() - 1 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 1 day ago
          verified_at: new Date(
            Date.now() - 1 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000,
          ).toISOString(),
          avatar:
            "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
          location_id: "loc_new_2",
          user_id: "user_new_2",
        },
      ],
      total: 2,
    },
    driverSummary: {
      totalDrivers: 2,
      activeDrivers: 2,
      inactiveDrivers: 0,
      pendingDrivers: 0,
      newThisMonth: 2,
    },
  },
};

export const ErrorState: Story = {
  args: {
    drivers: null,
    driversError: new Error(
      "Failed to load drivers. Please check your network connection and try again.",
    ),
    driverSummary: undefined,
  },
};

export const HighVolumeDrivers: Story = {
  args: {
    drivers: {
      items: mockDrivers.items,
      total: 1247,
    },
    pagination: {
      pageIndex: 12,
      pageSize: 50,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    driverSummary: {
      totalDrivers: 1247,
      activeDrivers: 892,
      inactiveDrivers: 234,
      pendingDrivers: 121,
      newThisMonth: 45,
    },
  },
};

export const ReadOnlyView: Story = {
  args: {
    drivers: mockDrivers,
    driverSummary,
    canManageDrivers: false,
    canDeleteDrivers: false,
    canCreateDrivers: false,
  },
};

export const LimitedPermissions: Story = {
  args: {
    drivers: mockDrivers,
    driverSummary,
    canManageDrivers: true,
    canDeleteDrivers: false,
    canCreateDrivers: true,
  },
};
