import { useQuery } from "@tanstack/react-query";
import { MapPin } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import type { LocationFormValues } from "@/components/forms/LocationForm";

import { useUpdateLocation } from "@/api/locations/use-update-location";
import LocationForm from "@/components/forms/LocationForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Location",
    toasts: {
      success: "Location updated successfully",
      error: "Failed to update location",
    },
    loading: "Loading location...",
    error: "Failed to load location",
    notFound: "Location not found",
    backButton: "Back to Locations",
  },
};

// Hook to get a single location
function useGetLocation(id: string) {
  return useQuery({
    queryKey: ["locations", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("locations")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditLocationPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: location, isLoading, error } = useGetLocation(id!);

  const updateLocation = useUpdateLocation({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/locations/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: LocationFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const locationData = {
      id,
      formatted: values.address.formatted,
      street: values.address.street,
      city: values.address.city,
      state: values.address.state,
      country: values.address.country,
      latitude: values.address.latitude,
      longitude: values.address.longitude,
      type: values.type,
    };

    updateLocation.mutate(locationData);
  };

  const handleCancel = () => {
    navigate(`/app/console/locations/${id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <MapPin className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <MapPin className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <ErrorAlert error={error} />
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/locations")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  if (!location) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <MapPin className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/locations")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: LocationFormValues = {
    id: location.id,
    address: {
      formatted: location.formatted,
      street: location.street || "",
      city: location.city || "",
      neighborhood: location.neighborhood || null,
      postal: location.postal || "",
      state: location.state || "",
      country: location.country,
      latitude: location.latitude,
      longitude: location.longitude,
      mapbox_id: "existing",
    },
    type: location.type,
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <MapPin className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <LocationForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={updateLocation.isPending}
        />
      </div>
    </div>
  );
}
