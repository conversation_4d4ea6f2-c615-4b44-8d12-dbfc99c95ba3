import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleLocationsPage } from "./ConsoleLocationsPage";

const meta: Meta<typeof ConsoleLocationsPage> = {
  title: "Pages/Console/Logistics/Locations",
  component: ConsoleLocationsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/locations" },
    }),
    docs: {
      description: {
        component: `
The ConsoleLocationsPage component provides a comprehensive interface for managing locations within the console application.

## Features
- **Location List Management**: Display locations with pagination, search, and filtering
- **CRUD Operations**: Create, read, update, and delete location records
- **Type Management**: Categorize locations by type (warehouse, distribution center, retail, etc.)
- **Geographic Management**: Handle locations across different regions, states, and countries
- **Bulk Operations**: Select and manage multiple locations simultaneously
- **Search & Filter**: Real-time search with type-based and geographic filtering
- **Analytics Dashboard**: Location summary statistics and metrics
- **Address Verification**: Location validation and geocoding capabilities
- **Delete Confirmation**: Safe deletion with confirmation dialogs

## Usage
This component follows the established console pattern with a presentation component that receives all data and handlers as props, ensuring clean separation of concerns between data management and UI rendering.
        `,
      },
    },
  },
  args: {
    // Default props
    isLoadingLocations: false,
    locationsError: null,
    searchQuery: "",
    onSearchQueryChange: fn(),
    locationType: undefined,
    onLocationTypeChange: fn(),
    pagination: {
      pageIndex: 0,
      pageSize: 10,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    deleteLocationId: null,
    setDeleteLocationId: fn(),
    onDeleteLocation: fn(),
    isDeletingLocation: false,
    onCreateLocation: fn(),
    onEditLocation: fn(),
    onViewLocation: fn(),
    onUpdateLocationType: fn(),
    onToggleLocationStatus: fn(),
    onVerifyLocation: fn(),
    selectedLocations: [],
    onSelectLocation: fn(),
    onSelectAllLocations: fn(),
    onBulkDelete: fn(),
    onBulkTypeChange: fn(),
    onBulkStatusToggle: fn(),
    onFilterByRegion: fn(),
    onFilterByState: fn(),
    onFilterByCountry: fn(),
    organizationId: "org_1",
    canManageLocations: true,
    canDeleteLocations: true,
    canCreateLocations: true,
    canVerifyLocations: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock location data for stories matching actual database schema
const mockLocations = {
  items: [
    {
      id: "loc_1",
      formatted: "123 Warehouse Drive, Los Angeles, CA 90210, USA",
      street: "123 Warehouse Drive",
      city: "Los Angeles",
      state: "CA",
      country: "USA",
      postal: "90210",
      neighborhood: "Downtown",
      suite: "",
      latitude: 34.0522,
      longitude: -118.2437,
      type: "warehouse" as const,
      organization_id: "org_1",
      driver_id: null,
      created_at: "2024-01-15T10:30:00Z",
    },
    {
      id: "loc_2",
      formatted: "456 Distribution Way, Chicago, IL 60601, USA",
      street: "456 Distribution Way",
      city: "Chicago",
      state: "IL",
      country: "USA",
      postal: "60601",
      neighborhood: "Loop",
      suite: "",
      latitude: 41.8781,
      longitude: -87.6298,
      type: "distribution_center" as const,
      organization_id: "org_1",
      driver_id: null,
      created_at: "2024-02-20T08:15:00Z",
    },
    {
      id: "loc_3",
      formatted: "789 Retail Plaza, New York, NY 10001, USA",
      street: "789 Retail Plaza",
      city: "New York",
      state: "NY",
      country: "USA",
      postal: "10001",
      neighborhood: "Manhattan",
      suite: "Suite 100",
      latitude: 40.7128,
      longitude: -74.006,
      type: "retail" as const,
      organization_id: "org_1",
      driver_id: null,
      created_at: "2024-03-10T16:45:00Z",
    },
    {
      id: "loc_4",
      formatted: "321 Commercial Boulevard, Miami, FL 33101, USA",
      street: "321 Commercial Boulevard",
      city: "Miami",
      state: "FL",
      country: "USA",
      postal: "33101",
      neighborhood: "Brickell",
      suite: "",
      latitude: 25.7617,
      longitude: -80.1918,
      type: "commercial" as const,
      organization_id: "org_1",
      driver_id: null,
      created_at: "2024-04-05T09:20:00Z",
    },
    {
      id: "loc_5",
      formatted: "654 Industrial Park, Houston, TX 77002, USA",
      street: "654 Industrial Park",
      city: "Houston",
      state: "TX",
      country: "USA",
      postal: "77002",
      neighborhood: "Industrial District",
      suite: "",
      latitude: 29.7604,
      longitude: -95.3698,
      type: "industrial" as const,
      organization_id: "org_1",
      driver_id: null,
      created_at: "2024-05-12T13:30:00Z",
    },
    {
      id: "loc_6",
      formatted: "987 Government Center, Washington, DC 20001, USA",
      street: "987 Government Center",
      city: "Washington",
      state: "DC",
      country: "USA",
      postal: "20001",
      neighborhood: "Federal Triangle",
      suite: "",
      latitude: 38.9072,
      longitude: -77.0369,
      type: "government" as const,
      organization_id: "org_1",
      driver_id: null,
      created_at: "2024-06-01T10:00:00Z",
    },
  ],
  total: 6,
};

const locationSummary = {
  totalLocations: 6,
  warehouseLocations: 1,
  distributionCenterLocations: 1,
  retailLocations: 1,
  commercialLocations: 1,
  verifiedLocations: 5,
  activeLocations: 6,
  newThisMonth: 2,
};

export const Default: Story = {
  args: {
    locations: mockLocations,
    locationSummary,
  },
};

export const Loading: Story = {
  args: {
    isLoadingLocations: true,
    locations: null,
    locationSummary: undefined,
  },
};

export const EmptyState: Story = {
  args: {
    locations: {
      items: [],
      total: 0,
    },
    locationSummary: {
      totalLocations: 0,
      warehouseLocations: 0,
      distributionCenterLocations: 0,
      retailLocations: 0,
      commercialLocations: 0,
      verifiedLocations: 0,
      activeLocations: 0,
      newThisMonth: 0,
    },
  },
};

export const SearchResults: Story = {
  args: {
    locations: {
      items: mockLocations.items.filter((l) => l.city.includes("Los Angeles")),
      total: 1,
    },
    searchQuery: "Los Angeles",
    locationSummary: {
      totalLocations: 1,
      warehouseLocations: 1,
      distributionCenterLocations: 0,
      retailLocations: 0,
      commercialLocations: 0,
      verifiedLocations: 1,
      activeLocations: 1,
      newThisMonth: 0,
    },
  },
};

export const FilteredByWarehouseType: Story = {
  args: {
    locations: {
      items: mockLocations.items.filter((l) => l.type === "warehouse"),
      total: 1,
    },
    locationType: "warehouse",
    locationSummary: {
      totalLocations: 1,
      warehouseLocations: 1,
      distributionCenterLocations: 0,
      retailLocations: 0,
      commercialLocations: 0,
      verifiedLocations: 1,
      activeLocations: 1,
      newThisMonth: 0,
    },
  },
};

export const FilteredByDistributionCenterType: Story = {
  args: {
    locations: {
      items: mockLocations.items.filter(
        (l) => l.type === "distribution_center",
      ),
      total: 1,
    },
    locationType: "distribution_center",
    locationSummary: {
      totalLocations: 1,
      warehouseLocations: 0,
      distributionCenterLocations: 1,
      retailLocations: 0,
      commercialLocations: 0,
      verifiedLocations: 1,
      activeLocations: 1,
      newThisMonth: 0,
    },
  },
};

export const FilteredByRetailType: Story = {
  args: {
    locations: {
      items: mockLocations.items.filter((l) => l.type === "retail"),
      total: 1,
    },
    locationType: "retail",
    locationSummary: {
      totalLocations: 1,
      warehouseLocations: 0,
      distributionCenterLocations: 0,
      retailLocations: 1,
      commercialLocations: 0,
      verifiedLocations: 1,
      activeLocations: 1,
      newThisMonth: 0,
    },
  },
};

export const PaginatedResults: Story = {
  args: {
    locations: {
      items: mockLocations.items.slice(0, 3),
      total: 25,
    },
    pagination: {
      pageIndex: 0,
      pageSize: 3,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    locationSummary: {
      totalLocations: 25,
      warehouseLocations: 8,
      distributionCenterLocations: 7,
      retailLocations: 6,
      commercialLocations: 4,
      verifiedLocations: 20,
      activeLocations: 23,
      newThisMonth: 4,
    },
  },
};

export const DeleteConfirmation: Story = {
  args: {
    locations: mockLocations,
    deleteLocationId: "loc_3",
    locationSummary,
  },
};

export const DeletingLocation: Story = {
  args: {
    locations: mockLocations,
    deleteLocationId: "loc_3",
    isDeletingLocation: true,
    locationSummary,
  },
};

export const BulkOperationsActive: Story = {
  args: {
    locations: mockLocations,
    selectedLocations: ["loc_1", "loc_2", "loc_3"],
    locationSummary,
  },
};

export const CrossCountryLocations: Story = {
  args: {
    locations: {
      items: [
        ...mockLocations.items,
        {
          id: "loc_7",
          formatted: "100 Maple Street, Toronto, ON M5H 2N2, Canada",
          street: "100 Maple Street",
          city: "Toronto",
          state: "ON",
          country: "Canada",
          postal: "M5H 2N2",
          neighborhood: "Downtown",
          suite: "",
          latitude: 43.6532,
          longitude: -79.3832,
          type: "warehouse" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-06-15T14:30:00Z",
        },
        {
          id: "loc_8",
          formatted: "42 Oxford Street, London, W1D 1LZ, United Kingdom",
          street: "42 Oxford Street",
          city: "London",
          state: "England",
          country: "United Kingdom",
          postal: "W1D 1LZ",
          neighborhood: "West End",
          suite: "",
          latitude: 51.5074,
          longitude: -0.1278,
          type: "retail" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-06-20T11:15:00Z",
        },
      ],
      total: 8,
    },
    locationSummary: {
      totalLocations: 8,
      warehouseLocations: 2,
      distributionCenterLocations: 1,
      retailLocations: 2,
      commercialLocations: 1,
      verifiedLocations: 6,
      activeLocations: 8,
      newThisMonth: 4,
    },
  },
};

export const NewOrganization: Story = {
  args: {
    locations: {
      items: [
        {
          id: "loc_new_1",
          formatted: "500 Startup Plaza, Austin, TX 78701, USA",
          street: "500 Startup Plaza",
          city: "Austin",
          state: "TX",
          country: "USA",
          postal: "78701",
          neighborhood: "Downtown",
          suite: "Floor 15",
          latitude: 30.2672,
          longitude: -97.7431,
          type: "commercial" as const,
          organization_id: "org_new",
          driver_id: null,
          created_at: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 2 days ago
        },
        {
          id: "loc_new_2",
          formatted: "200 Innovation Drive, Seattle, WA 98101, USA",
          street: "200 Innovation Drive",
          city: "Seattle",
          state: "WA",
          country: "USA",
          postal: "98101",
          neighborhood: "South Lake Union",
          suite: "",
          latitude: 47.6062,
          longitude: -122.3321,
          type: "warehouse" as const,
          organization_id: "org_new",
          driver_id: null,
          created_at: new Date(
            Date.now() - 1 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 1 day ago
        },
      ],
      total: 2,
    },
    locationSummary: {
      totalLocations: 2,
      warehouseLocations: 1,
      distributionCenterLocations: 0,
      retailLocations: 0,
      commercialLocations: 1,
      verifiedLocations: 2,
      activeLocations: 2,
      newThisMonth: 2,
    },
  },
};

export const HighVolumeLocations: Story = {
  args: {
    locations: {
      items: mockLocations.items,
      total: 2543,
    },
    pagination: {
      pageIndex: 47,
      pageSize: 50,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    locationSummary: {
      totalLocations: 2543,
      warehouseLocations: 425,
      distributionCenterLocations: 312,
      retailLocations: 896,
      commercialLocations: 634,
      verifiedLocations: 2104,
      activeLocations: 2398,
      newThisMonth: 87,
    },
  },
};

export const LocationTypesDistribution: Story = {
  args: {
    locations: {
      items: [
        {
          id: "loc_public_1",
          formatted: "750 Public Library Way, Denver, CO 80202, USA",
          street: "750 Public Library Way",
          city: "Denver",
          state: "CO",
          country: "USA",
          postal: "80202",
          neighborhood: "Capitol Hill",
          suite: "",
          latitude: 39.7392,
          longitude: -104.9903,
          type: "public" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-01-10T09:00:00Z",
        },
        {
          id: "loc_residential_1",
          formatted: "850 Residential Circle, Phoenix, AZ 85001, USA",
          street: "850 Residential Circle",
          city: "Phoenix",
          state: "AZ",
          country: "USA",
          postal: "85001",
          neighborhood: "Central Phoenix",
          suite: "",
          latitude: 33.4484,
          longitude: -112.074,
          type: "residential" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-02-15T14:30:00Z",
        },
        {
          id: "loc_other_1",
          formatted: "999 Other Location Ave, Portland, OR 97201, USA",
          street: "999 Other Location Ave",
          city: "Portland",
          state: "OR",
          country: "USA",
          postal: "97201",
          neighborhood: "Pearl District",
          suite: "",
          latitude: 45.5152,
          longitude: -122.6784,
          type: "other" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-03-20T16:45:00Z",
        },
      ],
      total: 3,
    },
    locationSummary: {
      totalLocations: 3,
      warehouseLocations: 0,
      distributionCenterLocations: 0,
      retailLocations: 0,
      commercialLocations: 0,
      verifiedLocations: 3,
      activeLocations: 3,
      newThisMonth: 1,
    },
  },
};

export const SearchAndFilter: Story = {
  args: {
    locations: {
      items: mockLocations.items.filter(
        (l) => l.type === "warehouse" && l.city.includes("Los Angeles"),
      ),
      total: 1,
    },
    searchQuery: "Los Angeles",
    locationType: "warehouse",
    locationSummary: {
      totalLocations: 1,
      warehouseLocations: 1,
      distributionCenterLocations: 0,
      retailLocations: 0,
      commercialLocations: 0,
      verifiedLocations: 1,
      activeLocations: 1,
      newThisMonth: 0,
    },
  },
};

export const GeographicCoverage: Story = {
  args: {
    locations: {
      items: [
        {
          id: "loc_west_1",
          formatted: "100 West Coast Logistics, San Francisco, CA 94102, USA",
          street: "100 West Coast Logistics",
          city: "San Francisco",
          state: "CA",
          country: "USA",
          postal: "94102",
          neighborhood: "SOMA",
          suite: "",
          latitude: 37.7749,
          longitude: -122.4194,
          type: "distribution_center" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-01-01T08:00:00Z",
        },
        {
          id: "loc_central_1",
          formatted: "200 Central Hub Drive, Kansas City, MO 64108, USA",
          street: "200 Central Hub Drive",
          city: "Kansas City",
          state: "MO",
          country: "USA",
          postal: "64108",
          neighborhood: "Crossroads",
          suite: "",
          latitude: 39.0997,
          longitude: -94.5786,
          type: "warehouse" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-02-01T09:00:00Z",
        },
        {
          id: "loc_east_1",
          formatted: "300 East Coast Terminal, Boston, MA 02101, USA",
          street: "300 East Coast Terminal",
          city: "Boston",
          state: "MA",
          country: "USA",
          postal: "02101",
          neighborhood: "Financial District",
          suite: "",
          latitude: 42.3601,
          longitude: -71.0589,
          type: "commercial" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-03-01T10:00:00Z",
        },
      ],
      total: 3,
    },
    locationSummary: {
      totalLocations: 3,
      warehouseLocations: 1,
      distributionCenterLocations: 1,
      retailLocations: 0,
      commercialLocations: 1,
      verifiedLocations: 3,
      activeLocations: 3,
      newThisMonth: 0,
    },
  },
};

export const UnverifiedLocations: Story = {
  args: {
    locations: {
      items: mockLocations.items.map((location) => ({
        ...location,
        latitude: 0,
        longitude: 0,
      })),
      total: 6,
    },
    locationSummary: {
      totalLocations: 6,
      warehouseLocations: 1,
      distributionCenterLocations: 1,
      retailLocations: 1,
      commercialLocations: 1,
      verifiedLocations: 0,
      activeLocations: 6,
      newThisMonth: 2,
    },
  },
};

export const ErrorState: Story = {
  args: {
    locations: null,
    locationsError: new Error(
      "Failed to load locations. Please check your network connection and try again.",
    ),
    locationSummary: undefined,
  },
};

export const ReadOnlyView: Story = {
  args: {
    locations: mockLocations,
    locationSummary,
    canManageLocations: false,
    canDeleteLocations: false,
    canCreateLocations: false,
    canVerifyLocations: false,
  },
};

export const LimitedPermissions: Story = {
  args: {
    locations: mockLocations,
    locationSummary,
    canManageLocations: true,
    canDeleteLocations: false,
    canCreateLocations: true,
    canVerifyLocations: false,
  },
};

export const RecentlyCreatedLocations: Story = {
  args: {
    locations: {
      items: mockLocations.items.map((location, index) => ({
        ...location,
        created_at: new Date(
          Date.now() - (index + 1) * 24 * 60 * 60 * 1000,
        ).toISOString(),
      })),
      total: 6,
    },
    locationSummary: {
      totalLocations: 6,
      warehouseLocations: 1,
      distributionCenterLocations: 1,
      retailLocations: 1,
      commercialLocations: 1,
      verifiedLocations: 6,
      activeLocations: 6,
      newThisMonth: 6,
    },
  },
};

export const LoadingWithPartialData: Story = {
  args: {
    isLoadingLocations: true,
    locations: {
      items: mockLocations.items.slice(0, 2),
      total: 6,
    },
    locationSummary: {
      totalLocations: 6,
      warehouseLocations: 1,
      distributionCenterLocations: 1,
      retailLocations: 1,
      commercialLocations: 1,
      verifiedLocations: 5,
      activeLocations: 6,
      newThisMonth: 2,
    },
  },
};

export const AddressValidationScenarios: Story = {
  args: {
    locations: {
      items: [
        {
          id: "loc_valid_1",
          formatted: "1600 Pennsylvania Avenue NW, Washington, DC 20500, USA",
          street: "1600 Pennsylvania Avenue NW",
          city: "Washington",
          state: "DC",
          country: "USA",
          postal: "20500",
          neighborhood: "Federal Triangle",
          suite: "",
          latitude: 38.8977,
          longitude: -77.0365,
          type: "government" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-01-01T00:00:00Z",
        },
        {
          id: "loc_invalid_1",
          formatted: "Invalid Address, Nowhere, XX 00000, USA",
          street: "Invalid Address",
          city: "Nowhere",
          state: "XX",
          country: "USA",
          postal: "00000",
          neighborhood: null,
          suite: "",
          latitude: 0,
          longitude: 0,
          type: "other" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-01-02T00:00:00Z",
        },
        {
          id: "loc_partial_1",
          formatted: "Some Street, Some City, CA, USA",
          street: "Some Street",
          city: "Some City",
          state: "CA",
          country: "USA",
          postal: "",
          neighborhood: null,
          suite: "",
          latitude: 0,
          longitude: 0,
          type: "residential" as const,
          organization_id: "org_1",
          driver_id: null,
          created_at: "2024-01-03T00:00:00Z",
        },
      ],
      total: 3,
    },
    locationSummary: {
      totalLocations: 3,
      warehouseLocations: 0,
      distributionCenterLocations: 0,
      retailLocations: 0,
      commercialLocations: 0,
      verifiedLocations: 1,
      activeLocations: 3,
      newThisMonth: 3,
    },
  },
};
