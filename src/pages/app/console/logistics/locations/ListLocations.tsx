"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { Pencil, Trash } from "lucide-react";
import { Link, useNavigate } from "react-router";

import type { useListLocations } from "@/api/locations";
import type { UseDataTableProps } from "@/components/tables";
import type { Enums } from "@/supabase/types";

import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type FilterOption = {
  value: string | undefined;
  label: string;
};

type FilterGroup = {
  id: string;
  label: string;
  options: FilterOption[];
};

const i18n = {
  en: {
    noLocation: "There are no locations yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search locations...",
    },
    headers: {
      id: "ID",
      formatted: "Address",
      type: "Type",
      city: "City",
      state: "State",
      country: "Country",
      created_at: "Created At",
      actions: "Actions",
    },
    filters: {
      type: {
        label: "Type",
        all: "All Types",
        billing: "Billing",
        commercial: "Commercial",
        industrial: "Industrial",
        government: "Government",
        public: "Public",
        residential: "Residential",
        warehouse: "Warehouse",
        distribution_center: "Distribution Center",
        retail: "Retail",
        other: "Other",
      },
    },
  },
  links: {
    locations: "/app/console/locations/[id]",
  },
};

const groupName = "location";

const filterGroups: FilterGroup[] = [
  {
    id: "type",
    label: i18n.en.filters.type.label,
    options: [
      { value: "ALL", label: i18n.en.filters.type.all },
      { value: "billing", label: i18n.en.filters.type.billing },
      { value: "commercial", label: i18n.en.filters.type.commercial },
      { value: "industrial", label: i18n.en.filters.type.industrial },
      { value: "government", label: i18n.en.filters.type.government },
      { value: "public", label: i18n.en.filters.type.public },
      { value: "residential", label: i18n.en.filters.type.residential },
      { value: "warehouse", label: i18n.en.filters.type.warehouse },
      {
        value: "distribution_center",
        label: i18n.en.filters.type.distribution_center,
      },
      { value: "retail", label: i18n.en.filters.type.retail },
      { value: "other", label: i18n.en.filters.type.other },
    ] satisfies { value: Enums<"location_type"> | "ALL"; label: string }[],
  },
];

export type LocationsQueryResult = Awaited<
  ReturnType<typeof useListLocations>
>["data"];
export type LocationsType = LocationsQueryResult["items"];
export type LocationType = LocationsType[number];
export type TableProps = UseDataTableProps<LocationType, LocationsType>;

// Links object used in the table
const tableLinks = {
  locations: "/app/console/locations/[id]",
};

export default function ListLocations({
  loading = false,
  locations,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onDelete,
}: PropsWithChildren<{
  loading?: boolean;
  locations?: LocationsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onDelete?: (id: string) => void;
}>) {
  const navigate = useNavigate();

  return (
    <ListTable
      loading={loading}
      data={locations}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      filterGroups={filterGroups}
      i18n={{
        emptyText: i18n.en.noLocation,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      columns={({ i18n, TableActions }) => [
        selectColumn as ColumnDef<LocationType, LocationType[]>,
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.id || "ID"}
            />
          ),
          cell: ({ row }) => (
            <Link
              to={tableLinks.locations.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "formatted",
          accessorKey: "formatted",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.formatted || "Address"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("formatted")}</div>,
        },
        {
          id: "type",
          accessorKey: "type",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.type || "Type"}
            />
          ),
          cell: ({ row }) => (
            <Badge variant="outline" className="capitalize">
              {row.getValue("type")}
            </Badge>
          ),
        },
        {
          id: "city",
          accessorKey: "city",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.city || "City"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("city")}</div>,
        },
        {
          id: "state",
          accessorKey: "state",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.state || "State"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("state")}</div>,
        },
        {
          id: "country",
          accessorKey: "country",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.country || "Country"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("country")}</div>,
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.created_at || "Created At"}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "actions",
          meta: {
            className: "w-[80px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TableActions table={table} i18n={i18n} />
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex items-center justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <span className="sr-only">Open menu</span>
                    <svg
                      width="15"
                      height="15"
                      viewBox="0 0 15 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                    >
                      <path
                        d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z"
                        fill="currentColor"
                        fillRule="evenodd"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() =>
                      navigate(`/app/console/locations/${row.original.id}/edit`)
                    }
                  >
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  {onDelete && (
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => onDelete(row.original.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ),
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
