import { MapPin } from "lucide-react";
import { useNavigate } from "react-router";

import type { LocationFormValues } from "@/components/forms/LocationForm";

import { useCreateLocation } from "@/api/locations/use-create-location";
import LocationForm from "@/components/forms/LocationForm";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "Create Location",
    toasts: {
      success: "Location created successfully",
      error: "Failed to create location",
    },
    backButton: "Back to Locations",
  },
};

export default function CreateLocationPage() {
  const navigate = useNavigate();

  const createLocation = useCreateLocation({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/locations/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: LocationFormValues) => {
    // Map form values to the API expected format
    const locationData = {
      formatted: values.address.formatted,
      street: values.address.street,
      city: values.address.city,
      state: values.address.state,
      country: values.address.country,
      latitude: values.address.latitude,
      longitude: values.address.longitude,
      type: values.type,
    };

    createLocation.mutate(locationData);
  };

  const handleCancel = () => {
    navigate("/app/console/locations");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <MapPin className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <LocationForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={createLocation.isPending}
        />
      </div>
    </div>
  );
}
