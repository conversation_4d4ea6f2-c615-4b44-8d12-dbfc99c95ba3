import { MapPin } from "lucide-react";
import { Link } from "react-router";

import type { LocationsQueryResult } from "@/pages/app/console/logistics/locations/ListLocations";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListLocations from "@/pages/app/console/logistics/locations/ListLocations";
import { Enums } from "@/supabase/types";

type LocationType = Enums<"location_type">;

export interface Location {
  id: string;
  formatted: string;
  street: string;
  city: string;
  state: string;
  country: string;
  postal_code?: string;
  latitude?: number;
  longitude?: number;
  type: LocationType;
  organization_id?: string;
  driver_id?: string;
  is_verified?: boolean;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

export interface LocationsListResponse {
  items: Location[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface LocationSearchParams {
  pageIndex: number;
  pageSize: number;
  search?: string;
  type?: LocationType;
}

export interface DeleteLocationHandler {
  (locationId: string): Promise<void>;
}

export interface ConsoleLocationsPageProps {
  // Location list data and loading states
  locations: LocationsQueryResult | null;
  isLoadingLocations: boolean;
  locationsError: Error | null;

  // Search and filter state
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  locationType: LocationType | undefined;
  onLocationTypeChange: (type: LocationType | undefined) => void;

  // Pagination state
  pagination: {
    pageIndex: number;
    pageSize: number;
    setPageIndex: (pageIndex: number) => void;
    setPageSize: (pageSize: number) => void;
  };

  // Delete functionality
  deleteLocationId: string | null;
  setDeleteLocationId: (id: string | null) => void;
  onDeleteLocation: DeleteLocationHandler;
  isDeletingLocation: boolean;

  // Location management actions
  onCreateLocation?: () => void;
  onEditLocation?: (locationId: string) => void;
  onViewLocation?: (locationId: string) => void;
  onUpdateLocationType?: (locationId: string, newType: LocationType) => void;
  onToggleLocationStatus?: (locationId: string, isActive: boolean) => void;
  onVerifyLocation?: (locationId: string) => void;

  // Location analytics and summary
  locationSummary?: {
    totalLocations: number;
    warehouseLocations: number;
    distributionCenterLocations: number;
    retailLocations: number;
    commercialLocations: number;
    verifiedLocations: number;
    activeLocations: number;
    newThisMonth: number;
  };

  // Bulk operations
  selectedLocations?: string[];
  onSelectLocation?: (locationId: string) => void;
  onSelectAllLocations?: (selected: boolean) => void;
  onBulkDelete?: (locationIds: string[]) => Promise<void>;
  onBulkTypeChange?: (
    locationIds: string[],
    newType: LocationType,
  ) => Promise<void>;
  onBulkStatusToggle?: (
    locationIds: string[],
    isActive: boolean,
  ) => Promise<void>;

  // Geographic filtering
  onFilterByRegion?: (region: string) => void;
  onFilterByState?: (state: string) => void;
  onFilterByCountry?: (country: string) => void;

  // Organization context
  organizationId?: string;
  canManageLocations?: boolean;
  canDeleteLocations?: boolean;
  canCreateLocations?: boolean;
  canVerifyLocations?: boolean;
}

const i18n = {
  en: {
    title: "Locations",
    addButton: "Add Location",
    deleteDialog: {
      title: "Are you sure?",
      description:
        "This action cannot be undone. This will permanently delete the location.",
      cancel: "Cancel",
      confirm: "Delete",
    },
    toast: {
      deleteSuccess: "Location deleted successfully.",
      deleteError: "Failed to delete location. Please try again.",
    },
    summary: {
      total: "Total Locations",
      warehouse: "Warehouses",
      distributionCenter: "Distribution Centers",
      retail: "Retail",
      commercial: "Commercial",
      verified: "Verified",
      active: "Active",
      newThisMonth: "New This Month",
    },
    search: {
      placeholder: "Search locations...",
      noResults: "No locations found",
      filtering: "Filtering by type",
    },
    actions: {
      view: "View Details",
      edit: "Edit Location",
      delete: "Delete Location",
      verify: "Verify Location",
      activate: "Activate Location",
      deactivate: "Deactivate Location",
      bulkDelete: "Delete Selected",
      bulkChangeType: "Change Type",
      bulkActivate: "Activate Selected",
      bulkDeactivate: "Deactivate Selected",
    },
    types: {
      billing: "Billing",
      commercial: "Commercial",
      industrial: "Industrial",
      government: "Government",
      public: "Public",
      residential: "Residential",
      warehouse: "Warehouse",
      distribution_center: "Distribution Center",
      retail: "Retail",
      other: "Other",
    },
    status: {
      verified: "Verified",
      unverified: "Unverified",
      active: "Active",
      inactive: "Inactive",
    },
  },
  links: {
    create: "/app/console/locations/create",
    view: (id: string) => `/app/console/locations/${id}`,
    edit: (id: string) => `/app/console/locations/${id}/edit`,
  },
};

export const ConsoleLocationsPage = ({
  locations,
  isLoadingLocations,
  locationsError,
  searchQuery,
  onSearchQueryChange,
  locationType,
  onLocationTypeChange,
  pagination,
  deleteLocationId,
  setDeleteLocationId,
  onDeleteLocation,
  isDeletingLocation,
  onCreateLocation,
  onEditLocation,
  onViewLocation,
  onUpdateLocationType,
  onToggleLocationStatus,
  onVerifyLocation,
  locationSummary,
  selectedLocations = [],
  onSelectLocation,
  onSelectAllLocations,
  onBulkDelete,
  onBulkTypeChange,
  onBulkStatusToggle,
  onFilterByRegion,
  onFilterByState,
  onFilterByCountry,
  organizationId,
  canManageLocations = true,
  canDeleteLocations = true,
  canCreateLocations = true,
  canVerifyLocations = true,
}: ConsoleLocationsPageProps) => {
  const hasSelectedLocations = selectedLocations.length > 0;

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MapPin className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          {hasSelectedLocations && (
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                {selectedLocations.length} selected
              </span>
              {onBulkDelete && canDeleteLocations && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onBulkDelete(selectedLocations)}
                  disabled={isDeletingLocation}
                >
                  {i18n.en.actions.bulkDelete}
                </Button>
              )}
              {onBulkTypeChange && canManageLocations && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    onBulkTypeChange(selectedLocations, "warehouse")
                  }
                  disabled={isDeletingLocation}
                >
                  {i18n.en.actions.bulkChangeType}
                </Button>
              )}
              {onBulkStatusToggle && canManageLocations && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onBulkStatusToggle(selectedLocations, true)}
                    disabled={isDeletingLocation}
                  >
                    {i18n.en.actions.bulkActivate}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onBulkStatusToggle(selectedLocations, false)}
                    disabled={isDeletingLocation}
                  >
                    {i18n.en.actions.bulkDeactivate}
                  </Button>
                </>
              )}
            </div>
          )}
          {canCreateLocations && (
            <Button asChild disabled={isLoadingLocations}>
              <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
            </Button>
          )}
        </div>
      </div>

      {/* Location Summary Cards */}
      {locationSummary && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4 lg:grid-cols-8">
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold">
              {locationSummary.totalLocations}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.total}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-blue-600">
              {locationSummary.warehouseLocations}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.warehouse}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-green-600">
              {locationSummary.distributionCenterLocations}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.distributionCenter}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-purple-600">
              {locationSummary.retailLocations}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.retail}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-orange-600">
              {locationSummary.commercialLocations}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.commercial}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-emerald-600">
              {locationSummary.verifiedLocations}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.verified}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-cyan-600">
              {locationSummary.activeLocations}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.active}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-pink-600">
              {locationSummary.newThisMonth}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.newThisMonth}
            </div>
          </div>
        </div>
      )}

      {/* Error Alert */}
      {locationsError && <ErrorAlert error={locationsError} />}

      {/* Locations List */}
      <ListLocations
        loading={isLoadingLocations}
        locations={locations}
        onDelete={setDeleteLocationId}
      />

      {/* Delete Confirmation Dialog */}
      <DialogConfirmation
        open={!!deleteLocationId}
        onOpenChange={(open) => {
          if (!open) setDeleteLocationId(null);
        }}
        onClick={() => {
          if (deleteLocationId) {
            return onDeleteLocation(deleteLocationId);
          }
          return Promise.resolve();
        }}
        title={i18n.en.deleteDialog.title}
        description={i18n.en.deleteDialog.description}
        action={i18n.en.deleteDialog.confirm}
        cancel={i18n.en.deleteDialog.cancel}
        useTrigger={false}
      />
    </div>
  );
};
