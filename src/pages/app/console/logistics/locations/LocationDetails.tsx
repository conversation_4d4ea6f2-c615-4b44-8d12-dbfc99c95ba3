import { useQuery } from "@tanstack/react-query";
import { Loader2, MapPin } from "lucide-react";
import { Link, useParams } from "react-router";

import LocationMap from "@/components/maps/LocationMap";
import { Button } from "@/components/ui/button";
import { supabase } from "@/supabase/client";

const LocationDetails = () => {
  const { id } = useParams<{ id: string }>();

  const {
    data: location,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["location", id],
    queryFn: async () => {
      if (!id) throw new Error("Location ID is required");

      const { data, error } = await supabase
        .from("locations")
        .select("*")
        .eq("id", id)
        .maybeSingle();

      if (error) throw error;
      return data;
    },
    enabled: !!id, // Only run the query if we have an ID
  });

  if (isLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !location) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center gap-4">
        <p className="text-destructive">Failed to load location details</p>
        <Button variant="outline" asChild>
          <Link to="/app/console/locations">Back to Locations</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">
            Location Details
          </h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            <p>{location.formatted}</p>
          </div>
        </div>
        <Button variant="outline" asChild>
          <Link to="/app/console/locations">Back to Locations</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="space-y-4 rounded-lg border p-4">
          <h2 className="text-lg font-semibold">Address Information</h2>
          <dl className="space-y-2">
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Street
              </dt>
              <dd>{location.street || "N/A"}</dd>
            </div>
            {location.suite && (
              <div>
                <dt className="text-muted-foreground text-sm font-medium">
                  Suite
                </dt>
                <dd>{location.suite}</dd>
              </div>
            )}
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                City
              </dt>
              <dd>{location.city || "N/A"}</dd>
            </div>
            {location.neighborhood && (
              <div>
                <dt className="text-muted-foreground text-sm font-medium">
                  Neighborhood
                </dt>
                <dd>{location.neighborhood}</dd>
              </div>
            )}
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                State
              </dt>
              <dd>{location.state || "N/A"}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Postal Code
              </dt>
              <dd>{location.postal || "N/A"}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Country
              </dt>
              <dd>{location.country}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Type
              </dt>
              <dd className="capitalize">{location.type}</dd>
            </div>
          </dl>
        </div>

        <div className="rounded-lg border" style={{ height: "400px" }}>
          <LocationMap
            latitude={location.latitude}
            longitude={location.longitude}
          />
        </div>
      </div>
    </div>
  );
};

export default LocationDetails;
