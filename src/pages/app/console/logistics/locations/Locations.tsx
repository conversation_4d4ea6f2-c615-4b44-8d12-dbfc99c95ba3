"use client";

import { useState } from "react";

import { useDeleteLocation, useListLocations } from "@/api/locations";
import {
  useSearchFilterValue,
  useSearchPagination,
  useSearchTextValue,
} from "@/components/search";
import { useToast } from "@/hooks/use-toast";
import { ConsoleLocationsPage } from "@/pages/app/console/logistics/locations/ConsoleLocationsPage";
import { supabase } from "@/supabase/client";
import { Enums } from "@/supabase/types";

type LocationType = Enums<"location_type">;

const i18n = {
  en: {
    toast: {
      deleteSuccess: "Location deleted successfully.",
      deleteError: "Failed to delete location. Please try again.",
      updateSuccess: "Location updated successfully.",
      updateError: "Failed to update location. Please try again.",
      verifySuccess: "Location verified successfully.",
      verifyError: "Failed to verify location. Please try again.",
      statusUpdateSuccess: "Location status updated successfully.",
      statusUpdateError: "Failed to update location status. Please try again.",
      bulkDeleteSuccess: "Locations deleted successfully.",
      bulkDeleteError: "Failed to delete locations. Please try again.",
      bulkUpdateSuccess: "Locations updated successfully.",
      bulkUpdateError: "Failed to update locations. Please try again.",
    },
  },
};

export default function LocationsPage() {
  const { toast } = useToast();
  const [deleteLocationId, setDeleteLocationId] = useState<string | null>(null);
  const [isDeletingLocation, setIsDeletingLocation] = useState(false);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);

  // Set up search hooks
  const pagination = useSearchPagination({
    group: "location",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });
  const locationsQuery = useSearchTextValue("location");
  const locationType = useSearchFilterValue<LocationType>("type", "location");

  // Use the hook with search parameters
  const {
    data: locations,
    isLoading,
    error,
    refetch,
  } = useListLocations({
    pageIndex: pagination.pagination.pageIndex,
    pageSize: pagination.pagination.pageSize,
    search: locationsQuery,
    type: locationType,
  });

  const deleteLocation = useDeleteLocation();

  const handleDelete = async (locationId: string) => {
    setIsDeletingLocation(true);
    try {
      await deleteLocation.mutateAsync({ id: locationId });
      await refetch();
      toast({
        title: "Success",
        description: i18n.en.toast.deleteSuccess,
      });
      setDeleteLocationId(null);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: i18n.en.toast.deleteError,
      });
      throw error;
    } finally {
      setIsDeletingLocation(false);
    }
  };

  // Calculate location summary from the current data
  const locationSummary = locations
    ? {
        totalLocations: locations.total,
        warehouseLocations: locations.items.filter(
          (l) => l.type === "warehouse",
        ).length,
        distributionCenterLocations: locations.items.filter(
          (l) => l.type === "distribution_center",
        ).length,
        retailLocations: locations.items.filter((l) => l.type === "retail")
          .length,
        commercialLocations: locations.items.filter(
          (l) => l.type === "commercial",
        ).length,
        verifiedLocations: locations.items.filter(
          (l) => l.latitude && l.longitude,
        ).length,
        activeLocations: locations.items.length, // All locations are considered active
        newThisMonth: locations.items.filter((l) => {
          const createdAt = new Date(l.created_at);
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          return createdAt >= startOfMonth;
        }).length,
      }
    : undefined;

  // Handler functions for location management
  const handleCreateLocation = () => {
    window.location.href = "/app/console/locations/create";
  };

  const handleEditLocation = (locationId: string) => {
    window.location.href = `/app/console/locations/${locationId}/edit`;
  };

  const handleViewLocation = (locationId: string) => {
    window.location.href = `/app/console/locations/${locationId}`;
  };

  const handleUpdateLocationType = async (
    locationId: string,
    newType: LocationType,
  ) => {
    try {
      const { error } = await supabase
        .from("locations")
        .update({ type: newType })
        .eq("id", locationId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.updateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.updateSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating location type:", error);
    }
  };

  const handleToggleLocationStatus = async (
    locationId: string,
    isActive: boolean,
  ) => {
    // Status management not implemented for current schema
    toast({
      title: "Info",
      description: "Status management feature not available",
    });
  };

  const handleVerifyLocation = async (locationId: string) => {
    // Verification not implemented for current schema
    toast({
      title: "Info",
      description: "Location verification feature not available",
    });
  };

  const handleBulkDelete = async (locationIds: string[]) => {
    setIsDeletingLocation(true);
    try {
      const { error } = await supabase
        .from("locations")
        .delete()
        .in("id", locationIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkDeleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkDeleteSuccess,
        });
        setSelectedLocations([]);
        refetch();
      }
    } finally {
      setIsDeletingLocation(false);
    }
  };

  const handleBulkTypeChange = async (
    locationIds: string[],
    newType: LocationType,
  ) => {
    try {
      const { error } = await supabase
        .from("locations")
        .update({ type: newType })
        .in("id", locationIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkUpdateSuccess,
        });
        setSelectedLocations([]);
        refetch();
      }
    } catch (error) {
      console.error("Error updating location types:", error);
    }
  };

  const handleBulkStatusToggle = async (
    locationIds: string[],
    isActive: boolean,
  ) => {
    // Bulk status management not implemented for current schema
    toast({
      title: "Info",
      description: "Bulk status management feature not available",
    });
  };

  const handleSelectLocation = (locationId: string) => {
    setSelectedLocations((prev) =>
      prev.includes(locationId)
        ? prev.filter((id) => id !== locationId)
        : [...prev, locationId],
    );
  };

  const handleSelectAllLocations = (selected: boolean) => {
    if (selected && locations) {
      setSelectedLocations(locations.items.map((location) => location.id));
    } else {
      setSelectedLocations([]);
    }
  };

  // Geographic filtering handlers
  const handleFilterByRegion = (region: string) => {
    // This would typically update search parameters to filter by region
    console.log("Filter by region:", region);
  };

  const handleFilterByState = (state: string) => {
    // This would typically update search parameters to filter by state
    console.log("Filter by state:", state);
  };

  const handleFilterByCountry = (country: string) => {
    // This would typically update search parameters to filter by country
    console.log("Filter by country:", country);
  };

  return (
    <ConsoleLocationsPage
      // Location list data and loading states
      locations={locations || null}
      isLoadingLocations={isLoading}
      locationsError={error}
      // Search and filter state
      searchQuery={locationsQuery}
      onSearchQueryChange={(query: string) => {
        // This would be handled by the search hook internally
      }}
      locationType={locationType}
      onLocationTypeChange={(type: LocationType | undefined) => {
        // This would be handled by the filter hook internally
      }}
      // Pagination state
      pagination={{
        pageIndex: pagination.pagination.pageIndex,
        pageSize: pagination.pagination.pageSize,
        setPageIndex: (pageIndex: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageIndex,
          }));
        },
        setPageSize: (pageSize: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageSize,
          }));
        },
      }}
      // Delete functionality
      deleteLocationId={deleteLocationId}
      setDeleteLocationId={setDeleteLocationId}
      onDeleteLocation={handleDelete}
      isDeletingLocation={isDeletingLocation}
      // Location management actions
      onCreateLocation={handleCreateLocation}
      onEditLocation={handleEditLocation}
      onViewLocation={handleViewLocation}
      onUpdateLocationType={handleUpdateLocationType}
      onToggleLocationStatus={handleToggleLocationStatus}
      onVerifyLocation={handleVerifyLocation}
      // Location analytics and summary
      locationSummary={locationSummary}
      // Bulk operations
      selectedLocations={selectedLocations}
      onSelectLocation={handleSelectLocation}
      onSelectAllLocations={handleSelectAllLocations}
      onBulkDelete={handleBulkDelete}
      onBulkTypeChange={handleBulkTypeChange}
      onBulkStatusToggle={handleBulkStatusToggle}
      // Geographic filtering
      onFilterByRegion={handleFilterByRegion}
      onFilterByState={handleFilterByState}
      onFilterByCountry={handleFilterByCountry}
      // Organization context
      organizationId={undefined}
      canManageLocations={true}
      canDeleteLocations={true}
      canCreateLocations={true}
      canVerifyLocations={true}
    />
  );
}
