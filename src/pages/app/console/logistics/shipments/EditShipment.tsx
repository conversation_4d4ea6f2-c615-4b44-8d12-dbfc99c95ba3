import { useQuery } from "@tanstack/react-query";
import { Truck } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import type { ShipmentFormValues } from "@/components/forms/ShipmentForm";

import { useUpdateShipment } from "@/api/shipments/use-update-shipment";
import ShipmentForm from "@/components/forms/ShipmentForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Shipment",
    toasts: {
      success: "Shipment updated successfully",
      error: "Failed to update shipment",
    },
    loading: "Loading shipment...",
    error: "Failed to load shipment",
    notFound: "Shipment not found",
    backButton: "Back to Shipments",
  },
};

// Hook to get a single shipment
function useGetShipment(id: string) {
  return useQuery({
    queryKey: ["shipments", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("shipments")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditShipmentPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: shipment, isLoading, error } = useGetShipment(id!);

  const updateShipment = useUpdateShipment({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/shipments/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: ShipmentFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const shipmentData = {
      id,
      mode: values.mode,
      source: values.source,
      weight: values.weight ? parseFloat(values.weight) : null,
      valuation: values.amount ? parseFloat(values.amount) : null,
      driver_id: values.driver_id || null,
      organization_id: values.organization_id || null,
    };

    updateShipment.mutate(shipmentData);
  };

  const handleCancel = () => {
    navigate(`/app/console/shipments/${id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Truck className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Truck className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <ErrorAlert error={error} />
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/shipments")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  if (!shipment) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Truck className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/shipments")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: ShipmentFormValues = {
    id: shipment.id,
    mode: shipment.mode,
    source: shipment.source,
    weight: shipment.weight?.toString() || "",
    weight_unit: "kg",
    amount: shipment.valuation?.toString() || "",
    driver_id: shipment.driver_id || "",
    organization_id: shipment.organization_id || "",
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Truck className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <ShipmentForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={updateShipment.isPending}
        />
      </div>
    </div>
  );
}
