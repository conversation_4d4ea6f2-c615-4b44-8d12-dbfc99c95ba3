import { Truck } from "lucide-react";
import { useNavigate } from "react-router";

import type { ShipmentFormValues } from "@/components/forms/ShipmentForm";

import { useCreateShipment } from "@/api/shipments/use-create-shipment";
import ShipmentForm from "@/components/forms/ShipmentForm";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "Create Shipment",
    toasts: {
      success: "Shipment created successfully",
      error: "Failed to create shipment",
    },
    backButton: "Back to Shipments",
  },
};

export default function CreateShipmentPage() {
  const navigate = useNavigate();

  const createShipment = useCreateShipment({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/shipments/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: ShipmentFormValues) => {
    // Map form values to the API expected format
    const shipmentData = {
      mode: values.mode,
      source: values.source,
      weight: values.weight ? parseFloat(values.weight) : null,
      valuation: values.amount ? parseFloat(values.amount) : null,
      driver_id: values.driver_id || null,
      organization_id: values.organization_id || null,
      status: "pending" as const,
    };

    createShipment.mutate(shipmentData);
  };

  const handleCancel = () => {
    navigate("/app/console/shipments");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Truck className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <ShipmentForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={createShipment.isPending}
        />
      </div>
    </div>
  );
}
