import { useQuery } from "@tanstack/react-query";
import { Loader2, Package } from "lucide-react";
import { Link, useParams } from "react-router";

import { Button } from "@/components/ui/button";
import { supabase } from "@/supabase/client";

export default function ShipmentDetails() {
  const { id } = useParams();

  const { data: shipment, isLoading } = useQuery({
    queryKey: ["shipment", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("shipments")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
  });

  if (isLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!shipment) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <p className="text-muted-foreground">Shipment not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">
            Shipment Details
          </h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <Package className="h-4 w-4" />
            <p>Shipment #{id}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to={`/app/console/shipments/${id}/edit`}>Edit Shipment</Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/app/console/shipments">Back to Shipments</Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        <div className="grid gap-2">
          <h2 className="text-lg font-semibold">Status</h2>
          <p>{shipment.status}</p>
        </div>

        <div className="grid gap-2">
          <h2 className="text-lg font-semibold">Mode</h2>
          <p>{shipment.mode}</p>
        </div>

        <div className="grid gap-2">
          <h2 className="text-lg font-semibold">Source</h2>
          <p>{shipment.source}</p>
        </div>

        {shipment.weight && (
          <div className="grid gap-2">
            <h2 className="text-lg font-semibold">Weight</h2>
            <p>{shipment.weight} kg</p>
          </div>
        )}

        {shipment.valuation && (
          <div className="grid gap-2">
            <h2 className="text-lg font-semibold">Valuation</h2>
            <p>${shipment.valuation}</p>
          </div>
        )}

        <div className="grid gap-2">
          <h2 className="text-lg font-semibold">Created At</h2>
          <p>{new Date(shipment.created_at).toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
}
