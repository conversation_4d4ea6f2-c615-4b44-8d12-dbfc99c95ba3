import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleShipmentsPage } from "./ConsoleShipmentsPage";

const meta: Meta<typeof ConsoleShipmentsPage> = {
  title: "Pages/Console/Logistics/Shipments",
  component: ConsoleShipmentsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/shipments" },
    }),
    docs: {
      description: {
        component: `
The ConsoleShipmentsPage component provides a comprehensive interface for managing shipments within the console application.

## Features
- **Shipment List Management**: Display shipments with pagination, search, and filtering
- **CRUD Operations**: Create, read, update, and delete shipment records
- **Status Management**: Track shipment lifecycle (pending, in_progress, completed, cancelled)
- **Driver Assignment**: Assign and unassign drivers to shipments
- **Route Management**: Optimize routes and manage shipment priorities
- **Bulk Operations**: Select and manage multiple shipments simultaneously
- **Search & Filter**: Real-time search with status, mode, and source filtering
- **Analytics Dashboard**: Shipment summary statistics, revenue tracking, and delivery metrics
- **Delete Confirmation**: Safe deletion with confirmation dialogs

## Usage
This component follows the established console pattern with a presentation component that receives all data and handlers as props, ensuring clean separation of concerns between data management and UI rendering.
        `,
      },
    },
  },
  args: {
    // Default props
    isLoadingShipments: false,
    shipmentsError: null,
    searchQuery: "",
    onSearchQueryChange: fn(),
    shipmentStatus: undefined,
    onShipmentStatusChange: fn(),
    shipmentMode: undefined,
    onShipmentModeChange: fn(),
    shipmentSource: undefined,
    onShipmentSourceChange: fn(),
    pagination: {
      pageIndex: 0,
      pageSize: 10,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    deleteShipmentId: null,
    setDeleteShipmentId: fn(),
    onDeleteShipment: fn(),
    isDeletingShipment: false,
    onCreateShipment: fn(),
    onEditShipment: fn(),
    onViewShipment: fn(),
    onUpdateShipmentStatus: fn(),
    onAssignDriver: fn(),
    onUnassignDriver: fn(),
    onStartShipment: fn(),
    onCompleteShipment: fn(),
    onCancelShipment: fn(),
    selectedShipments: [],
    onSelectShipment: fn(),
    onSelectAllShipments: fn(),
    onBulkDelete: fn(),
    onBulkStatusChange: fn(),
    onBulkAssignDriver: fn(),
    onBulkCancel: fn(),
    onOptimizeRoute: fn(),
    onSetPriority: fn(),
    onFilterByDriver: fn(),
    onFilterByOrganization: fn(),
    onFilterByRoute: fn(),
    organizationId: "org_1",
    canManageShipments: true,
    canDeleteShipments: true,
    canCreateShipments: true,
    canAssignDrivers: true,
    canModifyRoutes: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock shipment data for stories
const mockShipments = {
  items: [
    {
      id: "ship_001",
      status: "pending" as const,
      mode: "open" as const,
      source: "driver" as const,
      type: "ground" as const,
      weight: 15000,
      valuation: 45000,
      distance: 1250,
      duration: null,
      driver_id: null,
      organization_id: "org_1",
      load_id: "load_001",
      created_at: "2024-06-25T08:30:00Z",
      started_at: null,
      completed_at: null,
      cancelled_at: null,
      driver: null,
      organization: {
        id: "org_1",
        name: "Global Logistics Corp",
        type: "private",
        industry: "Manufacturing",
        size: "large",
        avatar: null,
      },
      load: {
        id: "load_001",
        type: "general",
        label: "Electronics Equipment",
        perishable: false,
        weight: 15000,
        valuation: 45000,
      },
      stops: [
        {
          id: "stop_001",
          sequence_number: 1,
          type: "pickup",
          label: "Origin Warehouse",
          arrived_at: null,
          departed_at: null,
          location: {
            id: "loc_001",
            formatted: "123 Main St, Chicago, IL 60601",
            latitude: 41.8781,
            longitude: -87.6298,
          },
        },
        {
          id: "stop_002",
          sequence_number: 2,
          type: "delivery",
          label: "Destination Facility",
          arrived_at: null,
          departed_at: null,
          location: {
            id: "loc_002",
            formatted: "456 Oak Ave, Dallas, TX 75201",
            latitude: 32.7767,
            longitude: -96.797,
          },
        },
      ],
    },
    {
      id: "ship_002",
      status: "in_progress" as const,
      mode: "open" as const,
      source: "api" as const,
      type: "refrigerated" as const,
      weight: 12000,
      valuation: 28000,
      distance: 850,
      duration: null,
      driver_id: "drv_001",
      organization_id: "org_2",
      load_id: "load_002",
      created_at: "2024-06-24T14:15:00Z",
      started_at: "2024-06-25T06:00:00Z",
      completed_at: null,
      cancelled_at: null,
      driver: {
        id: "drv_001",
        first_name: "John",
        last_name: "Miller",
        avatar: null,
        score: 4.8,
        tier: "premium",
        verified_at: "2024-01-15T10:00:00Z",
      },
      organization: {
        id: "org_2",
        name: "Fresh Foods Distribution",
        type: "private",
        industry: "Food & Beverage",
        size: "medium",
        avatar: null,
      },
      load: {
        id: "load_002",
        type: "refrigerated",
        label: "Fresh Produce",
        perishable: true,
        weight: 12000,
        valuation: 28000,
      },
      stops: [
        {
          id: "stop_003",
          sequence_number: 1,
          type: "pickup",
          label: "Cold Storage Facility",
          arrived_at: "2024-06-25T06:15:00Z",
          departed_at: "2024-06-25T07:30:00Z",
          location: {
            id: "loc_003",
            formatted: "789 Cold St, Phoenix, AZ 85001",
            latitude: 33.4484,
            longitude: -112.074,
          },
        },
        {
          id: "stop_004",
          sequence_number: 2,
          type: "delivery",
          label: "Distribution Center",
          arrived_at: null,
          departed_at: null,
          location: {
            id: "loc_004",
            formatted: "321 Fresh Blvd, Los Angeles, CA 90001",
            latitude: 34.0522,
            longitude: -118.2437,
          },
        },
      ],
    },
    {
      id: "ship_003",
      status: "completed" as const,
      mode: "rail" as const,
      source: "import" as const,
      type: "bulk" as const,
      weight: 80000,
      valuation: 120000,
      distance: 2100,
      duration: null,
      driver_id: "drv_002",
      organization_id: "org_3",
      load_id: "load_003",
      created_at: "2024-06-20T09:00:00Z",
      started_at: "2024-06-21T05:00:00Z",
      completed_at: "2024-06-23T18:30:00Z",
      cancelled_at: null,
      driver: {
        id: "drv_002",
        first_name: "Sarah",
        last_name: "Johnson",
        avatar: null,
        score: 4.9,
        tier: "platinum",
        verified_at: "2024-02-10T12:00:00Z",
      },
      organization: {
        id: "org_3",
        name: "Heavy Cargo Solutions",
        type: "individual",
        industry: "Construction",
        size: "large",
        avatar: null,
      },
      load: {
        id: "load_003",
        type: "bulk",
        label: "Steel Beams",
        perishable: false,
        weight: 80000,
        valuation: 120000,
      },
      stops: [
        {
          id: "stop_005",
          sequence_number: 1,
          type: "pickup",
          label: "Steel Mill",
          arrived_at: "2024-06-21T05:30:00Z",
          departed_at: "2024-06-21T08:00:00Z",
          location: {
            id: "loc_005",
            formatted: "555 Steel Way, Pittsburgh, PA 15201",
            latitude: 40.4406,
            longitude: -79.9959,
          },
        },
        {
          id: "stop_006",
          sequence_number: 2,
          type: "delivery",
          label: "Construction Site",
          arrived_at: "2024-06-23T16:00:00Z",
          departed_at: "2024-06-23T18:30:00Z",
          location: {
            id: "loc_006",
            formatted: "777 Build Ave, Denver, CO 80201",
            latitude: 39.7392,
            longitude: -104.9903,
          },
        },
      ],
    },
    {
      id: "ship_004",
      status: "cancelled" as const,
      mode: "air" as const,
      source: "driver" as const,
      type: "expedited" as const,
      weight: 500,
      valuation: 150000,
      distance: 3200,
      duration: null,
      driver_id: null,
      organization_id: "org_4",
      load_id: "load_004",
      created_at: "2024-06-22T16:45:00Z",
      started_at: null,
      completed_at: null,
      cancelled_at: "2024-06-23T10:30:00Z",
      driver: null,
      organization: {
        id: "org_4",
        name: "Express Air Cargo",
        type: "private",
        industry: "Technology",
        size: "small",
        avatar: null,
      },
      load: {
        id: "load_004",
        type: "expedited",
        label: "Medical Equipment",
        perishable: false,
        weight: 500,
        valuation: 150000,
      },
      stops: [
        {
          id: "stop_007",
          sequence_number: 1,
          type: "pickup",
          label: "Medical Facility",
          arrived_at: null,
          departed_at: null,
          location: {
            id: "loc_007",
            formatted: "100 Health Dr, Boston, MA 02101",
            latitude: 42.3601,
            longitude: -71.0589,
          },
        },
        {
          id: "stop_008",
          sequence_number: 2,
          type: "delivery",
          label: "Hospital",
          arrived_at: null,
          departed_at: null,
          location: {
            id: "loc_008",
            formatted: "200 Care St, Seattle, WA 98101",
            latitude: 47.6062,
            longitude: -122.3321,
          },
        },
      ],
    },
    {
      id: "ship_005",
      status: "pending" as const,
      mode: "sea" as const,
      source: "api" as const,
      type: "container" as const,
      weight: 25000,
      valuation: 75000,
      distance: 5500,
      duration: null,
      driver_id: null,
      organization_id: "org_5",
      load_id: "load_005",
      created_at: "2024-06-26T11:20:00Z",
      started_at: null,
      completed_at: null,
      cancelled_at: null,
      driver: null,
      organization: {
        id: "org_5",
        name: "International Shipping Co",
        type: "individual",
        industry: "Import/Export",
        size: "large",
        avatar: null,
      },
      load: {
        id: "load_005",
        type: "container",
        label: "Consumer Goods",
        perishable: false,
        weight: 25000,
        valuation: 75000,
      },
      stops: [
        {
          id: "stop_009",
          sequence_number: 1,
          type: "pickup",
          label: "Port of Long Beach",
          arrived_at: null,
          departed_at: null,
          location: {
            id: "loc_009",
            formatted: "Port of Long Beach, Long Beach, CA 90802",
            latitude: 33.7701,
            longitude: -118.1937,
          },
        },
        {
          id: "stop_010",
          sequence_number: 2,
          type: "delivery",
          label: "Port of Miami",
          arrived_at: null,
          departed_at: null,
          location: {
            id: "loc_010",
            formatted: "Port of Miami, Miami, FL 33132",
            latitude: 25.7743,
            longitude: -80.1937,
          },
        },
      ],
    },
  ],
  total: 5,
  page: 1,
  pageSize: 10,
  totalPages: 1,
};

const shipmentSummary = {
  totalShipments: 5,
  pendingShipments: 2,
  inProgressShipments: 1,
  completedShipments: 1,
  cancelledShipments: 1,
  assignedShipments: 2,
  unassignedShipments: 3,
  revenueThisMonth: 120000,
  avgDeliveryTime: 48,
  newThisMonth: 3,
};

export const Default: Story = {
  args: {
    shipments: mockShipments,
    shipmentSummary,
  },
};

export const Loading: Story = {
  args: {
    isLoadingShipments: true,
    shipments: null,
    shipmentSummary: undefined,
  },
};

export const EmptyState: Story = {
  args: {
    shipments: {
      items: [],
      total: 0,
      page: 1,
      pageSize: 10,
      totalPages: 0,
    },
    shipmentSummary: {
      totalShipments: 0,
      pendingShipments: 0,
      inProgressShipments: 0,
      completedShipments: 0,
      cancelledShipments: 0,
      assignedShipments: 0,
      unassignedShipments: 0,
      revenueThisMonth: 0,
      avgDeliveryTime: 0,
      newThisMonth: 0,
    },
  },
};

export const PendingShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter((s) => s.status === "pending"),
      total: 2,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentStatus: "pending",
    shipmentSummary: {
      totalShipments: 2,
      pendingShipments: 2,
      inProgressShipments: 0,
      completedShipments: 0,
      cancelledShipments: 0,
      assignedShipments: 0,
      unassignedShipments: 2,
      revenueThisMonth: 0,
      avgDeliveryTime: 0,
      newThisMonth: 2,
    },
  },
};

export const InProgressShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter((s) => s.status === "in_progress"),
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentStatus: "in_progress",
    shipmentSummary: {
      totalShipments: 1,
      pendingShipments: 0,
      inProgressShipments: 1,
      completedShipments: 0,
      cancelledShipments: 0,
      assignedShipments: 1,
      unassignedShipments: 0,
      revenueThisMonth: 0,
      avgDeliveryTime: 24,
      newThisMonth: 1,
    },
  },
};

export const CompletedShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter((s) => s.status === "completed"),
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentStatus: "completed",
    shipmentSummary: {
      totalShipments: 1,
      pendingShipments: 0,
      inProgressShipments: 0,
      completedShipments: 1,
      cancelledShipments: 0,
      assignedShipments: 1,
      unassignedShipments: 0,
      revenueThisMonth: 120000,
      avgDeliveryTime: 61,
      newThisMonth: 1,
    },
  },
};

export const CancelledShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter((s) => s.status === "cancelled"),
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentStatus: "cancelled",
    shipmentSummary: {
      totalShipments: 1,
      pendingShipments: 0,
      inProgressShipments: 0,
      completedShipments: 0,
      cancelledShipments: 1,
      assignedShipments: 0,
      unassignedShipments: 1,
      revenueThisMonth: 0,
      avgDeliveryTime: 0,
      newThisMonth: 1,
    },
  },
};

export const TruckShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter((s) => s.mode === "truck"),
      total: 2,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentMode: "truck",
    shipmentSummary: {
      totalShipments: 2,
      pendingShipments: 1,
      inProgressShipments: 1,
      completedShipments: 0,
      cancelledShipments: 0,
      assignedShipments: 1,
      unassignedShipments: 1,
      revenueThisMonth: 0,
      avgDeliveryTime: 24,
      newThisMonth: 2,
    },
  },
};

export const ManualSourceShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter((s) => s.source === "manual"),
      total: 2,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentSource: "manual",
    shipmentSummary: {
      totalShipments: 2,
      pendingShipments: 1,
      inProgressShipments: 0,
      completedShipments: 0,
      cancelledShipments: 1,
      assignedShipments: 0,
      unassignedShipments: 2,
      revenueThisMonth: 0,
      avgDeliveryTime: 0,
      newThisMonth: 2,
    },
  },
};

export const SearchResults: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter(
        (s) =>
          s.load?.label?.toLowerCase().includes("electronics") ||
          s.id.includes("001"),
      ),
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    searchQuery: "electronics",
    shipmentSummary: {
      totalShipments: 1,
      pendingShipments: 1,
      inProgressShipments: 0,
      completedShipments: 0,
      cancelledShipments: 0,
      assignedShipments: 0,
      unassignedShipments: 1,
      revenueThisMonth: 0,
      avgDeliveryTime: 0,
      newThisMonth: 1,
    },
  },
};

export const DeleteConfirmation: Story = {
  args: {
    shipments: mockShipments,
    deleteShipmentId: "ship_004",
    shipmentSummary,
  },
};

export const DeletingShipment: Story = {
  args: {
    shipments: mockShipments,
    deleteShipmentId: "ship_004",
    isDeletingShipment: true,
    shipmentSummary,
  },
};

export const BulkOperationsActive: Story = {
  args: {
    shipments: mockShipments,
    selectedShipments: ["ship_001", "ship_002", "ship_005"],
    shipmentSummary,
  },
};

export const PaginatedResults: Story = {
  args: {
    shipments: {
      items: mockShipments.items.slice(0, 3),
      total: 47,
      page: 1,
      pageSize: 3,
      totalPages: 16,
    },
    pagination: {
      pageIndex: 0,
      pageSize: 3,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    shipmentSummary: {
      totalShipments: 47,
      pendingShipments: 18,
      inProgressShipments: 12,
      completedShipments: 15,
      cancelledShipments: 2,
      assignedShipments: 35,
      unassignedShipments: 12,
      revenueThisMonth: 2400000,
      avgDeliveryTime: 36,
      newThisMonth: 8,
    },
  },
};

export const HighVolumeShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items,
      total: 2847,
      page: 15,
      pageSize: 50,
      totalPages: 57,
    },
    pagination: {
      pageIndex: 14,
      pageSize: 50,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    shipmentSummary: {
      totalShipments: 2847,
      pendingShipments: 567,
      inProgressShipments: 834,
      completedShipments: 1285,
      cancelledShipments: 161,
      assignedShipments: 2234,
      unassignedShipments: 613,
      revenueThisMonth: 15600000,
      avgDeliveryTime: 42,
      newThisMonth: 127,
    },
  },
};

export const MixedTransportModes: Story = {
  args: {
    shipments: {
      items: [
        ...mockShipments.items,
        {
          id: "ship_006",
          status: "pending" as const,
          mode: "rail" as const,
          source: "api" as const,
          type: "intermodal" as const,
          weight: 45000,
          valuation: 85000,
          distance: 1800,
          duration: null,
          driver_id: null,
          organization_id: "org_6",
          load_id: "load_006",
          created_at: "2024-06-26T15:30:00Z",
          started_at: null,
          completed_at: null,
          cancelled_at: null,
          driver: null,
          organization: {
            id: "org_6",
            name: "Intermodal Solutions",
            type: "individual",
            industry: "Logistics",
            size: "medium",
            avatar: null,
          },
          load: {
            id: "load_006",
            type: "intermodal",
            label: "Mixed Cargo",
            perishable: false,
            weight: 45000,
            valuation: 85000,
          },
          stops: [],
        },
      ],
      total: 6,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentSummary: {
      totalShipments: 6,
      pendingShipments: 3,
      inProgressShipments: 1,
      completedShipments: 1,
      cancelledShipments: 1,
      assignedShipments: 2,
      unassignedShipments: 4,
      revenueThisMonth: 120000,
      avgDeliveryTime: 48,
      newThisMonth: 4,
    },
  },
};

export const ErrorState: Story = {
  args: {
    shipments: null,
    shipmentsError: new Error(
      "Failed to load shipments. Please check your network connection and try again.",
    ),
    shipmentSummary: undefined,
  },
};

export const ReadOnlyView: Story = {
  args: {
    shipments: mockShipments,
    shipmentSummary,
    canManageShipments: false,
    canDeleteShipments: false,
    canCreateShipments: false,
    canAssignDrivers: false,
    canModifyRoutes: false,
  },
};

export const LimitedPermissions: Story = {
  args: {
    shipments: mockShipments,
    shipmentSummary,
    canManageShipments: true,
    canDeleteShipments: false,
    canCreateShipments: true,
    canAssignDrivers: false,
    canModifyRoutes: true,
  },
};

export const RecentlyCreatedShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.map((shipment, index) => ({
        ...shipment,
        created_at: new Date(
          Date.now() - (index + 1) * 6 * 60 * 60 * 1000,
        ).toISOString(), // Created 6, 12, 18, 24, 30 hours ago
      })),
      total: 5,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentSummary: {
      totalShipments: 5,
      pendingShipments: 2,
      inProgressShipments: 1,
      completedShipments: 1,
      cancelledShipments: 1,
      assignedShipments: 2,
      unassignedShipments: 3,
      revenueThisMonth: 120000,
      avgDeliveryTime: 48,
      newThisMonth: 5,
    },
  },
};

export const LoadingWithPartialData: Story = {
  args: {
    isLoadingShipments: true,
    shipments: {
      items: mockShipments.items.slice(0, 2),
      total: 5,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentSummary: {
      totalShipments: 5,
      pendingShipments: 2,
      inProgressShipments: 1,
      completedShipments: 1,
      cancelledShipments: 1,
      assignedShipments: 2,
      unassignedShipments: 3,
      revenueThisMonth: 120000,
      avgDeliveryTime: 48,
      newThisMonth: 3,
    },
  },
};

export const SearchAndFilter: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter(
        (s) => s.status === "pending" && s.mode === "truck",
      ),
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    searchQuery: "truck",
    shipmentStatus: "pending",
    shipmentMode: "truck",
    shipmentSummary: {
      totalShipments: 1,
      pendingShipments: 1,
      inProgressShipments: 0,
      completedShipments: 0,
      cancelledShipments: 0,
      assignedShipments: 0,
      unassignedShipments: 1,
      revenueThisMonth: 0,
      avgDeliveryTime: 0,
      newThisMonth: 1,
    },
  },
};

export const UrgentShipments: Story = {
  args: {
    shipments: {
      items: mockShipments.items.filter(
        (s) => s.type === "expedited" || s.load?.perishable,
      ),
      total: 2,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    },
    shipmentSummary: {
      totalShipments: 2,
      pendingShipments: 0,
      inProgressShipments: 1,
      completedShipments: 0,
      cancelledShipments: 1,
      assignedShipments: 1,
      unassignedShipments: 1,
      revenueThisMonth: 0,
      avgDeliveryTime: 24,
      newThisMonth: 2,
    },
  },
};
