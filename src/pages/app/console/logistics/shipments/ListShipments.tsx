"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { Link } from "react-router";

import type { useListShipments } from "@/api/shipments/use-list-shipments";
import type { UseDataTableProps } from "@/components/tables";

import { ShipmentModeBadge } from "@/components/common/types/ShipmentMode";
import { ShipmentSourceBadge } from "@/components/common/types/ShipmentSource";
import { ShipmentStatusBadge } from "@/components/common/types/ShipmentStatus";
import Currency from "@/components/shared/Currency";
import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Button } from "@/components/ui/button";
import { formatWeight } from "@/lib/formatters";

const i18n = {
  en: {
    noShipment: "There are no shipments yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search shipments...",
    },
    headers: {
      id: "ID",
      status: "Status",
      mode: "Mode",
      source: "Source",
      weight: "Weight",
      valuation: "Valuation",
      created_at: "Created At",
      actions: "Actions",
    },
    filters: {
      status: "Status",
      mode: "Mode",
      source: "Source",
      options: {
        status: {
          ALL: "All",
          DRAFT: "Draft",
          PENDING: "Pending",
          IN_PROGRESS: "In Progress",
          COMPLETED: "Completed",
          CANCELLED: "Cancelled",
        },
        mode: {
          ALL: "All",
          TRUCK: "Truck",
          RAIL: "Rail",
          AIR: "Air",
          SEA: "Sea",
        },
        source: {
          ALL: "All",
          MANUAL: "Manual",
          API: "API",
          IMPORT: "Import",
        },
      },
    },
  },
  links: {
    shipments: "/app/console/shipments/[id]",
  },
};

const groupName = "shipment";
const filterGroups = [
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.status.ALL },
      { value: "draft", label: i18n.en.filters.options.status.DRAFT },
      { value: "pending", label: i18n.en.filters.options.status.PENDING },
      {
        value: "in_progress",
        label: i18n.en.filters.options.status.IN_PROGRESS,
      },
      { value: "completed", label: i18n.en.filters.options.status.COMPLETED },
      { value: "cancelled", label: i18n.en.filters.options.status.CANCELLED },
    ],
  },
  {
    id: "mode",
    label: i18n.en.filters.mode,
    options: [
      { value: null, label: i18n.en.filters.options.mode.ALL },
      { value: "truck", label: i18n.en.filters.options.mode.TRUCK },
      { value: "rail", label: i18n.en.filters.options.mode.RAIL },
      { value: "air", label: i18n.en.filters.options.mode.AIR },
      { value: "sea", label: i18n.en.filters.options.mode.SEA },
    ],
  },
  {
    id: "source",
    label: i18n.en.filters.source,
    options: [
      { value: null, label: i18n.en.filters.options.source.ALL },
      { value: "manual", label: i18n.en.filters.options.source.MANUAL },
      { value: "api", label: i18n.en.filters.options.source.API },
      { value: "import", label: i18n.en.filters.options.source.IMPORT },
    ],
  },
];

export type ShipmentsQueryResult = Awaited<
  ReturnType<typeof useListShipments>
>["data"];
export type ShipmentsType = ShipmentsQueryResult["items"];
export type ShipmentType = ShipmentsType[number];
export type TableProps = UseDataTableProps<ShipmentType, ShipmentsType>;

// Links object used in the table
const tableLinks = {
  shipments: "/app/console/shipments/[id]",
};

export default function ListShipments({
  loading = false,
  shipments,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: PropsWithChildren<{
  loading?: boolean;
  shipments?: ShipmentsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}>) {
  return (
    <ListTable
      loading={loading}
      data={shipments}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      i18n={{
        emptyText: i18n.en.noShipment,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      filterGroups={filterGroups}
      columns={({ i18n, TableActions }) => [
        selectColumn as ColumnDef<ShipmentType, ShipmentType[]>,
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.id || "ID"}
            />
          ),
          cell: ({ row }) => (
            <Link
              to={tableLinks.shipments.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "status",
          accessorKey: "status",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.status || "Status"}
            />
          ),
          cell: ({ row }) => (
            <ShipmentStatusBadge status={row.getValue("status")} />
          ),
        },
        {
          id: "mode",
          accessorKey: "mode",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.mode || "Mode"}
            />
          ),
          cell: ({ row }) => <ShipmentModeBadge mode={row.getValue("mode")} />,
        },
        {
          id: "source",
          accessorKey: "source",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.source || "Source"}
            />
          ),
          cell: ({ row }) => (
            <ShipmentSourceBadge source={row.getValue("source")} />
          ),
        },
        {
          id: "weight",
          accessorKey: "weight",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.weight || "Weight"}
            />
          ),
          cell: ({ row }) => (
            <span className="font-mono tabular-nums">
              {formatWeight(row.original.weight)}
            </span>
          ),
        },
        {
          id: "valuation",
          accessorKey: "valuation",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.valuation || "Valuation"}
            />
          ),
          cell: ({ row }) => (
            <Currency
              loading={loading}
              amount={row.original.valuation || 0}
              className="font-mono tabular-nums"
            />
          ),
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.created_at || "Created At"}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "actions",
          meta: {
            className: "w-[32px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TableActions table={table} i18n={i18n} />
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex size-full items-center justify-end">
              <Button variant="ghost" size="sm" asChild>
                <Link
                  to={tableLinks.shipments.replace("[id]", row.original.id)}
                >
                  View
                </Link>
              </Button>
            </div>
          ),
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
