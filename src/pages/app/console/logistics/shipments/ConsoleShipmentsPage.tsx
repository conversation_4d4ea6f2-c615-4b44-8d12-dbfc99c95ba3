import { Package } from "lucide-react";
import { Link } from "react-router";

import type { ShipmentsQueryResult } from "@/pages/app/console/logistics/shipments/ListShipments";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListShipments from "@/pages/app/console/logistics/shipments/ListShipments";
import { Enums } from "@/supabase/types";

type ShipmentStatus = Enums<"shipment_status">;
type ShipmentMode = Enums<"shipment_mode">;
type ShipmentSource = Enums<"shipment_source">;
type ShipmentType = Enums<"shipment_type">;

export interface Shipment {
  id: string;
  status: ShipmentStatus;
  mode: ShipmentMode;
  source: ShipmentSource;
  type: ShipmentType | null;
  weight: number | null;
  valuation: number | null;
  distance: number | null;
  duration: unknown | null;
  driver_id: string | null;
  organization_id: string | null;
  load_id: string | null;
  created_at: string;
  started_at: string | null;
  completed_at: string | null;
  cancelled_at: string | null;
  driver?: {
    id: string;
    first_name: string;
    last_name: string;
    avatar: string | null;
    score: number;
    tier: string;
    verified_at: string | null;
  } | null;
  organization?: {
    id: string;
    name: string;
    type: string;
    industry: string;
    size: string;
    avatar: string | null;
  } | null;
  load?: {
    id: string;
    type: string;
    label: string | null;
    perishable: boolean;
    weight: number | null;
    valuation: number | null;
  } | null;
  stops?: Array<{
    id: string;
    sequence_number: number;
    type: string;
    label: string | null;
    arrived_at: string | null;
    departed_at: string | null;
    location: {
      id: string;
      formatted: string;
      latitude: number | null;
      longitude: number | null;
    } | null;
  }>;
}

export interface ShipmentsListResponse {
  items: Shipment[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ShipmentSearchParams {
  pageIndex: number;
  pageSize: number;
  search?: string;
  status?: ShipmentStatus;
  mode?: ShipmentMode;
  source?: ShipmentSource;
}

export interface DeleteShipmentHandler {
  (shipmentId: string): Promise<void>;
}

export interface ConsoleShipmentsPageProps {
  // Shipment list data and loading states
  shipments: ShipmentsQueryResult | null;
  isLoadingShipments: boolean;
  shipmentsError: Error | null;

  // Search and filter state
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  shipmentStatus: ShipmentStatus | undefined;
  onShipmentStatusChange: (status: ShipmentStatus | undefined) => void;
  shipmentMode: ShipmentMode | undefined;
  onShipmentModeChange: (mode: ShipmentMode | undefined) => void;
  shipmentSource: ShipmentSource | undefined;
  onShipmentSourceChange: (source: ShipmentSource | undefined) => void;

  // Pagination state
  pagination: {
    pageIndex: number;
    pageSize: number;
    setPageIndex: (pageIndex: number) => void;
    setPageSize: (pageSize: number) => void;
  };

  // Delete functionality
  deleteShipmentId: string | null;
  setDeleteShipmentId: (id: string | null) => void;
  onDeleteShipment: DeleteShipmentHandler;
  isDeletingShipment: boolean;

  // Shipment management actions
  onCreateShipment?: () => void;
  onEditShipment?: (shipmentId: string) => void;
  onViewShipment?: (shipmentId: string) => void;
  onUpdateShipmentStatus?: (
    shipmentId: string,
    newStatus: ShipmentStatus,
  ) => void;
  onAssignDriver?: (shipmentId: string, driverId: string) => void;
  onUnassignDriver?: (shipmentId: string) => void;
  onStartShipment?: (shipmentId: string) => void;
  onCompleteShipment?: (shipmentId: string) => void;
  onCancelShipment?: (shipmentId: string) => void;

  // Shipment analytics and summary
  shipmentSummary?: {
    totalShipments: number;
    pendingShipments: number;
    inProgressShipments: number;
    completedShipments: number;
    cancelledShipments: number;
    assignedShipments: number;
    unassignedShipments: number;
    revenueThisMonth: number;
    avgDeliveryTime: number;
    newThisMonth: number;
  };

  // Bulk operations
  selectedShipments?: string[];
  onSelectShipment?: (shipmentId: string) => void;
  onSelectAllShipments?: (selected: boolean) => void;
  onBulkDelete?: (shipmentIds: string[]) => Promise<void>;
  onBulkStatusChange?: (
    shipmentIds: string[],
    newStatus: ShipmentStatus,
  ) => Promise<void>;
  onBulkAssignDriver?: (
    shipmentIds: string[],
    driverId: string,
  ) => Promise<void>;
  onBulkCancel?: (shipmentIds: string[]) => Promise<void>;

  // Route and priority management
  onOptimizeRoute?: (shipmentId: string) => void;
  onSetPriority?: (
    shipmentId: string,
    priority: "standard" | "expedited" | "urgent",
  ) => void;

  // Driver and organization filtering
  onFilterByDriver?: (driverId: string) => void;
  onFilterByOrganization?: (organizationId: string) => void;
  onFilterByRoute?: (
    routeType: "local" | "regional" | "cross_country" | "international",
  ) => void;

  // Organization context
  organizationId?: string;
  canManageShipments?: boolean;
  canDeleteShipments?: boolean;
  canCreateShipments?: boolean;
  canAssignDrivers?: boolean;
  canModifyRoutes?: boolean;
}

const i18n = {
  en: {
    title: "Shipments",
    addButton: "Add Shipment",
    deleteDialog: {
      title: "Are you sure?",
      description:
        "This action cannot be undone. This will permanently delete the shipment and all associated data.",
      cancel: "Cancel",
      confirm: "Delete",
    },
    toast: {
      deleteSuccess: "Shipment deleted successfully.",
      deleteError: "Failed to delete shipment. Please try again.",
    },
    summary: {
      total: "Total Shipments",
      pending: "Pending",
      inProgress: "In Progress",
      completed: "Completed",
      cancelled: "Cancelled",
      assigned: "Assigned",
      unassigned: "Unassigned",
      revenue: "Revenue This Month",
      avgDeliveryTime: "Avg Delivery Time",
      newThisMonth: "New This Month",
    },
    search: {
      placeholder: "Search shipments...",
      noResults: "No shipments found",
      filtering: "Filtering by status",
    },
    actions: {
      view: "View Details",
      edit: "Edit Shipment",
      delete: "Delete Shipment",
      assign: "Assign Driver",
      unassign: "Unassign Driver",
      start: "Start Shipment",
      complete: "Complete Shipment",
      cancel: "Cancel Shipment",
      optimize: "Optimize Route",
      setPriority: "Set Priority",
      bulkDelete: "Delete Selected",
      bulkStatusChange: "Change Status",
      bulkAssign: "Assign Driver",
      bulkCancel: "Cancel Selected",
    },
    status: {
      pending: "Pending",
      scheduled: "Scheduled",
      assigned: "Assigned",
      confirmed: "Confirmed",
      in_progress: "In Progress",
      completed: "Completed",
      cancelled: "Cancelled",
    },
    mode: {
      open: "Open",
      closed: "Closed",
    },
    source: {
      driver: "Driver",
      organization: "Organization",
      system: "System",
    },
    type: {
      air: "Air",
      ocean: "Ocean",
      ground: "Ground",
      other: "Other",
    },
    priority: {
      standard: "Standard",
      expedited: "Expedited",
      urgent: "Urgent",
    },
    route: {
      local: "Local",
      regional: "Regional",
      cross_country: "Cross Country",
      international: "International",
    },
  },
  links: {
    create: "/app/console/shipments/create",
    view: (id: string) => `/app/console/shipments/${id}`,
    edit: (id: string) => `/app/console/shipments/${id}/edit`,
  },
};

export const ConsoleShipmentsPage = ({
  shipments,
  isLoadingShipments,
  shipmentsError,
  searchQuery,
  onSearchQueryChange,
  shipmentStatus,
  onShipmentStatusChange,
  shipmentMode,
  onShipmentModeChange,
  shipmentSource,
  onShipmentSourceChange,
  pagination,
  deleteShipmentId,
  setDeleteShipmentId,
  onDeleteShipment,
  isDeletingShipment,
  onCreateShipment,
  onEditShipment,
  onViewShipment,
  onUpdateShipmentStatus,
  onAssignDriver,
  onUnassignDriver,
  onStartShipment,
  onCompleteShipment,
  onCancelShipment,
  shipmentSummary,
  selectedShipments = [],
  onSelectShipment,
  onSelectAllShipments,
  onBulkDelete,
  onBulkStatusChange,
  onBulkAssignDriver,
  onBulkCancel,
  onOptimizeRoute,
  onSetPriority,
  onFilterByDriver,
  onFilterByOrganization,
  onFilterByRoute,
  organizationId,
  canManageShipments = true,
  canDeleteShipments = true,
  canCreateShipments = true,
  canAssignDrivers = true,
  canModifyRoutes = true,
}: ConsoleShipmentsPageProps) => {
  const hasSelectedShipments = selectedShipments.length > 0;

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Package className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          {hasSelectedShipments && (
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                {selectedShipments.length} selected
              </span>
              {onBulkDelete && canDeleteShipments && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onBulkDelete(selectedShipments)}
                  disabled={isDeletingShipment}
                >
                  {i18n.en.actions.bulkDelete}
                </Button>
              )}
              {onBulkStatusChange && canManageShipments && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    onBulkStatusChange(selectedShipments, "in_progress")
                  }
                  disabled={isDeletingShipment}
                >
                  {i18n.en.actions.bulkStatusChange}
                </Button>
              )}
              {onBulkAssignDriver && canAssignDrivers && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    onBulkAssignDriver(selectedShipments, "driver_1")
                  }
                  disabled={isDeletingShipment}
                >
                  {i18n.en.actions.bulkAssign}
                </Button>
              )}
              {onBulkCancel && canManageShipments && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkCancel(selectedShipments)}
                  disabled={isDeletingShipment}
                >
                  {i18n.en.actions.bulkCancel}
                </Button>
              )}
            </div>
          )}
          {canCreateShipments && (
            <Button asChild disabled={isLoadingShipments}>
              <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
            </Button>
          )}
        </div>
      </div>

      {/* Shipment Summary Cards */}
      {shipmentSummary && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5 lg:grid-cols-10">
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold">
              {shipmentSummary.totalShipments}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.total}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {shipmentSummary.pendingShipments}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.pending}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-blue-600">
              {shipmentSummary.inProgressShipments}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.inProgress}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-green-600">
              {shipmentSummary.completedShipments}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.completed}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-red-600">
              {shipmentSummary.cancelledShipments}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.cancelled}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-purple-600">
              {shipmentSummary.assignedShipments}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.assigned}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-orange-600">
              {shipmentSummary.unassignedShipments}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.unassigned}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-emerald-600">
              ${(shipmentSummary.revenueThisMonth / 100).toLocaleString()}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.revenue}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-cyan-600">
              {shipmentSummary.avgDeliveryTime}h
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.avgDeliveryTime}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-pink-600">
              {shipmentSummary.newThisMonth}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.newThisMonth}
            </div>
          </div>
        </div>
      )}

      {/* Error Alert */}
      {shipmentsError && <ErrorAlert error={shipmentsError} />}

      {/* Shipments List */}
      <ListShipments loading={isLoadingShipments} shipments={shipments} />

      {/* Delete Confirmation Dialog */}
      <DialogConfirmation
        open={!!deleteShipmentId}
        onOpenChange={(open) => {
          if (!open) setDeleteShipmentId(null);
        }}
        onClick={() => {
          if (deleteShipmentId) {
            return onDeleteShipment(deleteShipmentId);
          }
          return Promise.resolve();
        }}
        title={i18n.en.deleteDialog.title}
        description={i18n.en.deleteDialog.description}
        action={i18n.en.deleteDialog.confirm}
        cancel={i18n.en.deleteDialog.cancel}
        useTrigger={false}
      />
    </div>
  );
};
