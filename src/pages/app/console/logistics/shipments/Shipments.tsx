"use client";

import { useState } from "react";

import { useListShipments } from "@/api/shipments/use-list-shipments";
import {
  useSearchFilterValue,
  useSearchPagination,
  useSearchTextValue,
} from "@/components/search";
import { useToast } from "@/hooks/use-toast";
import { ConsoleShipmentsPage } from "@/pages/app/console/logistics/shipments/ConsoleShipmentsPage";
import { supabase } from "@/supabase/client";
import { Enums } from "@/supabase/types";

type ShipmentStatus = Enums<"shipment_status">;
type ShipmentMode = Enums<"shipment_mode">;
type ShipmentSource = Enums<"shipment_source">;

const i18n = {
  en: {
    toast: {
      deleteSuccess: "Shipment deleted successfully.",
      deleteError: "Failed to delete shipment. Please try again.",
      updateSuccess: "Shipment updated successfully.",
      updateError: "Failed to update shipment. Please try again.",
      bulkDeleteSuccess: "Shipments deleted successfully.",
      bulkDeleteError: "Failed to delete shipments. Please try again.",
      bulkUpdateSuccess: "Shipments updated successfully.",
      bulkUpdateError: "Failed to update shipments. Please try again.",
      statusUpdateSuccess: "Shipment status updated successfully.",
      statusUpdateError: "Failed to update shipment status. Please try again.",
      driverAssignSuccess: "Driver assigned successfully.",
      driverAssignError: "Failed to assign driver. Please try again.",
      driverUnassignSuccess: "Driver unassigned successfully.",
      driverUnassignError: "Failed to unassign driver. Please try again.",
      shipmentStartSuccess: "Shipment started successfully.",
      shipmentStartError: "Failed to start shipment. Please try again.",
      shipmentCompleteSuccess: "Shipment completed successfully.",
      shipmentCompleteError: "Failed to complete shipment. Please try again.",
      shipmentCancelSuccess: "Shipment cancelled successfully.",
      shipmentCancelError: "Failed to cancel shipment. Please try again.",
    },
  },
};

export default function ShipmentsPage() {
  const { toast } = useToast();
  const [deleteShipmentId, setDeleteShipmentId] = useState<string | null>(null);
  const [isDeletingShipment, setIsDeletingShipment] = useState(false);
  const [selectedShipments, setSelectedShipments] = useState<string[]>([]);

  // Set up search hooks
  const pagination = useSearchPagination({
    group: "shipment",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });
  const shipmentsQuery = useSearchTextValue("shipment");
  const shipmentStatus = useSearchFilterValue<ShipmentStatus>(
    "status",
    "shipment",
  );
  const shipmentMode = useSearchFilterValue<ShipmentMode>("mode", "shipment");
  const shipmentSource = useSearchFilterValue<ShipmentSource>(
    "source",
    "shipment",
  );

  // Use the hook with search parameters
  const {
    data: shipments,
    isLoading,
    error,
    refetch,
  } = useListShipments({
    pageIndex: pagination.pagination.pageIndex,
    pageSize: pagination.pagination.pageSize,
    search: shipmentsQuery,
    status: shipmentStatus,
    mode: shipmentMode,
    source: shipmentSource,
  });

  const handleDelete = async (shipmentId: string) => {
    setIsDeletingShipment(true);
    try {
      const { error } = await supabase
        .from("shipments")
        .delete()
        .eq("id", shipmentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.deleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.deleteSuccess,
        });
        setDeleteShipmentId(null);
        refetch();
      }
    } finally {
      setIsDeletingShipment(false);
    }
  };

  // Calculate shipment summary from the current data
  const shipmentSummary = shipments
    ? {
        totalShipments: shipments.total,
        pendingShipments: shipments.items.filter((s) => s.status === "pending")
          .length,
        inProgressShipments: shipments.items.filter(
          (s) => s.status === "in_progress",
        ).length,
        completedShipments: shipments.items.filter(
          (s) => s.status === "completed",
        ).length,
        cancelledShipments: shipments.items.filter(
          (s) => s.status === "cancelled",
        ).length,
        assignedShipments: shipments.items.filter((s) => s.driver_id !== null)
          .length,
        unassignedShipments: shipments.items.filter((s) => s.driver_id === null)
          .length,
        revenueThisMonth: shipments.items
          .filter((s) => {
            const createdAt = new Date(s.created_at);
            const now = new Date();
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            return createdAt >= startOfMonth && s.status === "completed";
          })
          .reduce((sum, s) => sum + (s.valuation || 0), 0),
        avgDeliveryTime: (() => {
          const completedShipments = shipments.items.filter(
            (s) => s.completed_at && s.started_at,
          );
          if (completedShipments.length === 0) return 0;
          const totalTime = completedShipments.reduce((sum, s) => {
            const started = new Date(s.started_at!).getTime();
            const completed = new Date(s.completed_at!).getTime();
            return sum + (completed - started);
          }, 0);
          return Math.round(
            totalTime / completedShipments.length / (1000 * 60 * 60),
          ); // Convert to hours
        })(),
        newThisMonth: shipments.items.filter((s) => {
          const createdAt = new Date(s.created_at);
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          return createdAt >= startOfMonth;
        }).length,
      }
    : undefined;

  // Handler functions for shipment management
  const handleCreateShipment = () => {
    window.location.href = "/app/console/shipments/create";
  };

  const handleEditShipment = (shipmentId: string) => {
    window.location.href = `/app/console/shipments/${shipmentId}/edit`;
  };

  const handleViewShipment = (shipmentId: string) => {
    window.location.href = `/app/console/shipments/${shipmentId}`;
  };

  const handleUpdateShipmentStatus = async (
    shipmentId: string,
    newStatus: ShipmentStatus,
  ) => {
    try {
      const updateData: {
        status: ShipmentStatus;
        started_at?: string;
        completed_at?: string;
        cancelled_at?: string;
      } = { status: newStatus };

      // Set timestamps based on status
      if (newStatus === "in_progress") {
        updateData.started_at = new Date().toISOString();
      } else if (newStatus === "completed") {
        updateData.completed_at = new Date().toISOString();
      } else if (newStatus === "cancelled") {
        updateData.cancelled_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from("shipments")
        .update(updateData)
        .eq("id", shipmentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.statusUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.statusUpdateSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating shipment status:", error);
    }
  };

  const handleAssignDriver = async (shipmentId: string, driverId: string) => {
    try {
      const { error } = await supabase
        .from("shipments")
        .update({ driver_id: driverId })
        .eq("id", shipmentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.driverAssignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.driverAssignSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error assigning driver:", error);
    }
  };

  const handleUnassignDriver = async (shipmentId: string) => {
    try {
      const { error } = await supabase
        .from("shipments")
        .update({ driver_id: null })
        .eq("id", shipmentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.driverUnassignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.driverUnassignSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error unassigning driver:", error);
    }
  };

  const handleStartShipment = async (shipmentId: string) => {
    await handleUpdateShipmentStatus(shipmentId, "in_progress");
  };

  const handleCompleteShipment = async (shipmentId: string) => {
    await handleUpdateShipmentStatus(shipmentId, "completed");
  };

  const handleCancelShipment = async (shipmentId: string) => {
    await handleUpdateShipmentStatus(shipmentId, "cancelled");
  };

  const handleBulkDelete = async (shipmentIds: string[]) => {
    setIsDeletingShipment(true);
    try {
      const { error } = await supabase
        .from("shipments")
        .delete()
        .in("id", shipmentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkDeleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkDeleteSuccess,
        });
        setSelectedShipments([]);
        refetch();
      }
    } finally {
      setIsDeletingShipment(false);
    }
  };

  const handleBulkStatusChange = async (
    shipmentIds: string[],
    newStatus: ShipmentStatus,
  ) => {
    try {
      const updateData: {
        status: ShipmentStatus;
        started_at?: string;
        completed_at?: string;
        cancelled_at?: string;
      } = { status: newStatus };

      // Set timestamps based on status
      if (newStatus === "in_progress") {
        updateData.started_at = new Date().toISOString();
      } else if (newStatus === "completed") {
        updateData.completed_at = new Date().toISOString();
      } else if (newStatus === "cancelled") {
        updateData.cancelled_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from("shipments")
        .update(updateData)
        .in("id", shipmentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkUpdateSuccess,
        });
        setSelectedShipments([]);
        refetch();
      }
    } catch (error) {
      console.error("Error updating shipment statuses:", error);
    }
  };

  const handleBulkAssignDriver = async (
    shipmentIds: string[],
    driverId: string,
  ) => {
    try {
      const { error } = await supabase
        .from("shipments")
        .update({ driver_id: driverId })
        .in("id", shipmentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkUpdateSuccess,
        });
        setSelectedShipments([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk assigning driver:", error);
    }
  };

  const handleBulkCancel = async (shipmentIds: string[]) => {
    await handleBulkStatusChange(shipmentIds, "cancelled");
  };

  const handleOptimizeRoute = (shipmentId: string) => {
    // TODO: Implement route optimization logic
    console.log("Optimizing route for shipment:", shipmentId);
    toast({
      title: "Route Optimization",
      description: "Route optimization feature coming soon.",
    });
  };

  const handleSetPriority = (
    shipmentId: string,
    priority: "standard" | "expedited" | "urgent",
  ) => {
    // TODO: Implement priority setting logic
    console.log("Setting priority for shipment:", shipmentId, priority);
    toast({
      title: "Priority Setting",
      description: "Priority setting feature coming soon.",
    });
  };

  const handleFilterByDriver = (driverId: string) => {
    // TODO: Implement driver filtering
    console.log("Filtering by driver:", driverId);
  };

  const handleFilterByOrganization = (organizationId: string) => {
    // TODO: Implement organization filtering
    console.log("Filtering by organization:", organizationId);
  };

  const handleFilterByRoute = (
    routeType: "local" | "regional" | "cross_country" | "international",
  ) => {
    // TODO: Implement route type filtering
    console.log("Filtering by route type:", routeType);
  };

  const handleSelectShipment = (shipmentId: string) => {
    setSelectedShipments((prev) =>
      prev.includes(shipmentId)
        ? prev.filter((id) => id !== shipmentId)
        : [...prev, shipmentId],
    );
  };

  const handleSelectAllShipments = (selected: boolean) => {
    if (selected && shipments) {
      setSelectedShipments(shipments.items.map((shipment) => shipment.id));
    } else {
      setSelectedShipments([]);
    }
  };

  return (
    <ConsoleShipmentsPage
      // Shipment list data and loading states
      shipments={shipments || null}
      isLoadingShipments={isLoading}
      shipmentsError={error}
      // Search and filter state
      searchQuery={shipmentsQuery}
      onSearchQueryChange={(query: string) => {
        // This would be handled by the search hook internally
      }}
      shipmentStatus={shipmentStatus}
      onShipmentStatusChange={(status: ShipmentStatus | undefined) => {
        // This would be handled by the filter hook internally
      }}
      shipmentMode={shipmentMode}
      onShipmentModeChange={(mode: ShipmentMode | undefined) => {
        // This would be handled by the filter hook internally
      }}
      shipmentSource={shipmentSource}
      onShipmentSourceChange={(source: ShipmentSource | undefined) => {
        // This would be handled by the filter hook internally
      }}
      // Pagination state
      pagination={{
        pageIndex: pagination.pagination.pageIndex,
        pageSize: pagination.pagination.pageSize,
        setPageIndex: (pageIndex: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageIndex,
          }));
        },
        setPageSize: (pageSize: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageSize,
          }));
        },
      }}
      // Delete functionality
      deleteShipmentId={deleteShipmentId}
      setDeleteShipmentId={setDeleteShipmentId}
      onDeleteShipment={handleDelete}
      isDeletingShipment={isDeletingShipment}
      // Shipment management actions
      onCreateShipment={handleCreateShipment}
      onEditShipment={handleEditShipment}
      onViewShipment={handleViewShipment}
      onUpdateShipmentStatus={handleUpdateShipmentStatus}
      onAssignDriver={handleAssignDriver}
      onUnassignDriver={handleUnassignDriver}
      onStartShipment={handleStartShipment}
      onCompleteShipment={handleCompleteShipment}
      onCancelShipment={handleCancelShipment}
      // Shipment analytics and summary
      shipmentSummary={shipmentSummary}
      // Bulk operations
      selectedShipments={selectedShipments}
      onSelectShipment={handleSelectShipment}
      onSelectAllShipments={handleSelectAllShipments}
      onBulkDelete={handleBulkDelete}
      onBulkStatusChange={handleBulkStatusChange}
      onBulkAssignDriver={handleBulkAssignDriver}
      onBulkCancel={handleBulkCancel}
      // Route and priority management
      onOptimizeRoute={handleOptimizeRoute}
      onSetPriority={handleSetPriority}
      // Driver and organization filtering
      onFilterByDriver={handleFilterByDriver}
      onFilterByOrganization={handleFilterByOrganization}
      onFilterByRoute={handleFilterByRoute}
      // Organization context
      organizationId={undefined}
      canManageShipments={true}
      canDeleteShipments={true}
      canCreateShipments={true}
      canAssignDrivers={true}
      canModifyRoutes={true}
    />
  );
}
