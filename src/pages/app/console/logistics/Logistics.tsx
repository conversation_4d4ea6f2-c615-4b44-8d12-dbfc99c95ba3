import { Helmet } from "react-helmet-async";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

function LogisticsPage() {
  return (
    <div>
      <Helmet>
        <title>Logistics | QuikSkope</title>
      </Helmet>

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Logistics</h1>
        <div className="flex gap-2">
          <Button variant="outline">Export</Button>
          <Button>Create New</Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="planning">Planning</TabsTrigger>
          <TabsTrigger value="tracking">Tracking</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Logistics Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Logistics dashboard overview and summary information.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="planning">
          <Card>
            <CardHeader>
              <CardTitle>Logistics Planning</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Plan and schedule logistics operations.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="tracking">
          <Card>
            <CardHeader>
              <CardTitle>Logistics Tracking</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Track and monitor logistics operations in real-time.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default LogisticsPage;
