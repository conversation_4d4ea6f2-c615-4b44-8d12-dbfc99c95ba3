import { AlertTriangle } from "lucide-react";
import { Link, useParams } from "react-router";

import { But<PERSON> } from "@/components/ui/button";

export default function IncidentDetails() {
  const { id } = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">
            Incident Details
          </h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            <p>Incident #{id}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to={`/app/console/incidents/${id}/edit`}>Edit Incident</Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/app/console/incidents">Back to Incidents</Link>
          </Button>
        </div>
      </div>
      <div className="flex min-h-[400px] items-center justify-center">
        <span className="text-muted-foreground">
          Incident details coming soon
        </span>
      </div>
    </div>
  );
}
