import { useQuery } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router";

import { useUpdateIncident } from "@/api/incidents/use-update-incident";
import IncidentForm, {
  IncidentFormValues,
} from "@/components/forms/IncidentForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Incident",
    toasts: {
      success: "Incident updated successfully",
      error: "Failed to update incident",
    },
    loading: "Loading incident...",
    error: "Failed to load incident",
    notFound: "Incident not found",
    backButton: "Back to Incidents",
  },
};

// Hook to get a single incident
function useGetIncident(id: string) {
  return useQuery({
    queryKey: ["incidents", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("incidents")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditIncidentPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: incident, isLoading, error } = useGetIncident(id!);

  const updateIncident = useUpdateIncident({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/incidents/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: IncidentFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const incidentData = {
      id,
      title: values.title,
      summary: values.description,
      type: values.type,
      severity: values.severity,
      status: values.status || "reported",
    };

    updateIncident.mutate(incidentData);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
          <Button
            variant="outline"
            onClick={() => navigate("/app/console/incidents")}
          >
            {i18n.en.backButton}
          </Button>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
          <Button
            variant="outline"
            onClick={() => navigate("/app/console/incidents")}
          >
            {i18n.en.backButton}
          </Button>
        </div>
        <ErrorAlert error={error} />
      </div>
    );
  }

  if (!incident) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
          <Button
            variant="outline"
            onClick={() => navigate("/app/console/incidents")}
          >
            {i18n.en.backButton}
          </Button>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: IncidentFormValues = {
    title: incident.title,
    description: incident.summary || "",
    type: incident.type,
    severity: incident.severity,
    status: incident.status,
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/incidents")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <IncidentForm onSubmit={handleSubmit} defaultValues={defaultValues} />
      </div>
    </div>
  );
}
