"use client";

import { useState } from "react";

import { useListIncidents } from "@/api/incidents/use-list-incidents";
import {
  useSearchFilterValue,
  useSearchPagination,
  useSearchTextValue,
} from "@/components/search";
import { useToast } from "@/hooks/use-toast";
import { ConsoleIncidentsPage } from "@/pages/app/console/logistics/incidents/ConsoleIncidentsPage";
import { supabase } from "@/supabase/client";
import { Enums } from "@/supabase/types";

type IncidentType = Enums<"incident_type">;
type IncidentSeverity = Enums<"incident_severity">;
type IncidentStatus = Enums<"incident_status">;

const i18n = {
  en: {
    toast: {
      deleteSuccess: "Incident deleted successfully.",
      deleteError: "Failed to delete incident. Please try again.",
      updateSuccess: "Incident updated successfully.",
      updateError: "Failed to update incident. Please try again.",
      assignSuccess: "Incident assigned successfully.",
      assignError: "Failed to assign incident. Please try again.",
      resolveSuccess: "Incident resolved successfully.",
      resolveError: "Failed to resolve incident. Please try again.",
      closeSuccess: "Incident closed successfully.",
      closeError: "Failed to close incident. Please try again.",
      reopenSuccess: "Incident reopened successfully.",
      reopenError: "Failed to reopen incident. Please try again.",
      bulkDeleteSuccess: "Incidents deleted successfully.",
      bulkDeleteError: "Failed to delete incidents. Please try again.",
      bulkAssignSuccess: "Incidents assigned successfully.",
      bulkAssignError: "Failed to assign incidents. Please try again.",
      bulkStatusUpdateSuccess: "Incident statuses updated successfully.",
      bulkStatusUpdateError:
        "Failed to update incident statuses. Please try again.",
      bulkSeverityUpdateSuccess: "Incident severities updated successfully.",
      bulkSeverityUpdateError:
        "Failed to update incident severities. Please try again.",
      bulkResolveSuccess: "Incidents resolved successfully.",
      bulkResolveError: "Failed to resolve incidents. Please try again.",
      bulkCloseSuccess: "Incidents closed successfully.",
      bulkCloseError: "Failed to close incidents. Please try again.",
    },
  },
};

export default function IncidentsPage() {
  const { toast } = useToast();
  const [deleteIncidentId, setDeleteIncidentId] = useState<string | null>(null);
  const [isDeletingIncident, setIsDeletingIncident] = useState(false);
  const [selectedIncidents, setSelectedIncidents] = useState<string[]>([]);

  // Set up search hooks
  const pagination = useSearchPagination({
    group: "incident",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });
  const incidentsQuery = useSearchTextValue("incident");
  const incidentType = useSearchFilterValue<IncidentType>("type", "incident");
  const incidentSeverity = useSearchFilterValue<IncidentSeverity>(
    "severity",
    "incident",
  );
  const incidentStatus = useSearchFilterValue<IncidentStatus>(
    "status",
    "incident",
  );
  const shipmentId = useSearchFilterValue<string>("shipment_id", "incident");
  const driverId = useSearchFilterValue<string>("driver_id", "incident");
  const loadId = useSearchFilterValue<string>("load_id", "incident");

  // Use the hook with search parameters
  const {
    data: incidents,
    isLoading,
    error,
    refetch,
  } = useListIncidents({
    pageIndex: pagination.pagination.pageIndex,
    pageSize: pagination.pagination.pageSize,
    search: incidentsQuery,
    type: incidentType,
    severity: incidentSeverity,
    status: incidentStatus,
    shipment_id: shipmentId,
    driver_id: driverId,
  });

  const handleDelete = async (incidentId: string) => {
    setIsDeletingIncident(true);
    try {
      const { error } = await supabase
        .from("incidents")
        .delete()
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.deleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.deleteSuccess,
        });
        setDeleteIncidentId(null);
        refetch();
      }
    } finally {
      setIsDeletingIncident(false);
    }
  };

  // Calculate incident summary from the current data
  const incidentSummary = incidents
    ? {
        totalIncidents: incidents.total,
        openIncidents: incidents.items.filter((i) => i.status === "reported")
          .length,
        investigatingIncidents: incidents.items.filter(
          (i) => i.status === "investigating",
        ).length,
        resolvedIncidents: incidents.items.filter(
          (i) => i.status === "resolved",
        ).length,
        closedIncidents: incidents.items.filter((i) => i.status === "closed")
          .length,
        criticalIncidents: incidents.items.filter(
          (i) => i.severity === "critical",
        ).length,
        highPriorityIncidents: incidents.items.filter(
          (i) => i.severity === "high",
        ).length,
        mediumPriorityIncidents: incidents.items.filter(
          (i) => i.severity === "medium",
        ).length,
        lowPriorityIncidents: incidents.items.filter(
          (i) => i.severity === "low",
        ).length,
        accidentIncidents: incidents.items.filter((i) => i.type === "accident")
          .length,
        mechanicalIncidents: incidents.items.filter(
          (i) => i.type === "mechanical",
        ).length,
        delayIncidents: incidents.items.filter((i) => i.type === "delay")
          .length,
        damageIncidents: incidents.items.filter((i) => i.type === "damage")
          .length,
        complianceIncidents: incidents.items.filter(
          (i) => i.type === "compliance",
        ).length,
        assignedIncidents: incidents.items.filter((i) => i.assigned_to).length,
        unassignedIncidents: incidents.items.filter((i) => !i.assigned_to)
          .length,
        newThisMonth: incidents.items.filter((i) => {
          const createdAt = new Date(i.created_at);
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          return createdAt >= startOfMonth;
        }).length,
        resolvedThisMonth: incidents.items.filter((i) => {
          if (!i.resolved_at) return false;
          const resolvedAt = new Date(i.resolved_at);
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          return resolvedAt >= startOfMonth;
        }).length,
        avgResolutionTime: calculateAverageResolutionTime(incidents.items),
      }
    : undefined;

  // Helper function to calculate average resolution time
  function calculateAverageResolutionTime(incidents: any[]): number {
    const resolvedIncidents = incidents.filter(
      (i) => i.resolved_at && i.created_at,
    );
    if (resolvedIncidents.length === 0) return 0;

    const totalResolutionTime = resolvedIncidents.reduce((sum, incident) => {
      const created = new Date(incident.created_at);
      const resolved = new Date(incident.resolved_at);
      const diffInHours =
        (resolved.getTime() - created.getTime()) / (1000 * 60 * 60);
      return sum + diffInHours;
    }, 0);

    return Math.round(totalResolutionTime / resolvedIncidents.length);
  }

  // Handler functions for incident management
  const handleCreateIncident = () => {
    window.location.href = "/app/console/incidents/create";
  };

  const handleEditIncident = (incidentId: string) => {
    window.location.href = `/app/console/incidents/${incidentId}/edit`;
  };

  const handleViewIncident = (incidentId: string) => {
    window.location.href = `/app/console/incidents/${incidentId}`;
  };

  const handleAssignIncident = async (
    incidentId: string,
    assigneeId: string,
  ) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({ assigned_to: assigneeId })
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.assignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.assignSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error assigning incident:", error);
    }
  };

  const handleUnassignIncident = async (incidentId: string) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({ assigned_to: null })
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.assignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.assignSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error unassigning incident:", error);
    }
  };

  const handleUpdateIncidentStatus = async (
    incidentId: string,
    newStatus: IncidentStatus,
  ) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({ status: newStatus })
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.updateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.updateSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating incident status:", error);
    }
  };

  const handleUpdateIncidentSeverity = async (
    incidentId: string,
    newSeverity: IncidentSeverity,
  ) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({ severity: newSeverity })
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.updateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.updateSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating incident severity:", error);
    }
  };

  const handleResolveIncident = async (incidentId: string) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({
          status: "resolved",
          resolved_at: new Date().toISOString(),
        })
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.resolveError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.resolveSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error resolving incident:", error);
    }
  };

  const handleCloseIncident = async (incidentId: string) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({
          status: "closed",
          closed_at: new Date().toISOString(),
        })
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.closeError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.closeSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error closing incident:", error);
    }
  };

  const handleReopenIncident = async (incidentId: string) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({
          status: "reported",
          resolved_at: null,
          closed_at: null,
        })
        .eq("id", incidentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.reopenError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.reopenSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error reopening incident:", error);
    }
  };

  const handleBulkDelete = async (incidentIds: string[]) => {
    setIsDeletingIncident(true);
    try {
      const { error } = await supabase
        .from("incidents")
        .delete()
        .in("id", incidentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkDeleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkDeleteSuccess,
        });
        setSelectedIncidents([]);
        refetch();
      }
    } finally {
      setIsDeletingIncident(false);
    }
  };

  const handleBulkAssign = async (
    incidentIds: string[],
    assigneeId: string,
  ) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({ assigned_to: assigneeId })
        .in("id", incidentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkAssignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkAssignSuccess,
        });
        setSelectedIncidents([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk assigning incidents:", error);
    }
  };

  const handleBulkStatusChange = async (
    incidentIds: string[],
    newStatus: IncidentStatus,
  ) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({ status: newStatus })
        .in("id", incidentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkStatusUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkStatusUpdateSuccess,
        });
        setSelectedIncidents([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk updating incident status:", error);
    }
  };

  const handleBulkSeverityChange = async (
    incidentIds: string[],
    newSeverity: IncidentSeverity,
  ) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({ severity: newSeverity })
        .in("id", incidentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkSeverityUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkSeverityUpdateSuccess,
        });
        setSelectedIncidents([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk updating incident severity:", error);
    }
  };

  const handleBulkResolve = async (incidentIds: string[]) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({
          status: "resolved",
          resolved_at: new Date().toISOString(),
        })
        .in("id", incidentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkResolveError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkResolveSuccess,
        });
        setSelectedIncidents([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk resolving incidents:", error);
    }
  };

  const handleBulkClose = async (incidentIds: string[]) => {
    try {
      const { error } = await supabase
        .from("incidents")
        .update({
          status: "closed",
          closed_at: new Date().toISOString(),
        })
        .in("id", incidentIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkCloseError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkCloseSuccess,
        });
        setSelectedIncidents([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk closing incidents:", error);
    }
  };

  const handleSelectIncident = (incidentId: string) => {
    setSelectedIncidents((prev) =>
      prev.includes(incidentId)
        ? prev.filter((id) => id !== incidentId)
        : [...prev, incidentId],
    );
  };

  const handleSelectAllIncidents = (selected: boolean) => {
    if (selected && incidents) {
      setSelectedIncidents(incidents.items.map((incident) => incident.id));
    } else {
      setSelectedIncidents([]);
    }
  };

  const handleGenerateReport = (filters: any) => {
    // Implementation for generating reports
    console.log("Generating report with filters:", filters);
  };

  const handleExportIncidents = (incidentIds: string[]) => {
    // Implementation for exporting incidents
    console.log("Exporting incidents:", incidentIds);
  };

  const handleViewAnalytics = () => {
    // Implementation for viewing analytics
    console.log("Viewing analytics");
  };

  return (
    <ConsoleIncidentsPage
      // Incident list data and loading states
      incidents={incidents || null}
      isLoadingIncidents={isLoading}
      incidentsError={error}
      // Search and filter state
      searchQuery={incidentsQuery}
      onSearchQueryChange={(query: string) => {
        // This would be handled by the search hook internally
      }}
      incidentType={incidentType}
      onIncidentTypeChange={(type: IncidentType | undefined) => {
        // This would be handled by the filter hook internally
      }}
      incidentSeverity={incidentSeverity}
      onIncidentSeverityChange={(severity: IncidentSeverity | undefined) => {
        // This would be handled by the filter hook internally
      }}
      incidentStatus={incidentStatus}
      onIncidentStatusChange={(status: IncidentStatus | undefined) => {
        // This would be handled by the filter hook internally
      }}
      // Pagination state
      pagination={{
        pageIndex: pagination.pagination.pageIndex,
        pageSize: pagination.pagination.pageSize,
        setPageIndex: (pageIndex: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageIndex,
          }));
        },
        setPageSize: (pageSize: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageSize,
          }));
        },
      }}
      // Delete functionality
      deleteIncidentId={deleteIncidentId}
      setDeleteIncidentId={setDeleteIncidentId}
      onDeleteIncident={handleDelete}
      isDeletingIncident={isDeletingIncident}
      // Incident management actions
      onCreateIncident={handleCreateIncident}
      onEditIncident={handleEditIncident}
      onViewIncident={handleViewIncident}
      onAssignIncident={handleAssignIncident}
      onUnassignIncident={handleUnassignIncident}
      onUpdateIncidentStatus={handleUpdateIncidentStatus}
      onUpdateIncidentSeverity={handleUpdateIncidentSeverity}
      onResolveIncident={handleResolveIncident}
      onCloseIncident={handleCloseIncident}
      onReopenIncident={handleReopenIncident}
      // Incident analytics and summary
      incidentSummary={incidentSummary}
      // Bulk operations
      selectedIncidents={selectedIncidents}
      onSelectIncident={handleSelectIncident}
      onSelectAllIncidents={handleSelectAllIncidents}
      onBulkDelete={handleBulkDelete}
      onBulkAssign={handleBulkAssign}
      onBulkStatusChange={handleBulkStatusChange}
      onBulkSeverityChange={handleBulkSeverityChange}
      onBulkResolve={handleBulkResolve}
      onBulkClose={handleBulkClose}
      // Related entity filters
      shipmentId={shipmentId}
      driverId={driverId}
      loadId={loadId}
      // Reporting and analytics
      onGenerateReport={handleGenerateReport}
      onExportIncidents={handleExportIncidents}
      onViewAnalytics={handleViewAnalytics}
      // Organization context
      organizationId={undefined}
      canManageIncidents={true}
      canDeleteIncidents={true}
      canCreateIncidents={true}
      canAssignIncidents={true}
      canResolveIncidents={true}
      canViewSensitiveData={true}
      canGenerateReports={true}
    />
  );
}
