import { Alert<PERSON>riangle } from "lucide-react";
import { Link } from "react-router";

import type { IncidentsQueryResult } from "@/pages/app/console/logistics/incidents/ListIncidents";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListIncidents from "@/pages/app/console/logistics/incidents/ListIncidents";
import { Enums } from "@/supabase/types";

type IncidentType = Enums<"incident_type">;
type IncidentSeverity = Enums<"incident_severity">;
type IncidentStatus = Enums<"incident_status">;

export interface Incident {
  id: string;
  title: string;
  description?: string;
  type: IncidentType;
  severity: IncidentSeverity;
  status: IncidentStatus;
  reported_by?: string;
  assigned_to?: string;
  shipment_id?: string;
  driver_id?: string;
  load_id?: string;
  stop_id?: string;
  verification_id?: string;
  location?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  closed_at?: string;
  organization_id?: string;
  shipment?: {
    id: string;
    status: string;
  } | null;
  driver?: {
    id: string;
    first_name: string;
    last_name: string;
  } | null;
  stop?: {
    id: string;
    label: string;
  } | null;
  load?: {
    id: string;
    label: string;
  } | null;
  verification?: {
    id: string;
    verified_at: string;
  } | null;
}

export interface IncidentsListResponse {
  items: Incident[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface IncidentSearchParams {
  pageIndex: number;
  pageSize: number;
  search?: string;
  type?: IncidentType;
  severity?: IncidentSeverity;
  status?: IncidentStatus;
  shipment_id?: string;
  driver_id?: string;
}

export interface DeleteIncidentHandler {
  (incidentId: string): Promise<void>;
}

export interface ConsoleIncidentsPageProps {
  // Incident list data and loading states
  incidents: IncidentsQueryResult | null;
  isLoadingIncidents: boolean;
  incidentsError: Error | null;

  // Search and filter state
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  incidentType: IncidentType | undefined;
  onIncidentTypeChange: (type: IncidentType | undefined) => void;
  incidentSeverity: IncidentSeverity | undefined;
  onIncidentSeverityChange: (severity: IncidentSeverity | undefined) => void;
  incidentStatus: IncidentStatus | undefined;
  onIncidentStatusChange: (status: IncidentStatus | undefined) => void;

  // Pagination state
  pagination: {
    pageIndex: number;
    pageSize: number;
    setPageIndex: (pageIndex: number) => void;
    setPageSize: (pageSize: number) => void;
  };

  // Delete functionality
  deleteIncidentId: string | null;
  setDeleteIncidentId: (id: string | null) => void;
  onDeleteIncident: DeleteIncidentHandler;
  isDeletingIncident: boolean;

  // Incident management actions
  onCreateIncident?: () => void;
  onEditIncident?: (incidentId: string) => void;
  onViewIncident?: (incidentId: string) => void;
  onAssignIncident?: (incidentId: string, assigneeId: string) => void;
  onUnassignIncident?: (incidentId: string) => void;
  onUpdateIncidentStatus?: (
    incidentId: string,
    newStatus: IncidentStatus,
  ) => void;
  onUpdateIncidentSeverity?: (
    incidentId: string,
    newSeverity: IncidentSeverity,
  ) => void;
  onResolveIncident?: (incidentId: string) => void;
  onCloseIncident?: (incidentId: string) => void;
  onReopenIncident?: (incidentId: string) => void;

  // Incident analytics and summary
  incidentSummary?: {
    totalIncidents: number;
    openIncidents: number;
    investigatingIncidents: number;
    resolvedIncidents: number;
    closedIncidents: number;
    criticalIncidents: number;
    highPriorityIncidents: number;
    mediumPriorityIncidents: number;
    lowPriorityIncidents: number;
    accidentIncidents: number;
    mechanicalIncidents: number;
    delayIncidents: number;
    damageIncidents: number;
    complianceIncidents: number;
    assignedIncidents: number;
    unassignedIncidents: number;
    newThisMonth: number;
    resolvedThisMonth: number;
    avgResolutionTime: number; // in hours
  };

  // Bulk operations
  selectedIncidents?: string[];
  onSelectIncident?: (incidentId: string) => void;
  onSelectAllIncidents?: (selected: boolean) => void;
  onBulkDelete?: (incidentIds: string[]) => Promise<void>;
  onBulkAssign?: (incidentIds: string[], assigneeId: string) => Promise<void>;
  onBulkStatusChange?: (
    incidentIds: string[],
    newStatus: IncidentStatus,
  ) => Promise<void>;
  onBulkSeverityChange?: (
    incidentIds: string[],
    newSeverity: IncidentSeverity,
  ) => Promise<void>;
  onBulkResolve?: (incidentIds: string[]) => Promise<void>;
  onBulkClose?: (incidentIds: string[]) => Promise<void>;

  // Filter and sorting
  onFilterByType?: (type: IncidentType) => void;
  onFilterBySeverity?: (severity: IncidentSeverity) => void;
  onFilterByStatus?: (status: IncidentStatus) => void;
  onFilterByAssignmentStatus?: (status: "assigned" | "unassigned") => void;
  onFilterByDateRange?: (startDate: Date, endDate: Date) => void;
  onSortByCreatedAt?: (order: "asc" | "desc") => void;
  onSortByResolvedAt?: (order: "asc" | "desc") => void;
  onSortBySeverity?: (order: "asc" | "desc") => void;

  // Related entity filters
  shipmentId?: string;
  onShipmentIdChange?: (shipmentId: string | undefined) => void;
  driverId?: string;
  onDriverIdChange?: (driverId: string | undefined) => void;
  loadId?: string;
  onLoadIdChange?: (loadId: string | undefined) => void;

  // Reporting and analytics
  onGenerateReport?: (filters: IncidentSearchParams) => void;
  onExportIncidents?: (incidentIds: string[]) => void;
  onViewAnalytics?: () => void;

  // Organization context
  organizationId?: string;
  canManageIncidents?: boolean;
  canDeleteIncidents?: boolean;
  canCreateIncidents?: boolean;
  canAssignIncidents?: boolean;
  canResolveIncidents?: boolean;
  canViewSensitiveData?: boolean;
  canGenerateReports?: boolean;
}

const i18n = {
  en: {
    title: "Incidents",
    addButton: "Report Incident",
    deleteDialog: {
      title: "Are you sure?",
      description:
        "This action cannot be undone. This will permanently delete the incident and all associated data.",
      cancel: "Cancel",
      confirm: "Delete",
    },
    toast: {
      deleteSuccess: "Incident deleted successfully.",
      deleteError: "Failed to delete incident. Please try again.",
    },
    summary: {
      total: "Total Incidents",
      open: "Open",
      investigating: "Investigating",
      resolved: "Resolved",
      closed: "Closed",
      critical: "Critical",
      high: "High Priority",
      medium: "Medium Priority",
      low: "Low Priority",
      accident: "Accidents",
      mechanical: "Mechanical",
      delay: "Delays",
      damage: "Damage",
      compliance: "Compliance",
      assigned: "Assigned",
      unassigned: "Unassigned",
      newThisMonth: "New This Month",
      resolvedThisMonth: "Resolved This Month",
      avgResolutionTime: "Avg Resolution Time",
    },
    search: {
      placeholder: "Search incidents...",
      noResults: "No incidents found",
      filtering: "Filtering incidents",
    },
    actions: {
      view: "View Details",
      edit: "Edit Incident",
      delete: "Delete Incident",
      assign: "Assign Incident",
      unassign: "Unassign Incident",
      resolve: "Mark Resolved",
      close: "Close Incident",
      reopen: "Reopen Incident",
      updateStatus: "Update Status",
      updateSeverity: "Update Severity",
      bulkDelete: "Delete Selected",
      bulkAssign: "Assign Selected",
      bulkStatusChange: "Change Status",
      bulkSeverityChange: "Change Severity",
      bulkResolve: "Resolve Selected",
      bulkClose: "Close Selected",
      generateReport: "Generate Report",
      exportIncidents: "Export Incidents",
      viewAnalytics: "View Analytics",
    },
    filters: {
      byType: "Filter by Type",
      bySeverity: "Filter by Severity",
      byStatus: "Filter by Status",
      byAssignment: "Filter by Assignment",
      byDateRange: "Filter by Date Range",
      assigned: "Assigned",
      unassigned: "Unassigned",
    },
    types: {
      accident: "Accident",
      delay: "Delay",
      damage: "Damage",
      theft: "Theft",
      compliance: "Compliance",
      mechanical: "Mechanical",
      weather: "Weather",
      other: "Other",
    },
    severities: {
      critical: "Critical",
      high: "High",
      medium: "Medium",
      low: "Low",
    },
    statuses: {
      reported: "Reported",
      investigating: "Investigating",
      resolved: "Resolved",
      closed: "Closed",
    },
  },
  links: {
    create: "/app/console/incidents/create",
    view: (id: string) => `/app/console/incidents/${id}`,
    edit: (id: string) => `/app/console/incidents/${id}/edit`,
  },
};

export const ConsoleIncidentsPage = ({
  incidents,
  isLoadingIncidents,
  incidentsError,
  searchQuery,
  onSearchQueryChange,
  incidentType,
  onIncidentTypeChange,
  incidentSeverity,
  onIncidentSeverityChange,
  incidentStatus,
  onIncidentStatusChange,
  pagination,
  deleteIncidentId,
  setDeleteIncidentId,
  onDeleteIncident,
  isDeletingIncident,
  onCreateIncident,
  onEditIncident,
  onViewIncident,
  onAssignIncident,
  onUnassignIncident,
  onUpdateIncidentStatus,
  onUpdateIncidentSeverity,
  onResolveIncident,
  onCloseIncident,
  onReopenIncident,
  incidentSummary,
  selectedIncidents = [],
  onSelectIncident,
  onSelectAllIncidents,
  onBulkDelete,
  onBulkAssign,
  onBulkStatusChange,
  onBulkSeverityChange,
  onBulkResolve,
  onBulkClose,
  onFilterByType,
  onFilterBySeverity,
  onFilterByStatus,
  onFilterByAssignmentStatus,
  onFilterByDateRange,
  onSortByCreatedAt,
  onSortByResolvedAt,
  onSortBySeverity,
  shipmentId,
  onShipmentIdChange,
  driverId,
  onDriverIdChange,
  loadId,
  onLoadIdChange,
  onGenerateReport,
  onExportIncidents,
  onViewAnalytics,
  organizationId,
  canManageIncidents = true,
  canDeleteIncidents = true,
  canCreateIncidents = true,
  canAssignIncidents = true,
  canResolveIncidents = true,
  canViewSensitiveData = true,
  canGenerateReports = true,
}: ConsoleIncidentsPageProps) => {
  const hasSelectedIncidents = selectedIncidents.length > 0;

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <AlertTriangle className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          {hasSelectedIncidents && (
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                {selectedIncidents.length} selected
              </span>
              {onBulkDelete && canDeleteIncidents && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onBulkDelete(selectedIncidents)}
                  disabled={isDeletingIncident}
                >
                  {i18n.en.actions.bulkDelete}
                </Button>
              )}
              {onBulkAssign && canAssignIncidents && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkAssign(selectedIncidents, "assignee_1")}
                  disabled={isDeletingIncident}
                >
                  {i18n.en.actions.bulkAssign}
                </Button>
              )}
              {onBulkStatusChange && canManageIncidents && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    onBulkStatusChange(selectedIncidents, "investigating")
                  }
                  disabled={isDeletingIncident}
                >
                  {i18n.en.actions.bulkStatusChange}
                </Button>
              )}
              {onBulkResolve && canResolveIncidents && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkResolve(selectedIncidents)}
                  disabled={isDeletingIncident}
                >
                  {i18n.en.actions.bulkResolve}
                </Button>
              )}
            </div>
          )}
          {canGenerateReports && (
            <Button
              variant="outline"
              onClick={() =>
                onGenerateReport &&
                onGenerateReport({
                  pageIndex: pagination.pageIndex,
                  pageSize: pagination.pageSize,
                  search: searchQuery,
                  type: incidentType,
                  severity: incidentSeverity,
                  status: incidentStatus,
                  shipment_id: shipmentId,
                  driver_id: driverId,
                })
              }
              disabled={isLoadingIncidents}
            >
              {i18n.en.actions.generateReport}
            </Button>
          )}
          {canCreateIncidents && (
            <Button asChild disabled={isLoadingIncidents}>
              <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
            </Button>
          )}
        </div>
      </div>

      {/* Incident Summary Cards */}
      {incidentSummary && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4 lg:grid-cols-8">
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold">
              {incidentSummary.totalIncidents}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.total}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-red-600">
              {incidentSummary.openIncidents}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.open}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-orange-600">
              {incidentSummary.investigatingIncidents}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.investigating}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-green-600">
              {incidentSummary.resolvedIncidents}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.resolved}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-gray-600">
              {incidentSummary.closedIncidents}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.closed}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-purple-600">
              {incidentSummary.criticalIncidents}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.critical}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-blue-600">
              {incidentSummary.newThisMonth}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.newThisMonth}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-cyan-600">
              {incidentSummary.avgResolutionTime}h
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.avgResolutionTime}
            </div>
          </div>
        </div>
      )}

      {/* Error Alert */}
      {incidentsError && <ErrorAlert error={incidentsError} />}

      {/* Incidents List */}
      <ListIncidents
        loading={isLoadingIncidents}
        incidents={incidents}
        onDelete={setDeleteIncidentId}
      />

      {/* Delete Confirmation Dialog */}
      <DialogConfirmation
        open={!!deleteIncidentId}
        onOpenChange={(open) => {
          if (!open) setDeleteIncidentId(null);
        }}
        onClick={() => {
          if (deleteIncidentId) {
            return onDeleteIncident(deleteIncidentId);
          }
          return Promise.resolve();
        }}
        title={i18n.en.deleteDialog.title}
        description={i18n.en.deleteDialog.description}
        action={i18n.en.deleteDialog.confirm}
        cancel={i18n.en.deleteDialog.cancel}
        useTrigger={false}
      />
    </div>
  );
};
