"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { AlertCircle, Link as LinkIcon } from "lucide-react";
import { Link } from "react-router";

import type { UseDataTableProps } from "@/components/tables";

import { useListIncidents } from "@/api/incidents/use-list-incidents";
import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Enums } from "@/supabase/types";

// Assuming these types exist or will be created
type IncidentType = Enums<"incident_type">;
type IncidentSeverity = Enums<"incident_severity">;
type IncidentStatus = Enums<"incident_status">;

// Define badge variant types to prevent using 'any'
type BadgeVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "accent";

// Use the actual structure from the API
export type IncidentsQueryResult = ReturnType<typeof useListIncidents>["data"];
export type IncidentsType = NonNullable<IncidentsQueryResult>["items"];
export type IncidentItemType = IncidentsType[number];
export type TableProps = UseDataTableProps<IncidentItemType, IncidentsType>;

const i18n = {
  en: {
    noIncident: "There are no incidents yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search incidents...",
    },
    headers: {
      id: "ID",
      title: "Title",
      type: "Type",
      severity: "Severity",
      status: "Status",
      reported_at: "Reported At",
      resolved_at: "Resolved At",
      actions: "Actions",
    },
    filters: {
      type: "Type",
      severity: "Severity",
      status: "Status",
      options: {
        type: {
          ALL: "All Types",
          ACCIDENT: "Accident",
          DELAY: "Delay",
          DAMAGE: "Damage",
          THEFT: "Theft",
          COMPLIANCE: "Compliance",
          MECHANICAL: "Mechanical",
          WEATHER: "Weather",
          OTHER: "Other",
        },
        severity: {
          ALL: "All Severities",
          CRITICAL: "Critical",
          HIGH: "High",
          MEDIUM: "Medium",
          LOW: "Low",
        },
        status: {
          ALL: "All Statuses",
          OPEN: "Reported",
          IN_PROGRESS: "Investigating",
          RESOLVED: "Resolved",
          CLOSED: "Closed",
        },
      },
    },
  },
  links: {
    incidents: "/app/console/incidents/[id]",
  },
};

const groupName = "incident";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.type.ALL },
      { value: "accident", label: i18n.en.filters.options.type.ACCIDENT },
      { value: "delay", label: i18n.en.filters.options.type.DELAY },
      { value: "damage", label: i18n.en.filters.options.type.DAMAGE },
      { value: "theft", label: i18n.en.filters.options.type.THEFT },
      { value: "compliance", label: i18n.en.filters.options.type.COMPLIANCE },
      { value: "mechanical", label: i18n.en.filters.options.type.MECHANICAL },
      { value: "weather", label: i18n.en.filters.options.type.WEATHER },
      { value: "other", label: i18n.en.filters.options.type.OTHER },
    ],
  },
  {
    id: "severity",
    label: i18n.en.filters.severity,
    options: [
      { value: null, label: i18n.en.filters.options.severity.ALL },
      { value: "critical", label: i18n.en.filters.options.severity.CRITICAL },
      { value: "high", label: i18n.en.filters.options.severity.HIGH },
      { value: "medium", label: i18n.en.filters.options.severity.MEDIUM },
      { value: "low", label: i18n.en.filters.options.severity.LOW },
    ],
  },
  {
    id: "status",
    label: i18n.en.filters.status,
    options: [
      { value: null, label: i18n.en.filters.options.status.ALL },
      { value: "reported", label: i18n.en.filters.options.status.OPEN },
      {
        value: "investigating",
        label: i18n.en.filters.options.status.IN_PROGRESS,
      },
      { value: "resolved", label: i18n.en.filters.options.status.RESOLVED },
      { value: "closed", label: i18n.en.filters.options.status.CLOSED },
    ],
  },
];

// Links object used in the table
const tableLinks = {
  incidents: "/app/console/incidents/[id]",
};

export default function ListIncidents({
  loading = false,
  incidents,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onDelete,
}: PropsWithChildren<{
  loading?: boolean;
  incidents?: IncidentsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onDelete?: (id: string) => void;
}>) {
  return (
    <ListTable
      loading={loading}
      data={incidents}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      i18n={{
        emptyText: i18n.en.noIncident,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      filterGroups={filterGroups}
      columns={({ i18n, TableActions }) => [
        selectColumn as ColumnDef<IncidentItemType, IncidentsType>,
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.id || "ID"}
            />
          ),
          cell: ({ row }) => (
            <Link
              to={tableLinks.incidents.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "title",
          accessorKey: "title",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.title || "Title"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("title") || "—"}</div>,
        },
        {
          id: "type",
          accessorKey: "type",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.type || "Type"}
            />
          ),
          cell: ({ row }) => (
            <Badge variant="outline">
              {(row.getValue("type") as string)
                ?.replace(/_/g, " ")
                ?.toUpperCase() || "—"}
            </Badge>
          ),
        },
        {
          id: "severity",
          accessorKey: "severity",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.severity || "Severity"}
            />
          ),
          cell: ({ row }) => {
            const severity = row.getValue("severity") as string;
            let badgeClass = "";

            switch (severity) {
              case "critical":
                badgeClass = "bg-red-100 text-red-700";
                break;
              case "high":
                badgeClass = "bg-orange-100 text-orange-700";
                break;
              case "medium":
                badgeClass = "bg-yellow-100 text-yellow-700";
                break;
              case "low":
                badgeClass = "bg-green-100 text-green-700";
                break;
              default:
                badgeClass = "text-muted-foreground";
            }

            return (
              <Badge
                variant="accent"
                className={`${badgeClass} flex items-center gap-1`}
              >
                <AlertCircle className="h-3 w-3" />
                <span>
                  {severity?.replace(/_/g, " ")?.toUpperCase() || "—"}
                </span>
              </Badge>
            );
          },
        },
        {
          id: "status",
          accessorKey: "status",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.status || "Status"}
            />
          ),
          cell: ({ row }) => {
            const status = row.getValue("status") as string;
            let variant: BadgeVariant = "outline";

            switch (status) {
              case "open":
              case "reported":
                variant = "destructive";
                break;
              case "in_progress":
              case "investigating":
                variant = "default";
                break;
              case "resolved":
                variant = "accent";
                break;
              case "closed":
                variant = "secondary";
                break;
            }

            return (
              <Badge variant={variant}>
                {status?.replace(/_/g, " ")?.toUpperCase() || "—"}
              </Badge>
            );
          },
        },
        {
          id: "reported_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.reported_at || "Reported At"}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "resolved_at",
          accessorKey: "resolved_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.resolved_at || "Resolved At"}
            />
          ),
          cell: ({ row }) => {
            const resolvedAt = row.getValue("resolved_at");
            return resolvedAt ? (
              <TimeAgo
                loading={loading}
                date={new Date(resolvedAt as string)}
                className="text-muted-foreground text-sm"
              />
            ) : (
              <span className="text-muted-foreground text-sm">—</span>
            );
          },
        },
        {
          id: "actions",
          meta: {
            className: "w-[32px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TableActions table={table} i18n={i18n} />
            </div>
          ),
          cell: ({ row }) => {
            const hasLinks = row.original.shipment_id || row.original.load_id;

            return (
              <div className="flex size-full items-center justify-end gap-2">
                {hasLinks && (
                  <Button
                    variant="ghost"
                    size="icon"
                    title="View related items"
                    className="h-8 w-8"
                  >
                    <LinkIcon className="h-4 w-4" />
                  </Button>
                )}
                <Button variant="ghost" size="sm" asChild>
                  <Link
                    to={tableLinks.incidents.replace("[id]", row.original.id)}
                  >
                    View
                  </Link>
                </Button>
                {onDelete && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive"
                    onClick={() => onDelete(row.original.id)}
                  >
                    Delete
                  </Button>
                )}
              </div>
            );
          },
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
