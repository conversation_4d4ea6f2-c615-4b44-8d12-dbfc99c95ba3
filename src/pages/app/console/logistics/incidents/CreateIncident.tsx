import { useNavigate } from "react-router";

import { useCreateIncident } from "@/api/incidents/use-create-incident";
import IncidentForm, {
  IncidentFormValues,
} from "@/components/forms/IncidentForm";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "Report Incident",
    toasts: {
      success: "Incident reported successfully",
      error: "Failed to report incident",
    },
  },
};

export default function CreateIncidentPage() {
  const navigate = useNavigate();

  const createIncident = useCreateIncident({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/incidents/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: IncidentFormValues) => {
    // Map form values to the API expected format
    const incidentData = {
      title: values.title,
      summary: values.description,
      type: values.type,
      severity: values.severity,
      status: values.status || "reported",
      shipment_id: values.organizationId || "", // This needs to be updated with actual shipment ID
    };

    createIncident.mutate(incidentData);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/incidents")}
        >
          Back to Incidents
        </Button>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <IncidentForm
          onSubmit={handleSubmit}
          defaultValues={{
            title: "",
            description: "",
            type: "other",
            severity: "medium",
            status: "reported",
          }}
        />
      </div>
    </div>
  );
}
