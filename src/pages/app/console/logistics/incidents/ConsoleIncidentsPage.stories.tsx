import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleIncidentsPage } from "./ConsoleIncidentsPage";

const meta: Meta<typeof ConsoleIncidentsPage> = {
  title: "Pages/Console/Logistics/Incidents",
  component: ConsoleIncidentsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/incidents" },
    }),
    docs: {
      description: {
        component: `
The ConsoleIncidentsPage component provides a comprehensive interface for managing incidents within the console application.

## Features
- **Incident Management**: Display incidents with pagination, search, and filtering
- **CRUD Operations**: Create, read, update, and delete incident records
- **Status Management**: Track incident progress from reported to closed
- **Severity Tracking**: Categorize incidents by severity level (critical, high, medium, low)
- **Type Classification**: Organize incidents by type (accident, mechanical, delay, etc.)
- **Assignment System**: Assign incidents to team members and track responsibility
- **Bulk Operations**: Select and manage multiple incidents simultaneously
- **Search & Filter**: Real-time search with type, severity, and status filtering
- **Analytics Dashboard**: Incident summary statistics and resolution metrics
- **Resolution Workflow**: Track incident resolution and closure processes
- **Delete Confirmation**: Safe deletion with confirmation dialogs

## Usage
This component follows the established console pattern with a presentation component that receives all data and handlers as props, ensuring clean separation of concerns between data management and UI rendering.
        `,
      },
    },
  },
  args: {
    // Default props
    isLoadingIncidents: false,
    incidentsError: null,
    searchQuery: "",
    onSearchQueryChange: fn(),
    incidentType: undefined,
    onIncidentTypeChange: fn(),
    incidentSeverity: undefined,
    onIncidentSeverityChange: fn(),
    incidentStatus: undefined,
    onIncidentStatusChange: fn(),
    pagination: {
      pageIndex: 0,
      pageSize: 10,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    deleteIncidentId: null,
    setDeleteIncidentId: fn(),
    onDeleteIncident: fn(),
    isDeletingIncident: false,
    onCreateIncident: fn(),
    onEditIncident: fn(),
    onViewIncident: fn(),
    onAssignIncident: fn(),
    onUnassignIncident: fn(),
    onUpdateIncidentStatus: fn(),
    onUpdateIncidentSeverity: fn(),
    onResolveIncident: fn(),
    onCloseIncident: fn(),
    onReopenIncident: fn(),
    selectedIncidents: [],
    onSelectIncident: fn(),
    onSelectAllIncidents: fn(),
    onBulkDelete: fn(),
    onBulkAssign: fn(),
    onBulkStatusChange: fn(),
    onBulkSeverityChange: fn(),
    onBulkResolve: fn(),
    onBulkClose: fn(),
    onFilterByType: fn(),
    onFilterBySeverity: fn(),
    onFilterByStatus: fn(),
    onFilterByAssignmentStatus: fn(),
    onFilterByDateRange: fn(),
    onSortByCreatedAt: fn(),
    onSortByResolvedAt: fn(),
    onSortBySeverity: fn(),
    shipmentId: undefined,
    onShipmentIdChange: fn(),
    driverId: undefined,
    onDriverIdChange: fn(),
    loadId: undefined,
    onLoadIdChange: fn(),
    onGenerateReport: fn(),
    onExportIncidents: fn(),
    onViewAnalytics: fn(),
    organizationId: "org_1",
    canManageIncidents: true,
    canDeleteIncidents: true,
    canCreateIncidents: true,
    canAssignIncidents: true,
    canResolveIncidents: true,
    canViewSensitiveData: true,
    canGenerateReports: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock incident data for stories
const mockIncidents = {
  items: [
    {
      id: "inc_1",
      title: "Vehicle Breakdown - Engine Failure",
      summary:
        "Driver reported engine failure on I-95 near mile marker 45. Vehicle towed to nearest service center.",
      type: "mechanical" as const,
      severity: "critical" as const,
      status: "investigating" as const,
      shipment_id: "shp_001",
      driver_id: "drv_001",
      load_id: "load_001",
      stop_id: null,
      verification_id: null,
      created_at: "2024-06-25T14:30:00Z",
      shipment: {
        id: "shp_001",
        status: "in_progress",
      },
      driver: {
        id: "drv_001",
        first_name: "John",
        last_name: "Smith",
      },
      stop: null,
      load: {
        id: "load_001",
        label: "Electronics Shipment - Miami to Jacksonville",
      },
      verification: null,
    },
    {
      id: "inc_2",
      title: "Delivery Delay - Traffic Accident",
      summary:
        "Traffic accident on highway caused 3-hour delay. Customer notified of revised ETA.",
      type: "delay" as const,
      severity: "medium" as const,
      status: "resolved" as const,
      shipment_id: "shp_002",
      driver_id: "drv_002",
      load_id: null,
      stop_id: "stop_001",
      verification_id: null,
      created_at: "2024-06-24T09:15:00Z",
      shipment: {
        id: "shp_002",
        status: "completed",
      },
      driver: {
        id: "drv_002",
        first_name: "Maria",
        last_name: "Rodriguez",
      },
      stop: {
        id: "stop_001",
        label: "Orlando Distribution Center",
      },
      load: null,
      verification: null,
    },
    {
      id: "inc_3",
      title: "Safety Violation - Hours of Service",
      summary:
        "Driver exceeded maximum driving hours. Immediate rest period enforced.",
      type: "other" as const,
      severity: "high" as const,
      status: "closed" as const,
      shipment_id: "shp_003",
      driver_id: "drv_003",
      load_id: null,
      stop_id: null,
      verification_id: "ver_001",
      created_at: "2024-06-23T22:45:00Z",
      shipment: {
        id: "shp_003",
        status: "completed",
      },
      driver: {
        id: "drv_003",
        first_name: "Carlos",
        last_name: "Johnson",
      },
      stop: null,
      load: null,
      verification: {
        id: "ver_001",
        verified_at: "2024-06-24T06:00:00Z",
      },
    },
    {
      id: "inc_4",
      title: "Package Damage - Forklift Accident",
      summary:
        "Forklift operator damaged packages during loading. 5 packages affected.",
      type: "damage" as const,
      severity: "medium" as const,
      status: "reported" as const,
      shipment_id: "shp_004",
      driver_id: null,
      load_id: "load_002",
      stop_id: "stop_002",
      verification_id: null,
      created_at: "2024-06-26T11:20:00Z",
      shipment: {
        id: "shp_004",
        status: "pending",
      },
      driver: null,
      stop: {
        id: "stop_002",
        label: "Miami Warehouse",
      },
      load: {
        id: "load_002",
        label: "Fragile Electronics - Miami to Orlando",
      },
      verification: null,
    },
    {
      id: "inc_5",
      title: "Weather Delay - Hurricane Warning",
      summary:
        "Hurricane warning issued for delivery route. Operations suspended until further notice.",
      type: "weather" as const,
      severity: "critical" as const,
      status: "investigating" as const,
      shipment_id: "shp_005",
      driver_id: "drv_004",
      load_id: null,
      stop_id: null,
      verification_id: null,
      created_at: "2024-06-26T16:00:00Z",
      shipment: {
        id: "shp_005",
        status: "in_progress",
      },
      driver: {
        id: "drv_004",
        first_name: "Lisa",
        last_name: "Garcia",
      },
      stop: null,
      load: null,
      verification: null,
    },
    {
      id: "inc_6",
      title: "Minor Accident - Parking Lot Fender Bender",
      summary:
        "Minor collision in customer parking lot. No injuries, minimal vehicle damage.",
      type: "accident" as const,
      severity: "low" as const,
      status: "resolved" as const,
      shipment_id: "shp_006",
      driver_id: "drv_005",
      load_id: null,
      stop_id: "stop_003",
      verification_id: null,
      created_at: "2024-06-22T13:45:00Z",
      shipment: {
        id: "shp_006",
        status: "completed",
      },
      driver: {
        id: "drv_005",
        first_name: "Michael",
        last_name: "Brown",
      },
      stop: {
        id: "stop_003",
        label: "Target Distribution Center",
      },
      load: null,
      verification: null,
    },
  ],
  total: 6,
};

const incidentSummary = {
  totalIncidents: 6,
  openIncidents: 1,
  investigatingIncidents: 2,
  resolvedIncidents: 2,
  closedIncidents: 1,
  criticalIncidents: 2,
  highPriorityIncidents: 1,
  mediumPriorityIncidents: 2,
  lowPriorityIncidents: 1,
  accidentIncidents: 1,
  mechanicalIncidents: 1,
  delayIncidents: 1,
  damageIncidents: 1,
  complianceIncidents: 1,
  assignedIncidents: 5,
  unassignedIncidents: 1,
  newThisMonth: 4,
  resolvedThisMonth: 2,
  avgResolutionTime: 8, // hours
};

export const Default: Story = {
  args: {
    incidents: mockIncidents,
    incidentSummary,
  },
};

export const Loading: Story = {
  args: {
    isLoadingIncidents: true,
    incidents: null,
    incidentSummary: undefined,
  },
};

export const EmptyState: Story = {
  args: {
    incidents: {
      items: [],
      total: 0,
    },
    incidentSummary: {
      totalIncidents: 0,
      openIncidents: 0,
      investigatingIncidents: 0,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 0,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 0,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 0,
      unassignedIncidents: 0,
      newThisMonth: 0,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const CriticalIncidentsOnly: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.filter((i) => i.severity === "critical"),
      total: 2,
    },
    incidentSeverity: "critical",
    incidentSummary: {
      totalIncidents: 2,
      openIncidents: 0,
      investigatingIncidents: 2,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 2,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 1,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 2,
      unassignedIncidents: 0,
      newThisMonth: 2,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const OpenIncidentsOnly: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.filter((i) => i.status === "reported"),
      total: 1,
    },
    incidentStatus: "reported",
    incidentSummary: {
      totalIncidents: 1,
      openIncidents: 1,
      investigatingIncidents: 0,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 0,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 1,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 0,
      delayIncidents: 0,
      damageIncidents: 1,
      complianceIncidents: 0,
      assignedIncidents: 0,
      unassignedIncidents: 1,
      newThisMonth: 1,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const MechanicalIncidentsOnly: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.filter((i) => i.type === "mechanical"),
      total: 1,
    },
    incidentType: "mechanical",
    incidentSummary: {
      totalIncidents: 1,
      openIncidents: 0,
      investigatingIncidents: 1,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 1,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 1,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 1,
      unassignedIncidents: 0,
      newThisMonth: 1,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const ResolvedIncidents: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.filter((i) => i.status === "resolved"),
      total: 2,
    },
    incidentStatus: "resolved",
    incidentSummary: {
      totalIncidents: 2,
      openIncidents: 0,
      investigatingIncidents: 0,
      resolvedIncidents: 2,
      closedIncidents: 0,
      criticalIncidents: 0,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 1,
      lowPriorityIncidents: 1,
      accidentIncidents: 1,
      mechanicalIncidents: 0,
      delayIncidents: 1,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 2,
      unassignedIncidents: 0,
      newThisMonth: 1,
      resolvedThisMonth: 2,
      avgResolutionTime: 12,
    },
  },
};

export const SearchResults: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.filter(
        (i) =>
          i.title.toLowerCase().includes("breakdown") ||
          i.summary?.toLowerCase().includes("breakdown"),
      ),
      total: 1,
    },
    searchQuery: "breakdown",
    incidentSummary: {
      totalIncidents: 1,
      openIncidents: 0,
      investigatingIncidents: 1,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 1,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 1,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 1,
      unassignedIncidents: 0,
      newThisMonth: 1,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const PaginatedResults: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.slice(0, 3),
      total: 25,
    },
    pagination: {
      pageIndex: 0,
      pageSize: 3,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    incidentSummary: {
      totalIncidents: 25,
      openIncidents: 8,
      investigatingIncidents: 7,
      resolvedIncidents: 6,
      closedIncidents: 4,
      criticalIncidents: 5,
      highPriorityIncidents: 8,
      mediumPriorityIncidents: 7,
      lowPriorityIncidents: 5,
      accidentIncidents: 3,
      mechanicalIncidents: 6,
      delayIncidents: 4,
      damageIncidents: 5,
      complianceIncidents: 7,
      assignedIncidents: 20,
      unassignedIncidents: 5,
      newThisMonth: 12,
      resolvedThisMonth: 8,
      avgResolutionTime: 15,
    },
  },
};

export const DeleteConfirmation: Story = {
  args: {
    incidents: mockIncidents,
    deleteIncidentId: "inc_4",
    incidentSummary,
  },
};

export const DeletingIncident: Story = {
  args: {
    incidents: mockIncidents,
    deleteIncidentId: "inc_4",
    isDeletingIncident: true,
    incidentSummary,
  },
};

export const BulkOperationsActive: Story = {
  args: {
    incidents: mockIncidents,
    selectedIncidents: ["inc_1", "inc_2", "inc_4"],
    incidentSummary,
  },
};

export const UnassignedIncidents: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.filter((i) => !i.driver_id),
      total: 1,
    },
    incidentSummary: {
      totalIncidents: 1,
      openIncidents: 1,
      investigatingIncidents: 0,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 0,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 1,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 0,
      delayIncidents: 0,
      damageIncidents: 1,
      complianceIncidents: 0,
      assignedIncidents: 0,
      unassignedIncidents: 1,
      newThisMonth: 1,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const HighVolumeIncidents: Story = {
  args: {
    incidents: {
      items: mockIncidents.items,
      total: 1247,
    },
    pagination: {
      pageIndex: 23,
      pageSize: 50,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    incidentSummary: {
      totalIncidents: 1247,
      openIncidents: 234,
      investigatingIncidents: 189,
      resolvedIncidents: 567,
      closedIncidents: 257,
      criticalIncidents: 45,
      highPriorityIncidents: 234,
      mediumPriorityIncidents: 567,
      lowPriorityIncidents: 401,
      accidentIncidents: 89,
      mechanicalIncidents: 234,
      delayIncidents: 345,
      damageIncidents: 156,
      complianceIncidents: 423,
      assignedIncidents: 1098,
      unassignedIncidents: 149,
      newThisMonth: 78,
      resolvedThisMonth: 156,
      avgResolutionTime: 24,
    },
  },
};

export const WeatherIncidents: Story = {
  args: {
    incidents: {
      items: [
        mockIncidents.items[4], // Weather incident
        {
          id: "inc_weather_2",
          title: "Severe Thunderstorm - Route Closure",
          summary:
            "Severe thunderstorm with hail forced temporary route closure. Seeking alternate route.",
          type: "weather" as const,
          severity: "high" as const,
          status: "investigating" as const,
          shipment_id: "shp_007",
          driver_id: "drv_006",
          load_id: null,
          stop_id: null,
          verification_id: null,
          created_at: "2024-06-26T19:15:00Z",
          shipment: {
            id: "shp_007",
            status: "in_progress",
          },
          driver: {
            id: "drv_006",
            first_name: "David",
            last_name: "Wilson",
          },
          stop: null,
          load: null,
          verification: null,
        },
      ],
      total: 2,
    },
    incidentType: "weather",
    incidentSummary: {
      totalIncidents: 2,
      openIncidents: 0,
      investigatingIncidents: 2,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 1,
      highPriorityIncidents: 1,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 0,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 2,
      unassignedIncidents: 0,
      newThisMonth: 2,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const OtherIncidents: Story = {
  args: {
    incidents: {
      items: [
        mockIncidents.items[2], // Other incident (Safety violation)
        {
          id: "inc_other_2",
          title: "DOT Inspection - Violation Found",
          summary:
            "DOT inspection revealed brake system violation. Vehicle out of service until repairs completed.",
          type: "other" as const,
          severity: "critical" as const,
          status: "investigating" as const,
          shipment_id: "shp_008",
          driver_id: "drv_007",
          load_id: null,
          stop_id: null,
          verification_id: "ver_002",
          created_at: "2024-06-25T10:30:00Z",
          shipment: {
            id: "shp_008",
            status: "in_progress",
          },
          driver: {
            id: "drv_007",
            first_name: "Patricia",
            last_name: "Davis",
          },
          stop: null,
          load: null,
          verification: {
            id: "ver_002",
            verified_at: "2024-06-25T11:00:00Z",
          },
        },
      ],
      total: 2,
    },
    incidentType: "other",
    incidentSummary: {
      totalIncidents: 2,
      openIncidents: 0,
      investigatingIncidents: 1,
      resolvedIncidents: 0,
      closedIncidents: 1,
      criticalIncidents: 1,
      highPriorityIncidents: 1,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 0,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 2,
      assignedIncidents: 2,
      unassignedIncidents: 0,
      newThisMonth: 1,
      resolvedThisMonth: 0,
      avgResolutionTime: 10,
    },
  },
};

export const NewOrganizationIncidents: Story = {
  args: {
    incidents: {
      items: [
        {
          id: "inc_new_1",
          title: "First Incident Report - Minor Delivery Issue",
          summary:
            "First incident logged for new organization. Package delivered to wrong address, corrected immediately.",
          type: "other" as const,
          severity: "low" as const,
          status: "resolved" as const,
          shipment_id: "shp_new_001",
          driver_id: "drv_new_001",
          load_id: null,
          stop_id: "stop_new_001",
          verification_id: null,
          created_at: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          shipment: {
            id: "shp_new_001",
            status: "completed",
          },
          driver: {
            id: "drv_new_001",
            first_name: "Alex",
            last_name: "Thompson",
          },
          stop: {
            id: "stop_new_001",
            label: "Tampa Customer Location",
          },
          load: null,
          verification: null,
        },
      ],
      total: 1,
    },
    incidentSummary: {
      totalIncidents: 1,
      openIncidents: 0,
      investigatingIncidents: 0,
      resolvedIncidents: 1,
      closedIncidents: 0,
      criticalIncidents: 0,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 1,
      accidentIncidents: 0,
      mechanicalIncidents: 0,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 1,
      unassignedIncidents: 0,
      newThisMonth: 1,
      resolvedThisMonth: 1,
      avgResolutionTime: 2,
    },
  },
};

export const SearchAndFilter: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.filter(
        (i) =>
          i.severity === "critical" && i.title.toLowerCase().includes("engine"),
      ),
      total: 1,
    },
    searchQuery: "engine",
    incidentSeverity: "critical",
    incidentSummary: {
      totalIncidents: 1,
      openIncidents: 0,
      investigatingIncidents: 1,
      resolvedIncidents: 0,
      closedIncidents: 0,
      criticalIncidents: 1,
      highPriorityIncidents: 0,
      mediumPriorityIncidents: 0,
      lowPriorityIncidents: 0,
      accidentIncidents: 0,
      mechanicalIncidents: 1,
      delayIncidents: 0,
      damageIncidents: 0,
      complianceIncidents: 0,
      assignedIncidents: 1,
      unassignedIncidents: 0,
      newThisMonth: 1,
      resolvedThisMonth: 0,
      avgResolutionTime: 0,
    },
  },
};

export const ErrorState: Story = {
  args: {
    incidents: null,
    incidentsError: new Error(
      "Failed to load incidents. Please check your network connection and try again.",
    ),
    incidentSummary: undefined,
  },
};

export const ReadOnlyView: Story = {
  args: {
    incidents: mockIncidents,
    incidentSummary,
    canManageIncidents: false,
    canDeleteIncidents: false,
    canCreateIncidents: false,
    canAssignIncidents: false,
    canResolveIncidents: false,
    canViewSensitiveData: false,
    canGenerateReports: false,
  },
};

export const LimitedPermissions: Story = {
  args: {
    incidents: mockIncidents,
    incidentSummary,
    canManageIncidents: true,
    canDeleteIncidents: false,
    canCreateIncidents: true,
    canAssignIncidents: false,
    canResolveIncidents: true,
    canViewSensitiveData: true,
    canGenerateReports: false,
  },
};

export const RecentIncidents: Story = {
  args: {
    incidents: {
      items: mockIncidents.items.map((incident, index) => ({
        ...incident,
        created_at: new Date(
          Date.now() - (index + 1) * 12 * 60 * 60 * 1000, // 12 hours apart
        ).toISOString(),
      })),
      total: 6,
    },
    incidentSummary: {
      totalIncidents: 6,
      openIncidents: 1,
      investigatingIncidents: 2,
      resolvedIncidents: 2,
      closedIncidents: 1,
      criticalIncidents: 2,
      highPriorityIncidents: 1,
      mediumPriorityIncidents: 2,
      lowPriorityIncidents: 1,
      accidentIncidents: 1,
      mechanicalIncidents: 1,
      delayIncidents: 1,
      damageIncidents: 1,
      complianceIncidents: 1,
      assignedIncidents: 5,
      unassignedIncidents: 1,
      newThisMonth: 6,
      resolvedThisMonth: 2,
      avgResolutionTime: 4,
    },
  },
};

export const LoadingWithPartialData: Story = {
  args: {
    isLoadingIncidents: true,
    incidents: {
      items: mockIncidents.items.slice(0, 2),
      total: 6,
    },
    incidentSummary: {
      totalIncidents: 6,
      openIncidents: 1,
      investigatingIncidents: 2,
      resolvedIncidents: 2,
      closedIncidents: 1,
      criticalIncidents: 2,
      highPriorityIncidents: 1,
      mediumPriorityIncidents: 2,
      lowPriorityIncidents: 1,
      accidentIncidents: 1,
      mechanicalIncidents: 1,
      delayIncidents: 1,
      damageIncidents: 1,
      complianceIncidents: 1,
      assignedIncidents: 5,
      unassignedIncidents: 1,
      newThisMonth: 4,
      resolvedThisMonth: 2,
      avgResolutionTime: 8,
    },
  },
};
