import { Package } from "lucide-react";
import { Link } from "react-router";

import { useDeleteLoad, useListLoads } from "@/api/loads";
import { useSearchFilterValue } from "@/components/search/filter";
import { useSearchPaginationValue } from "@/components/search/pagination";
import { useSearchTextValue } from "@/components/search/text";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Enums } from "@/supabase/types";
import ListLoads from "./ListLoads";

type LoadType = Enums<"load_type">;

const i18n = {
  en: {
    title: "Loads",
    addButton: "Add Load",
    toasts: {
      deleteSuccess: "Load deleted successfully",
      deleteError: "Failed to delete load",
    },
    delete: {
      title: "Delete load",
      description: "Are you sure you want to delete this load?",
      confirm: "Delete",
      cancel: "Cancel",
    },
  },
  links: {
    create: "/app/console/loads/create",
  },
};

export function LoadsView({
  loading,
  error,
  loads,
  onDelete,
}: {
  loading?: boolean;
  error?: Error | null;
  loads?: ReturnType<typeof useListLoads>["data"];
  onDelete?: (id: string) => void;
}) {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Package className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <Button asChild disabled={loading}>
          <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
        </Button>
      </div>

      {error && <ErrorAlert error={error} />}

      <ListLoads loading={loading} loads={loads} onDelete={onDelete} />
    </div>
  );
}

export default function LoadsPage() {
  const pagination = useSearchPaginationValue("load");
  const loadQuery = useSearchTextValue("load");
  const loadType = useSearchFilterValue<LoadType>("type", "load");
  const isPerishable = useSearchFilterValue<string>("perishable", "load");

  const {
    data: loads,
    isLoading,
    error,
    refetch,
  } = useListLoads({
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
    search: loadQuery,
    type: loadType,
    perishable:
      isPerishable === "true"
        ? true
        : isPerishable === "false"
          ? false
          : undefined,
  });

  const deleteLoad = useDeleteLoad({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.deleteSuccess,
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.deleteError,
        variant: "destructive",
      });
    },
  });

  const handleDelete = (id: string) => {
    if (confirm(i18n.en.delete.description)) {
      deleteLoad.mutate({ id });
    }
  };

  return (
    <LoadsView
      loading={isLoading}
      error={error}
      loads={loads}
      onDelete={handleDelete}
    />
  );
}
