import { useQuery } from "@tanstack/react-query";
import { Package } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import type { LoadFormValues } from "@/components/forms/LoadForm";

import { useUpdateLoad } from "@/api/loads/use-update-load";
import LoadForm from "@/components/forms/LoadForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Load",
    toasts: {
      success: "Load updated successfully",
      error: "Failed to update load",
    },
    loading: "Loading load...",
    error: "Failed to load load",
    notFound: "Load not found",
    backButton: "Back to Loads",
  },
};

// Hook to get a single load
function useGetLoad(id: string) {
  return useQuery({
    queryKey: ["loads", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("loads")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditLoadPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: load, isLoading, error } = useGetLoad(id!);

  const updateLoad = useUpdateLoad({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/loads/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: LoadFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const loadData = {
      id,
      label: values.label,
      type: values.type,
      perishable: values.perishable,
      weight: values.weight ? parseFloat(values.weight) : null,
      valuation: values.amount ? parseFloat(values.amount) : null,
      notes: values.notes,
      start_date: values.startDate?.toISOString() || null,
    };

    updateLoad.mutate(loadData);
  };

  const handleCancel = () => {
    navigate(`/app/console/loads/${id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Package className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Package className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <ErrorAlert error={error} />
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/loads")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  if (!load) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Package className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/loads")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: LoadFormValues = {
    id: load.id,
    label: load.label || "",
    type: load.type,
    perishable: load.perishable,
    weight: load.weight?.toString() || "",
    weight_unit: "kg",
    amount: load.valuation?.toString() || "",
    notes: load.notes || "",
    startDate: load.start_date ? new Date(load.start_date) : null,
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Package className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <LoadForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={updateLoad.isPending}
        />
      </div>
    </div>
  );
}
