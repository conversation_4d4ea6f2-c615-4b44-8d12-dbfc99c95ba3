import { useQuery } from "@tanstack/react-query";
import { Box, Loader2 } from "lucide-react";
import { Link, useParams } from "react-router";

import { Button } from "@/components/ui/button";
import { supabase } from "@/supabase/client";

const LoadDetails = () => {
  const { id } = useParams();

  const {
    data: load,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["loads", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("loads")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
  });

  if (isLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !load) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center gap-4">
        <p className="text-destructive">Failed to load details</p>
        <Button variant="outline" asChild>
          <Link to="/app/console/loads">Back to Loads</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Load Details</h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <Box className="h-4 w-4" />
            <p>{load.label}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to={`/app/console/loads/${id}/edit`}>Edit Load</Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/app/console/loads">Back to Loads</Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="space-y-4 rounded-lg border p-4">
          <h2 className="text-lg font-semibold">Load Information</h2>
          <dl className="space-y-2">
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Label
              </dt>
              <dd>{load.label || "N/A"}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Type
              </dt>
              <dd className="capitalize">{load.type.replace(/_/g, " ")}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Perishable
              </dt>
              <dd>{load.perishable ? "Yes" : "No"}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Weight
              </dt>
              <dd>{load.weight ? `${load.weight} kg` : "N/A"}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Valuation
              </dt>
              <dd>{load.valuation ? `$${load.valuation}` : "N/A"}</dd>
            </div>
            {load.notes && (
              <div>
                <dt className="text-muted-foreground text-sm font-medium">
                  Notes
                </dt>
                <dd>{load.notes}</dd>
              </div>
            )}
          </dl>
        </div>

        <div className="space-y-4 rounded-lg border p-4">
          <h2 className="text-lg font-semibold">Status Information</h2>
          <dl className="space-y-2">
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Status
              </dt>
              <dd className="capitalize">{load.status.replace(/_/g, " ")}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Created At
              </dt>
              <dd>{new Date(load.created_at).toLocaleString()}</dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
};

export default LoadDetails;
