"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { CheckCircle, XCircle } from "lucide-react";
import { Link } from "react-router";

import type { useListLoads } from "@/api/loads";
import type { UseDataTableProps } from "@/components/tables";

import Currency from "@/components/shared/Currency";
import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { formatWeight } from "@/lib/formatters";
import { Enums } from "@/supabase/types";

type LoadTypeEnum = Enums<"load_type">;

const i18n = {
  en: {
    noLoad: "There are no loads yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search loads...",
    },
    headers: {
      id: "ID",
      label: "Label",
      type: "Type",
      weight: "Weight",
      valuation: "Valuation",
      perishable: "Perishable",
      created_at: "Created At",
      actions: "Actions",
    },
    filters: {
      type: "Type",
      perishable: "Perishable",
      options: {
        type: {
          ALL: "All Types",
          GENERAL: "General",
          HAZARDOUS: "Hazardous",
          FRAGILE: "Fragile",
          REFRIGERATED: "Refrigerated",
          OVERSIZED: "Oversized",
        },
        perishable: {
          ALL: "All",
          YES: "Perishable",
          NO: "Non-Perishable",
        },
      },
    },
  },
  links: {
    loads: "/app/console/loads/[id]",
  },
};

const groupName = "load";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.type.ALL },
      { value: "general", label: i18n.en.filters.options.type.GENERAL },
      { value: "hazardous", label: i18n.en.filters.options.type.HAZARDOUS },
      { value: "fragile", label: i18n.en.filters.options.type.FRAGILE },
      {
        value: "refrigerated",
        label: i18n.en.filters.options.type.REFRIGERATED,
      },
      { value: "oversized", label: i18n.en.filters.options.type.OVERSIZED },
    ],
  },
  {
    id: "perishable",
    label: i18n.en.filters.perishable,
    options: [
      { value: null, label: i18n.en.filters.options.perishable.ALL },
      { value: "true", label: i18n.en.filters.options.perishable.YES },
      { value: "false", label: i18n.en.filters.options.perishable.NO },
    ],
  },
];

export type LoadsQueryResult = Awaited<ReturnType<typeof useListLoads>>["data"];
export type LoadsType = LoadsQueryResult["items"];
export type LoadItemType = LoadsType[number];
export type TableProps = UseDataTableProps<LoadItemType, LoadsType>;

// Links object used in the table
const tableLinks = {
  loads: "/app/console/loads/[id]",
};

export default function ListLoads({
  loading = false,
  loads,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onDelete,
}: PropsWithChildren<{
  loading?: boolean;
  loads?: LoadsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onDelete?: (id: string) => void;
}>) {
  return (
    <ListTable
      loading={loading}
      data={loads}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      i18n={{
        emptyText: i18n.en.noLoad,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      filterGroups={filterGroups}
      columns={({ i18n, TableActions }) => [
        selectColumn as ColumnDef<LoadItemType, LoadsType>,
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.id || "ID"}
            />
          ),
          cell: ({ row }) => (
            <Link
              to={tableLinks.loads.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "label",
          accessorKey: "label",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.label || "Label"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("label") || "—"}</div>,
        },
        {
          id: "type",
          accessorKey: "type",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.type || "Type"}
            />
          ),
          cell: ({ row }) => (
            <Badge variant="outline">
              {(row.getValue("type") as string)
                ?.replace(/_/g, " ")
                ?.toUpperCase() || "—"}
            </Badge>
          ),
        },
        {
          id: "weight",
          accessorKey: "weight",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.weight || "Weight"}
            />
          ),
          cell: ({ row }) => (
            <span className="font-mono tabular-nums">
              {formatWeight(row.original.weight)}
            </span>
          ),
        },
        {
          id: "valuation",
          accessorKey: "valuation",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.valuation || "Valuation"}
            />
          ),
          cell: ({ row }) => (
            <Currency
              loading={loading}
              amount={row.original.valuation || 0}
              className="font-mono tabular-nums"
            />
          ),
        },
        {
          id: "perishable",
          accessorKey: "perishable",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.perishable || "Perishable"}
            />
          ),
          cell: ({ row }) =>
            row.getValue("perishable") ? (
              <Badge
                variant="accent"
                className="flex items-center gap-1 bg-yellow-100 text-yellow-700"
              >
                <CheckCircle className="h-3 w-3" />
                <span>Perishable</span>
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="text-muted-foreground flex items-center gap-1"
              >
                <XCircle className="h-3 w-3" />
                <span>Non-Perishable</span>
              </Badge>
            ),
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.created_at || "Created At"}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "actions",
          meta: {
            className: "w-[32px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TableActions table={table} i18n={i18n} />
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex size-full items-center justify-end">
              <Button variant="ghost" size="sm" asChild>
                <Link to={tableLinks.loads.replace("[id]", row.original.id)}>
                  View
                </Link>
              </Button>
            </div>
          ),
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
