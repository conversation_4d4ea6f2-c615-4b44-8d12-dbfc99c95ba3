import { Package } from "lucide-react";
import { useNavigate } from "react-router";

import type { LoadFormValues } from "@/components/forms/LoadForm";
import type { Enums } from "@/supabase/types";

import { useCreateLoad } from "@/api/loads/use-create-load";
import LoadForm from "@/components/forms/LoadForm";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "Create Load",
    toasts: {
      success: "Load created successfully",
      error: "Failed to create load",
    },
    backButton: "Back to Loads",
  },
};

export default function CreateLoadPage() {
  const navigate = useNavigate();

  const createLoad = useCreateLoad({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/loads/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: LoadFormValues) => {
    // Map form values to the API expected format
    const loadData = {
      label: values.label,
      type: values.type as Enums<"load_type">,
      perishable: values.perishable,
      weight: values.weight ? parseFloat(values.weight) : null,
      valuation: values.amount ? parseFloat(values.amount) : null,
      notes: values.notes,
      start_date: values.startDate?.toISOString() || null,
    };

    createLoad.mutate(loadData);
  };

  const handleCancel = () => {
    navigate("/app/console/loads");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Package className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <LoadForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={createLoad.isPending}
        />
      </div>
    </div>
  );
}
