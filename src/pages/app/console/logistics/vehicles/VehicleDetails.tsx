import { useQuery } from "@tanstack/react-query";
import { Truck } from "lucide-react";
import { Link, useNavigate, useParams } from "react-router";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/supabase/client";

export default function VehicleDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const { data: vehicle, isLoading } = useQuery({
    queryKey: ["vehicle", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("vehicles")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
  });

  const handleDelete = async () => {
    const { error } = await supabase.from("vehicles").delete().eq("id", id);

    if (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
      return;
    }

    toast({
      title: "Success",
      description: "Vehicle deleted successfully",
    });
    navigate("/app/console/vehicles");
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-20" />
          </div>
        </div>
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (!vehicle) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Vehicle not found</h2>
          <p className="text-muted-foreground">
            The vehicle you're looking for doesn't exist or has been removed.
          </p>
          <Button
            variant="link"
            onClick={() => navigate("/app/console/vehicles")}
          >
            Back to Vehicles
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Vehicle Details</h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <Truck className="h-4 w-4" />
            <p>
              {vehicle.make} {vehicle.model} ({vehicle.year})
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to={`/app/console/vehicles/${id}/edit`}>Edit Vehicle</Link>
          </Button>
          <Button variant="outline" onClick={handleDelete}>
            Delete Vehicle
          </Button>
          <Button variant="outline" asChild>
            <Link to="/app/console/vehicles">Back to Vehicles</Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Vehicle Information</CardTitle>
          <CardDescription>
            Detailed information about this vehicle
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div>
            <p className="text-sm font-medium">Make</p>
            <p className="text-muted-foreground text-sm">{vehicle.make}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Model</p>
            <p className="text-muted-foreground text-sm">{vehicle.model}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Year</p>
            <p className="text-muted-foreground text-sm">{vehicle.year}</p>
          </div>
          <div>
            <p className="text-sm font-medium">License Plate</p>
            <p className="text-muted-foreground text-sm">
              {vehicle.license_plate}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">VIN</p>
            <p className="text-muted-foreground text-sm">{vehicle.vin}</p>
          </div>
          <div>
            <p className="text-sm font-medium">MC Number</p>
            <p className="text-muted-foreground text-sm">{vehicle.mc_number}</p>
          </div>
          <div>
            <p className="text-sm font-medium">US DOT</p>
            <p className="text-muted-foreground text-sm">{vehicle.us_dot}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
