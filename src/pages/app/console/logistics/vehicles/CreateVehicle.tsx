import { Car } from "lucide-react";
import { useNavigate } from "react-router";

import type { VehicleFormValues } from "@/components/forms/VehicleForm";

import { useCreateVehicle } from "@/api/vehicles/use-create-vehicle";
import VehicleForm from "@/components/forms/VehicleForm";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "Create Vehicle",
    toasts: {
      success: "Vehicle created successfully",
      error: "Failed to create vehicle",
    },
    backButton: "Back to Vehicles",
  },
};

export default function CreateVehiclePage() {
  const navigate = useNavigate();

  const createVehicle = useCreateVehicle({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/vehicles/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: VehicleFormValues) => {
    // Map form values to the API expected format
    const vehicleData = {
      make: values.make,
      model: values.model,
      year: values.year,
      license_plate: values.license_plate,
      vin: values.vin,
      mc_number: values.mc_number,
      us_dot: values.us_dot,
      driver_id: values.driver_id || null,
    };

    createVehicle.mutate(vehicleData);
  };

  const handleCancel = () => {
    navigate("/app/console/vehicles");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Car className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <VehicleForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={createVehicle.isPending}
        />
      </div>
    </div>
  );
}
