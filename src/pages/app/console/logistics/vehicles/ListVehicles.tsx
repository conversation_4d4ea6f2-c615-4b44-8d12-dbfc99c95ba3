"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { Pencil, Trash } from "lucide-react";
import { Link, useNavigate } from "react-router";

import type { useListVehicles } from "@/api/vehicles/use-list-vehicles";
import type { UseDataTableProps } from "@/components/tables";

import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const i18n = {
  en: {
    noVehicle: "There are no vehicles yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search vehicles...",
    },
    headers: {
      id: "ID",
      make: "Make",
      model: "Model",
      year: "Year",
      license_plate: "License Plate",
      mc_number: "MC Number",
      us_dot: "US DOT",
      created_at: "Created At",
      actions: "Actions",
    },
  },
  links: {
    vehicles: "/app/console/vehicles/[id]",
  },
};

const groupName = "vehicle";

export type VehiclesQueryResult = Awaited<
  ReturnType<typeof useListVehicles>
>["data"];
export type VehiclesType = VehiclesQueryResult["items"];
export type VehicleType = VehiclesType[number];
export type TableProps = UseDataTableProps<VehicleType, VehiclesType>;

// Links object used in the table
const tableLinks = {
  vehicles: "/app/console/vehicles/[id]",
};

export default function ListVehicles({
  loading = false,
  vehicles,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onDelete,
}: PropsWithChildren<{
  loading?: boolean;
  vehicles?: VehiclesQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onDelete?: (id: string) => void;
}>) {
  const navigate = useNavigate();

  return (
    <ListTable
      loading={loading}
      data={vehicles}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      filters={filters}
      filterGroups={[]}
      i18n={{
        emptyText: i18n.en.noVehicle,
        selection: i18n.en.selection,
        actions: i18n.en.actions,
        headers: i18n.en.headers,
      }}
      groupName={groupName}
      columns={({ i18n, TableActions }) => [
        selectColumn as ColumnDef<VehicleType, VehicleType[]>,
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.id || "ID"}
            />
          ),
          cell: ({ row }) => (
            <Link
              to={tableLinks.vehicles.replace("[id]", row.original.id)}
              className="font-medium hover:underline"
            >
              {row.getValue("id")}
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "make",
          accessorKey: "make",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.make || "Make"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("make")}</div>,
        },
        {
          id: "model",
          accessorKey: "model",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.model || "Model"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("model")}</div>,
        },
        {
          id: "year",
          accessorKey: "year",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.year || "Year"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("year")}</div>,
        },
        {
          id: "license_plate",
          accessorKey: "license_plate",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.license_plate || "License Plate"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("license_plate")}</div>,
        },
        {
          id: "mc_number",
          accessorKey: "mc_number",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.mc_number || "MC Number"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("mc_number")}</div>,
        },
        {
          id: "us_dot",
          accessorKey: "us_dot",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.us_dot || "US DOT"}
            />
          ),
          cell: ({ row }) => <div>{row.getValue("us_dot")}</div>,
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.headers?.created_at || "Created At"}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "actions",
          meta: {
            className: "w-[80px]",
          },
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TableActions table={table} i18n={i18n} />
            </div>
          ),
          cell: ({ row }) => (
            <div className="flex items-center justify-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <span className="sr-only">Open menu</span>
                    <svg
                      width="15"
                      height="15"
                      viewBox="0 0 15 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                    >
                      <path
                        d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z"
                        fill="currentColor"
                        fillRule="evenodd"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() =>
                      navigate(`/app/console/vehicles/${row.original.id}/edit`)
                    }
                  >
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  {onDelete && (
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => onDelete(row.original.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              <Button variant="ghost" size="sm" asChild className="ml-2">
                <Link to={tableLinks.vehicles.replace("[id]", row.original.id)}>
                  View
                </Link>
              </Button>
            </div>
          ),
        },
      ]}
    >
      {children}
    </ListTable>
  );
}
