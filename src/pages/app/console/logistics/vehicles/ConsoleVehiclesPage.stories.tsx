import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleVehiclesPage } from "./ConsoleVehiclesPage";

const meta: Meta<typeof ConsoleVehiclesPage> = {
  title: "Pages/Console/Logistics/Vehicles",
  component: ConsoleVehiclesPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/vehicles" },
    }),
    docs: {
      description: {
        component: `
The ConsoleVehiclesPage component provides a comprehensive interface for managing vehicles within the console application.

## Features
- **Vehicle Fleet Management**: Display vehicles with pagination, search, and filtering
- **CRUD Operations**: Create, read, update, and delete vehicle records
- **Driver Assignment**: Assign and unassign drivers to vehicles
- **Bulk Operations**: Select and manage multiple vehicles simultaneously
- **Search & Filter**: Real-time search with driver and type-based filtering
- **Fleet Analytics**: Vehicle summary statistics and fleet metrics
- **Compliance Tracking**: Vehicle inspections, maintenance, and regulatory compliance
- **Vehicle Types**: Support for trucks, trailers, vans, and specialized equipment
- **Maintenance Management**: Schedule and track vehicle maintenance

## Usage
This component follows the established console pattern with a presentation component that receives all data and handlers as props, ensuring clean separation of concerns between data management and UI rendering.
        `,
      },
    },
  },
  args: {
    // Default props
    isLoadingVehicles: false,
    vehiclesError: null,
    searchQuery: "",
    onSearchQueryChange: fn(),
    driverId: undefined,
    onDriverIdChange: fn(),
    pagination: {
      pageIndex: 0,
      pageSize: 10,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    deleteVehicleId: null,
    setDeleteVehicleId: fn(),
    onDeleteVehicle: fn(),
    isDeletingVehicle: false,
    onCreateVehicle: fn(),
    onEditVehicle: fn(),
    onViewVehicle: fn(),
    onAssignDriver: fn(),
    onUnassignDriver: fn(),
    onUpdateVehicleInfo: fn(),
    selectedVehicles: [],
    onSelectVehicle: fn(),
    onSelectAllVehicles: fn(),
    onBulkDelete: fn(),
    onBulkAssignDriver: fn(),
    onBulkUnassignDriver: fn(),
    onBulkUpdateInfo: fn(),
    onFilterByMake: fn(),
    onFilterByYear: fn(),
    onFilterByAssignmentStatus: fn(),
    onFilterByVehicleType: fn(),
    onScheduleMaintenance: fn(),
    onViewInspectionHistory: fn(),
    onViewMaintenanceHistory: fn(),
    onUpdateInsurance: fn(),
    onUpdateRegistration: fn(),
    organizationId: "org_1",
    canManageVehicles: true,
    canDeleteVehicles: true,
    canCreateVehicles: true,
    canAssignDrivers: true,
    canUpdateVehicleInfo: true,
    canScheduleMaintenance: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock vehicle data for stories
const mockVehicles = {
  items: [
    {
      id: "vhc_1",
      make: "Peterbilt",
      model: "579",
      year: 2022,
      license_plate: "TRK-2024",
      mc_number: "MC123456",
      us_dot: "DOT7890123",
      vin: "1NP5DB9X9ND123456",
      driver_id: "drv_1",
      created_at: "2024-01-15T10:30:00Z",
      driver: {
        id: "drv_1",
        first_name: "John",
        last_name: "Smith",
        email: "<EMAIL>",
        phone_number: "******-0101",
      },
    },
    {
      id: "vhc_2",
      make: "Kenworth",
      model: "T680",
      year: 2023,
      license_plate: "TRK-2025",
      mc_number: "MC123457",
      us_dot: "DOT7890124",
      vin: "1XKYDP9X0NJ123457",
      driver_id: "drv_2",
      created_at: "2024-02-20T08:15:00Z",
      driver: {
        id: "drv_2",
        first_name: "Maria",
        last_name: "Rodriguez",
        email: "<EMAIL>",
        phone_number: "******-0102",
      },
    },
    {
      id: "vhc_3",
      make: "Freightliner",
      model: "Cascadia",
      year: 2021,
      license_plate: "TRK-2026",
      mc_number: "MC123458",
      us_dot: "DOT7890125",
      vin: "3AKJGLDR0MSJK1234",
      driver_id: null,
      created_at: "2024-03-10T16:45:00Z",
      driver: null,
    },
    {
      id: "vhc_4",
      make: "Volvo",
      model: "VNL 760",
      year: 2022,
      license_plate: "TRK-2027",
      mc_number: "MC123459",
      us_dot: "DOT7890126",
      vin: "4V4NC9EJXNN123458",
      driver_id: "drv_3",
      created_at: "2024-04-05T09:20:00Z",
      driver: {
        id: "drv_3",
        first_name: "David",
        last_name: "Johnson",
        email: "<EMAIL>",
        phone_number: "******-0103",
      },
    },
    {
      id: "vhc_5",
      make: "Great Dane",
      model: "Dry Van Trailer",
      year: 2023,
      license_plate: "TRL-3001",
      mc_number: "MC123460",
      us_dot: "DOT7890127",
      vin: "1GRAA0629P1234567",
      driver_id: null,
      created_at: "2024-05-12T13:30:00Z",
      driver: null,
    },
    {
      id: "vhc_6",
      make: "Mercedes-Benz",
      model: "Sprinter 3500",
      year: 2024,
      license_plate: "VAN-4001",
      mc_number: "MC123461",
      us_dot: "DOT7890128",
      vin: "WDAPF4CD0P1234568",
      driver_id: "drv_4",
      created_at: "2024-06-01T10:00:00Z",
      driver: {
        id: "drv_4",
        first_name: "Sarah",
        last_name: "Wilson",
        email: "<EMAIL>",
        phone_number: "******-0104",
      },
    },
    {
      id: "vhc_7",
      make: "Mack",
      model: "Anthem",
      year: 2021,
      license_plate: "TRK-2028",
      mc_number: "MC123462",
      us_dot: "DOT7890129",
      vin: "1M2AX07C0MM123569",
      driver_id: null,
      created_at: "2024-06-15T14:30:00Z",
      driver: null,
    },
    {
      id: "vhc_8",
      make: "International",
      model: "LT Series",
      year: 2023,
      license_plate: "TRK-2029",
      mc_number: "MC123463",
      us_dot: "DOT7890130",
      vin: "1HTMPAAN0NH123570",
      driver_id: "drv_5",
      created_at: "2024-06-20T11:15:00Z",
      driver: {
        id: "drv_5",
        first_name: "Michael",
        last_name: "Chen",
        email: "<EMAIL>",
        phone_number: "******-0105",
      },
    },
  ],
  total: 8,
};

const vehicleSummary = {
  totalVehicles: 8,
  assignedVehicles: 5,
  unassignedVehicles: 3,
  activeVehicles: 8,
  inactiveVehicles: 0,
  trucksCount: 6,
  trailersCount: 1,
  vansCount: 1,
  maintenanceVehicles: 0,
  newThisMonth: 3,
};

export const Default: Story = {
  args: {
    vehicles: mockVehicles,
    vehicleSummary,
  },
};

export const Loading: Story = {
  args: {
    isLoadingVehicles: true,
    vehicles: null,
    vehicleSummary: undefined,
  },
};

export const EmptyState: Story = {
  args: {
    vehicles: {
      items: [],
      total: 0,
    },
    vehicleSummary: {
      totalVehicles: 0,
      assignedVehicles: 0,
      unassignedVehicles: 0,
      activeVehicles: 0,
      inactiveVehicles: 0,
      trucksCount: 0,
      trailersCount: 0,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 0,
    },
  },
};

export const SearchResults: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.filter((v) => v.make.includes("Peterbilt")),
      total: 1,
    },
    searchQuery: "Peterbilt",
    vehicleSummary: {
      totalVehicles: 1,
      assignedVehicles: 1,
      unassignedVehicles: 0,
      activeVehicles: 1,
      inactiveVehicles: 0,
      trucksCount: 1,
      trailersCount: 0,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 0,
    },
  },
};

export const FilteredByAssigned: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.filter((v) => v.driver_id !== null),
      total: 5,
    },
    vehicleSummary: {
      totalVehicles: 5,
      assignedVehicles: 5,
      unassignedVehicles: 0,
      activeVehicles: 5,
      inactiveVehicles: 0,
      trucksCount: 4,
      trailersCount: 0,
      vansCount: 1,
      maintenanceVehicles: 0,
      newThisMonth: 2,
    },
  },
};

export const FilteredByUnassigned: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.filter((v) => v.driver_id === null),
      total: 3,
    },
    vehicleSummary: {
      totalVehicles: 3,
      assignedVehicles: 0,
      unassignedVehicles: 3,
      activeVehicles: 3,
      inactiveVehicles: 0,
      trucksCount: 2,
      trailersCount: 1,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 1,
    },
  },
};

export const TrucksOnly: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.filter(
        (v) =>
          v.make.toLowerCase().includes("truck") ||
          [
            "Peterbilt",
            "Kenworth",
            "Freightliner",
            "Volvo",
            "Mack",
            "International",
          ].includes(v.make),
      ),
      total: 6,
    },
    vehicleSummary: {
      totalVehicles: 6,
      assignedVehicles: 4,
      unassignedVehicles: 2,
      activeVehicles: 6,
      inactiveVehicles: 0,
      trucksCount: 6,
      trailersCount: 0,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 2,
    },
  },
};

export const TrailersOnly: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.filter(
        (v) =>
          v.make.toLowerCase().includes("trailer") ||
          v.model.toLowerCase().includes("trailer"),
      ),
      total: 1,
    },
    vehicleSummary: {
      totalVehicles: 1,
      assignedVehicles: 0,
      unassignedVehicles: 1,
      activeVehicles: 1,
      inactiveVehicles: 0,
      trucksCount: 0,
      trailersCount: 1,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 1,
    },
  },
};

export const VansOnly: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.filter(
        (v) =>
          v.make.toLowerCase().includes("van") ||
          v.model.toLowerCase().includes("sprinter"),
      ),
      total: 1,
    },
    vehicleSummary: {
      totalVehicles: 1,
      assignedVehicles: 1,
      unassignedVehicles: 0,
      activeVehicles: 1,
      inactiveVehicles: 0,
      trucksCount: 0,
      trailersCount: 0,
      vansCount: 1,
      maintenanceVehicles: 0,
      newThisMonth: 1,
    },
  },
};

export const DeleteConfirmation: Story = {
  args: {
    vehicles: mockVehicles,
    deleteVehicleId: "vhc_3",
    vehicleSummary,
  },
};

export const DeletingVehicle: Story = {
  args: {
    vehicles: mockVehicles,
    deleteVehicleId: "vhc_3",
    isDeletingVehicle: true,
    vehicleSummary,
  },
};

export const BulkOperationsActive: Story = {
  args: {
    vehicles: mockVehicles,
    selectedVehicles: ["vhc_1", "vhc_2", "vhc_3"],
    vehicleSummary,
  },
};

export const PaginatedResults: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.slice(0, 5),
      total: 247,
    },
    pagination: {
      pageIndex: 0,
      pageSize: 5,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    vehicleSummary: {
      totalVehicles: 247,
      assignedVehicles: 189,
      unassignedVehicles: 58,
      activeVehicles: 235,
      inactiveVehicles: 12,
      trucksCount: 180,
      trailersCount: 45,
      vansCount: 22,
      maintenanceVehicles: 8,
      newThisMonth: 15,
    },
  },
};

export const MixedFleet: Story = {
  args: {
    vehicles: {
      items: [
        ...mockVehicles.items,
        {
          id: "vhc_9",
          make: "Utility",
          model: "Reefer Trailer",
          year: 2023,
          license_plate: "REF-5001",
          mc_number: "MC123464",
          us_dot: "DOT7890131",
          vin: "1UYVS25328M123571",
          driver_id: null,
          created_at: "2024-06-25T09:30:00Z",
          driver: null,
        },
        {
          id: "vhc_10",
          make: "Ford",
          model: "Transit 350",
          year: 2024,
          license_plate: "VAN-4002",
          mc_number: "MC123465",
          us_dot: "DOT7890132",
          vin: "1FTBW3XM4PKA23572",
          driver_id: "drv_6",
          created_at: "2024-06-28T15:45:00Z",
          driver: {
            id: "drv_6",
            first_name: "Jennifer",
            last_name: "Martinez",
            email: "<EMAIL>",
            phone_number: "******-0106",
          },
        },
      ],
      total: 10,
    },
    vehicleSummary: {
      totalVehicles: 10,
      assignedVehicles: 6,
      unassignedVehicles: 4,
      activeVehicles: 10,
      inactiveVehicles: 0,
      trucksCount: 6,
      trailersCount: 2,
      vansCount: 2,
      maintenanceVehicles: 0,
      newThisMonth: 5,
    },
  },
};

export const MaintenanceScheduled: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.map((vehicle, index) => ({
        ...vehicle,
        // Simulate some vehicles in maintenance
        maintenanceStatus: index < 2 ? "scheduled" : "none",
      })),
      total: 8,
    },
    vehicleSummary: {
      totalVehicles: 8,
      assignedVehicles: 5,
      unassignedVehicles: 3,
      activeVehicles: 6,
      inactiveVehicles: 0,
      trucksCount: 6,
      trailersCount: 1,
      vansCount: 1,
      maintenanceVehicles: 2,
      newThisMonth: 3,
    },
  },
};

export const NewOrganization: Story = {
  args: {
    vehicles: {
      items: [
        {
          id: "vhc_new_1",
          make: "Peterbilt",
          model: "389",
          year: 2024,
          license_plate: "NEW-001",
          mc_number: "MC999001",
          us_dot: "DOT9990001",
          vin: "1NP5DB9X0PN123001",
          driver_id: "drv_new_1",
          created_at: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          driver: {
            id: "drv_new_1",
            first_name: "Alex",
            last_name: "Thompson",
            email: "<EMAIL>",
            phone_number: "******-0201",
          },
        },
      ],
      total: 1,
    },
    vehicleSummary: {
      totalVehicles: 1,
      assignedVehicles: 1,
      unassignedVehicles: 0,
      activeVehicles: 1,
      inactiveVehicles: 0,
      trucksCount: 1,
      trailersCount: 0,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 1,
    },
  },
};

export const HighVolumeFleet: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items,
      total: 2847,
    },
    pagination: {
      pageIndex: 142,
      pageSize: 20,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    vehicleSummary: {
      totalVehicles: 2847,
      assignedVehicles: 2234,
      unassignedVehicles: 613,
      activeVehicles: 2698,
      inactiveVehicles: 149,
      trucksCount: 1856,
      trailersCount: 789,
      vansCount: 202,
      maintenanceVehicles: 89,
      newThisMonth: 127,
    },
  },
};

export const VehicleTypesDistribution: Story = {
  args: {
    vehicles: {
      items: [
        {
          id: "vhc_special_1",
          make: "Specialized",
          model: "Flatbed Trailer",
          year: 2023,
          license_plate: "FLT-6001",
          mc_number: "MC123466",
          us_dot: "DOT7890133",
          vin: "4P5H48C58NX123573",
          driver_id: null,
          created_at: "2024-01-10T09:00:00Z",
          driver: null,
        },
        {
          id: "vhc_special_2",
          make: "Specialized",
          model: "Tank Trailer",
          year: 2022,
          license_plate: "TNK-7001",
          mc_number: "MC123467",
          us_dot: "DOT7890134",
          vin: "1TK4853X7M5123574",
          driver_id: "drv_special_1",
          created_at: "2024-02-15T14:30:00Z",
          driver: {
            id: "drv_special_1",
            first_name: "Robert",
            last_name: "Davis",
            email: "<EMAIL>",
            phone_number: "******-0301",
          },
        },
        {
          id: "vhc_special_3",
          make: "Specialized",
          model: "Car Hauler",
          year: 2024,
          license_plate: "CAR-8001",
          mc_number: "MC123468",
          us_dot: "DOT7890135",
          vin: "5C4H485X7N5123575",
          driver_id: "drv_special_2",
          created_at: "2024-03-20T16:45:00Z",
          driver: {
            id: "drv_special_2",
            first_name: "Lisa",
            last_name: "Brown",
            email: "<EMAIL>",
            phone_number: "******-0302",
          },
        },
      ],
      total: 3,
    },
    vehicleSummary: {
      totalVehicles: 3,
      assignedVehicles: 2,
      unassignedVehicles: 1,
      activeVehicles: 3,
      inactiveVehicles: 0,
      trucksCount: 0,
      trailersCount: 3,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 1,
    },
  },
};

export const SearchAndFilter: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.filter(
        (v) => v.make.includes("Kenworth") && v.driver_id !== null,
      ),
      total: 1,
    },
    searchQuery: "Kenworth",
    driverId: "drv_2",
    vehicleSummary: {
      totalVehicles: 1,
      assignedVehicles: 1,
      unassignedVehicles: 0,
      activeVehicles: 1,
      inactiveVehicles: 0,
      trucksCount: 1,
      trailersCount: 0,
      vansCount: 0,
      maintenanceVehicles: 0,
      newThisMonth: 0,
    },
  },
};

export const ErrorState: Story = {
  args: {
    vehicles: null,
    vehiclesError: new Error(
      "Failed to load vehicles. Please check your network connection and try again.",
    ),
    vehicleSummary: undefined,
  },
};

export const ReadOnlyView: Story = {
  args: {
    vehicles: mockVehicles,
    vehicleSummary,
    canManageVehicles: false,
    canDeleteVehicles: false,
    canCreateVehicles: false,
    canAssignDrivers: false,
    canUpdateVehicleInfo: false,
    canScheduleMaintenance: false,
  },
};

export const LimitedPermissions: Story = {
  args: {
    vehicles: mockVehicles,
    vehicleSummary,
    canManageVehicles: true,
    canDeleteVehicles: false,
    canCreateVehicles: true,
    canAssignDrivers: false,
    canUpdateVehicleInfo: true,
    canScheduleMaintenance: false,
  },
};

export const RecentlyCreatedVehicles: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.map((vehicle, index) => ({
        ...vehicle,
        created_at: new Date(
          Date.now() - (index + 1) * 24 * 60 * 60 * 1000,
        ).toISOString(),
      })),
      total: 8,
    },
    vehicleSummary: {
      totalVehicles: 8,
      assignedVehicles: 5,
      unassignedVehicles: 3,
      activeVehicles: 8,
      inactiveVehicles: 0,
      trucksCount: 6,
      trailersCount: 1,
      vansCount: 1,
      maintenanceVehicles: 0,
      newThisMonth: 8,
    },
  },
};

export const LoadingWithPartialData: Story = {
  args: {
    isLoadingVehicles: true,
    vehicles: {
      items: mockVehicles.items.slice(0, 3),
      total: 8,
    },
    vehicleSummary: {
      totalVehicles: 8,
      assignedVehicles: 5,
      unassignedVehicles: 3,
      activeVehicles: 8,
      inactiveVehicles: 0,
      trucksCount: 6,
      trailersCount: 1,
      vansCount: 1,
      maintenanceVehicles: 0,
      newThisMonth: 3,
    },
  },
};

export const ComplianceTracking: Story = {
  args: {
    vehicles: mockVehicles,
    vehicleSummary: {
      ...vehicleSummary,
      maintenanceVehicles: 3, // Show more vehicles in maintenance for compliance tracking
    },
  },
};

export const GeographicDistribution: Story = {
  args: {
    vehicles: mockVehicles,
    vehicleSummary,
  },
};

export const MaintenanceAndInspections: Story = {
  args: {
    vehicles: {
      items: mockVehicles.items.slice(0, 3),
      total: 3,
    },
    vehicleSummary: {
      totalVehicles: 3,
      assignedVehicles: 2,
      unassignedVehicles: 1,
      activeVehicles: 2,
      inactiveVehicles: 1,
      trucksCount: 3,
      trailersCount: 0,
      vansCount: 0,
      maintenanceVehicles: 3,
      newThisMonth: 0,
    },
  },
};
