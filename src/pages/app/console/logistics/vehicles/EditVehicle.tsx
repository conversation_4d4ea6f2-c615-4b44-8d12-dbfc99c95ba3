import { useQuery } from "@tanstack/react-query";
import { Car } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import type { VehicleFormValues } from "@/components/forms/VehicleForm";

import { useUpdateVehicle } from "@/api/vehicles/use-update-vehicle";
import VehicleForm from "@/components/forms/VehicleForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Vehicle",
    toasts: {
      success: "Vehicle updated successfully",
      error: "Failed to update vehicle",
    },
    loading: "Loading vehicle...",
    error: "Failed to load vehicle",
    notFound: "Vehicle not found",
    backButton: "Back to Vehicles",
  },
};

// Hook to get a single vehicle
function useGetVehicle(id: string) {
  return useQuery({
    queryKey: ["vehicles", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("vehicles")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditVehiclePage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: vehicle, isLoading, error } = useGetVehicle(id!);

  const updateVehicle = useUpdateVehicle({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/vehicles/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (values: VehicleFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const vehicleData = {
      id,
      make: values.make,
      model: values.model,
      year: values.year,
      license_plate: values.license_plate,
      vin: values.vin,
      mc_number: values.mc_number,
      us_dot: values.us_dot,
      driver_id: values.driver_id || null,
    };

    updateVehicle.mutate(vehicleData);
  };

  const handleCancel = () => {
    navigate(`/app/console/vehicles/${id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Car className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Car className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <ErrorAlert error={error} />
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/vehicles")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  if (!vehicle) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Car className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/vehicles")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: VehicleFormValues = {
    make: vehicle.make,
    model: vehicle.model,
    year: vehicle.year,
    license_plate: vehicle.license_plate,
    vin: vehicle.vin,
    mc_number: vehicle.mc_number,
    us_dot: vehicle.us_dot,
    driver_id: vehicle.driver_id || undefined,
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Car className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>
      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <VehicleForm
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={updateVehicle.isPending}
        />
      </div>
    </div>
  );
}
