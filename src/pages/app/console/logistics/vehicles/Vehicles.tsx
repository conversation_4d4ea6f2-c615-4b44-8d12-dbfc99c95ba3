"use client";

import { useState } from "react";

import { useDeleteVehicle } from "@/api/vehicles";
import { useListVehicles } from "@/api/vehicles/use-list-vehicles";
import {
  useSearchFilterValue,
  useSearchPagination,
  useSearchTextValue,
} from "@/components/search";
import { useToast } from "@/hooks/use-toast";
import { ConsoleVehiclesPage } from "@/pages/app/console/logistics/vehicles/ConsoleVehiclesPage";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    toast: {
      deleteSuccess: "Vehicle deleted successfully.",
      deleteError: "Failed to delete vehicle. Please try again.",
      updateSuccess: "Vehicle updated successfully.",
      updateError: "Failed to update vehicle. Please try again.",
      assignSuccess: "Driver assigned successfully.",
      assignError: "Failed to assign driver. Please try again.",
      unassignSuccess: "Driver unassigned successfully.",
      unassignError: "Failed to unassign driver. Please try again.",
      bulkDeleteSuccess: "Vehicles deleted successfully.",
      bulkDeleteError: "Failed to delete vehicles. Please try again.",
      bulkAssignSuccess: "Drivers assigned successfully.",
      bulkAssignError: "Failed to assign drivers. Please try again.",
      bulkUnassignSuccess: "Drivers unassigned successfully.",
      bulkUnassignError: "Failed to unassign drivers. Please try again.",
      bulkUpdateSuccess: "Vehicles updated successfully.",
      bulkUpdateError: "Failed to update vehicles. Please try again.",
      maintenanceScheduled: "Maintenance scheduled successfully.",
      maintenanceError: "Failed to schedule maintenance. Please try again.",
    },
  },
};

export default function VehiclesPage() {
  const { toast } = useToast();
  const [deleteVehicleId, setDeleteVehicleId] = useState<string | null>(null);
  const [isDeletingVehicle, setIsDeletingVehicle] = useState(false);
  const [selectedVehicles, setSelectedVehicles] = useState<string[]>([]);

  // Set up search hooks
  const pagination = useSearchPagination({
    group: "vehicle",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });
  const vehiclesQuery = useSearchTextValue("vehicle");
  const driverId = useSearchFilterValue<string>("driver_id", "vehicle");

  // Use the hook with search parameters
  const {
    data: vehicles,
    isLoading,
    error,
    refetch,
  } = useListVehicles({
    pageIndex: pagination.pagination.pageIndex,
    pageSize: pagination.pagination.pageSize,
    search: vehiclesQuery,
    driver_id: driverId,
  });

  const deleteVehicle = useDeleteVehicle();

  const handleDelete = async (vehicleId: string) => {
    setIsDeletingVehicle(true);
    try {
      await deleteVehicle.mutateAsync({ id: vehicleId });
      await refetch();
      toast({
        title: "Success",
        description: i18n.en.toast.deleteSuccess,
      });
      setDeleteVehicleId(null);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: i18n.en.toast.deleteError,
      });
      throw error;
    } finally {
      setIsDeletingVehicle(false);
    }
  };

  // Calculate vehicle summary from the current data
  const vehicleSummary = vehicles
    ? {
        totalVehicles: vehicles.total,
        assignedVehicles: vehicles.items.filter((v) => v.driver_id).length,
        unassignedVehicles: vehicles.items.filter((v) => !v.driver_id).length,
        activeVehicles: vehicles.items.length, // All vehicles considered active for now
        inactiveVehicles: 0, // No inactive status in current schema
        trucksCount: vehicles.items.filter(
          (v) =>
            v.make.toLowerCase().includes("truck") ||
            v.model.toLowerCase().includes("truck"),
        ).length,
        trailersCount: vehicles.items.filter(
          (v) =>
            v.make.toLowerCase().includes("trailer") ||
            v.model.toLowerCase().includes("trailer"),
        ).length,
        vansCount: vehicles.items.filter(
          (v) =>
            v.make.toLowerCase().includes("van") ||
            v.model.toLowerCase().includes("van"),
        ).length,
        maintenanceVehicles: 0, // No maintenance status in current schema
        newThisMonth: vehicles.items.filter((v) => {
          const createdAt = new Date(v.created_at);
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          return createdAt >= startOfMonth;
        }).length,
      }
    : undefined;

  // Handler functions for vehicle management
  const handleCreateVehicle = () => {
    window.location.href = "/app/console/vehicles/create";
  };

  const handleEditVehicle = (vehicleId: string) => {
    window.location.href = `/app/console/vehicles/${vehicleId}/edit`;
  };

  const handleViewVehicle = (vehicleId: string) => {
    window.location.href = `/app/console/vehicles/${vehicleId}`;
  };

  const handleAssignDriver = async (vehicleId: string, driverId: string) => {
    try {
      const { error } = await supabase
        .from("vehicles")
        .update({ driver_id: driverId })
        .eq("id", vehicleId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.assignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.assignSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error assigning driver:", error);
    }
  };

  const handleUnassignDriver = async (vehicleId: string) => {
    try {
      const { error } = await supabase
        .from("vehicles")
        .update({ driver_id: null })
        .eq("id", vehicleId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.unassignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.unassignSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error unassigning driver:", error);
    }
  };

  const handleUpdateVehicleInfo = async (vehicleId: string, updates: any) => {
    try {
      const { error } = await supabase
        .from("vehicles")
        .update(updates)
        .eq("id", vehicleId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.updateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.updateSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating vehicle info:", error);
    }
  };

  const handleBulkDelete = async (vehicleIds: string[]) => {
    setIsDeletingVehicle(true);
    try {
      const { error } = await supabase
        .from("vehicles")
        .delete()
        .in("id", vehicleIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkDeleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkDeleteSuccess,
        });
        setSelectedVehicles([]);
        refetch();
      }
    } finally {
      setIsDeletingVehicle(false);
    }
  };

  const handleBulkAssignDriver = async (
    vehicleIds: string[],
    driverId: string,
  ) => {
    try {
      const { error } = await supabase
        .from("vehicles")
        .update({ driver_id: driverId })
        .in("id", vehicleIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkAssignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkAssignSuccess,
        });
        setSelectedVehicles([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk assigning drivers:", error);
    }
  };

  const handleBulkUnassignDriver = async (vehicleIds: string[]) => {
    try {
      const { error } = await supabase
        .from("vehicles")
        .update({ driver_id: null })
        .in("id", vehicleIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkUnassignError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkUnassignSuccess,
        });
        setSelectedVehicles([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk unassigning drivers:", error);
    }
  };

  const handleBulkUpdateInfo = async (vehicleIds: string[], updates: any) => {
    try {
      const { error } = await supabase
        .from("vehicles")
        .update(updates)
        .in("id", vehicleIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkUpdateSuccess,
        });
        setSelectedVehicles([]);
        refetch();
      }
    } catch (error) {
      console.error("Error bulk updating vehicles:", error);
    }
  };

  const handleSelectVehicle = (vehicleId: string) => {
    setSelectedVehicles((prev) =>
      prev.includes(vehicleId)
        ? prev.filter((id) => id !== vehicleId)
        : [...prev, vehicleId],
    );
  };

  const handleSelectAllVehicles = (selected: boolean) => {
    if (selected && vehicles) {
      setSelectedVehicles(vehicles.items.map((vehicle) => vehicle.id));
    } else {
      setSelectedVehicles([]);
    }
  };

  // Filter handler functions
  const handleFilterByMake = (make: string) => {
    // This would be implemented with proper search/filter hooks
    console.log("Filter by make:", make);
  };

  const handleFilterByYear = (year: number) => {
    // This would be implemented with proper search/filter hooks
    console.log("Filter by year:", year);
  };

  const handleFilterByAssignmentStatus = (
    status: "assigned" | "unassigned",
  ) => {
    // This would be implemented with proper search/filter hooks
    console.log("Filter by assignment status:", status);
  };

  const handleFilterByVehicleType = (
    type: "truck" | "trailer" | "van" | "other",
  ) => {
    // This would be implemented with proper search/filter hooks
    console.log("Filter by vehicle type:", type);
  };

  // Maintenance and compliance handlers
  const handleScheduleMaintenance = (vehicleId: string) => {
    toast({
      title: "Success",
      description: i18n.en.toast.maintenanceScheduled,
    });
    console.log("Schedule maintenance for vehicle:", vehicleId);
  };

  const handleViewInspectionHistory = (vehicleId: string) => {
    window.location.href = `/app/console/vehicles/${vehicleId}/inspections`;
  };

  const handleViewMaintenanceHistory = (vehicleId: string) => {
    window.location.href = `/app/console/vehicles/${vehicleId}/maintenance`;
  };

  const handleUpdateInsurance = (vehicleId: string) => {
    window.location.href = `/app/console/vehicles/${vehicleId}/insurance`;
  };

  const handleUpdateRegistration = (vehicleId: string) => {
    window.location.href = `/app/console/vehicles/${vehicleId}/registration`;
  };

  return (
    <ConsoleVehiclesPage
      // Vehicle list data and loading states
      vehicles={vehicles || null}
      isLoadingVehicles={isLoading}
      vehiclesError={error}
      // Search and filter state
      searchQuery={vehiclesQuery}
      onSearchQueryChange={(query: string) => {
        // This would be handled by the search hook internally
      }}
      driverId={driverId}
      onDriverIdChange={(driverId: string | undefined) => {
        // This would be handled by the filter hook internally
      }}
      // Pagination state
      pagination={{
        pageIndex: pagination.pagination.pageIndex,
        pageSize: pagination.pagination.pageSize,
        setPageIndex: (pageIndex: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageIndex,
          }));
        },
        setPageSize: (pageSize: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageSize,
          }));
        },
      }}
      // Delete functionality
      deleteVehicleId={deleteVehicleId}
      setDeleteVehicleId={setDeleteVehicleId}
      onDeleteVehicle={handleDelete}
      isDeletingVehicle={isDeletingVehicle}
      // Vehicle management actions
      onCreateVehicle={handleCreateVehicle}
      onEditVehicle={handleEditVehicle}
      onViewVehicle={handleViewVehicle}
      onAssignDriver={handleAssignDriver}
      onUnassignDriver={handleUnassignDriver}
      onUpdateVehicleInfo={handleUpdateVehicleInfo}
      // Vehicle analytics and summary
      vehicleSummary={vehicleSummary}
      // Bulk operations
      selectedVehicles={selectedVehicles}
      onSelectVehicle={handleSelectVehicle}
      onSelectAllVehicles={handleSelectAllVehicles}
      onBulkDelete={handleBulkDelete}
      onBulkAssignDriver={handleBulkAssignDriver}
      onBulkUnassignDriver={handleBulkUnassignDriver}
      onBulkUpdateInfo={handleBulkUpdateInfo}
      // Fleet management filters
      onFilterByMake={handleFilterByMake}
      onFilterByYear={handleFilterByYear}
      onFilterByAssignmentStatus={handleFilterByAssignmentStatus}
      onFilterByVehicleType={handleFilterByVehicleType}
      // Compliance and maintenance
      onScheduleMaintenance={handleScheduleMaintenance}
      onViewInspectionHistory={handleViewInspectionHistory}
      onViewMaintenanceHistory={handleViewMaintenanceHistory}
      onUpdateInsurance={handleUpdateInsurance}
      onUpdateRegistration={handleUpdateRegistration}
      // Organization context
      organizationId={undefined}
      canManageVehicles={true}
      canDeleteVehicles={true}
      canCreateVehicles={true}
      canAssignDrivers={true}
      canUpdateVehicleInfo={true}
      canScheduleMaintenance={true}
    />
  );
}
