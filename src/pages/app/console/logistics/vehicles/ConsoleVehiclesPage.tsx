import { Truck } from "lucide-react";
import { Link } from "react-router";

import type { VehiclesQueryResult } from "@/pages/app/console/logistics/vehicles/ListVehicles";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListVehicles from "@/pages/app/console/logistics/vehicles/ListVehicles";

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  license_plate: string;
  mc_number: string;
  us_dot: string;
  vin: string;
  driver_id: string | null;
  created_at: string;
  driver?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
  } | null;
}

export interface VehiclesListResponse {
  items: Vehicle[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface VehicleSearchParams {
  pageIndex: number;
  pageSize: number;
  search?: string;
  driver_id?: string;
}

export interface DeleteVehicleHandler {
  (vehicleId: string): Promise<void>;
}

export interface ConsoleVehiclesPageProps {
  // Vehicle list data and loading states
  vehicles: VehiclesQueryResult | null;
  isLoadingVehicles: boolean;
  vehiclesError: Error | null;

  // Search and filter state
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  driverId: string | undefined;
  onDriverIdChange: (driverId: string | undefined) => void;

  // Pagination state
  pagination: {
    pageIndex: number;
    pageSize: number;
    setPageIndex: (pageIndex: number) => void;
    setPageSize: (pageSize: number) => void;
  };

  // Delete functionality
  deleteVehicleId: string | null;
  setDeleteVehicleId: (id: string | null) => void;
  onDeleteVehicle: DeleteVehicleHandler;
  isDeletingVehicle: boolean;

  // Vehicle management actions
  onCreateVehicle?: () => void;
  onEditVehicle?: (vehicleId: string) => void;
  onViewVehicle?: (vehicleId: string) => void;
  onAssignDriver?: (vehicleId: string, driverId: string) => void;
  onUnassignDriver?: (vehicleId: string) => void;
  onUpdateVehicleInfo?: (
    vehicleId: string,
    updates: Partial<
      Pick<
        Vehicle,
        | "make"
        | "model"
        | "year"
        | "license_plate"
        | "mc_number"
        | "us_dot"
        | "vin"
      >
    >,
  ) => void;

  // Vehicle analytics and summary
  vehicleSummary?: {
    totalVehicles: number;
    assignedVehicles: number;
    unassignedVehicles: number;
    activeVehicles: number;
    inactiveVehicles: number;
    trucksCount: number;
    trailersCount: number;
    vansCount: number;
    maintenanceVehicles: number;
    newThisMonth: number;
  };

  // Bulk operations
  selectedVehicles?: string[];
  onSelectVehicle?: (vehicleId: string) => void;
  onSelectAllVehicles?: (selected: boolean) => void;
  onBulkDelete?: (vehicleIds: string[]) => Promise<void>;
  onBulkAssignDriver?: (
    vehicleIds: string[],
    driverId: string,
  ) => Promise<void>;
  onBulkUnassignDriver?: (vehicleIds: string[]) => Promise<void>;
  onBulkUpdateInfo?: (
    vehicleIds: string[],
    updates: Partial<Pick<Vehicle, "mc_number" | "us_dot">>,
  ) => Promise<void>;

  // Fleet management
  onFilterByMake?: (make: string) => void;
  onFilterByYear?: (year: number) => void;
  onFilterByAssignmentStatus?: (status: "assigned" | "unassigned") => void;
  onFilterByVehicleType?: (type: "truck" | "trailer" | "van" | "other") => void;

  // Compliance and maintenance
  onScheduleMaintenance?: (vehicleId: string) => void;
  onViewInspectionHistory?: (vehicleId: string) => void;
  onViewMaintenanceHistory?: (vehicleId: string) => void;
  onUpdateInsurance?: (vehicleId: string) => void;
  onUpdateRegistration?: (vehicleId: string) => void;

  // Organization context
  organizationId?: string;
  canManageVehicles?: boolean;
  canDeleteVehicles?: boolean;
  canCreateVehicles?: boolean;
  canAssignDrivers?: boolean;
  canUpdateVehicleInfo?: boolean;
  canScheduleMaintenance?: boolean;
}

const i18n = {
  en: {
    title: "Vehicles",
    addButton: "Add Vehicle",
    deleteDialog: {
      title: "Are you sure?",
      description:
        "This action cannot be undone. This will permanently delete the vehicle and all associated data.",
      cancel: "Cancel",
      confirm: "Delete",
    },
    toast: {
      deleteSuccess: "Vehicle deleted successfully.",
      deleteError: "Failed to delete vehicle. Please try again.",
    },
    summary: {
      total: "Total Vehicles",
      assigned: "Assigned",
      unassigned: "Unassigned",
      active: "Active",
      inactive: "Inactive",
      trucks: "Trucks",
      trailers: "Trailers",
      vans: "Vans",
      maintenance: "In Maintenance",
      newThisMonth: "New This Month",
    },
    search: {
      placeholder: "Search vehicles...",
      noResults: "No vehicles found",
      filtering: "Filtering by driver",
    },
    actions: {
      view: "View Details",
      edit: "Edit Vehicle",
      delete: "Delete Vehicle",
      assign: "Assign Driver",
      unassign: "Unassign Driver",
      scheduleMaintenance: "Schedule Maintenance",
      viewInspections: "View Inspections",
      viewMaintenance: "View Maintenance",
      updateInsurance: "Update Insurance",
      updateRegistration: "Update Registration",
      bulkDelete: "Delete Selected",
      bulkAssign: "Assign Driver",
      bulkUnassign: "Unassign Drivers",
      bulkUpdate: "Update Info",
    },
    filters: {
      byMake: "Filter by Make",
      byYear: "Filter by Year",
      byAssignment: "Filter by Assignment",
      byType: "Filter by Type",
      assigned: "Assigned",
      unassigned: "Unassigned",
    },
    vehicleTypes: {
      truck: "Truck",
      trailer: "Trailer",
      van: "Van",
      other: "Other",
    },
    status: {
      active: "Active",
      inactive: "Inactive",
      maintenance: "In Maintenance",
      outOfService: "Out of Service",
    },
  },
  links: {
    create: "/app/console/vehicles/create",
    view: (id: string) => `/app/console/vehicles/${id}`,
    edit: (id: string) => `/app/console/vehicles/${id}/edit`,
  },
};

export const ConsoleVehiclesPage = ({
  vehicles,
  isLoadingVehicles,
  vehiclesError,
  searchQuery,
  onSearchQueryChange,
  driverId,
  onDriverIdChange,
  pagination,
  deleteVehicleId,
  setDeleteVehicleId,
  onDeleteVehicle,
  isDeletingVehicle,
  onCreateVehicle,
  onEditVehicle,
  onViewVehicle,
  onAssignDriver,
  onUnassignDriver,
  onUpdateVehicleInfo,
  vehicleSummary,
  selectedVehicles = [],
  onSelectVehicle,
  onSelectAllVehicles,
  onBulkDelete,
  onBulkAssignDriver,
  onBulkUnassignDriver,
  onBulkUpdateInfo,
  onFilterByMake,
  onFilterByYear,
  onFilterByAssignmentStatus,
  onFilterByVehicleType,
  onScheduleMaintenance,
  onViewInspectionHistory,
  onViewMaintenanceHistory,
  onUpdateInsurance,
  onUpdateRegistration,
  organizationId,
  canManageVehicles = true,
  canDeleteVehicles = true,
  canCreateVehicles = true,
  canAssignDrivers = true,
  canUpdateVehicleInfo = true,
  canScheduleMaintenance = true,
}: ConsoleVehiclesPageProps) => {
  const hasSelectedVehicles = selectedVehicles.length > 0;

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Truck className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          {hasSelectedVehicles && (
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                {selectedVehicles.length} selected
              </span>
              {onBulkDelete && canDeleteVehicles && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onBulkDelete(selectedVehicles)}
                  disabled={isDeletingVehicle}
                >
                  {i18n.en.actions.bulkDelete}
                </Button>
              )}
              {onBulkAssignDriver && canAssignDrivers && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    onBulkAssignDriver(selectedVehicles, "driver_1")
                  }
                  disabled={isDeletingVehicle}
                >
                  {i18n.en.actions.bulkAssign}
                </Button>
              )}
              {onBulkUnassignDriver && canAssignDrivers && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkUnassignDriver(selectedVehicles)}
                  disabled={isDeletingVehicle}
                >
                  {i18n.en.actions.bulkUnassign}
                </Button>
              )}
              {onBulkUpdateInfo && canUpdateVehicleInfo && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    onBulkUpdateInfo(selectedVehicles, {
                      mc_number: "MC123456",
                    })
                  }
                  disabled={isDeletingVehicle}
                >
                  {i18n.en.actions.bulkUpdate}
                </Button>
              )}
            </div>
          )}
          {canCreateVehicles && (
            <Button asChild disabled={isLoadingVehicles}>
              <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
            </Button>
          )}
        </div>
      </div>

      {/* Vehicle Summary Cards */}
      {vehicleSummary && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5 lg:grid-cols-9">
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold">
              {vehicleSummary.totalVehicles}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.total}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-green-600">
              {vehicleSummary.assignedVehicles}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.assigned}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-orange-600">
              {vehicleSummary.unassignedVehicles}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.unassigned}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-blue-600">
              {vehicleSummary.activeVehicles}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.active}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-gray-600">
              {vehicleSummary.inactiveVehicles}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.inactive}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-purple-600">
              {vehicleSummary.trucksCount}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.trucks}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-cyan-600">
              {vehicleSummary.trailersCount}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.trailers}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-red-600">
              {vehicleSummary.maintenanceVehicles}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.maintenance}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-pink-600">
              {vehicleSummary.newThisMonth}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.newThisMonth}
            </div>
          </div>
        </div>
      )}

      {/* Error Alert */}
      {vehiclesError && <ErrorAlert error={vehiclesError} />}

      {/* Vehicles List */}
      <ListVehicles
        loading={isLoadingVehicles}
        vehicles={vehicles}
        onDelete={setDeleteVehicleId}
      />

      {/* Delete Confirmation Dialog */}
      <DialogConfirmation
        open={!!deleteVehicleId}
        onOpenChange={(open) => {
          if (!open) setDeleteVehicleId(null);
        }}
        onClick={() => {
          if (deleteVehicleId) {
            return onDeleteVehicle(deleteVehicleId);
          }
          return Promise.resolve();
        }}
        title={i18n.en.deleteDialog.title}
        description={i18n.en.deleteDialog.description}
        action={i18n.en.deleteDialog.confirm}
        cancel={i18n.en.deleteDialog.cancel}
        useTrigger={false}
      />
    </div>
  );
};
