"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { Mail, Phone, User } from "lucide-react";
import { Link } from "react-router";

import type { UseDataTableProps } from "@/components/tables";

import { useListContacts } from "@/api/contacts/use-list-contacts";
import ContactEmail from "@/components/common/ContactEmail";
import ContactName from "@/components/common/ContactName";
import ContactPhone from "@/components/common/ContactPhone";
import TimeAgo from "@/components/shared/TimeAgo";
import { selectColumn } from "@/components/tables/columns";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import ListTable from "@/components/tables/ListTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Enums } from "@/supabase/types";

// Using the proper type from Supabase
type ContactType = Enums<"contact_type">;

// Define badge variant types to prevent using 'any'
type BadgeVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "accent";

export type ContactsQueryResult = ReturnType<typeof useListContacts>["data"];
export type ContactsType = NonNullable<ContactsQueryResult>["items"];
export type ContactItemType = ContactsType[number];
export type TableProps = UseDataTableProps<ContactItemType, ContactItemType[]>;

const i18n = {
  en: {
    noContact: "There are no contacts yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search contacts...",
    },
    headers: {
      id: "ID",
      name: "Name",
      email: "Email",
      phone: "Phone",
      organization: "Organization",
      type: "Type",
      created_at: "Created At",
      actions: "Actions",
    },
    filters: {
      type: "Type",
      options: {
        type: {
          ALL: "All Types",
          BILLING: "Billing",
          MANAGER: "Manager",
          DISPATCHER: "Dispatcher",
          SAFETY_OFFICER: "Safety Officer",
          MAINTENANCE: "Maintenance",
          WAREHOUSE: "Warehouse",
          CUSTOMS: "Customs",
          LOGISTICS: "Logistics",
          SALES: "Sales",
          SUPPORT: "Support",
          OTHER: "Other",
        },
      },
    },
  },
  links: {
    contacts: "/app/console/contacts/[id]",
  },
};

const groupName = "contact";
const filterGroups = [
  {
    id: "type",
    label: i18n.en.filters.type,
    options: [
      { value: null, label: i18n.en.filters.options.type.ALL },
      { value: "billing", label: i18n.en.filters.options.type.BILLING },
      { value: "manager", label: i18n.en.filters.options.type.MANAGER },
      { value: "dispatcher", label: i18n.en.filters.options.type.DISPATCHER },
      {
        value: "safety_officer",
        label: i18n.en.filters.options.type.SAFETY_OFFICER,
      },
      { value: "maintenance", label: i18n.en.filters.options.type.MAINTENANCE },
      { value: "warehouse", label: i18n.en.filters.options.type.WAREHOUSE },
      { value: "customs", label: i18n.en.filters.options.type.CUSTOMS },
      { value: "logistics", label: i18n.en.filters.options.type.LOGISTICS },
      { value: "sales", label: i18n.en.filters.options.type.SALES },
      { value: "support", label: i18n.en.filters.options.type.SUPPORT },
      { value: "other", label: i18n.en.filters.options.type.OTHER },
    ],
  },
];

// Links object used in the table
const tableLinks = {
  contacts: "/app/console/contacts/[id]",
};

export default function ListContacts({
  loading = false,
  contacts,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  onDelete,
}: PropsWithChildren<{
  loading?: boolean;
  contacts?: ContactsQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  onDelete?: (id: string) => void;
}>) {
  // Create a properly typed data object
  const tableData = {
    items: contacts?.items || [],
    total: contacts?.total || 0,
  };

  return (
    <div>
      <ListTable
        loading={loading}
        data={tableData}
        defaultPageSize={defaultPageSize}
        defaultPageIndex={defaultPageIndex}
        filters={filters}
        i18n={{
          emptyText: i18n.en.noContact,
          selection: i18n.en.selection,
          actions: i18n.en.actions,
          headers: i18n.en.headers,
        }}
        groupName={groupName}
        filterGroups={filterGroups}
        columns={({ i18n, TableActions }) => [
          selectColumn as ColumnDef<ContactItemType, ContactItemType[]>,
          {
            id: "id",
            accessorKey: "id",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.headers?.id || "ID"}
              />
            ),
            cell: ({ row }) => (
              <Link
                to={tableLinks.contacts.replace("[id]", row.original.id)}
                className="font-medium hover:underline"
              >
                {row.getValue("id")}
              </Link>
            ),
            enableHiding: false,
          },
          {
            id: "name",
            accessorFn: (row) => `${row.first_name} ${row.last_name}`,
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.headers?.name || "Name"}
              />
            ),
            cell: ({ row }) => (
              <div className="flex items-center gap-2">
                <User className="text-muted-foreground h-4 w-4" />
                <ContactName
                  name={row.getValue("first_name") as string}
                  link={tableLinks.contacts.replace("[id]", row.original.id)}
                  showCopyButton={false}
                />
              </div>
            ),
          },
          {
            id: "email",
            accessorKey: "email",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.headers?.email || "Email"}
              />
            ),
            cell: ({ row }) => (
              <div className="flex items-center gap-2">
                <Mail className="text-muted-foreground h-4 w-4" />
                <ContactEmail
                  email={row.getValue("email") as string}
                  showCopy={false}
                />
              </div>
            ),
          },
          {
            id: "phone",
            accessorKey: "phone_number",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.headers?.phone || "Phone"}
              />
            ),
            cell: ({ row }) => (
              <div className="flex items-center gap-2">
                <Phone className="text-muted-foreground h-4 w-4" />
                <ContactPhone
                  phone={row.getValue("phone_number") as string}
                  showCopy={false}
                />
              </div>
            ),
          },
          {
            id: "organization",
            accessorKey: "organization_id",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.headers?.organization || "Organization"}
              />
            ),
            cell: ({ row }) => (
              <div>{row.getValue("organization_id") || "—"}</div>
            ),
          },
          {
            id: "type",
            accessorKey: "type",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.headers?.type || "Type"}
              />
            ),
            cell: ({ row }) => {
              const type = row.getValue("type") as ContactType;
              let variant: BadgeVariant = "outline";

              switch (type) {
                case "billing":
                  variant = "destructive";
                  break;
                case "manager":
                  variant = "default";
                  break;
                case "dispatcher":
                  variant = "secondary";
                  break;
                case "safety_officer":
                  variant = "accent";
                  break;
                default:
                  variant = "outline";
              }

              return (
                <Badge variant={variant}>
                  {type?.replace(/_/g, " ")?.toUpperCase() || "—"}
                </Badge>
              );
            },
          },
          {
            id: "created_at",
            accessorKey: "created_at",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.headers?.created_at || "Created At"}
              />
            ),
            cell: ({ row }) => (
              <TimeAgo
                loading={loading}
                date={new Date(row.getValue("created_at"))}
                className="text-muted-foreground text-sm"
              />
            ),
          },
          {
            id: "actions",
            meta: {
              className: "w-[32px]",
            },
            header: ({ table }) => (
              <div className="flex size-full items-center justify-end">
                <TableActions table={table} i18n={i18n} />
              </div>
            ),
            cell: ({ row }) => (
              <div className="flex size-full items-center justify-end gap-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link
                    to={tableLinks.contacts.replace("[id]", row.original.id)}
                  >
                    View
                  </Link>
                </Button>
                {onDelete && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive"
                    onClick={() => onDelete(row.original.id)}
                  >
                    Delete
                  </Button>
                )}
              </div>
            ),
          },
        ]}
      >
        {children}
      </ListTable>
    </div>
  );
}
