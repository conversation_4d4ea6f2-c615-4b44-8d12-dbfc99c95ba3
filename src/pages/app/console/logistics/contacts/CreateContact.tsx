import { Users } from "lucide-react";
import { useNavigate } from "react-router";

import { useCreateContact } from "@/api/contacts/use-create-contact";
import ContactForm, { ContactFormValues } from "@/components/forms/ContactForm";
import { toast } from "@/components/ui/use-toast";

const i18n = {
  en: {
    title: "Create Contact",
    toasts: {
      success: "Contact created successfully",
      error: "Failed to create contact",
    },
  },
};

export default function CreateContact() {
  const navigate = useNavigate();

  const createContact = useCreateContact({
    onSuccess: (data) => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/contacts/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (values: ContactFormValues) => {
    // Map form values to the API expected format
    const contactData = {
      first_name: values.first_name,
      last_name: values.last_name,
      email: values.email,
      phone_number: values.phone || null,
      type: values.type,
      organization_id: values.organization_id || null,
      location_id: values.location_id || null,
    };

    createContact.mutate(contactData);
  };

  const handleCancel = () => {
    navigate("/app/console/contacts");
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Users className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>

      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <ContactForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={createContact.isPending}
        />
      </div>
    </div>
  );
}
