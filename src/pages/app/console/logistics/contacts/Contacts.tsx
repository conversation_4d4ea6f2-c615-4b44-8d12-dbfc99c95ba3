"use client";

import { useState } from "react";

import { useListContacts } from "@/api/contacts/use-list-contacts";
import {
  useSearchFilterValue,
  useSearchPagination,
  useSearchTextValue,
} from "@/components/search";
import { useToast } from "@/hooks/use-toast";
import { ConsoleContactsPage } from "@/pages/app/console/logistics/contacts/ConsoleContactsPage";
import { supabase } from "@/supabase/client";
import { Enums } from "@/supabase/types";

type ContactType = Enums<"contact_type">;

const i18n = {
  en: {
    toast: {
      deleteSuccess: "Contact deleted successfully.",
      deleteError: "Failed to delete contact. Please try again.",
      updateSuccess: "Contact updated successfully.",
      updateError: "Failed to update contact. Please try again.",
      bulkDeleteSuccess: "Contacts deleted successfully.",
      bulkDeleteError: "Failed to delete contacts. Please try again.",
      bulkUpdateSuccess: "Contacts updated successfully.",
      bulkUpdateError: "Failed to update contacts. Please try again.",
    },
  },
};

export default function ContactsPage() {
  const { toast } = useToast();
  const [deleteContactId, setDeleteContactId] = useState<string | null>(null);
  const [isDeletingContact, setIsDeletingContact] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);

  // Set up search hooks
  const pagination = useSearchPagination({
    group: "contact",
    defaultPageSize: 10,
    defaultPageIndex: 0,
  });
  const contactsQuery = useSearchTextValue("contact");
  const contactType = useSearchFilterValue<ContactType>("type", "contact");

  // Use the hook with search parameters
  const {
    data: contacts,
    isLoading,
    error,
    refetch,
  } = useListContacts({
    pageIndex: pagination.pagination.pageIndex,
    pageSize: pagination.pagination.pageSize,
    search: contactsQuery,
    type: contactType,
  });

  const handleDelete = async (contactId: string) => {
    setIsDeletingContact(true);
    try {
      const { error } = await supabase
        .from("contacts")
        .delete()
        .eq("id", contactId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.deleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.deleteSuccess,
        });
        setDeleteContactId(null);
        refetch();
      }
    } finally {
      setIsDeletingContact(false);
    }
  };

  // Calculate contact summary from the current data
  const contactSummary = contacts
    ? {
        totalContacts: contacts.total,
        billingContacts: contacts.items.filter((c) => c.type === "billing")
          .length,
        managerContacts: contacts.items.filter((c) => c.type === "manager")
          .length,
        dispatcherContacts: contacts.items.filter(
          (c) => c.type === "dispatcher",
        ).length,
        newThisMonth: contacts.items.filter((c) => {
          const createdAt = new Date(c.created_at);
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          return createdAt >= startOfMonth;
        }).length,
      }
    : undefined;

  // Handler functions for contact management
  const handleCreateContact = () => {
    // Navigate to create contact page
    window.location.href = "/app/console/contacts/create";
  };

  const handleEditContact = (contactId: string) => {
    window.location.href = `/app/console/contacts/${contactId}/edit`;
  };

  const handleViewContact = (contactId: string) => {
    window.location.href = `/app/console/contacts/${contactId}`;
  };

  const handleUpdateContactType = async (
    contactId: string,
    newType: ContactType,
  ) => {
    try {
      const { error } = await supabase
        .from("contacts")
        .update({ type: newType })
        .eq("id", contactId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.updateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.updateSuccess,
        });
        refetch();
      }
    } catch (error) {
      console.error("Error updating contact type:", error);
    }
  };

  const handleBulkDelete = async (contactIds: string[]) => {
    setIsDeletingContact(true);
    try {
      const { error } = await supabase
        .from("contacts")
        .delete()
        .in("id", contactIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkDeleteError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkDeleteSuccess,
        });
        setSelectedContacts([]);
        refetch();
      }
    } finally {
      setIsDeletingContact(false);
    }
  };

  const handleBulkTypeChange = async (
    contactIds: string[],
    newType: ContactType,
  ) => {
    try {
      const { error } = await supabase
        .from("contacts")
        .update({ type: newType })
        .in("id", contactIds);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: i18n.en.toast.bulkUpdateError,
        });
        throw error;
      } else {
        toast({
          title: "Success",
          description: i18n.en.toast.bulkUpdateSuccess,
        });
        setSelectedContacts([]);
        refetch();
      }
    } catch (error) {
      console.error("Error updating contact types:", error);
    }
  };

  const handleSelectContact = (contactId: string) => {
    setSelectedContacts((prev) =>
      prev.includes(contactId)
        ? prev.filter((id) => id !== contactId)
        : [...prev, contactId],
    );
  };

  const handleSelectAllContacts = (selected: boolean) => {
    if (selected && contacts) {
      setSelectedContacts(contacts.items.map((contact) => contact.id));
    } else {
      setSelectedContacts([]);
    }
  };

  return (
    <ConsoleContactsPage
      // Contact list data and loading states
      contacts={contacts || null}
      isLoadingContacts={isLoading}
      contactsError={error}
      // Search and filter state
      searchQuery={contactsQuery}
      onSearchQueryChange={(query: string) => {
        // This would be handled by the search hook internally
      }}
      contactType={contactType}
      onContactTypeChange={(type: ContactType | undefined) => {
        // This would be handled by the filter hook internally
      }}
      // Pagination state
      pagination={{
        pageIndex: pagination.pagination.pageIndex,
        pageSize: pagination.pagination.pageSize,
        setPageIndex: (pageIndex: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageIndex,
          }));
        },
        setPageSize: (pageSize: number) => {
          pagination.setPagination((state) => ({
            ...state,
            pageSize,
          }));
        },
      }}
      // Delete functionality
      deleteContactId={deleteContactId}
      setDeleteContactId={setDeleteContactId}
      onDeleteContact={handleDelete}
      isDeletingContact={isDeletingContact}
      // Contact management actions
      onCreateContact={handleCreateContact}
      onEditContact={handleEditContact}
      onViewContact={handleViewContact}
      onUpdateContactType={handleUpdateContactType}
      // Contact analytics and summary
      contactSummary={contactSummary}
      // Bulk operations
      selectedContacts={selectedContacts}
      onSelectContact={handleSelectContact}
      onSelectAllContacts={handleSelectAllContacts}
      onBulkDelete={handleBulkDelete}
      onBulkTypeChange={handleBulkTypeChange}
      // Organization context
      organizationId={undefined}
      canManageContacts={true}
      canDeleteContacts={true}
      canCreateContacts={true}
    />
  );
}
