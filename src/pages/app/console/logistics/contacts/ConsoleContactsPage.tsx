import { Users } from "lucide-react";
import { Link } from "react-router";

import type { ContactsQueryResult } from "@/pages/app/console/logistics/contacts/ListContacts";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import ListContacts from "@/pages/app/console/logistics/contacts/ListContacts";
import { Enums } from "@/supabase/types";

type ContactType = Enums<"contact_type">;

export interface Contact {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  type: ContactType;
  organization_id?: string;
  created_at: string;
  updated_at: string;
}

export interface ContactsListResponse {
  items: Contact[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ContactSearchParams {
  pageIndex: number;
  pageSize: number;
  search?: string;
  type?: ContactType;
}

export interface DeleteContactHandler {
  (contactId: string): Promise<void>;
}

export interface ConsoleContactsPageProps {
  // Contact list data and loading states
  contacts: ContactsQueryResult | null;
  isLoadingContacts: boolean;
  contactsError: Error | null;

  // Search and filter state
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  contactType: ContactType | undefined;
  onContactTypeChange: (type: ContactType | undefined) => void;

  // Pagination state
  pagination: {
    pageIndex: number;
    pageSize: number;
    setPageIndex: (pageIndex: number) => void;
    setPageSize: (pageSize: number) => void;
  };

  // Delete functionality
  deleteContactId: string | null;
  setDeleteContactId: (id: string | null) => void;
  onDeleteContact: DeleteContactHandler;
  isDeletingContact: boolean;

  // Contact management actions
  onCreateContact?: () => void;
  onEditContact?: (contactId: string) => void;
  onViewContact?: (contactId: string) => void;
  onUpdateContactType?: (contactId: string, newType: ContactType) => void;

  // Contact analytics and summary
  contactSummary?: {
    totalContacts: number;
    billingContacts: number;
    managerContacts: number;
    dispatcherContacts: number;
    newThisMonth: number;
  };

  // Bulk operations
  selectedContacts?: string[];
  onSelectContact?: (contactId: string) => void;
  onSelectAllContacts?: (selected: boolean) => void;
  onBulkDelete?: (contactIds: string[]) => Promise<void>;
  onBulkTypeChange?: (
    contactIds: string[],
    newType: ContactType,
  ) => Promise<void>;

  // Organization context
  organizationId?: string;
  canManageContacts?: boolean;
  canDeleteContacts?: boolean;
  canCreateContacts?: boolean;
}

const i18n = {
  en: {
    title: "Contacts",
    addButton: "Add Contact",
    deleteDialog: {
      title: "Are you sure?",
      description:
        "This action cannot be undone. This will permanently delete the contact.",
      cancel: "Cancel",
      confirm: "Delete",
    },
    toast: {
      deleteSuccess: "Contact deleted successfully.",
      deleteError: "Failed to delete contact. Please try again.",
    },
    summary: {
      total: "Total Contacts",
      billing: "Billing",
      manager: "Manager",
      dispatcher: "Dispatcher",
      newThisMonth: "New This Month",
    },
    search: {
      placeholder: "Search contacts...",
      noResults: "No contacts found",
      filtering: "Filtering by type",
    },
    actions: {
      view: "View Details",
      edit: "Edit Contact",
      delete: "Delete Contact",
      bulkDelete: "Delete Selected",
      bulkChangeType: "Change Type",
    },
    types: {
      billing: "Billing",
      manager: "Manager",
      dispatcher: "Dispatcher",
      safety_officer: "Safety Officer",
      maintenance: "Maintenance",
      warehouse: "Warehouse",
      customs: "Customs",
      logistics: "Logistics",
      sales: "Sales",
      support: "Support",
      other: "Other",
    },
  },
  links: {
    create: "/app/console/contacts/create",
    view: (id: string) => `/app/console/contacts/${id}`,
    edit: (id: string) => `/app/console/contacts/${id}/edit`,
  },
};

export const ConsoleContactsPage = ({
  contacts,
  isLoadingContacts,
  contactsError,
  searchQuery,
  onSearchQueryChange,
  contactType,
  onContactTypeChange,
  pagination,
  deleteContactId,
  setDeleteContactId,
  onDeleteContact,
  isDeletingContact,
  onCreateContact,
  onEditContact,
  onViewContact,
  onUpdateContactType,
  contactSummary,
  selectedContacts = [],
  onSelectContact,
  onSelectAllContacts,
  onBulkDelete,
  onBulkTypeChange,
  organizationId,
  canManageContacts = true,
  canDeleteContacts = true,
  canCreateContacts = true,
}: ConsoleContactsPageProps) => {
  const hasSelectedContacts = selectedContacts.length > 0;

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          {hasSelectedContacts && (
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                {selectedContacts.length} selected
              </span>
              {onBulkDelete && canDeleteContacts && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onBulkDelete(selectedContacts)}
                  disabled={isDeletingContact}
                >
                  {i18n.en.actions.bulkDelete}
                </Button>
              )}
              {onBulkTypeChange && canManageContacts && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onBulkTypeChange(selectedContacts, "manager")}
                  disabled={isDeletingContact}
                >
                  {i18n.en.actions.bulkChangeType}
                </Button>
              )}
            </div>
          )}
          {canCreateContacts && (
            <Button asChild disabled={isLoadingContacts}>
              <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
            </Button>
          )}
        </div>
      </div>

      {/* Contact Summary Cards */}
      {contactSummary && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold">
              {contactSummary.totalContacts}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.total}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-red-600">
              {contactSummary.billingContacts}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.billing}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-blue-600">
              {contactSummary.managerContacts}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.manager}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-green-600">
              {contactSummary.dispatcherContacts}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.dispatcher}
            </div>
          </div>
          <div className="rounded-lg border p-4">
            <div className="text-2xl font-bold text-purple-600">
              {contactSummary.newThisMonth}
            </div>
            <div className="text-muted-foreground text-sm">
              {i18n.en.summary.newThisMonth}
            </div>
          </div>
        </div>
      )}

      {/* Error Alert */}
      {contactsError && <ErrorAlert error={contactsError} />}

      {/* Contacts List */}
      <ListContacts
        loading={isLoadingContacts}
        contacts={contacts}
        onDelete={setDeleteContactId}
      />

      {/* Delete Confirmation Dialog */}
      <DialogConfirmation
        open={!!deleteContactId}
        onOpenChange={(open) => {
          if (!open) setDeleteContactId(null);
        }}
        onClick={() => {
          if (deleteContactId) {
            return onDeleteContact(deleteContactId);
          }
          return Promise.resolve();
        }}
        title={i18n.en.deleteDialog.title}
        description={i18n.en.deleteDialog.description}
        action={i18n.en.deleteDialog.confirm}
        cancel={i18n.en.deleteDialog.cancel}
        useTrigger={false}
      />
    </div>
  );
};
