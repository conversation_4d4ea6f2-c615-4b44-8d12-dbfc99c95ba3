import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";
import { fn } from "storybook/test";

import { ConsoleContactsPage } from "./ConsoleContactsPage";

const meta: Meta<typeof ConsoleContactsPage> = {
  title: "Pages/Console/Logistics/Contacts",
  component: ConsoleContactsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/console/contacts" },
    }),
    docs: {
      description: {
        component: `
The ConsoleContactsPage component provides a comprehensive interface for managing contacts within the console application.

## Features
- **Contact List Management**: Display contacts with pagination, search, and filtering
- **CRUD Operations**: Create, read, update, and delete contact records
- **Type Management**: Categorize contacts by type (billing, manager, dispatcher, etc.)
- **Bulk Operations**: Select and manage multiple contacts simultaneously
- **Search & Filter**: Real-time search with type-based filtering
- **Analytics Dashboard**: Contact summary statistics and metrics
- **Delete Confirmation**: Safe deletion with confirmation dialogs

## Usage
This component follows the established console pattern with a presentation component that receives all data and handlers as props, ensuring clean separation of concerns between data management and UI rendering.
        `,
      },
    },
  },
  args: {
    // Default props
    isLoadingContacts: false,
    contactsError: null,
    searchQuery: "",
    onSearchQueryChange: fn(),
    contactType: undefined,
    onContactTypeChange: fn(),
    pagination: {
      pageIndex: 0,
      pageSize: 10,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    deleteContactId: null,
    setDeleteContactId: fn(),
    onDeleteContact: fn(),
    isDeletingContact: false,
    onCreateContact: fn(),
    onEditContact: fn(),
    onViewContact: fn(),
    onUpdateContactType: fn(),
    selectedContacts: [],
    onSelectContact: fn(),
    onSelectAllContacts: fn(),
    onBulkDelete: fn(),
    onBulkTypeChange: fn(),
    organizationId: "org_1",
    canManageContacts: true,
    canDeleteContacts: true,
    canCreateContacts: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock contact data for stories
const mockContacts = {
  items: [
    {
      id: "cnt_1",
      first_name: "Sarah",
      last_name: "Johnson",
      email: "<EMAIL>",
      phone_number: "******-0101",
      type: "billing" as const,
      organization_id: "org_1",
      location_id: null,
      created_at: "2024-01-15T10:30:00Z",
      updated_at: "2024-01-15T10:30:00Z",
    },
    {
      id: "cnt_2",
      first_name: "Michael",
      last_name: "Chen",
      email: "<EMAIL>",
      phone_number: "******-0102",
      type: "manager" as const,
      organization_id: "org_1",
      location_id: null,
      created_at: "2024-02-20T08:15:00Z",
      updated_at: "2024-02-20T08:15:00Z",
    },
    {
      id: "cnt_3",
      first_name: "Emily",
      last_name: "Rodriguez",
      email: "<EMAIL>",
      phone_number: "******-0103",
      type: "dispatcher" as const,
      organization_id: "org_1",
      location_id: null,
      created_at: "2024-03-10T16:45:00Z",
      updated_at: "2024-03-10T16:45:00Z",
    },
    {
      id: "cnt_4",
      first_name: "David",
      last_name: "Thompson",
      email: "<EMAIL>",
      phone_number: "******-0104",
      type: "safety_officer" as const,
      organization_id: "org_1",
      location_id: null,
      created_at: "2024-04-05T09:20:00Z",
      updated_at: "2024-04-05T09:20:00Z",
    },
    {
      id: "cnt_5",
      first_name: "Lisa",
      last_name: "Anderson",
      email: "<EMAIL>",
      phone_number: "******-0105",
      type: "maintenance" as const,
      organization_id: "org_1",
      location_id: null,
      created_at: "2024-05-12T13:30:00Z",
      updated_at: "2024-05-12T13:30:00Z",
    },
    {
      id: "cnt_6",
      first_name: "Robert",
      last_name: "Garcia",
      email: "<EMAIL>",
      phone_number: "******-0106",
      type: "warehouse" as const,
      organization_id: "org_1",
      location_id: null,
      created_at: "2024-06-01T10:00:00Z",
      updated_at: "2024-06-01T10:00:00Z",
    },
  ],
  total: 6,
};

const contactSummary = {
  totalContacts: 6,
  billingContacts: 1,
  managerContacts: 1,
  dispatcherContacts: 1,
  newThisMonth: 2,
};

export const Default: Story = {
  args: {
    contacts: mockContacts,
    contactSummary,
  },
};

export const Loading: Story = {
  args: {
    isLoadingContacts: true,
    contacts: null,
    contactSummary: undefined,
  },
};

export const EmptyState: Story = {
  args: {
    contacts: {
      items: [],
      total: 0,
    },
    contactSummary: {
      totalContacts: 0,
      billingContacts: 0,
      managerContacts: 0,
      dispatcherContacts: 0,
      newThisMonth: 0,
    },
  },
};

export const SearchResults: Story = {
  args: {
    contacts: {
      items: mockContacts.items.filter((c) => c.first_name.includes("Sarah")),
      total: 1,
    },
    searchQuery: "Sarah",
    contactSummary: {
      totalContacts: 1,
      billingContacts: 1,
      managerContacts: 0,
      dispatcherContacts: 0,
      newThisMonth: 0,
    },
  },
};

export const FilteredByBillingType: Story = {
  args: {
    contacts: {
      items: mockContacts.items.filter((c) => c.type === "billing"),
      total: 1,
    },
    contactType: "billing",
    contactSummary: {
      totalContacts: 1,
      billingContacts: 1,
      managerContacts: 0,
      dispatcherContacts: 0,
      newThisMonth: 0,
    },
  },
};

export const FilteredByManagerType: Story = {
  args: {
    contacts: {
      items: mockContacts.items.filter((c) => c.type === "manager"),
      total: 1,
    },
    contactType: "manager",
    contactSummary: {
      totalContacts: 1,
      billingContacts: 0,
      managerContacts: 1,
      dispatcherContacts: 0,
      newThisMonth: 0,
    },
  },
};

export const FilteredByDispatcherType: Story = {
  args: {
    contacts: {
      items: mockContacts.items.filter((c) => c.type === "dispatcher"),
      total: 1,
    },
    contactType: "dispatcher",
    contactSummary: {
      totalContacts: 1,
      billingContacts: 0,
      managerContacts: 0,
      dispatcherContacts: 1,
      newThisMonth: 0,
    },
  },
};

export const PaginatedResults: Story = {
  args: {
    contacts: {
      items: mockContacts.items.slice(0, 3),
      total: 25,
    },
    pagination: {
      pageIndex: 0,
      pageSize: 3,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    contactSummary: {
      totalContacts: 25,
      billingContacts: 8,
      managerContacts: 7,
      dispatcherContacts: 6,
      newThisMonth: 4,
    },
  },
};

export const DeleteConfirmation: Story = {
  args: {
    contacts: mockContacts,
    deleteContactId: "cnt_3",
    contactSummary,
  },
};

export const DeletingContact: Story = {
  args: {
    contacts: mockContacts,
    deleteContactId: "cnt_3",
    isDeletingContact: true,
    contactSummary,
  },
};

export const BulkOperationsActive: Story = {
  args: {
    contacts: mockContacts,
    selectedContacts: ["cnt_1", "cnt_2", "cnt_3"],
    contactSummary,
  },
};

export const MixedContactTypes: Story = {
  args: {
    contacts: {
      items: [
        ...mockContacts.items,
        {
          id: "cnt_7",
          first_name: "Jennifer",
          last_name: "Martinez",
          email: "<EMAIL>",
          phone_number: "******-0107",
          type: "customs" as const,
          organization_id: "org_1",
          location_id: null,
          created_at: "2024-06-15T14:30:00Z",
          updated_at: "2024-06-15T14:30:00Z",
        },
        {
          id: "cnt_8",
          first_name: "James",
          last_name: "Wilson",
          email: "<EMAIL>",
          phone_number: "******-0108",
          type: "logistics" as const,
          organization_id: "org_1",
          location_id: null,
          created_at: "2024-06-20T11:15:00Z",
          updated_at: "2024-06-20T11:15:00Z",
        },
      ],
      total: 8,
    },
    contactSummary: {
      totalContacts: 8,
      billingContacts: 1,
      managerContacts: 1,
      dispatcherContacts: 1,
      newThisMonth: 4,
    },
  },
};

export const NewOrganization: Story = {
  args: {
    contacts: {
      items: [
        {
          id: "cnt_new_1",
          first_name: "Alex",
          last_name: "Thompson",
          email: "<EMAIL>",
          phone_number: "******-0201",
          type: "manager" as const,
          organization_id: "org_new",
          location_id: null,
          created_at: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 2 days ago
          updated_at: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000,
          ).toISOString(),
        },
        {
          id: "cnt_new_2",
          first_name: "Jessica",
          last_name: "Lee",
          email: "<EMAIL>",
          phone_number: "******-0202",
          type: "billing" as const,
          organization_id: "org_new",
          location_id: null,
          created_at: new Date(
            Date.now() - 1 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 1 day ago
          updated_at: new Date(
            Date.now() - 1 * 24 * 60 * 60 * 1000,
          ).toISOString(),
        },
      ],
      total: 2,
    },
    contactSummary: {
      totalContacts: 2,
      billingContacts: 1,
      managerContacts: 1,
      dispatcherContacts: 0,
      newThisMonth: 2,
    },
  },
};

export const HighVolumeContacts: Story = {
  args: {
    contacts: {
      items: mockContacts.items,
      total: 1847,
    },
    pagination: {
      pageIndex: 23,
      pageSize: 50,
      setPageIndex: fn(),
      setPageSize: fn(),
    },
    contactSummary: {
      totalContacts: 1847,
      billingContacts: 234,
      managerContacts: 567,
      dispatcherContacts: 432,
      newThisMonth: 78,
    },
  },
};

export const ContactTypesDistribution: Story = {
  args: {
    contacts: {
      items: [
        {
          id: "cnt_sales_1",
          first_name: "Mark",
          last_name: "Johnson",
          email: "<EMAIL>",
          phone_number: "******-0301",
          type: "sales" as const,
          organization_id: "org_1",
          location_id: null,
          created_at: "2024-01-10T09:00:00Z",
          updated_at: "2024-01-10T09:00:00Z",
        },
        {
          id: "cnt_support_1",
          first_name: "Amanda",
          last_name: "Davis",
          email: "<EMAIL>",
          phone_number: "******-0302",
          type: "support" as const,
          organization_id: "org_1",
          location_id: null,
          created_at: "2024-02-15T14:30:00Z",
          updated_at: "2024-02-15T14:30:00Z",
        },
        {
          id: "cnt_other_1",
          first_name: "Chris",
          last_name: "Brown",
          email: "<EMAIL>",
          phone_number: "******-0303",
          type: "other" as const,
          organization_id: "org_1",
          location_id: null,
          created_at: "2024-03-20T16:45:00Z",
          updated_at: "2024-03-20T16:45:00Z",
        },
      ],
      total: 3,
    },
    contactSummary: {
      totalContacts: 3,
      billingContacts: 0,
      managerContacts: 0,
      dispatcherContacts: 0,
      newThisMonth: 1,
    },
  },
};

export const SearchAndFilter: Story = {
  args: {
    contacts: {
      items: mockContacts.items.filter(
        (c) => c.type === "manager" && c.first_name.includes("Michael"),
      ),
      total: 1,
    },
    searchQuery: "Michael",
    contactType: "manager",
    contactSummary: {
      totalContacts: 1,
      billingContacts: 0,
      managerContacts: 1,
      dispatcherContacts: 0,
      newThisMonth: 0,
    },
  },
};

export const ErrorState: Story = {
  args: {
    contacts: null,
    contactsError: new Error(
      "Failed to load contacts. Please check your network connection and try again.",
    ),
    contactSummary: undefined,
  },
};

export const ReadOnlyView: Story = {
  args: {
    contacts: mockContacts,
    contactSummary,
    canManageContacts: false,
    canDeleteContacts: false,
    canCreateContacts: false,
  },
};

export const LimitedPermissions: Story = {
  args: {
    contacts: mockContacts,
    contactSummary,
    canManageContacts: true,
    canDeleteContacts: false,
    canCreateContacts: true,
  },
};

export const RecentlyCreatedContacts: Story = {
  args: {
    contacts: {
      items: mockContacts.items.map((contact, index) => ({
        ...contact,
        created_at: new Date(
          Date.now() - (index + 1) * 24 * 60 * 60 * 1000,
        ).toISOString(),
        updated_at: new Date(
          Date.now() - (index + 1) * 24 * 60 * 60 * 1000,
        ).toISOString(),
      })),
      total: 6,
    },
    contactSummary: {
      totalContacts: 6,
      billingContacts: 1,
      managerContacts: 1,
      dispatcherContacts: 1,
      newThisMonth: 6,
    },
  },
};

export const LoadingWithPartialData: Story = {
  args: {
    isLoadingContacts: true,
    contacts: {
      items: mockContacts.items.slice(0, 2),
      total: 6,
    },
    contactSummary: {
      totalContacts: 6,
      billingContacts: 1,
      managerContacts: 1,
      dispatcherContacts: 1,
      newThisMonth: 2,
    },
  },
};
