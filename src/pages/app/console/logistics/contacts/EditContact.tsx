import { useQuery } from "@tanstack/react-query";
import { Users } from "lucide-react";
import { useNavigate, useParams } from "react-router";

import { useUpdateContact } from "@/api/contacts/use-update-contact";
import ContactForm, { ContactFormValues } from "@/components/forms/ContactForm";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

const i18n = {
  en: {
    title: "Edit Contact",
    toasts: {
      success: "Contact updated successfully",
      error: "Failed to update contact",
    },
    loading: "Loading contact...",
    error: "Failed to load contact",
    notFound: "Contact not found",
    backButton: "Back to Contacts",
  },
};

// Hook to get a single contact
function useGetContact(id: string) {
  return useQuery({
    queryKey: ["contacts", "get", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("contacts")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!id,
  });
}

export default function EditContact() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: contact, isLoading, error } = useGetContact(id!);

  const updateContact = useUpdateContact({
    onSuccess: () => {
      toast({
        title: i18n.en.toasts.success,
      });
      navigate(`/app/console/contacts/${id}`);
    },
    onError: (error) => {
      toast({
        title: i18n.en.toasts.error,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (values: ContactFormValues) => {
    if (!id) return;

    // Map form values to the API expected format
    const contactData = {
      id,
      first_name: values.first_name,
      last_name: values.last_name,
      email: values.email,
      phone_number: values.phone || null,
      type: values.type,
      organization_id: values.organization_id || null,
      location_id: values.location_id || null,
    };

    updateContact.mutate(contactData);
  };

  const handleCancel = () => {
    navigate(`/app/console/contacts/${id}`);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-2">
          <Users className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <div className="bg-card rounded-lg border p-6 shadow-xs">
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-2">
          <Users className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <ErrorAlert error={error} />
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/contacts")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  if (!contact) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-2">
          <Users className="size-8" />
          <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
        </div>
        <p className="text-muted-foreground">{i18n.en.notFound}</p>
        <Button
          variant="outline"
          onClick={() => navigate("/app/console/contacts")}
        >
          {i18n.en.backButton}
        </Button>
      </div>
    );
  }

  // Transform API data to form values
  const defaultValues: ContactFormValues = {
    first_name: contact.first_name,
    last_name: contact.last_name,
    email: contact.email,
    phone: contact.phone_number || "",
    type: contact.type,
    organization_id: contact.organization_id || undefined,
    location_id: contact.location_id || undefined,
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Users className="size-8" />
        <h1 className="text-3xl font-bold tracking-tight">{i18n.en.title}</h1>
      </div>

      <div className="bg-card rounded-lg border p-6 shadow-xs">
        <ContactForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          disabled={updateContact.isPending}
          defaultValues={defaultValues}
        />
      </div>
    </div>
  );
}
