import { Building2, Truck } from "lucide-react";
import { useNavigate } from "react-router";

import { Button } from "@/components/ui/button";

const DashboardSelector = () => {
  const navigate = useNavigate();

  return (
    <div className="container py-16">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-4 text-center text-4xl font-bold">
          Select Your Dashboard
        </h1>
        <p className="text-muted-foreground mb-12 text-center text-xl">
          Choose which dashboard you'd like to access
        </p>

        <div className="grid gap-8 md:grid-cols-2">
          <Button
            variant="outline"
            className="hover:border-primary hover:bg-primary/5 flex h-64 flex-col items-center justify-center space-y-4"
            onClick={() => navigate("/app/drivers")}
          >
            <Truck className="h-16 w-16" />
            <div className="text-center">
              <h2 className="mb-2 text-2xl font-semibold">Drivers Dashboard</h2>
              <p className="text-muted-foreground">
                Manage deliveries and track shipments
              </p>
            </div>
          </Button>

          <Button
            variant="outline"
            className="hover:border-primary hover:bg-primary/5 flex h-64 flex-col items-center justify-center space-y-4"
            onClick={() => navigate("/app/console")}
          >
            <Building2 className="h-16 w-16" />
            <div className="text-center">
              <h2 className="mb-2 text-2xl font-semibold">
                Organizations Dashboard
              </h2>
              <p className="text-muted-foreground">
                Manage your organization and team
              </p>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DashboardSelector;
