# Pages

This directory contains the page components that compose our application views. Pages are the intersection point where data operations meet presentational components, serving as the composition layer of our application.

> [!KNOWLEDGE]
>
> **Two-Tiered Page Architecture**
> Data-driven pages follow a strict two-tiered architecture:
>
> - **Presentation Component** (`*View`): Pure UI rendering with props
> - **Data Component** (`*Page`): Handles data fetching, state, and business logic
>
> Example of the pattern:
>
> ```tsx
> // Presentation Component
> export function DriversView({
>   loading,
>   error,
>   drivers,
>   onDelete,
> }: DriversViewProps) {
>   return (
>     <div className="flex flex-col gap-6">
>       <PageHeader title="Drivers" />
>       <ListDrivers loading={loading} drivers={drivers} onDelete={onDelete} />
>     </div>
>   );
> }
>
> // Data Component
> export default function DriversPage() {
>   // State & data hooks
>   const pagination = useSearchPaginationValue({ group: "driver" });
>   const { data, isLoading, error } = useListDrivers({
>     pageIndex: pagination.pageIndex,
>     pageSize: pagination.pageSize,
>   });
>
>   // Business logic
>   const handleDelete = async (id) => {
>     // Implementation
>   };
>
>   // Composition
>   return (
>     <DriversView
>       loading={isLoading}
>       error={error}
>       drivers={data}
>       onDelete={handleDelete}
>     />
>   );
> }
> ```
>
> **Data Integration**
> Pages handle all data operations through:
>
> - API hooks (e.g., `useLoads`, `useShipments`)
> - Mutations (e.g., `useCreateLoad`, `useUpdateShipment`)
> - Context providers (e.g., `OrganizationProvider`)
> - Route params and queries
>
> **Component Composition**
> Pages compose various component types:
>
> ```tsx
> function LoadDetailPage() {
>   const { id } = useParams();
>   const { data, isLoading } = useLoad(id);
>   const { toast } = useToast();
>
>   return (
>     <ConsoleLayout>
>       <LoadDetailHeader load={data} />
>       <LoadForm defaultValues={data} onSubmit={handleSubmit} />
>       <ShipmentsList
>         loadId={id}
>         onError={() => toast({ variant: "destructive" })}
>       />
>     </ConsoleLayout>
>   );
> }
> ```

## Directory Structure

```
pages/
├── app/              # Authenticated application views
│   ├── console/     # Main console interface
│   ├── onboarding/  # User onboarding flow
│   └── settings/    # User/org settings
├── auth/            # Authentication views
├── public/          # Public marketing pages
└── README.md        # This file
```

## Page Categories

### Public Pages (`public/`)

- Marketing pages
- Landing pages
- Contact forms
- Public documentation

### Authentication Pages (`auth/`)

- Sign in/up flows
- Password reset
- Email verification
- OAuth callbacks

### Application Pages (`app/`)

- Protected dashboard views
- Data management interfaces
- Settings and configuration
- User/org onboarding

## Best Practices

1. **Data Handling**
   - Use API hooks from `src/api/` for data operations
   - Avoid inline data fetching
   - Handle loading and error states consistently
   - Manage mutations through API hooks

2. **Component Composition**
   - Use appropriate layout components
   - Pass data down through props
   - Handle loading states with skeletons
   - Use error boundaries for error handling

3. **Routing**
   - Connect to routes defined in `src/routes/`
   - Handle route parameters properly
   - Manage navigation state
   - Handle route transitions

4. **State Management**
   - Keep state at appropriate level
   - Use context where needed
   - Handle form state properly
   - Manage side effects

5. **Performance**
   - Implement proper data caching
   - Use pagination where needed
   - Optimize data fetching
   - Handle large datasets

## Common Patterns

### Basic Page Structure

```tsx
function LoadsPage() {
  const { data, isLoading } = useLoads();

  return (
    <ConsoleLayout>
      <PageHeader title="Loads" />
      <LoadsList data={data} isLoading={isLoading} />
    </ConsoleLayout>
  );
}
```

### Form Pages

```tsx
function CreateLoadPage() {
  const navigate = useNavigate();
  const createLoad = useCreateLoad();

  const handleSubmit = async (values) => {
    await createLoad.mutateAsync(values);
    navigate("/app/console/loads");
  };

  return (
    <ConsoleLayout>
      <LoadForm onSubmit={handleSubmit} />
    </ConsoleLayout>
  );
}
```

### Detail Pages

```tsx
function ShipmentDetailPage() {
  const { id } = useParams();
  const { data, isLoading } = useShipment(id);

  if (isLoading) return <LoadingSpinner />;

  return (
    <ConsoleLayout>
      <ShipmentDetail shipment={data} />
      <ShipmentTimeline shipment={data} />
      <ShipmentDocuments shipmentId={id} />
    </ConsoleLayout>
  );
}
```

## Route Integration

Pages are connected to routes in `src/routes/` following these patterns:

```tsx
// src/routes/app.tsx
export const appRoutes = [
  {
    path: "console",
    element: <ConsoleLayout />,
    children: [
      {
        path: "loads",
        element: <LoadsPage />,
      },
      {
        path: "loads/:id",
        element: <LoadDetailPage />,
      },
    ],
  },
];
```

## Error Handling

Pages should implement proper error handling:

```tsx
function LoadDetailPage() {
  const { id } = useParams();
  const { data, error } = useLoad(id);

  if (error) {
    return (
      <ErrorLayout>
        <ErrorMessage error={error} />
        <Button onClick={() => navigate(-1)}>Go Back</Button>
      </ErrorLayout>
    );
  }

  return <LoadDetail load={data} />;
}
```

## List Component Pattern

List components follow a consistent pattern that enables reuse and standardization:

### Pattern Structure

1. **Base Component**: `ListTable` from shared components
2. **Domain-Specific List**: `List{Entity}` (e.g., `ListDrivers`, `ListShipments`)
3. **Integration**: Used in corresponding View component

### Implementation Guidelines

```tsx
// src/pages/app/console/entity/ListEntity.tsx
export default function ListEntity({
  loading = false,
  entities,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  onDelete,
}: {
  loading?: boolean;
  entities?: ReturnType<typeof useListEntities>["data"];
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onDelete?: (id: string) => void;
}) {
  return (
    <ListTable
      loading={loading}
      data={entities}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      groupName="entity"
      filterGroups={
        [
          /* filter definitions */
        ]
      }
      columns={({ i18n }) => [
        // Column definitions
      ]}
    />
  );
}
```

### Key Features

- **i18n Configuration**: Text constants for headers, filters, and messages
- **Column Definitions**: Standardized columns with consistent accessors
- **Cell Rendering**: Custom renderers for specialized data types (dates, currency, status)
- **Actions**: Standard patterns for view, edit, delete operations
- **Shared Components**: Reuse of UI components like:
  - `TimeAgo`: For date display
  - `Currency`: For monetary values
  - `StatusBadge`: For status indicators
  - `DropdownMenu`: For action menus
  - `DialogConfirmation`: For delete confirmations

### Integration with Search & Filters

List components rely on URL-based state management hooks:

- `useSearchPaginationValue`: For page size and index
- `useSearchTextValue`: For search terms
- `useSearchFilterValue`: For status and other filters

This enables:

- Bookmarkable search results
- Browser history navigation
- Consistent UX across the application
