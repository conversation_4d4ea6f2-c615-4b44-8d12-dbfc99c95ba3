import { Link } from "react-router";

import { SignUpForm } from "@/components/authentication/sign-up-form";
import Logo from "@/components/brand/Logo";

export default function SignUp() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Beautiful gradient background */}
      <div className="from-primary via-primary/90 to-primary/70 absolute inset-0 bg-gradient-to-br" />
      <div className="absolute inset-0 bg-gradient-to-bl from-emerald-500/20 via-transparent to-blue-500/15" />

      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-1/3 right-1/3 h-80 w-80 animate-pulse rounded-full bg-white/10 blur-3xl"
          style={{ animationDelay: "0.5s" }}
        />
        <div
          className="absolute bottom-1/3 left-1/3 h-72 w-72 animate-pulse rounded-full bg-white/5 blur-2xl"
          style={{ animationDelay: "1.5s" }}
        />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8">
        {/* Logo branding */}
        <div className="mb-8">
          <Link to="/" className="inline-block">
            <Logo />
          </Link>
        </div>

        {/* Welcome message */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-white drop-shadow-sm sm:text-4xl">
            Join QuikSkope
          </h1>
          <p className="mt-2 text-lg text-white/80 drop-shadow-sm">
            Create your account and start tracking
          </p>
        </div>

        {/* Sign-up form container */}
        <div className="w-full max-w-md">
          <div className="glass-effect rounded-xl p-1">
            <div className="bg-background/95 rounded-lg backdrop-blur-sm">
              <SignUpForm className="p-6" />
            </div>
          </div>
        </div>

        {/* Footer links */}
        <div className="mt-8 text-center">
          <p className="text-sm text-white/60">
            Already have an account?{" "}
            <Link
              to="/auth/sign-in"
              className="font-medium text-white underline underline-offset-4 transition-colors hover:text-white/80"
            >
              Sign in
            </Link>
          </p>
        </div>

        {/* Features highlight */}
        <div className="mt-12 grid grid-cols-1 gap-4 text-center sm:grid-cols-3 sm:gap-8">
          <div className="text-white/70">
            <div className="text-2xl font-bold">⚡</div>
            <p className="text-sm">Lightning Fast</p>
          </div>
          <div className="text-white/70">
            <div className="text-2xl font-bold">🛡️</div>
            <p className="text-sm">Secure & Reliable</p>
          </div>
          <div className="text-white/70">
            <div className="text-2xl font-bold">📱</div>
            <p className="text-sm">Mobile Ready</p>
          </div>
        </div>
      </div>
    </div>
  );
}
