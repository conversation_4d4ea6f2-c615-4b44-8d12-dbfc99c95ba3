import { Link } from "react-router";

import { ForgotPasswordForm } from "@/components/authentication/forgot-password-form";
import Logo from "@/components/brand/Logo";

export default function ForgotPassword() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Beautiful gradient background */}
      <div className="from-primary via-primary/90 to-primary/70 absolute inset-0 bg-gradient-to-br" />
      <div className="absolute inset-0 bg-gradient-to-bl from-violet-500/20 via-transparent to-indigo-500/15" />

      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/2 left-1/2 h-96 w-96 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-white/10 blur-3xl" />
        <div
          className="absolute top-1/4 right-1/4 h-48 w-48 animate-pulse rounded-full bg-white/5 blur-2xl"
          style={{ animationDelay: "2s" }}
        />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4">
        {/* Logo branding */}
        <div className="mb-8">
          <Link to="/" className="inline-block">
            <Logo />
          </Link>
        </div>

        {/* Welcome message */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-white drop-shadow-sm sm:text-4xl">
            Reset Password
          </h1>
          <p className="mt-2 text-lg text-white/80 drop-shadow-sm">
            We'll help you get back into your account
          </p>
        </div>

        {/* Forgot password form container */}
        <div className="w-full max-w-md">
          <div className="glass-effect rounded-xl p-1">
            <div className="bg-background/95 rounded-lg backdrop-blur-sm">
              <ForgotPasswordForm className="p-6" />
            </div>
          </div>
        </div>

        {/* Footer links */}
        <div className="mt-8 space-y-2 text-center">
          <p className="text-sm text-white/60">
            Remember your password?{" "}
            <Link
              to="/auth/sign-in"
              className="font-medium text-white underline underline-offset-4 transition-colors hover:text-white/80"
            >
              Sign in
            </Link>
          </p>
          <p className="text-sm text-white/60">
            Don't have an account?{" "}
            <Link
              to="/auth/sign-up"
              className="font-medium text-white underline underline-offset-4 transition-colors hover:text-white/80"
            >
              Sign up
            </Link>
          </p>
        </div>

        {/* Help section */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 rounded-full bg-white/10 px-4 py-2 text-white/70 backdrop-blur-sm">
            <div className="text-lg">💡</div>
            <span className="text-sm">Need help? Contact our support team</span>
          </div>
        </div>
      </div>
    </div>
  );
}
