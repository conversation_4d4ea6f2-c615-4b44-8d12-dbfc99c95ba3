import type { Meta, StoryObj } from "@storybook/react-vite";

import { MemoryRouter } from "react-router";

import ForgotPassword from "./forgot-password";
import Invitation from "./invitation";
import SignIn from "./sign-in";
import SignUp from "./sign-up";
import SignUpSuccess from "./sign-up-success";
import UpdatePassword from "./update-password";

const meta: Meta = {
  title: "Pages/Auth",
  decorators: [
    (Story) => (
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "Beautiful authentication pages with stunning gradient backgrounds and glass-effect form containers.",
      },
    },
  },
};

export default meta;

export const SignInPage: StoryObj = {
  render: () => <SignIn />,
  parameters: {
    docs: {
      description: {
        story:
          "Sign-in page with welcoming message and clean login form. Features animated background elements and glass-effect styling.",
      },
    },
  },
};

export const SignUpPage: StoryObj = {
  render: () => <SignUp />,
  parameters: {
    docs: {
      description: {
        story:
          "Sign-up page with feature highlights and comprehensive registration form. Includes animated background and branding elements.",
      },
    },
  },
};

export const ForgotPasswordPage: StoryObj = {
  render: () => <ForgotPassword />,
  parameters: {
    docs: {
      description: {
        story:
          "Password reset page with helpful messaging and support information. Features centered layout with glass-effect container.",
      },
    },
  },
};

// Dark mode variants to showcase dark mode compatibility
export const SignInDark: StoryObj = {
  render: () => <SignIn />,
  parameters: {
    backgrounds: { default: "dark" },
    docs: {
      description: {
        story:
          "Sign-in page in dark mode, showing seamless dark theme integration.",
      },
    },
  },
};

export const SignUpDark: StoryObj = {
  render: () => <SignUp />,
  parameters: {
    backgrounds: { default: "dark" },
    docs: {
      description: {
        story:
          "Sign-up page in dark mode with feature highlights remaining visible.",
      },
    },
  },
};

export const ForgotPasswordDark: StoryObj = {
  render: () => <ForgotPassword />,
  parameters: {
    backgrounds: { default: "dark" },
    docs: {
      description: {
        story:
          "Password reset page in dark mode with help section clearly visible.",
      },
    },
  },
};

export const SignUpSuccessPage: StoryObj = {
  render: () => <SignUpSuccess />,
  parameters: {
    docs: {
      description: {
        story:
          "Success page shown after account creation with email verification instructions and next steps.",
      },
    },
  },
};

export const UpdatePasswordPage: StoryObj = {
  render: () => <UpdatePassword />,
  parameters: {
    docs: {
      description: {
        story:
          "Password update page for users who clicked the reset link from their email.",
      },
    },
  },
};

export const SignUpSuccessDark: StoryObj = {
  render: () => <SignUpSuccess />,
  parameters: {
    backgrounds: { default: "dark" },
    docs: {
      description: {
        story:
          "Success page in dark mode showing celebration and next steps clearly.",
      },
    },
  },
};

export const UpdatePasswordDark: StoryObj = {
  render: () => <UpdatePassword />,
  parameters: {
    backgrounds: { default: "dark" },
    docs: {
      description: {
        story: "Password update page in dark mode with security messaging.",
      },
    },
  },
};

export const InvitationPage: StoryObj = {
  render: () => <Invitation />,
  parameters: {
    reactRouter: {
      routePath: "/auth/invitation/:id",
      routeParams: { id: "inv_123456789" },
    },
    docs: {
      description: {
        story:
          "Invitation page for team invites with deep link ID parameter. Currently a placeholder with future implementation comments.",
      },
    },
  },
};

export const InvitationNoId: StoryObj = {
  render: () => <Invitation />,
  parameters: {
    docs: {
      description: {
        story:
          "Invitation page without ID parameter to show fallback behavior.",
      },
    },
  },
};

export const InvitationDark: StoryObj = {
  render: () => <Invitation />,
  parameters: {
    backgrounds: { default: "dark" },
    reactRouter: {
      routePath: "/auth/invitation/:id",
      routeParams: { id: "inv_dark_mode_test" },
    },
    docs: {
      description: {
        story:
          "Invitation page in dark mode showing placeholder content and development notes.",
      },
    },
  },
};
