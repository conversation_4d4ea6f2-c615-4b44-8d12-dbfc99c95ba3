import { Link, useParams } from "react-router";

import Logo from "@/components/brand/Logo";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function Invitation() {
  const { id } = useParams<{ id: string }>();

  // TODO: Future Implementation
  // 1. Fetch invitation data from database using the ID parameter
  // 2. Validate invitation status (pending, expired, used, etc.)
  // 3. Extract organization/team information from invitation
  // 4. Handle different invitation types (team member, admin, driver, etc.)
  // 5. Pre-populate sign-up form with invitation details
  // 6. Automatically accept invitation after successful registration
  // 7. Redirect to appropriate dashboard based on user role

  // Example API call structure for future:
  // const { data: invitation, isLoading, error } = useGetInvitation(id);
  // if (invitation?.status === 'expired') return <ExpiredInvitation />
  // if (invitation?.status === 'used') return <AlreadyUsedInvitation />

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Beautiful gradient background */}
      <div className="from-primary via-primary/90 to-primary/70 absolute inset-0 bg-gradient-to-br" />
      <div className="absolute inset-0 bg-gradient-to-bl from-purple-500/20 via-transparent to-pink-500/15" />

      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-1/4 left-1/4 h-96 w-96 animate-pulse rounded-full bg-white/10 blur-3xl"
          style={{ animationDelay: "0.8s" }}
        />
        <div
          className="absolute right-1/4 bottom-1/4 h-80 w-80 animate-pulse rounded-full bg-white/5 blur-2xl"
          style={{ animationDelay: "2s" }}
        />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4">
        {/* Logo branding */}
        <div className="mb-8">
          <Link to="/" className="inline-block">
            <Logo />
          </Link>
        </div>

        {/* Invitation message */}
        <div className="mb-8 text-center">
          <div className="mb-4 text-6xl">📧</div>
          <h1 className="text-3xl font-bold text-white drop-shadow-sm sm:text-4xl">
            You're Invited!
          </h1>
          <p className="mt-2 text-lg text-white/80 drop-shadow-sm">
            Join your team on QuikSkope
          </p>
        </div>

        {/* Invitation card container */}
        <div className="w-full max-w-md">
          <div className="glass-effect rounded-xl p-1">
            <div className="bg-background/95 rounded-lg backdrop-blur-sm">
              <Card className="border-0 bg-transparent shadow-none">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">Invitation Preview</CardTitle>
                  <CardDescription>
                    Feature coming soon - Database integration pending
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Debug info for development */}
                  <div className="bg-muted/50 rounded-lg p-3 text-sm">
                    <p className="mb-1 font-medium">Invitation ID:</p>
                    <p className="font-mono text-xs break-all">
                      {id || "No ID provided"}
                    </p>
                  </div>

                  <div className="text-muted-foreground space-y-2 text-center text-sm">
                    <p>
                      🚧 This page will soon display invitation details from the
                      database.
                    </p>
                    <p>
                      For now, you can continue to sign up or sign in manually.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <Button asChild className="w-full">
                      <Link to="/auth/sign-up">
                        Accept Invitation & Sign Up
                      </Link>
                    </Button>

                    <Button variant="outline" asChild className="w-full">
                      <Link to="/auth/sign-in">
                        Already have an account? Sign In
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Future features preview */}
        <div className="mt-12 grid grid-cols-1 gap-4 text-center sm:grid-cols-3 sm:gap-8">
          <div className="text-white/70">
            <div className="text-2xl font-bold">👥</div>
            <p className="text-sm">Team Info</p>
          </div>
          <div className="text-white/70">
            <div className="text-2xl font-bold">🔗</div>
            <p className="text-sm">Auto-Join</p>
          </div>
          <div className="text-white/70">
            <div className="text-2xl font-bold">⚡</div>
            <p className="text-sm">Quick Setup</p>
          </div>
        </div>

        {/* Development note */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center gap-2 rounded-full bg-white/10 px-4 py-2 text-white/60 backdrop-blur-sm">
            <div className="text-lg">⚙️</div>
            <span className="text-sm">
              Development mode - Invitation system coming soon
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
