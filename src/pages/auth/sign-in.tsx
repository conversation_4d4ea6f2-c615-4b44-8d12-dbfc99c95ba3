import { Link } from "react-router";

import { LoginForm } from "@/components/authentication/login-form";
import Logo from "@/components/brand/Logo";

export default function SignIn() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Beautiful gradient background */}
      <div className="from-primary via-primary/90 to-primary/70 absolute inset-0 bg-gradient-to-br" />
      <div className="absolute inset-0 bg-gradient-to-bl from-blue-500/20 via-transparent to-purple-500/15" />

      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 h-96 w-96 animate-pulse rounded-full bg-white/10 blur-3xl" />
        <div
          className="absolute right-1/4 bottom-1/4 h-64 w-64 animate-pulse rounded-full bg-white/5 blur-2xl"
          style={{ animationDelay: "1s" }}
        />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4">
        {/* Logo branding */}
        <div className="mb-8">
          <Link to="/" className="inline-block">
            <Logo />
          </Link>
        </div>

        {/* Welcome message */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-white drop-shadow-sm sm:text-4xl">
            Welcome back
          </h1>
          <p className="mt-2 text-lg text-white/80 drop-shadow-sm">
            Sign in to your QuikSkope account
          </p>
        </div>

        {/* Login form container */}
        <div className="w-full max-w-md">
          <div className="glass-effect rounded-xl p-1">
            <div className="bg-background/95 rounded-lg backdrop-blur-sm">
              <LoginForm className="p-6" />
            </div>
          </div>
        </div>

        {/* Footer links */}
        <div className="mt-8 text-center">
          <p className="text-sm text-white/60">
            New to QuikSkope?{" "}
            <Link
              to="/auth/sign-up"
              className="font-medium text-white underline underline-offset-4 transition-colors hover:text-white/80"
            >
              Create an account
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
