# Authentication Pages

Beautiful authentication pages for QuikSkope with stunning gradient backgrounds and glass-effect styling.

## Features

### Design Elements

- **Gradient Backgrounds**: Each page features unique gradient combinations using primary brand colors
- **Animated Background**: Subtle floating elements with pulse animations for visual appeal
- **Glass Effect**: Forms are contained in glass-effect containers with backdrop blur
- **Brand Integration**: QuikSkope logo prominently displayed with proper branding
- **Dark Mode Compatible**: All pages work seamlessly in both light and dark themes

### Pages

#### Sign In (`/auth/sign-in`)

- Welcome back messaging
- Clean login form with email and password
- Links to sign up and forgot password
- Blue-purple gradient background theme

#### Sign Up (`/auth/sign-up`)

- "Join QuikSkope" messaging
- Comprehensive registration form
- Feature highlights with icons (Lightning Fast, Secure & Reliable, Mobile Ready)
- Emerald-blue gradient background theme
- Links to sign in

#### Forgot Password (`/auth/forgot-password`)

- "Reset Password" messaging
- Email input for password reset
- Help section with support contact
- Violet-indigo gradient background theme
- Links to both sign in and sign up

#### Sign Up Success (`/auth/sign-up-success`)

- "Welcome to QuikSkope!" celebration messaging
- Email verification instructions
- Next steps with progress indicators (📧 Check Email, ✅ Verify Account, 🚀 Start Tracking)
- Green-emerald gradient background theme
- Navigation to sign in and home

#### Update Password (`/auth/update-password`)

- "Set New Password" messaging for email reset flow
- Secure password input form
- Security assurance messaging
- Teal-cyan gradient background theme
- Link back to sign in

#### Invitation (`/auth/invitation/:id`)

- **🚧 PLACEHOLDER - Future Implementation**
- "You're Invited!" messaging for team invitations
- Deep link support with invitation ID parameter
- Purple-pink gradient background theme
- Development mode with invitation ID display
- Placeholder buttons for sign up and sign in
- **Comments included for future database integration**

## Implementation

### Layout Structure

```
Full-screen container
├── Gradient background layers
├── Animated background elements
├── Main content (centered)
│   ├── Logo branding
│   ├── Page title and description
│   ├── Glass-effect form container
│   │   └── Form component (LoginForm/SignUpForm/ForgotPasswordForm)
│   ├── Footer navigation links
│   └── Additional page-specific content
```

### Styling Approach

- Uses semantic CSS variables for dark mode compatibility
- Glass-effect utility class for consistent styling
- Responsive design with mobile-first approach
- Smooth transitions and hover effects
- Drop shadows and backdrop blur for depth

### Form Integration

Each page wraps the corresponding form component from `@/components/authentication/`:

- `LoginForm` - Email/password with validation and error handling
- `SignUpForm` - Registration with password confirmation
- `ForgotPasswordForm` - Password reset with success states
- `UpdatePasswordForm` - New password entry for reset completion
- Success page uses Card components for confirmation messaging
- **Invitation page** - Currently placeholder with Card components (no form integration yet)

## Usage

```typescript
import { SignIn, SignUp, ForgotPassword, SignUpSuccess, UpdatePassword, Invitation } from "@/pages/auth";

// Use in routes
<Route path="/auth/sign-in" element={<SignIn />} />
<Route path="/auth/sign-up" element={<SignUp />} />
<Route path="/auth/forgot-password" element={<ForgotPassword />} />
<Route path="/auth/sign-up-success" element={<SignUpSuccess />} />
<Route path="/auth/update-password" element={<UpdatePassword />} />
<Route path="/auth/invitation/:id" element={<Invitation />} />
```

## Authentication Flow

1. **Sign Up Flow**: `/auth/sign-up` → `/auth/sign-up-success` → Email verification → `/auth/sign-in`
2. **Sign In Flow**: `/auth/sign-in` → Authenticated app
3. **Password Reset Flow**: `/auth/forgot-password` → Email → `/auth/update-password` → `/auth/sign-in`
4. **Invitation Flow** _(Future)_: `/auth/invitation/:id` → Database lookup → Pre-populated `/auth/sign-up` → Auto-join team

## Future Invitation System

The invitation page (`/auth/invitation/:id`) is currently a placeholder with comprehensive implementation comments. Future features will include:

### Database Integration

- Fetch invitation data using the ID parameter
- Validate invitation status (pending, expired, used)
- Extract organization/team information
- Handle different invitation types (team member, admin, driver)

### User Experience

- Pre-populate sign-up form with invitation details
- Automatically accept invitation after successful registration
- Redirect to appropriate dashboard based on user role
- Handle edge cases (expired, already used, invalid invitations)

### Example URL

```
https://yourapp.com/auth/invitation/inv_abc123xyz
```

### Implementation TODOs

See detailed comments in `src/pages/auth/invitation.tsx` for complete implementation roadmap.

## Storybook

Each page includes comprehensive Storybook stories:

- Light and dark mode variants
- Full documentation with descriptions
- Interactive examples

View in Storybook under `Auth/Pages` category.

## Brand Consistency

All pages maintain brand consistency with:

- Primary color gradients
- QuikSkope logo and typography
- Consistent spacing and typography scales
- Professional yet approachable messaging
