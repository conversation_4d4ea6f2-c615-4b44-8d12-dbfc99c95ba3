import { Link } from "react-router";

import Logo from "@/components/brand/Logo";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function SignUpSuccess() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Beautiful gradient background */}
      <div className="from-primary via-primary/90 to-primary/70 absolute inset-0 bg-gradient-to-br" />
      <div className="absolute inset-0 bg-gradient-to-bl from-green-500/20 via-transparent to-emerald-500/15" />

      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-1/3 left-1/3 h-80 w-80 animate-pulse rounded-full bg-white/10 blur-3xl"
          style={{ animationDelay: "0.5s" }}
        />
        <div
          className="absolute right-1/3 bottom-1/3 h-72 w-72 animate-pulse rounded-full bg-white/5 blur-2xl"
          style={{ animationDelay: "1.5s" }}
        />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4">
        {/* Logo branding */}
        <div className="mb-8">
          <Link to="/" className="inline-block">
            <Logo />
          </Link>
        </div>

        {/* Success message */}
        <div className="mb-8 text-center">
          <div className="mb-4 text-6xl">🎉</div>
          <h1 className="text-3xl font-bold text-white drop-shadow-sm sm:text-4xl">
            Welcome to QuikSkope!
          </h1>
          <p className="mt-2 text-lg text-white/80 drop-shadow-sm">
            Your account has been created successfully
          </p>
        </div>

        {/* Success card container */}
        <div className="w-full max-w-md">
          <div className="glass-effect rounded-xl p-1">
            <div className="bg-background/95 rounded-lg backdrop-blur-sm">
              <Card className="border-0 bg-transparent shadow-none">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">Check Your Email</CardTitle>
                  <CardDescription>
                    We've sent you a confirmation email to verify your account
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-muted-foreground text-center text-sm">
                    <p>
                      Please check your inbox and click the confirmation link to
                      activate your account.
                    </p>
                    <p className="mt-2">
                      Don't see the email? Check your spam folder.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <Button asChild className="w-full">
                      <Link to="/auth/sign-in">Continue to Sign In</Link>
                    </Button>

                    <Button variant="outline" asChild className="w-full">
                      <Link to="/">Back to Home</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Next steps */}
        <div className="mt-12 grid grid-cols-1 gap-4 text-center sm:grid-cols-3 sm:gap-8">
          <div className="text-white/70">
            <div className="text-2xl font-bold">📧</div>
            <p className="text-sm">Check Email</p>
          </div>
          <div className="text-white/70">
            <div className="text-2xl font-bold">✅</div>
            <p className="text-sm">Verify Account</p>
          </div>
          <div className="text-white/70">
            <div className="text-2xl font-bold">🚀</div>
            <p className="text-sm">Start Tracking</p>
          </div>
        </div>
      </div>
    </div>
  );
}
