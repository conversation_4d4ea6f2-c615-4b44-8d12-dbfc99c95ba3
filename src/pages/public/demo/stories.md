# Demo Storybook Organization

This document outlines the comprehensive Storybook organization for the demo section, which showcases a complete logistics management system.

## Structure Overview

All demo-related stories are now organized under the **"Demo"** top-level category with the following sub-categories:

### 📊 Demo/Overview

High-level views of the complete demo system with different tab configurations:

- **DashboardView**: Complete dashboard with analytics and tracking
- **LogisticsView**: Load management and operational planning
- **DocumentsView**: Document management system interface
- **VerificationsView**: Driver and vehicle verification workflows

### 🎛️ Demo/Layout

Layout component stories showing the tabbed navigation system:

- **Default**: Basic layout structure
- **WithDashboardContent**: Layout with dashboard content
- **DocumentsTab**: Document management interface
- **VerificationsTab**: Verification system interface
- **LogisticsTab**: Logistics operations interface
- **AllTabs**: Comprehensive view of all tabs
- **EmptyState**: Layout with no content

### 📈 Demo/Dashboard

Main dashboard page stories:

- **Default**: Standard dashboard view with all components

### 🧩 Demo/Components

Individual dashboard component stories for detailed design system documentation:

#### Analytics & Metrics

- **AnalyticsSection**: Performance trends, efficiency metrics, chat widget
  - Default, LowPerformance, HighPerformance, BusyChat variants

#### Shipment Management

- **ShipmentDetailsSection**: Driver info, parcel details, pricing
  - Default, InTransit, Loading, HighValue variants
- **ShipmentMapSection**: Real Mapbox integration with route visualization
  - 9 scenarios including various routes, optimizations, and distances
- **TrackingMapSection**: Route tracking with gradient visualizations
  - Default, ShortRoute, LongRoute, MultipleStops variants

#### Operations

- **LoadSection**: Load management operations
  - Default scenario for operational workflows
- **TruckCapacitySection**: Vehicle capacity and status visualization
  - Various capacity levels (0-98%), different statuses
- **AlertsSection**: Notifications and alerts management
  - Single/multiple alerts, different severities, empty states

### 📄 Demo/Pages

Complete page-level stories for different sections of the demo:

#### Document Management

- **Documents**: Main documents list and management
- **DocumentDetails**: Individual document viewing
- **DocumentCreate**: Document creation workflow
- **DocumentScan**: Document scanning interface

#### Verification System

- **VerificationsList**: List of all verifications
- **VerificationDetails**: Individual verification details
- **VerificationPage**: Main verification interface

#### Operations

- **Logistics**: Load management and planning interface
  - Default, WithActiveLoads, EmptyState variants
- **ShipmentDetails**: Individual shipment tracking pages
  - Default, InTransit, HighValueLoad, LongHaulRoute variants

## Key Features

### 🎨 Design System Integration

- All components use semantic CSS variables for dark mode compatibility
- Consistent prop patterns and TypeScript interfaces
- Comprehensive error handling and loading states

### 🗺️ Mapbox Integration

- Real Mapbox GL JS integration in ShipmentMapSection
- Completely disabled interactions with transparent overlay
- Route visualization with color-coded waypoints
- Auto-fitting bounds and optimization indicators

### 🔄 Route-Aware Navigation

- Tabs automatically update based on current route
- Navigation between different demo sections
- Proper TypeScript typing for tab values

### 📱 Responsive Design

- Mobile-optimized layouts with icon-only tabs
- Responsive component breakpoints
- Flexible grid systems for different screen sizes

## Usage Guidelines

### For Developers

1. Use component stories for isolated development and testing
2. Reference page stories for complete user flow testing
3. Layout stories demonstrate navigation and tab behavior
4. Overview stories show complete system integration

### For Designers

1. Component stories provide detailed design specifications
2. Multiple variants show different states and configurations
3. Page stories demonstrate complete user journeys
4. Layout stories show navigation and information architecture

### For Product Teams

1. Overview stories demonstrate complete feature sets
2. Page stories show end-to-end workflows
3. Component variants illustrate different business scenarios
4. Stories include comprehensive documentation and descriptions

## Technical Implementation

### Story Configuration

- All stories use proper router decorators for navigation testing
- Fullscreen layout for page-level stories
- Padded layout for component stories
- Comprehensive parameter configuration for routing

### Documentation

- Each story includes detailed descriptions
- Business context provided for different variants
- Technical specifications for component usage
- Integration guidelines for development teams

This organization provides a comprehensive, navigable, and well-documented showcase of the entire demo logistics management system while maintaining clean separation of concerns between layout, components, and pages.
