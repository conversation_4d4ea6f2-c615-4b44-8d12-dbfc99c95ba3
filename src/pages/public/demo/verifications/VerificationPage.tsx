import { useEffect, useState } from "react";
import {
  ArrowLeft,
  Camera,
  Check,
  MapPin,
  RotateCcw,
  SendIcon,
  Truck,
  Upload,
} from "lucide-react";
import { Link, useParams } from "react-router";

import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { useVerificationsStore } from "@/lib/store/useVerificationsStore";

export default function VerificationPage() {
  const { id } = useParams<{ id: string }>();
  const { getVerification } = useVerificationsStore();
  const [verification, setVerification] = useState(
    id ? getVerification(id) : undefined,
  );
  const { toast } = useToast();

  // Process state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  // Capture data
  const [locationData, setLocationData] = useState<{
    captured: boolean;
    latitude?: number;
    longitude?: number;
    timestamp?: string;
  }>({
    captured: false,
  });

  const [imageData, setImageData] = useState<{
    captured: boolean;
    previewUrl?: string;
    file?: File;
    metadata?: {
      name: string;
      type: string;
      size: string;
      dimensions: string;
      modified: string;
    };
  }>({
    captured: false,
  });

  const [shippingData, setShippingData] = useState<{
    captured: boolean;
    manifestNumber?: string;
    mcId?: string;
    usdotId?: string;
    licensePlate?: string;
    licenseState?: string;
  }>({
    captured: false,
  });

  const [verificationComplete, setVerificationComplete] = useState(false);
  const [verificationResults, setVerificationResults] = useState<{
    success: boolean;
    confidence?: number;
    info?: string[];
    warnings?: string[];
    logs?: string[];
  }>({
    success: false,
  });

  useEffect(() => {
    if (id) {
      const data = getVerification(id);
      setVerification(data);

      // For demo purposes, pre-fill some shipping data from the verification
      if (data?.vehicle) {
        setShippingData({
          captured: false,
          mcId: data.vehicle.mc_number,
          usdotId: data.vehicle.us_dot,
          licensePlate: data.vehicle.license_plate,
        });
      }
    }
  }, [id, getVerification]);

  const captureLocation = () => {
    if (navigator.geolocation) {
      toast({
        title: "Accessing location",
        description: "Please allow location access when prompted",
      });

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const timestamp = new Date().toLocaleString();
          setLocationData({
            captured: true,
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            timestamp,
          });
          toast({
            title: "Location captured",
            description: "Your current location has been recorded",
          });
        },
        (error) => {
          console.error("Error getting location:", error);
          toast({
            variant: "destructive",
            title: "Location error",
            description: `Failed to get location: ${error.message}`,
          });
        },
      );
    } else {
      toast({
        variant: "destructive",
        title: "Not supported",
        description: "Geolocation is not supported by your browser",
      });
    }
  };

  const resetLocation = () => {
    setLocationData({
      captured: false,
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target?.result) {
          const now = new Date().toLocaleString();

          setImageData({
            captured: true,
            previewUrl: event.target.result.toString(),
            file,
            metadata: {
              name: file.name,
              type: file.type,
              size: `${(file.size / 1024).toFixed(2)} KB`,
              dimensions: "1588x1544", // Mocked for demo
              modified: now,
            },
          });
        }
      };

      reader.readAsDataURL(file);
    }
  };

  const resetImage = () => {
    setImageData({
      captured: false,
    });
  };

  const captureShippingInfo = () => {
    // For demo, we'll simulate capturing shipping info from the verification or generate random data
    const manifestNumber = `KHJAF-${Math.floor(100 + Math.random() * 900)}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`;
    const mcId =
      shippingData.mcId || `${Math.floor(100000 + Math.random() * 900000)}`;
    const usdotId =
      shippingData.usdotId ||
      `${Math.floor(10000000 + Math.random() * 90000000)}`;
    const licensePlate =
      shippingData.licensePlate ||
      `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(1000 + Math.random() * 9000)}`;
    const states = ["NY", "CA", "TX", "FL", "IL", "PA"];
    const licenseState = states[Math.floor(Math.random() * states.length)];

    setShippingData({
      captured: true,
      manifestNumber,
      mcId,
      usdotId,
      licensePlate,
      licenseState,
    });
  };

  const resetShippingInfo = () => {
    setShippingData({
      captured: false,
    });
  };

  const verifyData = () => {
    // Simulate verification process
    toast({
      title: "Processing verification",
      description: "Please wait while we verify your data",
    });

    // Simulate API call delay
    setTimeout(() => {
      setVerificationComplete(true);

      // Mock verification results
      setVerificationResults({
        success: true,
        confidence: 98,
        info: [
          "Image is not a picture of another picture 📸",
          "Image is of a truck 🚚",
        ],
        warnings: [
          "Image metadata does not have GPS data",
          "Image metadata does not have a creation time",
          "MC ID does not match manifest",
          "US DOT ID does not match manifest",
          "License plate does not match manifest",
          "License state does not match manifest",
        ],
        logs: [
          'event "image processing" took 0.013 seconds',
          'event "image metadata reading" took 0 seconds',
          'event "ai scan" took 5.866 seconds',
          'event "ai digest" took 0 seconds',
        ],
      });

      setCurrentStep(4);

      toast({
        title: "Verification complete",
        description: "Your verification has been processed",
      });
    }, 3000);
  };

  const renderStepIndicator = () => {
    return (
      <div className="mb-8">
        <h1 className="mb-2 text-2xl font-bold">Scan Process</h1>
        <div className="relative mb-8 flex items-center justify-between">
          {/* Progress line */}
          <div className="absolute top-1/2 right-0 left-0 z-0 h-0.5 -translate-y-1/2 bg-gray-200"></div>

          {/* Step circles */}
          {[1, 2, 3, 4].map((step) => (
            <div
              key={step}
              className={`z-10 flex h-10 w-10 items-center justify-center rounded-full ${
                step < currentStep
                  ? "bg-green-500 text-white"
                  : step === currentStep
                    ? "bg-green-500 text-white"
                    : "bg-gray-300 text-gray-600"
              }`}
            >
              {step < currentStep ? (
                <Check className="h-5 w-5" />
              ) : (
                <span>{step}</span>
              )}
            </div>
          ))}
        </div>

        {/* Step labels */}
        <div className="flex justify-between text-sm">
          <div className="w-1/4 text-center">
            <p className="font-medium">Capture Location</p>
          </div>
          <div className="w-1/4 text-center">
            <p className="font-medium">Capture Image</p>
          </div>
          <div className="w-1/4 text-center">
            <p className="font-medium">Shipping Info</p>
          </div>
          <div className="w-1/4 text-center">
            <p className="font-medium">Verify Data</p>
          </div>
        </div>
      </div>
    );
  };

  const renderLocationStep = () => {
    return (
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Location Captured</CardTitle>
          {locationData.captured && (
            <Button variant="outline" size="icon" onClick={resetLocation}>
              <RotateCcw className="h-4 w-4" />
              <span className="sr-only">Reset</span>
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {locationData.captured ? (
            <div className="space-y-2">
              <div className="flex items-center text-green-600">
                <Check className="mr-2 h-5 w-5" />
                <span className="text-lg">Successfully captured</span>
              </div>
              <div className="mt-4 space-y-1">
                <p className="text-gray-700">
                  Latitude: {locationData.latitude?.toFixed(6)}
                </p>
                <p className="text-gray-700">
                  Longitude: {locationData.longitude?.toFixed(6)}
                </p>
                <p className="text-gray-700">
                  Timestamp: {locationData.timestamp}
                </p>
              </div>
            </div>
          ) : (
            <div className="py-6 text-center">
              <MapPin className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <p className="mb-4 text-gray-500">
                Tap the button below to capture your current location
              </p>
              <Button onClick={captureLocation}>
                <MapPin className="mr-2 h-4 w-4" />
                Capture Location
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="justify-end">
          {locationData.captured && (
            <Button onClick={() => setCurrentStep(2)}>
              Next Step
              <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  };

  const renderImageStep = () => {
    return (
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Image Captured</CardTitle>
          {imageData.captured && (
            <Button variant="outline" size="icon" onClick={resetImage}>
              <RotateCcw className="h-4 w-4" />
              <span className="sr-only">Reset</span>
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {imageData.captured ? (
            <div className="space-y-4">
              <div className="flex items-center text-green-600">
                <Check className="mr-2 h-5 w-5" />
                <span className="text-lg">Successfully captured</span>
              </div>

              {/* Image preview */}
              <div className="mt-4 overflow-hidden rounded-md border border-gray-200">
                <img
                  src={imageData.previewUrl}
                  alt="Captured vehicle"
                  className="h-auto max-h-64 w-full object-contain"
                />
              </div>

              {/* Image metadata */}
              <div className="mt-4 rounded-md bg-gray-50 p-4">
                <h3 className="mb-2 font-medium">Image Metadata:</h3>
                <div className="space-y-1 font-mono text-sm">
                  <p>File name: {imageData.metadata?.name}</p>
                  <p>File type: {imageData.metadata?.type}</p>
                  <p>File size: {imageData.metadata?.size}</p>
                  <p>Dimensions: {imageData.metadata?.dimensions}</p>
                  <p>Last modified: {imageData.metadata?.modified}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="py-6 text-center">
              <Camera className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <p className="mb-4 text-gray-500">
                Tap the button below to capture or upload an image
              </p>

              <div className="flex justify-center gap-4">
                <Button variant="outline" asChild>
                  <label htmlFor="upload-image" className="cursor-pointer">
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Image
                    <input
                      type="file"
                      id="upload-image"
                      className="hidden"
                      accept="image/*"
                      onChange={handleFileChange}
                    />
                  </label>
                </Button>

                <Button variant="outline" asChild>
                  <label htmlFor="capture-image" className="cursor-pointer">
                    <Camera className="mr-2 h-4 w-4" />
                    Capture Photo
                    <input
                      type="file"
                      id="capture-image"
                      className="hidden"
                      accept="image/*"
                      capture="environment"
                      onChange={handleFileChange}
                    />
                  </label>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="justify-between">
          <Button variant="outline" onClick={() => setCurrentStep(1)}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          {imageData.captured && (
            <Button onClick={() => setCurrentStep(3)}>
              Next Step
              <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  };

  const renderShippingStep = () => {
    return (
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Shipping Information</CardTitle>
          {shippingData.captured && (
            <Button variant="outline" size="icon" onClick={resetShippingInfo}>
              <RotateCcw className="h-4 w-4" />
              <span className="sr-only">Reset</span>
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {shippingData.captured ? (
            <div className="space-y-4">
              <div className="flex items-center text-green-600">
                <Check className="mr-2 h-5 w-5" />
                <span className="text-lg">Successfully captured</span>
              </div>

              <div className="mt-4 space-y-3">
                <p className="text-gray-700">
                  Manifest Number: {shippingData.manifestNumber}
                </p>
                <p className="text-gray-700">MC ID: {shippingData.mcId}</p>
                <p className="text-gray-700">
                  USDOT ID: {shippingData.usdotId}
                </p>
                <p className="text-gray-700">
                  License Plate: {shippingData.licensePlate}
                </p>
                <p className="text-gray-700">
                  License State: {shippingData.licenseState}
                </p>
              </div>
            </div>
          ) : (
            <div className="py-6 text-center">
              <Truck className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <p className="mb-4 text-gray-500">
                Tap the button below to scan or enter shipping information
              </p>
              <Button onClick={captureShippingInfo}>
                <Truck className="mr-2 h-4 w-4" />
                Capture Shipping Info
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="justify-between">
          <Button variant="outline" onClick={() => setCurrentStep(2)}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          {shippingData.captured && (
            <Button onClick={() => setCurrentStep(4)}>
              Next Step
              <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  };

  const renderVerifyStep = () => {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Verify Data</CardTitle>
        </CardHeader>
        <CardContent>
          {verificationComplete ? (
            <div className="space-y-6">
              <div>
                <h2 className="mb-4 text-xl font-bold">Scan Results</h2>
                <div className="mb-4 flex items-center text-green-600">
                  <Check className="mr-2 h-5 w-5" />
                  <span className="text-lg">Verification Successful</span>
                </div>

                <h3 className="mb-2 text-lg font-semibold">
                  Additional Information:
                </h3>
                <p className="mb-4">
                  Scan successful - confidence in results{" "}
                  {verificationResults.confidence}%
                </p>

                <h3 className="mb-2 text-lg font-semibold text-green-600">
                  Info:
                </h3>
                <ul className="mb-4 space-y-2">
                  {verificationResults.info?.map((info, index) => (
                    <li key={index} className="flex items-center text-gray-700">
                      <Check className="mr-2 h-5 w-5 text-green-500" />
                      {info}
                    </li>
                  ))}
                </ul>

                <h3 className="mb-2 text-lg font-semibold text-amber-600">
                  Warnings:
                </h3>
                <ul className="mb-4 space-y-2">
                  {verificationResults.warnings?.map((warning, index) => (
                    <li key={index} className="flex items-start text-gray-700">
                      <span className="mr-2 text-amber-500">⚠️</span>
                      {warning}
                    </li>
                  ))}
                </ul>

                <h3 className="mb-2 text-lg font-semibold">Logs:</h3>
                <ul className="space-y-1 font-mono text-sm text-gray-600">
                  {verificationResults.logs?.map((log, index) => (
                    <li key={index}>{log}</li>
                  ))}
                </ul>
              </div>
            </div>
          ) : (
            <div className="py-6 text-center">
              <SendIcon className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <p className="mb-6 text-gray-500">
                Ready to submit your verification data? Click the button below
                to process all information.
              </p>
              <Button
                className="w-full py-6 text-lg"
                onClick={verifyData}
                disabled={
                  !locationData.captured ||
                  !imageData.captured ||
                  !shippingData.captured
                }
              >
                <SendIcon className="mr-2 h-5 w-5" />
                Verify Data
              </Button>

              {(!locationData.captured ||
                !imageData.captured ||
                !shippingData.captured) && (
                <p className="mt-4 text-sm text-amber-600">
                  Please complete all previous steps before verification
                </p>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="justify-between">
          {!verificationComplete && (
            <>
              <Button variant="outline" onClick={() => setCurrentStep(3)}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
            </>
          )}

          {verificationComplete && (
            <Button asChild className="w-full">
              <Link to={`/verifications/${id}`}>
                Return to Verification Details
              </Link>
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  };

  if (!verification) {
    return (
      <div className="container py-8">
        <div className="mb-6 flex items-center">
          <Link
            to="/verifications"
            className="mr-4 flex items-center text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="mr-1 h-4 w-4" />
            Back to list
          </Link>
          <h1 className="text-2xl font-bold">Verification Not Found</h1>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <p>The verification you're looking for could not be found.</p>
            <Button asChild className="mt-4">
              <Link to="/verifications">Return to Verifications</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center">
        <Link
          to={`/verifications/${id}`}
          className="mr-4 flex items-center text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back to details
        </Link>
        <h1 className="text-2xl font-bold">Verification Process</h1>
      </div>

      {renderStepIndicator()}

      {currentStep === 1 && renderLocationStep()}
      {currentStep === 2 && renderImageStep()}
      {currentStep === 3 && renderShippingStep()}
      {currentStep === 4 && renderVerifyStep()}
    </div>
  );
}
