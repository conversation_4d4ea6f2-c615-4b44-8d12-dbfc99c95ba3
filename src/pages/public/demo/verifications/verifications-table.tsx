"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import {
  CheckCircle,
  Circle,
  ExternalLink,
  FileText,
  MoreHorizontal,
  Shield,
  Truck,
  User,
} from "lucide-react";
import { Link } from "react-router";

import TimeAgo from "@/components/shared/TimeAgo";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import Table from "@/components/tables/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

// Types based on the verification store structure
export type VerificationType = {
  id: string;
  driver?: {
    id: string;
    first_name: string;
    last_name: string;
    email?: string;
    avatar?: string;
  };
  vehicle?: {
    id: string;
    make: string;
    model: string;
    year: number;
    license_plate?: string;
  };
  document?: {
    id: string;
    url: string;
    type?: string;
  };
  created_at: string;
  verified_at?: string;
  pickup_number?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
};

export type VerificationData = {
  verifications: VerificationType[];
  total: number;
};

interface VerificationsTableProps extends PropsWithChildren {
  loading?: boolean;
  verifications?: VerificationType[];
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onDelete?: (id: string) => Promise<boolean>;
  onVerify?: (id: string) => Promise<boolean>;
}

const i18n = {
  en: {
    title: "Verifications",
    noVerifications: "No verifications found",
    headers: {
      id: "ID",
      driver: "Driver",
      vehicle: "Vehicle",
      status: "Status",
      document: "Document",
      created_at: "Created",
      verified_at: "Verified",
      pickup_number: "Pickup #",
      actions: "Actions",
    },
    actions: {
      search: "Search verifications...",
      tableSettings: "Table settings",
      tableActions: "Table actions",
      view: "View Details",
      verify: "Verify",
      delete: "Delete",
      moreActions: "More actions",
    },
    status: {
      verified: "Verified",
      pending: "Pending",
      expired: "Expired",
    },
  },
  links: {
    verifications: "/verifications/[id]",
    verify: "/verifications/[id]/verify",
  },
};

export const groupName = "verification";

const formatDriverName = (driver?: VerificationType["driver"]) => {
  if (!driver) return "Unknown Driver";
  return `${driver.first_name} ${driver.last_name}`;
};

const formatVehicle = (vehicle?: VerificationType["vehicle"]) => {
  if (!vehicle) return "No vehicle assigned";
  return `${vehicle.make} ${vehicle.model} (${vehicle.year})`;
};

const getVerificationStatus = (verification: VerificationType) => {
  if (verification.verified_at) return "verified";

  // Check if verification is expired (older than 24 hours)
  const createdAt = new Date(verification.created_at);
  const now = new Date();
  const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);

  if (hoursDiff > 24) return "expired";

  return "pending";
};

export default function VerificationsTable({
  loading = false,
  verifications = [],
  defaultPageSize = 10,
  defaultPageIndex = 0,
  onDelete,
  onVerify,
  children,
}: VerificationsTableProps) {
  const tableData = useMemo(
    () => ({
      items: verifications,
      total: verifications.length,
    }),
    [verifications],
  );

  const columns = useMemo(
    () =>
      [
        {
          id: "id",
          accessorKey: "id",
          header: ({ column }) => (
            <DataTableColumnHeader column={column} title={i18n.en.headers.id} />
          ),
          cell: ({ row }) => (
            <Link
              to={i18n.links.verifications.replace("[id]", row.original.id)}
              className="flex items-center gap-2 font-medium hover:underline"
            >
              <Shield className="text-primary h-4 w-4" />
              <span className="font-mono text-sm">
                #{row.getValue("id")?.toString().substring(0, 8)}
              </span>
            </Link>
          ),
          enableHiding: false,
        },
        {
          id: "driver",
          accessorKey: "driver",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.driver}
            />
          ),
          cell: ({ row }) => {
            const driver = row.original.driver;
            if (!driver) {
              return (
                <div className="text-muted-foreground flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span className="text-sm">Unknown Driver</span>
                </div>
              );
            }

            return (
              <div className="flex min-w-0 items-center gap-3">
                <Avatar className="h-8 w-8 flex-shrink-0">
                  <AvatarImage
                    src={driver.avatar}
                    alt={formatDriverName(driver)}
                  />
                  <AvatarFallback className="text-xs">
                    {driver.first_name?.[0]}
                    {driver.last_name?.[0]}
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium">
                    {formatDriverName(driver)}
                  </p>
                  {driver.email && (
                    <p className="text-muted-foreground truncate text-xs">
                      {driver.email}
                    </p>
                  )}
                </div>
              </div>
            );
          },
        },
        {
          id: "vehicle",
          accessorKey: "vehicle",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.vehicle}
            />
          ),
          cell: ({ row }) => {
            const vehicle = row.original.vehicle;
            return (
              <div className="flex items-center gap-2">
                <Truck className="text-muted-foreground h-4 w-4 flex-shrink-0" />
                <div className="min-w-0">
                  <p className="truncate text-sm">{formatVehicle(vehicle)}</p>
                  {vehicle?.license_plate && (
                    <p className="text-muted-foreground font-mono text-xs">
                      {vehicle.license_plate}
                    </p>
                  )}
                </div>
              </div>
            );
          },
        },
        {
          id: "status",
          accessorKey: "verified_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.status}
            />
          ),
          cell: ({ row }) => {
            const status = getVerificationStatus(row.original);

            return (
              <div className="flex items-center gap-2">
                {status === "verified" ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <Badge
                      variant="default"
                      className="bg-green-100 text-green-700 hover:bg-green-100"
                    >
                      Verified
                    </Badge>
                  </>
                ) : status === "expired" ? (
                  <>
                    <Circle className="h-4 w-4 text-red-600" />
                    <Badge variant="destructive">Expired</Badge>
                  </>
                ) : (
                  <>
                    <Circle className="h-4 w-4 text-amber-600" />
                    <Badge
                      variant="secondary"
                      className="bg-amber-100 text-amber-700 hover:bg-amber-100"
                    >
                      Pending
                    </Badge>
                  </>
                )}
              </div>
            );
          },
        },
        {
          id: "document",
          accessorKey: "document",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.document}
            />
          ),
          cell: ({ row }) => {
            const document = row.original.document;
            if (!document) {
              return (
                <span className="text-muted-foreground text-sm">
                  No document
                </span>
              );
            }

            return (
              <div className="flex items-center gap-2">
                <FileText className="text-primary h-4 w-4" />
                <a
                  href={document.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary flex items-center gap-1 text-sm hover:underline"
                  onClick={(e) => e.stopPropagation()}
                >
                  View Document
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            );
          },
        },
        {
          id: "pickup_number",
          accessorKey: "pickup_number",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.pickup_number}
            />
          ),
          cell: ({ row }) => {
            const pickupNumber = row.original.pickup_number;
            if (!pickupNumber) {
              return <span className="text-muted-foreground text-sm">—</span>;
            }

            return (
              <span className="bg-muted rounded px-2 py-1 font-mono text-sm">
                {pickupNumber}
              </span>
            );
          },
        },
        {
          id: "created_at",
          accessorKey: "created_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.created_at}
            />
          ),
          cell: ({ row }) => (
            <TimeAgo
              loading={loading}
              date={new Date(row.getValue("created_at"))}
              className="text-muted-foreground text-sm"
            />
          ),
        },
        {
          id: "verified_at",
          accessorKey: "verified_at",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.verified_at}
            />
          ),
          cell: ({ row }) => {
            const verifiedAt = row.original.verified_at;
            if (!verifiedAt) {
              return <span className="text-muted-foreground text-sm">—</span>;
            }

            return (
              <TimeAgo
                loading={loading}
                date={new Date(verifiedAt)}
                className="text-muted-foreground text-sm"
              />
            );
          },
        },
        {
          id: "actions",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.actions}
            />
          ),
          cell: ({ row }) => {
            const verification = row.original;
            const status = getVerificationStatus(verification);

            return (
              <div className="flex items-center gap-2">
                {status === "pending" && onVerify && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onVerify(verification.id)}
                    className="h-8 px-2"
                  >
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Verify
                  </Button>
                )}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <span className="sr-only">
                        {i18n.en.actions.moreActions}
                      </span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link
                        to={i18n.links.verifications.replace(
                          "[id]",
                          verification.id,
                        )}
                      >
                        <User className="mr-2 h-4 w-4" />
                        {i18n.en.actions.view}
                      </Link>
                    </DropdownMenuItem>
                    {status === "pending" && (
                      <DropdownMenuItem asChild>
                        <Link
                          to={i18n.links.verify.replace(
                            "[id]",
                            verification.id,
                          )}
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          {i18n.en.actions.verify}
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    {onDelete && (
                      <DropdownMenuItem
                        onClick={() => onDelete(verification.id)}
                        className="text-destructive"
                      >
                        <Shield className="mr-2 h-4 w-4" />
                        {i18n.en.actions.delete}
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            );
          },
          enableHiding: false,
        },
      ] as ColumnDef<VerificationType, VerificationType[]>[],
    [loading, onDelete, onVerify],
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {i18n.en.title}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table
          loading={loading}
          data={tableData}
          columns={columns}
          config={{
            groupName,
            i18n: {
              noData: i18n.en.noVerifications,
              selection: "Select verifications",
              actions: i18n.en.actions,
            },
          }}
          defaultPageSize={defaultPageSize}
          defaultPageIndex={defaultPageIndex}
        >
          {children}
        </Table>
      </CardContent>
    </Card>
  );
}
