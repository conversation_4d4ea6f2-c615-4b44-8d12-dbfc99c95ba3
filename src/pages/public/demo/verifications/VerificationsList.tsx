import { useEffect, useState } from "react";
import {
  Calendar,
  CheckCircle,
  Circle,
  FileCheck,
  Grid,
  Info,
  List,
  Plus,
  Search,
  Shield,
  Sparkles,
  Table2,
  TrendingUp,
  Truck,
  User,
} from "lucide-react";
import { <PERSON> } from "react-router";

import TimeAgo from "@/components/shared/TimeAgo";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { useVerificationsStore } from "@/lib/store/useVerificationsStore";
import { cn } from "@/lib/utils";
import VerificationsTable from "./verifications-table";

export default function VerificationsList() {
  const [searchQuery, setSearchQuery] = useState("");
  const { verifications, createDemoVerification } = useVerificationsStore();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");

  useEffect(() => {
    // Simulate network delay for realistic loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Simulate loading effect when creating a new verification
  const handleCreateNew = () => {
    setIsLoading(true);

    // Simulate network delay for realistic loading
    setTimeout(() => {
      const newVerification = createDemoVerification();
      setIsLoading(false);

      toast({
        title: "Verification created",
        description: `Verification ID: ${newVerification.id.slice(0, 8)}...`,
        duration: 3000,
      });
    }, 800);
  };

  // Filter verifications based on search query
  const filteredVerifications = verifications?.filter((verification) => {
    if (!searchQuery) return true;

    // Simple search implementation - can be expanded as needed
    const searchLower = searchQuery.toLowerCase();
    return (
      verification.id.toLowerCase().includes(searchLower) ||
      verification.driver?.first_name?.toLowerCase().includes(searchLower) ||
      verification.driver?.last_name?.toLowerCase().includes(searchLower)
    );
  });

  const toggleViewMode = () => {
    setViewMode((prev) => (prev === "cards" ? "table" : "cards"));
  };

  // Get verification stats for better UX
  const verificationStats = {
    total: verifications?.length || 0,
    verified: verifications?.filter((v) => v.verified_at).length || 0,
    pending: verifications?.filter((v) => !v.verified_at).length || 0,
  };

  if (loading) {
    return (
      <div className="container grid max-w-full grid-cols-1 gap-4 px-4 py-4 md:px-6 md:py-8">
        <div className="mb-4 flex flex-col justify-between gap-4 sm:mb-6 sm:flex-row sm:items-center">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-full sm:w-32" />
        </div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Skeleton key={i} className="h-48 sm:h-64" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container grid max-w-full grid-cols-1 gap-4 px-4 py-4 md:px-6 md:py-8">
      {/* Header */}
      <div className="mb-4 flex flex-col justify-between gap-4 sm:mb-6 sm:flex-row sm:items-start">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl font-bold sm:text-3xl">Verifications</h1>
          <p className="text-muted-foreground mt-1 text-sm sm:text-base">
            Manage driver identity and document verifications
          </p>
          {/* Quick Stats */}
          <div className="mt-3 flex items-center gap-4 text-xs sm:text-sm">
            <span className="flex items-center gap-1">
              <Shield className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="font-medium">
                {verificationStats.total}
              </span>{" "}
              Total
            </span>
            <span className="flex items-center gap-1 text-green-600">
              <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="font-medium">
                {verificationStats.verified}
              </span>{" "}
              Verified
            </span>
            <span className="flex items-center gap-1 text-amber-600">
              <Circle className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="font-medium">
                {verificationStats.pending}
              </span>{" "}
              Pending
            </span>
          </div>
        </div>
        <Button variant="outline" asChild className="flex-shrink-0">
          <Link to="/request-demo">
            <Info className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Learn More</span>
            <span className="sm:hidden">Info</span>
          </Link>
        </Button>
      </div>

      {/* About Card */}
      <Card className="mb-4 sm:mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-base sm:text-lg">
            Verification System
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm sm:text-base">
            Secure, authenticated pickup operations with driver identity
            verification and document validation. Generate pickup numbers for
            final authorization.
          </p>
          <div className="mt-4 flex flex-col gap-3 text-sm sm:flex-row sm:items-center sm:gap-6">
            <div className="flex items-center">
              <Shield className="text-primary mr-2 h-4 w-4 flex-shrink-0" />
              <span>Identity Verification</span>
            </div>
            <div className="flex items-center">
              <FileCheck className="text-primary mr-2 h-4 w-4 flex-shrink-0" />
              <span>Document Validation</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Verification Stats */}
      <section className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-3">
        {/* Verification Rate */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-muted-foreground text-sm font-medium">
              Verification Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline justify-between">
              <div>
                <div className="text-2xl font-bold">99.9%</div>
                <p className="text-muted-foreground text-xs">Last 30 days</p>
              </div>
              <div className="flex items-center text-sm text-green-600">
                <TrendingUp className="mr-1 h-4 w-4" />
                +2.1%
              </div>
            </div>
            <div className="bg-muted mt-4 h-2 overflow-hidden rounded-full">
              <div className="h-full w-[99.9%] rounded-full bg-gradient-to-r from-green-500 to-green-600" />
            </div>
          </CardContent>
        </Card>

        {/* Average Processing Time */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-muted-foreground text-sm font-medium">
              Avg. Processing Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline justify-between">
              <div>
                <div className="text-2xl font-bold">2.4m</div>
                <p className="text-muted-foreground text-xs">
                  Per verification
                </p>
              </div>
              <div className="flex items-center text-sm text-green-600">
                <TrendingUp className="mr-1 h-4 w-4" />
                -18s
              </div>
            </div>
            <div className="mt-4 grid h-8 grid-cols-7 gap-1">
              {[65, 72, 58, 81, 69, 77, 84].map((height, i) => (
                <div key={i} className="flex items-end rounded-sm bg-blue-200">
                  <div
                    className="w-full rounded-sm bg-blue-500 transition-all"
                    style={{ height: `${height}%` }}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Daily Volume */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-muted-foreground text-sm font-medium">
              Daily Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline justify-between">
              <div>
                <div className="text-2xl font-bold">147</div>
                <p className="text-muted-foreground text-xs">Today</p>
              </div>
              <div className="flex items-center text-sm text-blue-600">
                <TrendingUp className="mr-1 h-4 w-4" />
                +12
              </div>
            </div>
            <div className="mt-4 flex h-8 items-end justify-between gap-1">
              {[45, 52, 48, 61, 58, 67, 73, 69, 77, 82, 75, 89, 94, 87].map(
                (height, i) => (
                  <div
                    key={i}
                    className="flex-1 rounded-sm bg-purple-500 transition-all hover:bg-purple-600"
                    style={{ height: `${height}%` }}
                  />
                ),
              )}
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview" className="text-xs sm:text-sm">
            Overview
          </TabsTrigger>
          <TabsTrigger value="all" className="text-xs sm:text-sm">
            All Verifications
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 sm:space-y-6">
          {/* Controls Section */}
          <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
            <h2 className="text-lg font-semibold sm:text-xl">
              Recent Verifications
              {filteredVerifications?.length !== verifications?.length && (
                <span className="text-muted-foreground ml-2 text-sm font-normal">
                  ({filteredVerifications?.length} of {verifications?.length})
                </span>
              )}
            </h2>

            <div className="flex flex-col items-stretch gap-2 sm:flex-row sm:items-center">
              {/* Search Input */}
              <div className="relative flex-1 sm:flex-initial">
                <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                <Input
                  type="search"
                  placeholder="Search verifications..."
                  className="w-full pl-8 sm:w-64"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* View Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleViewMode}
                  title={`Switch to ${viewMode === "cards" ? "table" : "cards"} view`}
                  className="flex-shrink-0"
                >
                  {viewMode === "cards" ? (
                    <Table2 className="h-4 w-4" />
                  ) : (
                    <Grid className="h-4 w-4" />
                  )}
                </Button>

                <Button
                  onClick={handleCreateNew}
                  disabled={isLoading}
                  className="flex-shrink-0"
                >
                  {isLoading ? (
                    <>
                      <Sparkles className="mr-2 h-4 w-4 animate-pulse" />
                      <span className="hidden sm:inline">Creating...</span>
                      <span className="sm:hidden">...</span>
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      <span className="hidden sm:inline">New Verification</span>
                      <span className="sm:hidden">New</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Content Display */}
          {isLoading ? (
            viewMode === "cards" ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Card key={i} className="p-4">
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                      <Skeleton className="h-12 w-12 flex-shrink-0 rounded-full" />
                      <div className="min-w-0 flex-1 space-y-2">
                        <Skeleton className="h-4 w-full sm:w-48" />
                        <Skeleton className="h-4 w-full sm:w-32" />
                      </div>
                      <Skeleton className="h-9 w-full sm:w-24" />
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-4 flex-1" />
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          ) : filteredVerifications?.length ? (
            viewMode === "cards" ? (
              // Cards View - Show recent 5
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3">
                {filteredVerifications.slice(0, 5).map((verification) => (
                  <Link
                    to={`/verifications/${verification.id}`}
                    key={verification.id}
                    className="group no-underline"
                  >
                    <Card className="h-full transition-all group-hover:scale-[1.02] hover:shadow-md">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between gap-2">
                          <CardTitle className="truncate text-base sm:text-lg">
                            {verification.driver?.first_name}{" "}
                            {verification.driver?.last_name}
                          </CardTitle>
                          {verification.verified_at ? (
                            <div className="flex flex-shrink-0 items-center text-green-600">
                              <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5" />
                            </div>
                          ) : (
                            <div className="flex flex-shrink-0 items-center text-amber-500">
                              <Circle className="h-4 w-4 sm:h-5 sm:w-5" />
                            </div>
                          )}
                        </div>
                        <div className="text-muted-foreground flex items-center gap-2 text-xs sm:text-sm">
                          <Calendar className="h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                          <TimeAgo date={new Date(verification.created_at)} />
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <User className="text-muted-foreground h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                            <span className="truncate text-xs sm:text-sm">
                              {verification.driver?.email || "No email"}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Truck className="text-muted-foreground h-3 w-3 flex-shrink-0 sm:h-4 sm:w-4" />
                            <span className="truncate text-xs sm:text-sm">
                              {verification.vehicle ? (
                                <>
                                  {verification.vehicle.make}{" "}
                                  {verification.vehicle.model} (
                                  {verification.vehicle.year})
                                </>
                              ) : (
                                "No vehicle"
                              )}
                            </span>
                          </div>
                        </div>

                        {verification.document && (
                          <div className="aspect-video overflow-hidden rounded-md bg-gray-100">
                            <img
                              src={verification.document.url}
                              alt="Verification document"
                              className="h-full w-full object-cover transition-transform group-hover:scale-105"
                            />
                          </div>
                        )}

                        <div className="flex items-center justify-between">
                          <span
                            className={cn(
                              "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                              verification.verified_at
                                ? "bg-green-100 text-green-700"
                                : "bg-amber-100 text-amber-700",
                            )}
                          >
                            {verification.verified_at ? "Verified" : "Pending"}
                          </span>
                          <Link
                            to={`/verifications/${verification.id}/verify`}
                            className="text-primary text-xs hover:underline sm:text-sm"
                            onClick={(e) => e.stopPropagation()}
                          >
                            Verify →
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            ) : (
              // Table View
              <VerificationsTable
                verifications={filteredVerifications.slice(0, 10)}
                loading={false}
              />
            )
          ) : (
            // Empty State
            <div className="bg-muted/20 rounded-lg py-8 text-center sm:py-12">
              <Shield className="text-muted-foreground mx-auto h-8 w-8 sm:h-12 sm:w-12" />
              <h3 className="mt-4 text-base font-medium sm:text-lg">
                No verifications found
              </h3>
              <p className="text-muted-foreground mt-1 px-4 text-sm sm:text-base">
                {searchQuery
                  ? "Try adjusting your search terms"
                  : "Create your first verification to get started"}
              </p>
              {searchQuery && (
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                  className="mt-4"
                  size="sm"
                >
                  Clear Search
                </Button>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="all" className="space-y-4 sm:space-y-6">
          <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
            <h2 className="text-lg font-semibold sm:text-xl">
              All Verifications
            </h2>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                <Input
                  type="search"
                  placeholder="Search all verifications..."
                  className="w-full pl-8 sm:w-64"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button onClick={handleCreateNew} disabled={isLoading}>
                <Plus className="mr-2 h-4 w-4" />
                New
              </Button>
            </div>
          </div>

          <VerificationsTable
            verifications={filteredVerifications || []}
            loading={false}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
