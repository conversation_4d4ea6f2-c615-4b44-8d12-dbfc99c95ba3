import type { Meta, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import VerificationDetails from "./VerificationDetails";

const meta: Meta<typeof VerificationDetails> = {
  title: "Demo/Pages/VerificationDetails",
  component: VerificationDetails,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "demo-verification-123" },
      },
      routing: { path: "/demo/verifications/:id/details" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
