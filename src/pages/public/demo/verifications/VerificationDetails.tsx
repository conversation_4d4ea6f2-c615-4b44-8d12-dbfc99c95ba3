import { useEffect, useState } from "react";
import {
  ArrowLeft,
  Calendar,
  CheckCircle,
  FileText,
  MapPin,
  Shield,
  Truck,
  User,
} from "lucide-react";
import { Link, useParams } from "react-router";

import TimeAgo from "@/components/shared/TimeAgo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useVerificationsStore } from "@/lib/store/useVerificationsStore";
import { cn } from "@/lib/utils";

export default function VerificationDetails() {
  const { id } = useParams<{ id: string }>();
  const { getVerification } = useVerificationsStore();
  const [verification, setVerification] = useState(
    id ? getVerification(id) : undefined,
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      setLoading(true);
      // Simulate network delay for realistic demo
      const timer = setTimeout(() => {
        setVerification(getVerification(id));
        setLoading(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [id, getVerification]);

  if (loading) {
    return (
      <div className="container py-8">
        <div className="mb-6 flex items-center gap-2">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-6 w-64" />
        </div>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="space-y-6 lg:col-span-2">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
          <div>
            <Skeleton className="h-96 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (!verification) {
    return (
      <div className="container grid max-w-full grid-cols-1 gap-4 px-4 py-4 md:px-6 md:py-8">
        <div className="mb-6 flex items-center">
          <Button variant="outline" size="sm" asChild className="mr-4">
            <Link to="/verifications">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to List
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Verification Not Found</h1>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Shield className="text-muted-foreground mb-4 h-12 w-12" />
            <h2 className="mb-2 text-xl font-semibold">
              Verification Not Found
            </h2>
            <p className="text-muted-foreground mb-6 max-w-md text-center">
              The verification you're looking for could not be found or might
              have been deleted.
            </p>
            <Button asChild>
              <Link to="/verifications">Return to Verifications List</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center">
        <Button variant="outline" size="sm" asChild className="mr-4">
          <Link to="/verifications">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to List
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Verification Details</h1>
          <p className="text-muted-foreground">ID: {verification.id}</p>
        </div>
        <div className="ml-auto flex gap-2">
          <Button asChild>
            <Link
              to={`/verifications/${verification.id}/verify`}
              className="flex items-center"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Verify This Shipment
            </Link>
          </Button>

          {verification.verified_at ? (
            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
              Verified
            </span>
          ) : (
            <span className="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800">
              Pending
            </span>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main content - left side */}
        <div className="space-y-6 lg:col-span-2">
          {/* Driver Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Driver Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {verification.driver ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Name
                      </h3>
                      <p>
                        {verification.driver.first_name}{" "}
                        {verification.driver.last_name}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Email
                      </h3>
                      <p>{verification.driver.email}</p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Phone
                      </h3>
                      <p>
                        {verification.driver.phone_number || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Driver ID
                      </h3>
                      <p className="font-mono text-sm">
                        {verification.driver.id}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">
                  No driver information available
                </p>
              )}
            </CardContent>
          </Card>

          {/* Vehicle Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center">
                <Truck className="mr-2 h-5 w-5" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {verification.vehicle ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Make & Model
                      </h3>
                      <p>
                        {verification.vehicle.make} {verification.vehicle.model}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Year
                      </h3>
                      <p>{verification.vehicle.year}</p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        License Plate
                      </h3>
                      <p>{verification.vehicle.license_plate}</p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        US DOT Number
                      </h3>
                      <p>{verification.vehicle.us_dot || "Not provided"}</p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        MC Number
                      </h3>
                      <p>{verification.vehicle.mc_number || "Not provided"}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">
                  No vehicle information available
                </p>
              )}
            </CardContent>
          </Card>

          {/* Document Information */}
          {verification.document && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Document Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Document Name
                      </h3>
                      <p>{verification.document.name}</p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Type
                      </h3>
                      <p>{verification.document.content_type || "Unknown"}</p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Created
                      </h3>
                      <TimeAgo
                        date={new Date(verification.document.created_at)}
                      />
                    </div>
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Size
                      </h3>
                      <p>
                        {verification.document.size
                          ? `${Math.round(verification.document.size / 1024)} KB`
                          : "Unknown"}
                      </p>
                    </div>
                  </div>
                  <a
                    href={verification.document.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mt-4 block"
                  >
                    <img
                      src={verification.document.url}
                      alt={verification.document.name}
                      className="border-border h-auto max-w-full rounded-md border"
                    />
                  </a>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar - right side */}
        <div>
          <div className="space-y-6">
            {/* Verification Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Verification Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-muted-foreground text-sm font-medium">
                      Status
                    </h3>
                    <div
                      className={cn(
                        "mt-1 flex items-center text-sm font-medium",
                        verification.verified_at
                          ? "text-green-600"
                          : "text-amber-600",
                      )}
                    >
                      <div
                        className={cn(
                          "mr-2 h-2 w-2 rounded-full",
                          verification.verified_at
                            ? "bg-green-600"
                            : "bg-amber-600",
                        )}
                      />
                      {verification.verified_at
                        ? "Verified"
                        : "Pending Verification"}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-muted-foreground text-sm font-medium">
                      Created At
                    </h3>
                    <div className="mt-1 flex items-center">
                      <Calendar className="text-muted-foreground mr-2 h-4 w-4" />
                      <TimeAgo date={new Date(verification.created_at)} />
                    </div>
                  </div>

                  {verification.verified_at && (
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Verified At
                      </h3>
                      <div className="mt-1 flex items-center">
                        <Calendar className="text-muted-foreground mr-2 h-4 w-4" />
                        <TimeAgo date={new Date(verification.verified_at)} />
                      </div>
                    </div>
                  )}

                  {verification.verified_by && (
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Verified By
                      </h3>
                      <div className="mt-1 flex items-center">
                        <Shield className="text-muted-foreground mr-2 h-4 w-4" />
                        <span>{verification.verified_by}</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Location Information */}
            {verification.stop?.location && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center">
                    <MapPin className="mr-2 h-5 w-5" />
                    Location
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Address
                      </h3>
                      <p className="mt-1">
                        {verification.stop.location.formatted}
                      </p>
                    </div>
                    {verification.stop.location.latitude &&
                      verification.stop.location.longitude && (
                        <div>
                          <h3 className="text-muted-foreground text-sm font-medium">
                            Coordinates
                          </h3>
                          <p className="mt-1 font-mono text-sm">
                            {verification.stop.location.latitude.toFixed(6)},{" "}
                            {verification.stop.location.longitude.toFixed(6)}
                          </p>
                        </div>
                      )}
                    <div>
                      <h3 className="text-muted-foreground text-sm font-medium">
                        Stop Type
                      </h3>
                      <p className="mt-1 capitalize">
                        {verification.stop.type}
                      </p>
                    </div>
                    {verification.stop.label && (
                      <div>
                        <h3 className="text-muted-foreground text-sm font-medium">
                          Stop Label
                        </h3>
                        <p className="mt-1">{verification.stop.label}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Additional Notes */}
            {verification.notes && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{verification.notes}</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
