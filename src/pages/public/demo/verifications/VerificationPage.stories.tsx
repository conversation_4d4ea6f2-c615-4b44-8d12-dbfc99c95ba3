import type { Meta, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import VerificationPage from "./VerificationPage";

const meta: Meta<typeof VerificationPage> = {
  title: "Demo/Pages/VerificationPage",
  component: VerificationPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "demo-verification-123" },
      },
      routing: { path: "/demo/verifications/:id" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
