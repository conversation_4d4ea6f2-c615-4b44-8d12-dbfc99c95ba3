import { useState } from "react";

import type { ShipmentData } from "../shipments/list/table";

import LocationMap from "@/components/maps/LocationMap";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { LoadSection } from "../shipments/components";
import ListShipments from "../shipments/list/table";
import { LogisticsStats } from "./LogisticsStats";

// Mock data for demo shipments
const mockShipmentData: ShipmentData = {
  shipments: [
    {
      id: "shipment-001",
      status: "in_progress",
      mode: "closed",
      source: "system",
      weight: 8453,
      valuation: 125000,
      created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      started_at: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
      driver: {
        id: "driver-001",
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        avatar: "/api/placeholder/40/40",
      },
      organization: {
        id: "org-001",
        name: "QuikSkope Logistics",
        avatar_url: "/api/placeholder/32/32",
      },
      stops: [
        {
          id: "stop-001",
          sequence_number: 1,
          type: "origin",
          location: {
            id: "loc-001",
            formatted: "Miami Distribution Center, FL",
            latitude: 25.7617,
            longitude: -80.1918,
          },
        },
        {
          id: "stop-002",
          sequence_number: 2,
          type: "destination",
          location: {
            id: "loc-002",
            formatted: "Atlanta Warehouse, GA",
            latitude: 33.749,
            longitude: -84.388,
          },
        },
      ],
    },
    {
      id: "shipment-002",
      status: "pending",
      mode: "open",
      source: "driver",
      weight: 3200,
      valuation: 75000,
      created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      driver: {
        id: "driver-002",
        first_name: "Sarah",
        last_name: "Chen",
        avatar: "/api/placeholder/40/40",
      },
      organization: {
        id: "org-002",
        name: "Express Freight Co",
        avatar_url: "/api/placeholder/32/32",
      },
      stops: [
        {
          id: "stop-003",
          sequence_number: 1,
          type: "origin",
          location: {
            id: "loc-003",
            formatted: "Houston Port, TX",
            latitude: 29.7604,
            longitude: -95.3698,
          },
        },
        {
          id: "stop-004",
          sequence_number: 2,
          type: "destination",
          location: {
            id: "loc-004",
            formatted: "Denver Distribution, CO",
            latitude: 39.7392,
            longitude: -104.9903,
          },
        },
      ],
    },
    {
      id: "shipment-003",
      status: "completed",
      mode: "closed",
      source: "organization",
      weight: 12750,
      valuation: 200000,
      created_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
      completed_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      driver: {
        id: "driver-003",
        first_name: "Mike",
        last_name: "Thompson",
        avatar: "/api/placeholder/40/40",
      },
      organization: {
        id: "org-003",
        name: "National Carriers",
        avatar_url: "/api/placeholder/32/32",
      },
      stops: [
        {
          id: "stop-005",
          sequence_number: 1,
          type: "origin",
          location: {
            id: "loc-005",
            formatted: "Los Angeles Port, CA",
            latitude: 34.0522,
            longitude: -118.2437,
          },
        },
        {
          id: "stop-006",
          sequence_number: 2,
          type: "destination",
          location: {
            id: "loc-006",
            formatted: "Phoenix Depot, AZ",
            latitude: 33.4484,
            longitude: -112.074,
          },
        },
      ],
    },
    {
      id: "shipment-004",
      status: "assigned",
      mode: "closed",
      source: "system",
      weight: 5600,
      valuation: 89000,
      created_at: new Date(Date.now() - 21600000).toISOString(), // 6 hours ago
      driver: {
        id: "driver-004",
        first_name: "Lisa",
        last_name: "Anderson",
        avatar: "/api/placeholder/40/40",
      },
      organization: {
        id: "org-004",
        name: "Regional Transport",
        avatar_url: "/api/placeholder/32/32",
      },
      stops: [
        {
          id: "stop-007",
          sequence_number: 1,
          type: "origin",
          location: {
            id: "loc-007",
            formatted: "Chicago Hub, IL",
            latitude: 41.8781,
            longitude: -87.6298,
          },
        },
        {
          id: "stop-008",
          sequence_number: 2,
          type: "destination",
          location: {
            id: "loc-008",
            formatted: "Detroit Center, MI",
            latitude: 42.3314,
            longitude: -83.0458,
          },
        },
      ],
    },
    {
      id: "shipment-005",
      status: "cancelled",
      mode: "open",
      source: "driver",
      weight: 7800,
      valuation: 110000,
      created_at: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
      organization: {
        id: "org-005",
        name: "Fast Track Logistics",
        avatar_url: "/api/placeholder/32/32",
      },
      stops: [
        {
          id: "stop-009",
          sequence_number: 1,
          type: "origin",
          location: {
            id: "loc-009",
            formatted: "Seattle Terminal, WA",
            latitude: 47.6062,
            longitude: -122.3321,
          },
        },
        {
          id: "stop-010",
          sequence_number: 2,
          type: "destination",
          location: {
            id: "loc-010",
            formatted: "Portland Warehouse, OR",
            latitude: 45.5152,
            longitude: -122.6784,
          },
        },
      ],
    },
  ],
  total: 5,
};

// Active shipment location for map display (Miami Distribution Center)
const activeShipmentLocation = {
  latitude: 25.7617,
  longitude: -80.1918,
  name: "Miami Distribution Center, FL",
  status: "in_progress",
  driver: "John Rodriguez",
  destination: "Atlanta Warehouse, GA",
};

export default function LogisticsPage() {
  const [loading] = useState(false);

  const handleDeleteShipment = async (id: string): Promise<boolean> => {
    // Mock delete functionality
    console.log("Delete shipment:", id);
    return true;
  };

  return (
    <div className="container grid max-w-full grid-cols-1 gap-4 px-4 py-4 md:px-6 md:py-8">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Logistics Overview</h1>
        <p className="text-muted-foreground">
          Comprehensive logistics management with real-time tracking and
          analytics
        </p>
      </div>

      {/* Statistics Section */}
      <section>
        <h2 className="mb-4 text-xl font-semibold">Key Metrics</h2>
        <LogisticsStats loading={loading} />
      </section>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="text-xs sm:text-sm">
            Overview
          </TabsTrigger>
          <TabsTrigger value="loads" className="text-xs sm:text-sm">
            Loads
          </TabsTrigger>
          <TabsTrigger value="shipments" className="text-xs sm:text-sm">
            Shipments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="grid grid-cols-1 gap-4">
          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            {/* Map Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">
                  Active Shipment Location
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="h-[250px] sm:h-[300px]">
                  <LocationMap
                    latitude={activeShipmentLocation.latitude}
                    longitude={activeShipmentLocation.longitude}
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Current Location:
                    </span>
                    <Badge variant="default" className="text-xs">
                      {activeShipmentLocation.status
                        .replace("_", " ")
                        .toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    {activeShipmentLocation.name}
                  </p>
                  <div className="grid grid-cols-1 gap-2 text-xs sm:grid-cols-2">
                    <div>
                      <span className="font-medium">Driver:</span>{" "}
                      {activeShipmentLocation.driver}
                    </div>
                    <div>
                      <span className="font-medium">Destination:</span>{" "}
                      {activeShipmentLocation.destination}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">
                  Today's Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  <div className="space-y-2">
                    <p className="text-muted-foreground text-xs sm:text-sm">
                      Active Shipments
                    </p>
                    <p className="text-xl font-bold sm:text-2xl">89</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-muted-foreground text-xs sm:text-sm">
                      On Schedule
                    </p>
                    <p className="text-xl font-bold text-green-600 sm:text-2xl">
                      94%
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-muted-foreground text-xs sm:text-sm">
                      Revenue Today
                    </p>
                    <p className="text-xl font-bold sm:text-2xl">$18.2K</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-muted-foreground text-xs sm:text-sm">
                      Fuel Efficiency
                    </p>
                    <p className="text-xl font-bold text-blue-600 sm:text-2xl">
                      7.2 MPG
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Shipments Preview */}

          <h3 className="text-lg font-semibold">Recent Shipments</h3>
          <ListShipments
            loading={loading}
            shipments={mockShipmentData}
            defaultPageSize={3}
            onDelete={handleDeleteShipment}
          />
        </TabsContent>

        <TabsContent value="loads" className="space-y-4 sm:space-y-6">
          <LoadSection />
        </TabsContent>

        <TabsContent
          value="shipments"
          className="grid w-full max-w-full grid-cols-1 gap-4"
        >
          <h3 className="text-lg font-semibold">Shipments</h3>
          <div className="grid w-full grid-cols-1 gap-6 sm:grid-cols-2">
            <LocationMap
              latitude={activeShipmentLocation.latitude}
              longitude={activeShipmentLocation.longitude}
            />
            <ListShipments
              loading={loading}
              shipments={mockShipmentData}
              defaultPageSize={10}
              onDelete={handleDeleteShipment}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
