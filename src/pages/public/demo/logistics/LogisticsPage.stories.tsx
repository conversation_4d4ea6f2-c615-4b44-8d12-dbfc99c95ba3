import type { Meta, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import LogisticsPage from "./index";

const meta: Meta<typeof LogisticsPage> = {
  title: "Demo/Pages/Logistics",
  component: LogisticsPage,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/demo/logistics" },
    }),
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithActiveLoads: Story = {
  args: {},
  parameters: {
    ...meta.parameters,
    docs: {
      description: {
        story: "Logistics page with active load management operations.",
      },
    },
  },
};

export const EmptyState: Story = {
  args: {},
  parameters: {
    ...meta.parameters,
    docs: {
      description: {
        story: "Logistics page when no loads are currently active.",
      },
    },
  },
};
