import { BarChart3, Package, TrendingUp, Truck, Users } from "lucide-react";

import Currency from "@/components/shared/Currency";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export interface LogisticsMetrics {
  totalShipments: number;
  activeShipments: number;
  totalDrivers: number;
  activeDrivers: number;
  totalRevenue: number;
  weeklyRevenue: number;
  averageDeliveryTime: number;
  completionRate: number;
}

interface LogisticsStatsProps {
  metrics?: LogisticsMetrics;
  loading?: boolean;
}

// Mock data for demo purposes
const mockMetrics: LogisticsMetrics = {
  totalShipments: 1247,
  activeShipments: 89,
  totalDrivers: 156,
  activeDrivers: 124,
  totalRevenue: 847500,
  weeklyRevenue: 125300,
  averageDeliveryTime: 18.5,
  completionRate: 94.2,
};

export function LogisticsStats({
  metrics = mockMetrics,
  loading = false,
}: LogisticsStatsProps) {
  const stats = [
    {
      title: "Total Shipments",
      value: metrics.totalShipments.toLocaleString(),
      icon: Package,
      change: "+12.5%",
      changeType: "positive" as const,
      description: "All time shipments",
    },
    {
      title: "Active Shipments",
      value: metrics.activeShipments.toLocaleString(),
      icon: Truck,
      change: "+5.2%",
      changeType: "positive" as const,
      description: "Currently in transit",
    },
    {
      title: "Active Drivers",
      value: `${metrics.activeDrivers}/${metrics.totalDrivers}`,
      icon: Users,
      change: "+8.1%",
      changeType: "positive" as const,
      description: "Drivers on duty",
    },
    {
      title: "Weekly Revenue",
      value: (
        <Currency
          amount={metrics.weeklyRevenue}
          loading={loading}
          className="text-xl font-bold sm:text-2xl"
        />
      ),
      icon: TrendingUp,
      change: "+15.3%",
      changeType: "positive" as const,
      description: "This week's earnings",
    },
    {
      title: "Completion Rate",
      value: `${metrics.completionRate}%`,
      icon: BarChart3,
      change: "+2.1%",
      changeType: "positive" as const,
      description: "Successfully delivered",
    },
    {
      title: "Avg Delivery Time",
      value: `${metrics.averageDeliveryTime}h`,
      icon: Package,
      change: "-1.5h",
      changeType: "positive" as const,
      description: "Average time to deliver",
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4 lg:grid-cols-3">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <Icon className="text-muted-foreground h-4 w-4 flex-shrink-0" />
            </CardHeader>
            <CardContent className="space-y-1">
              <div className="text-xl font-bold sm:text-2xl">
                {typeof stat.value === "string" ? stat.value : stat.value}
              </div>
              <div className="flex items-center justify-between gap-2">
                <CardDescription className="text-muted-foreground min-w-0 flex-1 text-xs">
                  {stat.description}
                </CardDescription>
                <Badge
                  variant={
                    stat.changeType === "positive" ? "default" : "destructive"
                  }
                  className="flex-shrink-0 text-xs"
                >
                  {stat.change}
                </Badge>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
