import { <PERSON>, Eye, MapPin, More<PERSON><PERSON><PERSON><PERSON>, Truck } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const shipments = [
  {
    id: "QS-2024-001",
    origin: "Chicago, IL",
    destination: "Atlanta, GA",
    driver: "<PERSON>",
    driverRating: 4.9,
    status: "in_transit",
    progress: 65,
    eta: "2:30 PM EST",
    cargo: "Electronics",
    temperature: "38°F",
    lastUpdate: "2 min ago",
  },
  {
    id: "QS-2024-002",
    origin: "Dallas, TX",
    destination: "Phoenix, AZ",
    driver: "<PERSON>",
    driverRating: 4.8,
    status: "pickup_verified",
    progress: 15,
    eta: "Tomorrow 11:00 AM",
    cargo: "Automotive Parts",
    temperature: "N/A",
    lastUpdate: "15 min ago",
  },
  {
    id: "QS-2024-003",
    origin: "Los Angeles, CA",
    destination: "Seattle, WA",
    driver: "<PERSON>",
    driverRating: 4.7,
    status: "delayed",
    progress: 45,
    eta: "6:45 PM PST (Delayed)",
    cargo: "Food & Beverage",
    temperature: "35°F",
    lastUpdate: "1 hour ago",
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "in_transit":
      return "bg-blue-100 text-blue-800";
    case "pickup_verified":
      return "bg-green-100 text-green-800";
    case "delayed":
      return "bg-red-100 text-red-800";
    case "approaching_delivery":
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case "in_transit":
      return "In Transit";
    case "pickup_verified":
      return "Pickup Verified";
    case "delayed":
      return "Delayed";
    case "approaching_delivery":
      return "Approaching Delivery";
    default:
      return status;
  }
};

export function ActiveShipments() {
  return (
    <Card className="shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100">
            <Truck className="h-4 w-4 text-blue-600" />
          </div>
          <CardTitle className="text-base font-medium">
            Active Shipments
          </CardTitle>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {shipments.map((shipment) => (
            <div
              key={shipment.id}
              className="rounded-lg p-4 transition-shadow hover:shadow-sm"
            >
              <div className="mb-3 flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-foreground font-semibold">
                    {shipment.id}
                  </div>
                  <Badge
                    className={`${getStatusColor(shipment.status)} border-0`}
                  >
                    {getStatusText(shipment.status)}
                  </Badge>
                </div>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Eye className="h-4 w-4" />
                </Button>
              </div>

              <div className="mb-4 grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-muted-foreground">From:</span>
                    <span className="font-medium">{shipment.origin}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-muted-foreground">To:</span>
                    <span className="font-medium">{shipment.destination}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <Truck className="h-4 w-4 text-gray-400" />
                    <span className="text-muted-foreground">Driver:</span>
                    <span className="font-medium">{shipment.driver}</span>
                    <span className="text-yellow-500">
                      ⭐{shipment.driverRating}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-muted-foreground">ETA:</span>
                    <span className="font-medium">{shipment.eta}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-muted-foreground">Cargo:</span>
                    <span className="ml-1 font-medium">{shipment.cargo}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-muted-foreground">Temp:</span>
                    <span className="ml-1 font-medium">
                      {shipment.temperature}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">{shipment.progress}%</span>
                </div>
                <div className="h-2 w-full rounded-full">
                  <div
                    className="h-2 rounded-full bg-gradient-to-r from-orange-400 to-orange-500 transition-all duration-300"
                    style={{ width: `${shipment.progress}%` }}
                  ></div>
                </div>
                <div className="text-muted-foreground text-xs">
                  Last update: {shipment.lastUpdate}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
