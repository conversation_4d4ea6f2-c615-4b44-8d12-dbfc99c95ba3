import {
  CheckCircle,
  Package,
  TrendingDown,
  TrendingUp,
  Truck,
} from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";

const stats = [
  {
    title: "Pending Orders",
    value: "1,245",
    subtitle: "Awaiting Confirmations",
    change: "-5%",
    trend: "down",
    icon: Package,
    color: "bg-pink-100",
    iconColor: "text-pink-500",
  },
  {
    title: "In Transit",
    value: "3,524",
    subtitle: "On the way to destination",
    change: "+15%",
    trend: "up",
    icon: Truck,
    color: "bg-blue-100",
    iconColor: "text-blue-500",
  },
  {
    title: "Delivered Shipments",
    value: "5,320",
    subtitle: "Successfully delivered",
    change: "+20%",
    trend: "up",
    icon: CheckCircle,
    color: "bg-green-100",
    iconColor: "text-green-500",
  },
];

export function StatsOverview() {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        const isPositive = stat.trend === "up";

        return (
          <Card
            key={index}
            className="overflow-hidden shadow-sm transition-shadow hover:shadow-md"
          >
            <CardContent className="p-0">
              <div className="flex items-start">
                <div className="p-5">
                  <div
                    className={`flex h-10 w-10 items-center justify-center rounded-lg ${stat.color}`}
                  >
                    <Icon className={`h-5 w-5 ${stat.iconColor}`} />
                  </div>
                </div>
                <div className="flex-1 p-5">
                  <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                    {stat.title}
                  </h3>
                  <div className="flex items-baseline justify-between">
                    <p className="text-foreground text-3xl font-bold">
                      {stat.value}
                    </p>
                    <div
                      className={`flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                        isPositive
                          ? "bg-green-50 text-green-600"
                          : "bg-red-50 text-red-600"
                      }`}
                    >
                      {isPositive ? (
                        <TrendingUp className="mr-1 h-3 w-3" />
                      ) : (
                        <TrendingDown className="mr-1 h-3 w-3" />
                      )}
                      {stat.change}
                    </div>
                  </div>
                  <p className="text-muted-foreground mt-1 text-xs">
                    {stat.subtitle}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
