import { Bar<PERSON>hart3, FileText, Plus, Settings, Users } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const actions = [
  {
    title: "Create Shipment",
    description: "Schedule a new shipment",
    icon: Plus,
    color: "blue",
  },
  {
    title: "Generate Report",
    description: "Export performance data",
    icon: BarChart3,
    color: "green",
  },
  {
    title: "Manage Carriers",
    description: "View carrier network",
    icon: Users,
    color: "purple",
  },
  {
    title: "Upload Documents",
    description: "Add shipment paperwork",
    icon: FileText,
    color: "orange",
  },
];

export function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>Quick Actions</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {actions.map((action) => {
            const Icon = action.icon;
            return (
              <Button
                key={action.title}
                variant="outline"
                className="flex h-auto flex-col items-center space-y-2 p-4 transition-shadow hover:shadow-md"
              >
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-lg ${
                    action.color === "blue"
                      ? "bg-blue-100 text-blue-600"
                      : action.color === "green"
                        ? "bg-green-100 text-green-600"
                        : action.color === "purple"
                          ? "bg-purple-100 text-purple-600"
                          : "bg-orange-100 text-orange-600"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium">{action.title}</p>
                  <p className="text-muted-foreground text-xs">
                    {action.description}
                  </p>
                </div>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
