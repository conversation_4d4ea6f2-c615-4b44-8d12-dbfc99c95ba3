import { Download, FileText, MoreHorizontal } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const invoices = [
  {
    id: "INV-2024-001",
    amount: "$2,450.00",
    status: "Paid",
    date: "June 3, 2024",
  },
  {
    id: "INV-2024-002",
    amount: "$1,890.00",
    status: "Pending",
    date: "June 2, 2024",
  },
  {
    id: "INV-2024-003",
    amount: "$3,200.00",
    status: "Paid",
    date: "June 1, 2024",
  },
];

export function RecentInvoices() {
  return (
    <Card className="border shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100">
            <FileText className="h-4 w-4 text-blue-600" />
          </div>
          <CardTitle className="text-base font-medium">
            Recent Invoices
          </CardTitle>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-3">
        {invoices.map((invoice) => (
          <div
            key={invoice.id}
            className="flex items-center justify-between rounded-lg p-3"
          >
            <div className="flex-1">
              <p className="text-foreground text-sm font-medium">
                {invoice.id}
              </p>
              <p className="text-muted-foreground text-xs">{invoice.date}</p>
            </div>
            <div className="text-right">
              <p className="text-foreground text-sm font-medium">
                {invoice.amount}
              </p>
              <p
                className={`text-xs ${invoice.status === "Paid" ? "text-green-600" : "text-orange-600"}`}
              >
                {invoice.status}
              </p>
            </div>
            <Button variant="ghost" size="sm" className="ml-2 h-8 w-8 p-0">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
