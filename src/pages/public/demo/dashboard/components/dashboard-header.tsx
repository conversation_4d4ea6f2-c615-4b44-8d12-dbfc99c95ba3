import { Bell, Plus, Search, Settings, User } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";

export function DashboardHeader() {
  return (
    <header className="border-b bg-white px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-foreground text-2xl font-bold">Dashboard</h1>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
            <input
              type="text"
              placeholder="Search shipments..."
              className="w-64 rounded-lg border py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-orange-500"
            />
          </div>

          <Button className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700">
            <Plus className="mr-2 h-4 w-4" />
            New Shipment
          </Button>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="text-muted-foreground h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500"></span>
            </Button>
            <Button variant="ghost" size="sm">
              <Settings className="text-muted-foreground h-5 w-5" />
            </Button>
            <Button variant="ghost" size="sm">
              <User className="text-muted-foreground h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
