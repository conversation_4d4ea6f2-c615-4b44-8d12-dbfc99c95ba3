import { MapPin } from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";

export function ShipmentMap() {
  return (
    <Card className="shadow-sm">
      <CardContent className="p-0">
        <div className="relative h-64 overflow-hidden rounded-lg bg-gradient-to-br from-green-100 to-blue-100">
          {/* Map placeholder with some visual elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-green-200/30 to-blue-200/30"></div>

          {/* Simulated map markers */}
          <div className="absolute top-4 left-4">
            <div className="h-3 w-3 animate-pulse rounded-full bg-red-500"></div>
          </div>
          <div className="absolute top-12 right-8">
            <div className="h-3 w-3 animate-pulse rounded-full bg-blue-500"></div>
          </div>
          <div className="absolute bottom-8 left-12">
            <div className="h-3 w-3 animate-pulse rounded-full bg-green-500"></div>
          </div>
          <div className="absolute right-4 bottom-4">
            <div className="h-3 w-3 animate-pulse rounded-full bg-orange-500"></div>
          </div>

          {/* Map overlay */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="rounded-lg bg-white/90 p-4 text-center backdrop-blur-sm">
              <MapPin className="mx-auto mb-2 h-8 w-8 text-gray-600" />
              <p className="text-foreground text-sm font-medium">
                Live Shipment Tracking
              </p>
              <p className="text-xs text-gray-600">4 active shipments</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
