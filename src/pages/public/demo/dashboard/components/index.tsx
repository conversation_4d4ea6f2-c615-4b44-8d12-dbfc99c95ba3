import { Layout } from "@/components/layout";
import { ActiveShipments } from "./active-shipments";
import { DashboardHeader } from "./dashboard-header";
import { DashboardSidebar } from "./dashboard-sidebar";
import { LiveShipmentStatus } from "./live-shipment-status";
import { PerformanceCharts } from "./performance-charts";
import { RecentInvoices } from "./recent-invoices";
import { ReportsSection } from "./reports-section";
import { ShipmentMap } from "./shipment-map";
import { StatsOverview } from "./stats-overview";

export default function DashboardPage() {
  return (
    <Layout>
      <div className="flex min-h-screen bg-gray-50">
        <DashboardSidebar />

        <div className="flex flex-1 flex-col">
          <DashboardHeader />

          <main className="flex-1 space-y-6 overflow-y-auto p-6">
            {/* Stats Overview */}
            <StatsOverview />

            {/* Reports Section */}
            <ReportsSection />

            {/* Main Content Grid */}
            <div className="grid gap-6 lg:grid-cols-3">
              {/* Left Column - Charts */}
              <div className="space-y-6 lg:col-span-2">
                <PerformanceCharts />
                <ActiveShipments />
              </div>

              {/* Right Column - Live Data & Map */}
              <div className="space-y-6">
                <LiveShipmentStatus />
                <ShipmentMap />
                <RecentInvoices />
              </div>
            </div>
          </main>
        </div>
      </div>
    </Layout>
  );
}
