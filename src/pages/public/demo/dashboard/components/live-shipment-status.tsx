import {
  <PERSON>,
  <PERSON><PERSON>,
  MapPin,
  MoreHorizontal,
  Package,
  Truck,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

const liveShipments = [
  {
    id: "QS2024736210",
    time: "11:30 AM",
    date: "May 31",
    status: "Packaging",
    location: "4517 Washington Ave, Manchester, KY",
    type: "packaging",
  },
  {
    id: "QS2024736211",
    time: "09:15 AM",
    date: "June 01",
    status: "In Transit",
    location: "Out for delivery via TRX Logistics",
    type: "transit",
  },
  {
    id: "QS2024736212",
    time: "01:20 PM",
    date: "June 02",
    status: "Delivery",
    location: "ETA: June 3, 2025 - 2:00 PM",
    type: "delivery",
  },
  {
    id: "QS2024736213",
    time: "----",
    date: "----",
    status: "Order Delivered",
    location: "6391 Elgin St, Celina, Delaware 10299",
    type: "delivered",
  },
];

const getStatusIcon = (type: string) => {
  switch (type) {
    case "packaging":
      return Package;
    case "transit":
      return Truck;
    case "delivery":
      return Clock;
    case "delivered":
      return MapPin;
    default:
      return Package;
  }
};

const getStatusColor = (type: string) => {
  switch (type) {
    case "packaging":
      return "bg-orange-100 text-orange-800";
    case "transit":
      return "bg-blue-100 text-blue-800";
    case "delivery":
      return "bg-purple-100 text-purple-800";
    case "delivered":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export function LiveShipmentStatus() {
  return (
    <Card className="shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-100">
            <Truck className="h-4 w-4 text-orange-600" />
          </div>
          <CardTitle className="text-base font-medium">
            Live Shipments Status
          </CardTitle>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {liveShipments.map((shipment, index) => {
          const Icon = getStatusIcon(shipment.type);
          return (
            <div
              key={shipment.id}
              className="flex items-start space-x-3 rounded-lg p-3"
            >
              <div className="flex-shrink-0">
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-lg ${
                    shipment.type === "packaging"
                      ? "bg-orange-100"
                      : shipment.type === "transit"
                        ? "bg-blue-100"
                        : shipment.type === "delivery"
                          ? "bg-purple-100"
                          : "bg-green-100"
                  }`}
                >
                  <Icon
                    className={`h-4 w-4 ${
                      shipment.type === "packaging"
                        ? "text-orange-600"
                        : shipment.type === "transit"
                          ? "text-blue-600"
                          : shipment.type === "delivery"
                            ? "text-purple-600"
                            : "text-green-600"
                    }`}
                  />
                </div>
              </div>

              <div className="min-w-0 flex-1">
                <div className="mb-1 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <p className="text-foreground text-sm font-medium">
                      Shipment ID
                    </p>
                    <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
                      <Copy className="text-muted-foreground h-3 w-3" />
                    </Button>
                  </div>
                  <div className="text-muted-foreground flex items-center space-x-2 text-xs">
                    <span>{shipment.time}</span>
                    <span>{shipment.date}</span>
                  </div>
                </div>
                <p className="text-muted-foreground mb-2 text-xs">
                  {shipment.id}
                </p>

                <div className="flex items-center justify-between">
                  <Badge
                    className={`${getStatusColor(shipment.type)} border-0 text-xs`}
                  >
                    {shipment.status}
                  </Badge>
                </div>

                <p className="text-muted-foreground mt-2 text-xs">
                  {shipment.location}
                </p>
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
