import { AlertTriangle, Clock, Thermometer, X } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const alerts = [
  {
    id: 1,
    type: "delay",
    title: "Shipment Delayed",
    message: "QS-2024-003 delayed by 2 hours due to weather conditions",
    severity: "medium",
    time: "1 hour ago",
    icon: Clock,
  },
  {
    id: 2,
    type: "temperature",
    title: "Temperature Alert",
    message: "QS-2024-001 temperature reading outside acceptable range",
    severity: "high",
    time: "30 min ago",
    icon: Thermometer,
  },
  {
    id: 3,
    type: "route",
    title: "Route Deviation",
    message: "QS-2024-004 has deviated from planned route",
    severity: "low",
    time: "45 min ago",
    icon: AlertTriangle,
  },
];

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case "high":
      return "bg-red-100 text-red-800";
    case "medium":
      return "bg-yellow-100 text-yellow-800";
    case "low":
      return "bg-blue-100 text-blue-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export function AlertsPanel() {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Active Alerts</span>
          </CardTitle>
          <Badge className="border-0 bg-red-100 text-red-800">
            {alerts.length}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alerts.map((alert) => {
            const Icon = alert.icon;
            return (
              <div key={alert.id} className="rounded-lg p-3">
                <div className="mb-2 flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <Icon className="text-muted-foreground h-4 w-4" />
                    <span className="text-sm font-medium">{alert.title}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      className={`${getSeverityColor(alert.severity)} border-0 text-xs`}
                    >
                      {alert.severity}
                    </Badge>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <p className="text-muted-foreground mb-2 text-sm">
                  {alert.message}
                </p>
                <p className="text-muted-foreground text-xs">{alert.time}</p>
              </div>
            );
          })}
        </div>
        <Button variant="outline" size="sm" className="mt-4 w-full">
          View All Alerts
        </Button>
      </CardContent>
    </Card>
  );
}
