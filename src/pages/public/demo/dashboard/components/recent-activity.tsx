import {
  Alert<PERSON>riangle,
  <PERSON><PERSON><PERSON>cle,
  Clock,
  FileText,
  Truck,
} from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const activities = [
  {
    id: 1,
    type: "delivery_completed",
    title: "Delivery Completed",
    description: "QS-2024-005 delivered to Boston, MA",
    time: "5 min ago",
    icon: CheckCircle,
    color: "green",
  },
  {
    id: 2,
    type: "pickup_verified",
    title: "Pickup Verified",
    description: "QS-2024-006 picked up from Denver, CO",
    time: "12 min ago",
    icon: Truck,
    color: "blue",
  },
  {
    id: 3,
    type: "document_uploaded",
    title: "Document Uploaded",
    description: "BOL signed for QS-2024-003",
    time: "25 min ago",
    icon: FileText,
    color: "purple",
  },
  {
    id: 4,
    type: "delay_alert",
    title: "Delay Alert",
    description: "QS-2024-002 delayed due to weather",
    time: "1 hour ago",
    icon: AlertTriangle,
    color: "orange",
  },
  {
    id: 5,
    type: "shipment_created",
    title: "New Shipment Created",
    description: "QS-2024-007 scheduled for pickup",
    time: "2 hours ago",
    icon: Clock,
    color: "gray",
  },
];

export function RecentActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Recent Activity</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full ${
                    activity.color === "green"
                      ? "bg-green-100"
                      : activity.color === "blue"
                        ? "bg-blue-100"
                        : activity.color === "purple"
                          ? "bg-purple-100"
                          : activity.color === "orange"
                            ? "bg-orange-100"
                            : "bg-gray-100"
                  }`}
                >
                  <Icon
                    className={`h-4 w-4 ${
                      activity.color === "green"
                        ? "text-green-600"
                        : activity.color === "blue"
                          ? "text-blue-600"
                          : activity.color === "purple"
                            ? "text-purple-600"
                            : activity.color === "orange"
                              ? "text-orange-600"
                              : "text-gray-600"
                    }`}
                  />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-foreground text-sm font-medium">
                    {activity.title}
                  </p>
                  <p className="text-muted-foreground text-sm">
                    {activity.description}
                  </p>
                  <p className="text-muted-foreground mt-1 text-xs">
                    {activity.time}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
