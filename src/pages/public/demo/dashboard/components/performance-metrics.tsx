import { Bar<PERSON>hart3, TrendingUp } from "lucide-react";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";

const deliveryData = [
  { month: "Jan", onTime: 92, total: 156 },
  { month: "Feb", onTime: 89, total: 142 },
  { month: "Mar", onTime: 94, total: 168 },
  { month: "Apr", onTime: 91, total: 159 },
  { month: "May", onTime: 96, total: 173 },
  { month: "Jun", onTime: 94, total: 181 },
];

const carrierPerformance = [
  { name: "Elite Transport", rating: 4.9, shipments: 45 },
  { name: "Reliable Freight", rating: 4.7, shipments: 38 },
  { name: "Swift Logistics", rating: 4.8, shipments: 32 },
  { name: "Prime Carriers", rating: 4.6, shipments: 28 },
  { name: "Express Lines", rating: 4.5, shipments: 24 },
];

export function PerformanceMetrics() {
  return (
    <div className="grid gap-8 lg:grid-cols-2">
      {/* Delivery Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Delivery Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              onTime: {
                label: "On-Time Deliveries",
                color: "hsl(var(--chart-1))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={deliveryData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <ChartTooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="rounded-lg border p-3 shadow-lg">
                          <p className="font-semibold">{label}</p>
                          <p className="text-primary">
                            On-Time: {data.onTime}%
                          </p>
                          <p className="text-muted-foreground">
                            Total Shipments: {data.total}
                          </p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="onTime"
                  stroke="var(--color-onTime)"
                  strokeWidth={3}
                  dot={{ fill: "var(--color-onTime)", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Top Carriers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Top Performing Carriers</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {carrierPerformance.map((carrier, index) => (
              <div
                key={carrier.name}
                className="flex items-center justify-between rounded-lg p-3"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 text-sm font-bold text-white">
                    {index + 1}
                  </div>
                  <div>
                    <p className="text-foreground font-medium">
                      {carrier.name}
                    </p>
                    <p className="text-muted-foreground text-sm">
                      {carrier.shipments} shipments
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-1">
                    <span className="text-yellow-500">⭐</span>
                    <span className="font-semibold">{carrier.rating}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
