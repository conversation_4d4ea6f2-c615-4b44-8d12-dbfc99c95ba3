import {
  <PERSON>R<PERSON>,
  BarChart3,
  Calendar,
  TrendingUp,
  Truck,
} from "lucide-react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";

const monthlyData = [
  { name: "Jan", shipments: 420, revenue: 65000, onTime: 92 },
  { name: "Feb", shipments: 380, revenue: 59000, onTime: 89 },
  { name: "Mar", shipments: 510, revenue: 73000, onTime: 94 },
  { name: "Apr", shipments: 470, revenue: 68000, onTime: 91 },
  { name: "May", shipments: 540, revenue: 79000, onTime: 96 },
  { name: "Jun", shipments: 590, revenue: 84000, onTime: 94 },
];

const carrierPerformance = [
  { name: "Elite Transport", score: 92 },
  { name: "Reliable Freight", score: 87 },
  { name: "Swift Logistics", score: 85 },
  { name: "Prime Carriers", score: 82 },
  { name: "Express Lines", score: 78 },
];

export function ReportsSection() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-foreground text-xl font-semibold">
          Performance Reports
        </h2>
        <div className="text-primary flex items-center space-x-2 text-sm">
          <span>View all reports</span>
          <ArrowRight className="h-4 w-4" />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Monthly Shipments Chart */}
        <Card className="border shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100">
                <BarChart3 className="h-4 w-4 text-blue-600" />
              </div>
              <CardTitle className="text-base font-medium">
                Monthly Shipments
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                shipments: {
                  label: "Shipments",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[200px] w-full"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={monthlyData}
                  margin={{ top: 10, right: 10, left: -20, bottom: 0 }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    vertical={false}
                    stroke="#f0f0f0"
                  />
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    fontSize={12}
                  />
                  <YAxis axisLine={false} tickLine={false} fontSize={12} />
                  <ChartTooltip />
                  <Bar
                    dataKey="shipments"
                    fill="url(#colorShipments)"
                    radius={[4, 4, 0, 0]}
                    barSize={20}
                  />
                  <defs>
                    <linearGradient
                      id="colorShipments"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#ff7c43" stopOpacity={1} />
                      <stop
                        offset="100%"
                        stopColor="#ff7c43"
                        stopOpacity={0.8}
                      />
                    </linearGradient>
                  </defs>
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Revenue Trend Chart */}
        <Card className="border shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-100">
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
              <CardTitle className="text-base font-medium">
                Revenue Trend
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                revenue: {
                  label: "Revenue",
                  color: "hsl(var(--chart-2))",
                },
              }}
              className="h-[200px] w-full"
            >
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={monthlyData}
                  margin={{ top: 10, right: 10, left: -20, bottom: 0 }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    vertical={false}
                    stroke="#f0f0f0"
                  />
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    fontSize={12}
                  />
                  <YAxis axisLine={false} tickLine={false} fontSize={12} />
                  <ChartTooltip />
                  <defs>
                    <linearGradient
                      id="colorRevenue"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor="#4ade80" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#4ade80" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <Area
                    type="monotone"
                    dataKey="revenue"
                    stroke="#4ade80"
                    fillOpacity={1}
                    fill="url(#colorRevenue)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* On-Time Performance Chart */}
        <Card className="border shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              <CardTitle className="text-base font-medium">
                On-Time Performance
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                onTime: {
                  label: "On-Time %",
                  color: "hsl(var(--chart-3))",
                },
              }}
              className="h-[200px] w-full"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={monthlyData}
                  margin={{ top: 10, right: 10, left: -20, bottom: 0 }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    vertical={false}
                    stroke="#f0f0f0"
                  />
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    fontSize={12}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    fontSize={12}
                    domain={[80, 100]}
                  />
                  <ChartTooltip />
                  <Line
                    type="monotone"
                    dataKey="onTime"
                    stroke="#a855f7"
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#a855f7", strokeWidth: 0 }}
                    activeDot={{ r: 6, fill: "#a855f7", strokeWidth: 0 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Carrier Performance Chart */}
      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-100">
                <Truck className="h-4 w-4 text-orange-600" />
              </div>
              <CardTitle className="text-base font-medium">
                Carrier Performance
              </CardTitle>
            </div>
            <div className="text-muted-foreground text-xs">Last 30 days</div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {carrierPerformance.map((carrier, index) => (
              <div key={carrier.name} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-foreground font-medium">
                    {carrier.name}
                  </span>
                  <span className="text-muted-foreground">
                    {carrier.score}%
                  </span>
                </div>
                <div className="bg-muted h-2 w-full rounded-full">
                  <div
                    className="h-2 rounded-full bg-gradient-to-r from-orange-400 to-orange-500"
                    style={{ width: `${carrier.score}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
