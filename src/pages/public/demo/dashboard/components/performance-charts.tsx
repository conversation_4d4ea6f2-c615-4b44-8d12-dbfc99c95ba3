import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const performanceData = [
  { month: "May", delayRate: 85, successRate: 92, efficiency: 78 },
  { month: "June", delayRate: 78, successRate: 95, efficiency: 85 },
];

const metrics = [
  { label: "Top Performing Month:", value: "June (80.49%)", icon: "🏆" },
  { label: "Highest Delay Rate:", value: "May (11.7%)", icon: "⚠️" },
  { label: "Most Exported Category:", value: "Electronics", icon: "📱" },
  { label: "Total Exports:", value: "24,302 Units", icon: "📦" },
];

export function PerformanceCharts() {
  return (
    <Card className="shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-red-100">
            <BarChart3 className="h-4 w-4 text-red-600" />
          </div>
          <CardTitle className="text-base font-medium">
            Export/Import Trends
          </CardTitle>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Chart Area */}
        <div className="space-y-6">
          {performanceData.map((data, index) => (
            <div key={data.month} className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-foreground font-medium">
                  {data.month}
                </span>
              </div>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Delay Rate</span>
                    <div className="text-muted-foreground flex space-x-2">
                      <span>0%</span>
                      <span>20%</span>
                      <span>40%</span>
                      <span>60%</span>
                      <span>80%</span>
                      <span>100%</span>
                    </div>
                  </div>
                  <div className="bg-muted h-3 w-full rounded-full">
                    <div
                      className="h-3 rounded-full bg-gradient-to-r from-orange-400 to-orange-500"
                      style={{ width: `${data.delayRate}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Success Rate</span>
                  </div>
                  <div className="bg-muted h-3 w-full rounded-full">
                    <div
                      className="h-3 rounded-full bg-gradient-to-r from-orange-500 to-orange-600"
                      style={{ width: `${data.successRate}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Efficiency</span>
                  </div>
                  <div className="bg-muted h-3 w-full rounded-full">
                    <div
                      className="h-3 rounded-full bg-gradient-to-r from-red-400 to-red-500"
                      style={{ width: `${data.efficiency}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="border-t pt-4">
          <p className="text-muted-foreground mb-4 text-xs">
            Performance summary based on the last 60 days of operations
          </p>
          <div className="grid grid-cols-2 gap-4">
            {metrics.map((metric, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span className="text-lg">{metric.icon}</span>
                <div>
                  <p className="text-muted-foreground text-xs">
                    {metric.label}
                  </p>
                  <p className="text-sm font-medium">{metric.value}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
