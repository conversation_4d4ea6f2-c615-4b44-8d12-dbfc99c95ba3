import {
  BarChart3,
  ChevronDown,
  DollarSign,
  FileText,
  Home,
  MapPin,
  Package,
  Settings,
  Shield,
  TrendingUp,
  Truck,
  Users,
} from "lucide-react";

const navigation = [
  { name: "Dashboard", icon: Home, current: true },
  { name: "Shipments", icon: Truck, current: false },
  { name: "Orders", icon: Package, current: false },
  { name: "Inventory", icon: Package, current: false },
  { name: "Carriers", icon: Users, current: false },
  { name: "Routes", icon: MapPin, current: false },
  { name: "Documents", icon: FileText, current: false },
  { name: "Payments", icon: DollarSign, current: false },
  { name: "Analytics", icon: BarChart3, current: false },
  { name: "Compliance", icon: Shield, current: false },
  { name: "Reports", icon: TrendingUp, current: false },
  { name: "Settings", icon: Settings, current: false },
];

export function DashboardSidebar() {
  return (
    <div className="flex w-64 flex-col border-r bg-white">
      <div className="border-b p-4">
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-orange-500 to-orange-600">
            <Truck className="h-5 w-5 text-white" />
          </div>
          <span className="text-foreground text-xl font-bold">QuikSkope</span>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-3">
          <div className="text-muted-foreground flex items-center justify-between px-3 py-2 text-xs font-semibold tracking-wider uppercase">
            <span>Product Monitoring</span>
            <ChevronDown className="h-4 w-4" />
          </div>

          <nav className="mt-2 space-y-1">
            {navigation.slice(0, 5).map((item) => {
              const Icon = item.icon;
              return (
                <a
                  key={item.name}
                  href="#"
                  className={`group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                    item.current
                      ? "bg-muted text-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  }`}
                >
                  <Icon className="text-muted-foreground mr-3 h-5 w-5" />
                  {item.name}
                </a>
              );
            })}
          </nav>

          <div className="text-muted-foreground mt-6 flex items-center justify-between px-3 py-2 text-xs font-semibold tracking-wider uppercase">
            <span>Product Development</span>
            <ChevronDown className="h-4 w-4" />
          </div>

          <nav className="mt-2 space-y-1">
            {navigation.slice(5, 9).map((item) => {
              const Icon = item.icon;
              return (
                <a
                  key={item.name}
                  href="#"
                  className={`group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                    item.current
                      ? "bg-muted text-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  }`}
                >
                  <Icon className="text-muted-foreground mr-3 h-5 w-5" />
                  {item.name}
                </a>
              );
            })}
          </nav>

          <div className="text-muted-foreground mt-6 flex items-center justify-between px-3 py-2 text-xs font-semibold tracking-wider uppercase">
            <span>Components</span>
            <ChevronDown className="h-4 w-4" />
          </div>

          <nav className="mt-2 space-y-1">
            {navigation.slice(9).map((item) => {
              const Icon = item.icon;
              return (
                <a
                  key={item.name}
                  href="#"
                  className={`group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                    item.current
                      ? "bg-muted text-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  }`}
                >
                  <Icon className="text-muted-foreground mr-3 h-5 w-5" />
                  {item.name}
                </a>
              );
            })}
          </nav>
        </div>
      </div>
    </div>
  );
}
