import ShipmentsPage from "../shipments/list";
import { ActiveShipments } from "./components/active-shipments";
import { LiveShipmentStatus } from "./components/live-shipment-status";
import { PerformanceCharts } from "./components/performance-charts";
import { RecentInvoices } from "./components/recent-invoices";
import { ReportsSection } from "./components/reports-section";
import { ShipmentMap } from "./components/shipment-map";
import { StatsOverview } from "./components/stats-overview";

export default function DashboardPage() {
  return (
    <div className="container grid max-w-full grid-cols-1 gap-4 px-4 py-4 md:px-6 md:py-8">
      {/* Dashboard Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Dashboard Overview</h1>
        <p className="text-muted-foreground">
          Real-time insights and analytics for your operations
        </p>
      </div>

      {/* Stats Overview */}
      <StatsOverview />

      {/* Reports Section */}
      <ReportsSection />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Left Column - Charts */}
        <div className="grid grid-cols-1 gap-4 lg:col-span-2">
          <PerformanceCharts />
          <ActiveShipments />
        </div>

        {/* Right Column - Live Data & Map */}
        <div className="grid grid-cols-1 gap-4">
          <LiveShipmentStatus />
          <ShipmentMap />
          <RecentInvoices />
        </div>
      </div>

      <ShipmentsPage />
    </div>
  );
}
