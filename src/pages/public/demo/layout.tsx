import { ReactNode, useEffect, useState } from "react";
import { Bar<PERSON>hart3, FileText, Shield, Truck } from "lucide-react";
import { useLocation, useNavigate } from "react-router";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

type TabValue = "dashboard" | "logistics" | "documents" | "verifications";

interface DashboardLayoutProps {
  children: ReactNode;
  defaultTab?: TabValue;
}

export function DashboardLayout({
  children,
  defaultTab = "dashboard",
}: DashboardLayoutProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabValue>(defaultTab);

  // Map routes to tab values
  const getTabFromPath = (pathname: string): TabValue => {
    if (pathname === "/demo" || pathname === "/demo/") return "dashboard";
    if (pathname.startsWith("/demo/logistics")) return "logistics";
    if (pathname.startsWith("/demo/documents")) return "documents";
    if (pathname.startsWith("/demo/verifications")) return "verifications";
    return "dashboard";
  };

  // Map tab values to routes
  const getRouteFromTab = (tab: string): string => {
    switch (tab) {
      case "dashboard":
        return "/demo";
      case "logistics":
        return "/demo/logistics";
      case "documents":
        return "/demo/documents";
      case "verifications":
        return "/demo/verifications";
      default:
        return "/demo";
    }
  };

  // Update active tab based on current route
  useEffect(() => {
    const tabFromPath = getTabFromPath(location.pathname);
    setActiveTab(tabFromPath);
  }, [location.pathname]);

  // Handle tab navigation
  const handleTabChange = (value: string) => {
    const route = getRouteFromTab(value);
    navigate(route);
  };

  return (
    <div className="bg-background min-h-screen p-2">
      <div className="mx-auto max-w-7xl">
        {/* Container with top padding to accommodate the tab bar */}
        <div className="pt-6">
          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="relative"
          >
            {/* Tabs positioned at the top border - responsive centering */}
            <div className="relative">
              <TabsList className="bg-card border-border absolute top-0 left-1/2 z-10 h-12 -translate-x-1/2 -translate-y-1/2 transform rounded-lg border p-1">
                <TabsTrigger
                  value="dashboard"
                  className="data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center gap-2 px-3 py-2 text-sm data-[state=active]:shadow-sm sm:px-4"
                >
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Dashboard</span>
                </TabsTrigger>
                <TabsTrigger
                  value="logistics"
                  className="data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center gap-2 px-3 py-2 text-sm data-[state=active]:shadow-sm sm:px-4"
                >
                  <Truck className="h-4 w-4" />
                  <span className="hidden sm:inline">Logistics</span>
                </TabsTrigger>
                <TabsTrigger
                  value="documents"
                  className="data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center gap-2 px-3 py-2 text-sm data-[state=active]:shadow-sm sm:px-4"
                >
                  <FileText className="h-4 w-4" />
                  <span className="hidden sm:inline">Documents</span>
                </TabsTrigger>
                <TabsTrigger
                  value="verifications"
                  className="data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center gap-2 px-3 py-2 text-sm data-[state=active]:shadow-sm sm:px-4"
                >
                  <Shield className="h-4 w-4" />
                  <span className="hidden sm:inline">Verifications</span>
                </TabsTrigger>
              </TabsList>

              {/* Main content area with border */}
              <div className="border-border bg-card rounded-lg border shadow-sm">
                {/* Content area with padding to accommodate the tabs */}
                <div className="pt-6">{children}</div>
              </div>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
