import { useEffect, useState } from "react";
import { Arrow<PERSON><PERSON><PERSON>, Clock, Download, FileText, Trash2 } from "lucide-react";
import { Link, useNavigate, useParams } from "react-router";

import type { ProcessedDocumentData } from "@/components/documents/templates/DocumentTemplate";
import type { Tables } from "@/supabase/types";

import DocumentAnalysis from "@/components/documents/DocumentAnalysis";
import DocumentExtractedFields from "@/components/documents/DocumentExtractedFields";
import DocumentInfoCard from "@/components/documents/DocumentInfoCard";
import DocumentPreviewPane from "@/components/documents/DocumentPreviewPane";
import DocumentWarnings from "@/components/documents/DocumentWarnings";
import DocumentTemplate from "@/components/documents/templates/DocumentTemplate";
import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Header } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { useDocumentOperations } from "./use-documents";
import { useSupabaseDocuments } from "./useSupabaseDocuments";

type Document = Tables<"documents">;

// Type for legacy analysis format (backwards compatibility)
interface LegacyAnalysis {
  documentType?: string;
  extractedFields?: Record<string, unknown>;
  warnings?: string[];
  [key: string]: unknown;
}

// Type for metadata format - simplified to match what we actually store
interface DocumentMetadata {
  // Rich processed data (new format)
  processedData?: ProcessedDocumentData;

  // File and identification info
  filePackage?: {
    originalName: string;
    sanitizedName: string;
    contentType: string;
    fileSize: number;
    fileExtension: string;
    fileId: string;
    filePath: string;
  };
  identification?: {
    documentType: string;
    confidence: number;
    extractedFields: Record<string, unknown>;
    pageCount?: number;
    warnings: string[];
  };

  // Legacy support
  analysis?: LegacyAnalysis | string;
  [key: string]: unknown;
}

export default function DocumentDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getDocument, deleteAndRefresh } = useDocumentOperations();
  const { reAnalyzeDocument } = useSupabaseDocuments();
  const [document, setDocument] = useState<Document | null>(null);
  const [activeTab, setActiveTab] = useState("preview");
  const [legacyAnalysis, setLegacyAnalysis] = useState<LegacyAnalysis | null>(
    null,
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const loadDocument = async () => {
      if (id) {
        setIsLoading(true);
        try {
          const doc = await getDocument(id);
          setDocument(doc);

          // Extract legacy analysis for backwards compatibility with existing components
          if (
            doc?.metadata &&
            typeof doc.metadata === "object" &&
            doc.metadata !== null
          ) {
            const metadata = doc.metadata as DocumentMetadata;

            // Try to extract legacy analysis for the sidebar components
            if (metadata.analysis && typeof metadata.analysis === "object") {
              const analysis = metadata.analysis as Record<string, unknown>;
              if (analysis.documentType && analysis.extractedFields) {
                setLegacyAnalysis({
                  documentType: analysis.documentType as string,
                  extractedFields: analysis.extractedFields as Record<
                    string,
                    unknown
                  >,
                  warnings: (analysis.warnings as string[]) || [],
                  confidence: analysis.confidence as number,
                });
              } else if (
                analysis.identification &&
                typeof analysis.identification === "object"
              ) {
                const identification = analysis.identification as Record<
                  string,
                  unknown
                >;
                setLegacyAnalysis({
                  documentType:
                    (identification.documentType as string) || "unknown",
                  extractedFields:
                    (identification.extractedFields as Record<
                      string,
                      unknown
                    >) || {},
                  warnings: (identification.warnings as string[]) || [],
                  confidence: identification.confidence as number,
                });
              } else {
                setLegacyAnalysis(analysis as LegacyAnalysis);
              }
            } else if (metadata.identification) {
              setLegacyAnalysis({
                documentType: metadata.identification.documentType || "unknown",
                extractedFields: metadata.identification.extractedFields || {},
                warnings: metadata.identification.warnings || [],
                confidence: metadata.identification.confidence,
              });
            }
          }
        } catch (error) {
          console.error("Failed to load document:", error);
          toast({
            title: "Error loading document",
            description: "Failed to load the document. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadDocument();
  }, [id, getDocument, toast]);

  const handleDelete = async () => {
    if (!document) return;

    try {
      await deleteAndRefresh(document.id);

      toast({
        title: "Document deleted",
        description: "The document has been successfully removed.",
        duration: 3000,
      });

      navigate("/demo/documents");
    } catch (error) {
      console.error("Error deleting document:", error);
      toast({
        title: "Error deleting document",
        description: "Failed to delete the document. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  const handleAnalyze = async () => {
    if (!document) return;

    setIsAnalyzing(true);

    try {
      // Fetch the document file from the URL
      const response = await fetch(document.url);
      if (!response.ok) {
        throw new Error("Failed to fetch document file");
      }

      const blob = await response.blob();
      const file = new File([blob], document.name, {
        type: document.content_type,
      });

      // Use the consolidated hook method to re-analyze and update the document
      const updatedDocument = await reAnalyzeDocument(document.id, file);

      if (updatedDocument) {
        // Update local document state
        setDocument(updatedDocument);

        // Update analysis states based on the new metadata
        if (
          updatedDocument.metadata &&
          typeof updatedDocument.metadata === "object" &&
          updatedDocument.metadata !== null
        ) {
          const metadata = updatedDocument.metadata as DocumentMetadata;

          // Try to extract legacy analysis for the sidebar components
          if (metadata.analysis && typeof metadata.analysis === "object") {
            const analysis = metadata.analysis as Record<string, unknown>;
            if (analysis.documentType && analysis.extractedFields) {
              setLegacyAnalysis({
                documentType: analysis.documentType as string,
                extractedFields: analysis.extractedFields as Record<
                  string,
                  unknown
                >,
                warnings: (analysis.warnings as string[]) || [],
                confidence: analysis.confidence as number,
              });
            } else if (
              analysis.identification &&
              typeof analysis.identification === "object"
            ) {
              const identification = analysis.identification as Record<
                string,
                unknown
              >;
              setLegacyAnalysis({
                documentType:
                  (identification.documentType as string) || "unknown",
                extractedFields:
                  (identification.extractedFields as Record<string, unknown>) ||
                  {},
                warnings: (identification.warnings as string[]) || [],
                confidence: identification.confidence as number,
              });
            } else {
              setLegacyAnalysis(analysis as LegacyAnalysis);
            }
          } else if (metadata.identification) {
            setLegacyAnalysis({
              documentType: metadata.identification.documentType || "unknown",
              extractedFields: metadata.identification.extractedFields || {},
              warnings: metadata.identification.warnings || [],
              confidence: metadata.identification.confidence,
            });
          }
        }

        toast({
          title: "Analysis Complete",
          description: "Document has been analyzed successfully.",
          duration: 3000,
        });
      }
    } catch (error) {
      console.error("Error analyzing document:", error);
      toast({
        title: "Analysis Failed",
        description: "Failed to analyze the document. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (!document) {
    return (
      <div className="container py-8">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/demo/documents")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Document not found</h1>
            <p className="text-muted-foreground">
              The requested document could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Map Supabase document to expected format for components
  const mappedDocument = {
    name: document.name,
    content_type: document.content_type,
    size: document.size,
    created_at: document.created_at,
    updated_at: document.updated_at,
    type: document.type,
    metadata: document.metadata as
      | { fileId?: string; cachePath?: string; scan_method?: string }
      | undefined,
  };

  // Ensure analysis has proper structure for components (legacy compatibility)
  const analysisData =
    legacyAnalysis && typeof legacyAnalysis === "object"
      ? {
          ...legacyAnalysis,
          warnings: Array.isArray(legacyAnalysis.warnings)
            ? legacyAnalysis.warnings
            : [],
        }
      : null;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
    });
  };

  return (
    <div className="container grid max-w-full grid-cols-1 gap-4 px-4 py-4 md:px-6 md:py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate("/demo/documents")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{document.name}</h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>{document.content_type}</span>
            <span>•</span>
            <span>{formatFileSize(document.size)}</span>
            <span>•</span>
            <Clock className="h-4 w-4" />
            <span>{formatDate(document.created_at)}</span>
          </div>
        </div>
        <div className="ml-auto flex gap-2">
          <Button
            variant="destructive"
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
          <Button asChild>
            <a
              href={document.url}
              download={document.name}
              target="_blank"
              rel="noreferrer"
            >
              <Download className="mr-2 h-4 w-4" />
              Download
            </a>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="pb-0">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                  <TabsTrigger value="analysis">Analysis</TabsTrigger>
                </TabsList>
                <TabsContent value="preview" className="mt-6">
                  <DocumentPreviewPane document={document} />
                </TabsContent>
                <TabsContent value="analysis" className="mt-6">
                  <DocumentTemplate
                    rawMetadata={
                      document?.metadata
                        ? (document.metadata as Record<string, unknown>)
                        : null
                    }
                    legacyAnalysis={analysisData}
                    onRefresh={handleAnalyze}
                    isRefreshing={isAnalyzing}
                  />
                </TabsContent>
              </Tabs>
            </CardHeader>
          </Card>
        </div>

        <div>
          <DocumentInfoCard document={mappedDocument} analysis={analysisData} />

          {analysisData?.extractedFields &&
            Object.keys(analysisData.extractedFields).length > 0 && (
              <DocumentExtractedFields fields={analysisData.extractedFields} />
            )}

          {analysisData?.warnings && analysisData.warnings.length > 0 && (
            <DocumentWarnings warnings={analysisData.warnings} />
          )}
        </div>
      </div>

      <DialogConfirmation
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title={`Delete Document: ${document.name}`}
        description="Are you sure you want to delete this document? This action cannot be undone."
        onClick={handleDelete}
        Icon={Trash2}
        variant="destructive"
        action="Delete"
        cancel="Cancel"
        useTrigger={false}
      />
    </div>
  );
}
