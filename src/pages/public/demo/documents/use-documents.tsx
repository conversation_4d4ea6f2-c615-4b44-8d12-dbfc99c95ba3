import { useCallback } from "react";
import { useNavigate } from "react-router";

import type { Document, DocumentContextValue } from "./document-context";

import {
  useDocumentContext,
  useDocumentDetails,
  useDocumentList,
  useDocumentUpload,
} from "./document-context";

// Re-export all context hooks
export {
  useDocumentContext,
  useDocumentUpload,
  useDocumentList,
  useDocumentDetails,
  type DocumentContextValue,
  type Document,
};

// Enhanced hook for document operations with navigation
export function useDocumentOperations() {
  const navigate = useNavigate();
  const context = useDocumentContext();

  const uploadAndNavigate = useCallback(
    async (
      file: File,
      options?: {
        scanMethod?: string;
        description?: string;
        navigateToDocument?: boolean;
        navigateToList?: boolean;
      },
    ) => {
      const {
        navigateToDocument = false,
        navigateToList = false,
        ...uploadOptions
      } = options || {};

      try {
        const result = await context.processAndStoreDocument({
          file,
          ...uploadOptions,
        });

        if (result?.document) {
          if (navigateToDocument) {
            navigate(`/demo/documents/${result.document.id}`);
          } else if (navigateToList) {
            navigate("/demo/documents");
          }
        }

        return result;
      } catch (error) {
        console.error("Upload and navigate failed:", error);
        throw error;
      }
    },
    [context.processAndStoreDocument, navigate],
  );

  const deleteAndRefresh = useCallback(
    async (documentId: string) => {
      const success = await context.deleteDocument(documentId);
      if (success) {
        await context.fetchDocuments();
      }
      return success;
    },
    [context.deleteDocument, context.fetchDocuments],
  );

  const clearAllAndRefresh = useCallback(async () => {
    const success = await context.clearAllDocuments();
    if (success) {
      await context.fetchDocuments();
    }
    return success;
  }, [context.clearAllDocuments, context.fetchDocuments]);

  return {
    ...context,
    uploadAndNavigate,
    deleteAndRefresh,
    clearAllAndRefresh,
  };
}

// Hook for upload progress monitoring
export function useUploadProgress() {
  const {
    uploadProgress,
    hasActiveUploads,
    completedUploads,
    failedUploads,
    totalUploads,
  } = useDocumentUpload();

  const getUploadStatus = useCallback(
    (fileName: string) => {
      return uploadProgress.find((upload) => upload.fileName === fileName);
    },
    [uploadProgress],
  );

  const getOverallProgress = useCallback(() => {
    if (totalUploads === 0) return 0;
    return Math.round((completedUploads / totalUploads) * 100);
  }, [completedUploads, totalUploads]);

  const hasErrors = failedUploads > 0;
  const isComplete =
    totalUploads > 0 && completedUploads === totalUploads && !hasActiveUploads;

  return {
    uploadProgress,
    hasActiveUploads,
    completedUploads,
    failedUploads,
    totalUploads,
    hasErrors,
    isComplete,
    getUploadStatus,
    getOverallProgress,
  };
}

// Hook for document filtering and searching
export function useDocumentSearch() {
  const { documents } = useDocumentList();

  const searchDocuments = useCallback(
    (query: string) => {
      if (!query.trim()) return documents;

      const searchLower = query.toLowerCase();
      return documents.filter(
        (document) =>
          document.id.toLowerCase().includes(searchLower) ||
          document.name.toLowerCase().includes(searchLower) ||
          document.type.toLowerCase().includes(searchLower) ||
          (
            document.metadata as { original_filename?: string } | null
          )?.original_filename
            ?.toLowerCase()
            .includes(searchLower),
      );
    },
    [documents],
  );

  const filterByType = useCallback(
    (type: string) => {
      return documents.filter((document) => document.type === type);
    },
    [documents],
  );

  const filterByDateRange = useCallback(
    (startDate: Date, endDate: Date) => {
      return documents.filter((document) => {
        const docDate = new Date(document.created_at);
        return docDate >= startDate && docDate <= endDate;
      });
    },
    [documents],
  );

  const getDocumentTypes = useCallback(() => {
    const types = new Set(documents.map((doc) => doc.type));
    return Array.from(types);
  }, [documents]);

  return {
    documents,
    searchDocuments,
    filterByType,
    filterByDateRange,
    getDocumentTypes,
  };
}

// Hook for document statistics
export function useDocumentStats() {
  const { documents } = useDocumentList();

  const stats = {
    total: documents.length,
    byType: documents.reduce(
      (acc, doc) => {
        acc[doc.type] = (acc[doc.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    ),
    totalSize: documents.reduce((acc, doc) => acc + (doc.size || 0), 0),
    recentUploads: documents.filter((doc) => {
      const uploadDate = new Date(doc.created_at);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return uploadDate > weekAgo;
    }).length,
  };

  return stats;
}

// Utility functions
export const documentUtils = {
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },

  getDocumentTypeLabel: (type: string): string => {
    return type
      .replace(/_/g, " ")
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  },

  isImageDocument: (document: Document): boolean => {
    return document.content_type?.startsWith("image/") || false;
  },

  isPdfDocument: (document: Document): boolean => {
    return document.content_type === "application/pdf";
  },

  getDocumentIcon: (document: Document): string => {
    if (documentUtils.isImageDocument(document)) return "🖼️";
    if (documentUtils.isPdfDocument(document)) return "📄";
    return "📎";
  },
};
