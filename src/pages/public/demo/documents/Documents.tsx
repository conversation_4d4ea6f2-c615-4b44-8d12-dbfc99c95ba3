import { useState } from "react";
import { FileText, Plus, <PERSON>, Sparkles, Trash2, Upload } from "lucide-react";
import { <PERSON> } from "react-router";

import DocumentFormDialog from "@/components/documents/DocumentFormDialog";
import ListDocuments from "@/components/documents/listing";
import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useDocumentList, useDocumentOperations } from "./use-documents";

type DocumentFormValues = {
  templateData: {
    shipment: { number: string };
  };
};

export default function Documents() {
  const [isClearDialogOpen, setIsClearDialogOpen] = useState(false);
  const [isDocumentDialogOpen, setIsDocumentDialogOpen] = useState(false);
  const { documents, isLoading } = useDocumentList();
  const { clearAllAndRefresh, deleteDocument } = useDocumentOperations();
  const { toast } = useToast();

  const handleCreateDocument = (values: DocumentFormValues) => {
    // This would integrate with the document creation system
    // For now, showing a placeholder toast
    toast({
      title: "Feature Coming Soon",
      description: "Document creation from templates will be available soon.",
      duration: 3000,
    });
    setIsDocumentDialogOpen(false);
  };

  const handleClearAll = async () => {
    try {
      await clearAllAndRefresh();
      toast({
        title: "All documents cleared",
        description: "All documents have been removed from the system",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "Error clearing documents",
        description: "Failed to clear all documents. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <div className="container grid max-w-full grid-cols-1 gap-4 px-4 py-4 md:px-6 md:py-8">
      <div className="mb-6 flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-2xl font-bold md:text-3xl">Documents</h1>
          <p className="text-muted-foreground mt-1 text-sm md:text-base">
            Public demo of the document management system
          </p>
        </div>
        {/* <Button variant="outline" asChild className="w-full md:w-auto">
          <Link to="/request-demo">
            <Info className="mr-2 h-4 w-4" />
            Request Full Demo
          </Link>
        </Button> */}
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>About Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm md:text-base">
            QuikSkope's document management system enables drivers to quickly
            scan, process, and store important documents. The system extracts
            and validates key information using AI, ensuring data accuracy and
            compliance.
          </p>
          <div className="mt-4 flex flex-wrap items-center gap-4 text-xs md:gap-6 md:text-sm">
            <div className="flex items-center">
              <Shield className="text-primary mr-2 h-4 w-4" />
              <span>Secure Document Management</span>
            </div>
            <div className="flex items-center">
              <FileText className="text-primary mr-2 h-4 w-4" />
              <span>AI-Powered Document Analysis</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2">
        <Button
          onClick={() => setIsDocumentDialogOpen(true)}
          disabled={true}
          className="hover:bg-muted/50 h-32 flex-col gap-3 p-6 text-lg font-semibold shadow-md transition-all hover:shadow-lg disabled:opacity-50"
        >
          {isLoading ? (
            <>
              <Sparkles className="size-16 animate-pulse" />
              <span>Creating...</span>
            </>
          ) : (
            <>
              <Plus className="size-16" />
              <span>Create Document</span>
              <span className="text-sm font-normal opacity-80">
                Build a new document from templates
              </span>
            </>
          )}
        </Button>
        <Button
          variant="outline"
          asChild={true}
          disabled={true}
          className="hover:bg-muted/50 h-32 flex-col gap-3 p-6 text-lg font-semibold shadow-md transition-all hover:shadow-lg disabled:opacity-50"
        >
          <Link to="/demo/documents/scan">
            <Upload className="size-16" />
            <span>Scan Document</span>
            <span className="text-sm font-normal opacity-80">
              Upload and analyze existing documents
            </span>
          </Link>
        </Button>
      </div>

      <ListDocuments
        documents={{
          documents: documents.map((document) => ({
            ...document,
            organization: {
              id: document.organization_id,
              name: "Demo Organization",
            },
          })),
          total: documents.length,
        }}
        loading={isLoading}
        defaultPageSize={10}
        defaultPageIndex={0}
        onDelete={deleteDocument}
      />

      <DocumentFormDialog
        title="Create New Document"
        description="Select a document type to begin"
        onSubmit={handleCreateDocument}
        isOpen={isDocumentDialogOpen}
        onOpenChange={setIsDocumentDialogOpen}
      />

      <DialogConfirmation
        open={isClearDialogOpen}
        onOpenChange={setIsClearDialogOpen}
        title="Clear All Documents"
        description="Are you sure you want to delete all documents? This action cannot be undone."
        onClick={handleClearAll}
        Icon={Trash2}
        variant="destructive"
        action="Clear All"
        cancel="Cancel"
        useTrigger={false}
      />
    </div>
  );
}
