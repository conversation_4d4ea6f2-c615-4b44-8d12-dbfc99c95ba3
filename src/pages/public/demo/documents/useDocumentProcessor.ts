import { useState } from "react";

import { useToast } from "@/components/ui/use-toast";
import { useDocumentsStore } from "@/lib/store/useDocumentsStore";
import { supabase } from "@/supabase/client";

interface ProcessDocumentOptions {
  file: File;
  previewUrl?: string | null;
  scanMethod?: string;
}

interface DocumentProcessResult {
  success: boolean;
  filePackage?: {
    originalName: string;
    sanitizedName: string;
    contentType: string;
    fileSize: number;
    fileExtension: string;
    fileId: string;
    filePath: string;
  };
  identification?: {
    documentType: string;
    confidence: number;
    extractedFields: Record<string, unknown>;
    pageCount?: number;
    warnings: string[];
  };
  analysis?: string;
  [key: string]: unknown;
}

export function useDocumentProcessor() {
  const { toast } = useToast();
  const { addDocument } = useDocumentsStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingResult, setProcessingResult] =
    useState<DocumentProcessResult | null>(null);

  const processDocument = async (options: ProcessDocumentOptions) => {
    const { file, previewUrl, scanMethod = "upload" } = options;

    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a file to process",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Create form data for file upload
      const formData = new FormData();
      formData.append("file", file);

      console.log("Starting document processing...");

      // Call Supabase Edge Function
      const { data, error } = await supabase.functions.invoke(
        "process-document",
        {
          body: formData,
        },
      );

      if (error) {
        console.error("Error from process-document function:", error);
        throw error;
      }

      console.log("Document processing result:", data);
      setProcessingResult(data);

      // Parse analysis if it's a string to get ProcessedDocumentData
      let processedDocumentData = null;
      if (data.analysis && typeof data.analysis === "string") {
        try {
          processedDocumentData = JSON.parse(data.analysis);
        } catch (e) {
          console.warn("Could not parse analysis JSON:", e);
        }
      }

      // Extract data from new format structure
      const fileInfo = data.filePackage || {};
      const identification = data.identification || {};

      // Create a document entry with analysis results
      const document = {
        id: `doc-${Math.random().toString(36).substring(2, 10)}`,
        name: fileInfo.sanitizedName || file.name,
        type: "scanned",
        content_type: fileInfo.contentType || file.type,
        size: fileInfo.fileSize || file.size,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        url: previewUrl || "",
        storage_path: fileInfo.filePath || `documents/${file.name}`,
        metadata: {
          description: "Document scanned by user",
          scan_method: scanMethod,
          original_filename: fileInfo.originalName || file.name,

          // Store the rich ProcessedDocumentData if available
          ...(processedDocumentData
            ? {
                processedData: processedDocumentData,
              }
            : {}),

          // Store file package info
          ...(data.filePackage
            ? {
                filePackage: data.filePackage,
              }
            : {}),

          // Store identification info
          ...(data.identification
            ? {
                identification: data.identification,
              }
            : {}),

          // Legacy fields for backward compatibility
          documentType: identification.documentType,
          confidence: identification.confidence,
          extractedFields: identification.extractedFields,
          warnings: identification.warnings,
          analysis: processedDocumentData || data.analysis,
        },
      };

      // Add document to the store
      addDocument(document);

      // Show success toast
      toast({
        title: "Document Processed",
        description:
          "Your document has been scanned and analyzed successfully.",
      });

      return { document, result: data };
    } catch (error) {
      console.error("Error processing document:", error);
      toast({
        title: "Processing Failed",
        description:
          "There was an error processing your document. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  const resetProcessing = () => {
    setProcessingResult(null);
    setIsProcessing(false);
  };

  return {
    processDocument,
    isProcessing,
    processingResult,
    resetProcessing,
  };
}
