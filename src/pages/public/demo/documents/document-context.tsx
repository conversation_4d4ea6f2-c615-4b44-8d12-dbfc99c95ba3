import React, { createContext, ReactNode, useContext } from "react";

import type { Tables } from "@/supabase/types";

import { useSupabaseDocuments } from "./useSupabaseDocuments";

// Types
type Document = Tables<"documents">;

interface DocumentContextValue {
  // Data
  documents: Document[];
  uploadProgress: Array<{
    fileName: string;
    progress: number;
    status: "uploading" | "processing" | "complete" | "error";
  }>;
  processingResult: unknown | null;

  // Loading states
  isLoading: boolean;
  isProcessing: boolean;

  // Operations
  processAndStoreDocument: (options: {
    file: File;
    scanMethod?: string;
    description?: string;
  }) => Promise<{ document: Document; analysisResult: unknown } | null>;
  fetchDocuments: () => Promise<void>;
  getDocument: (id: string) => Promise<Document | null>;
  deleteDocument: (id: string) => Promise<boolean>;
  clearAllDocuments: () => Promise<boolean>;
  resetProcessing: () => void;

  // Computed values
  hasActiveUploads: boolean;
  completedUploads: number;
  failedUploads: number;
  totalUploads: number;
}

// Create context
const DocumentContext = createContext<DocumentContextValue | undefined>(
  undefined,
);

// Provider props
interface DocumentProviderProps {
  children: ReactNode;
}

// Provider component
export function DocumentProvider({ children }: DocumentProviderProps) {
  const supabaseDocuments = useSupabaseDocuments();

  // Computed values for upload tracking
  const hasActiveUploads = supabaseDocuments.uploadProgress.some(
    (upload) => upload.status === "uploading" || upload.status === "processing",
  );

  const completedUploads = supabaseDocuments.uploadProgress.filter(
    (upload) => upload.status === "complete",
  ).length;

  const failedUploads = supabaseDocuments.uploadProgress.filter(
    (upload) => upload.status === "error",
  ).length;

  const totalUploads = supabaseDocuments.uploadProgress.length;

  const contextValue: DocumentContextValue = {
    // Data from hook
    documents: supabaseDocuments.documents,
    uploadProgress: supabaseDocuments.uploadProgress,
    processingResult: supabaseDocuments.processingResult,

    // Loading states
    isLoading: supabaseDocuments.isLoading,
    isProcessing: supabaseDocuments.isProcessing,

    // Operations
    processAndStoreDocument: supabaseDocuments.processAndStoreDocument,
    fetchDocuments: supabaseDocuments.fetchDocuments,
    getDocument: supabaseDocuments.getDocument,
    deleteDocument: supabaseDocuments.deleteDocument,
    clearAllDocuments: supabaseDocuments.clearAllDocuments,
    resetProcessing: supabaseDocuments.resetProcessing,

    // Computed values
    hasActiveUploads,
    completedUploads,
    failedUploads,
    totalUploads,
  };

  return (
    <DocumentContext.Provider value={contextValue}>
      {children}
    </DocumentContext.Provider>
  );
}

// Hook to use the context
export function useDocumentContext() {
  const context = useContext(DocumentContext);

  if (context === undefined) {
    throw new Error(
      "useDocumentContext must be used within a DocumentProvider",
    );
  }

  return context;
}

// Additional hooks for specific use cases
export function useDocumentUpload() {
  const context = useDocumentContext();

  return {
    processAndStoreDocument: context.processAndStoreDocument,
    uploadProgress: context.uploadProgress,
    isProcessing: context.isProcessing,
    hasActiveUploads: context.hasActiveUploads,
    completedUploads: context.completedUploads,
    failedUploads: context.failedUploads,
    totalUploads: context.totalUploads,
    resetProcessing: context.resetProcessing,
  };
}

export function useDocumentList() {
  const context = useDocumentContext();

  return {
    documents: context.documents,
    isLoading: context.isLoading,
    fetchDocuments: context.fetchDocuments,
    deleteDocument: context.deleteDocument,
    clearAllDocuments: context.clearAllDocuments,
  };
}

export function useDocumentDetails(documentId?: string) {
  const context = useDocumentContext();

  // Find document in current list or fetch if needed
  const document = documentId
    ? context.documents.find((doc) => doc.id === documentId)
    : null;

  return {
    document,
    getDocument: context.getDocument,
    deleteDocument: context.deleteDocument,
    isLoading: context.isLoading,
  };
}

// Export types for external use
export type { DocumentContextValue, Document };
