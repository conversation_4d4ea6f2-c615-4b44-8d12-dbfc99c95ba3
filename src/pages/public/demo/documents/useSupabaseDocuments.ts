import { useCallback, useEffect, useState } from "react";

import type { Enums, Json, Tables, TablesInsert } from "@/supabase/types";

import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/supabase/client";

type Document = Tables<"documents">;
type DocumentInsert = TablesInsert<"documents">;

interface ProcessDocumentOptions {
  file: File;
  scanMethod?: string;
  description?: string;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: "uploading" | "processing" | "complete" | "error";
}

export function useSupabaseDocuments() {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [processingResult, setProcessingResult] = useState<unknown | null>(
    null,
  );

  // Fetch documents from Supabase
  const fetchDocuments = useCallback(async () => {
    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from("documents")
        .select("*")
        .eq("bucket_id", "demo")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching documents:", error);
        toast({
          title: "Error fetching documents",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      setDocuments(data || []);
    } catch (error) {
      console.error("Error in fetchDocuments:", error);
      toast({
        title: "Error",
        description: "Failed to fetch documents",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Auto-fetch documents on mount
  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  // Upload file to Supabase storage
  const uploadFile = useCallback(async (file: File): Promise<string | null> => {
    const fileName = `${Date.now()}-${file.name}`;
    const filePath = `demo/${fileName}`;

    try {
      // Update upload progress
      setUploadProgress((prev) => [
        ...prev,
        {
          fileName: file.name,
          progress: 0,
          status: "uploading",
        },
      ]);

      console.log(`Attempting to upload file to: ${filePath}`);
      console.log(`File details:`, {
        name: file.name,
        size: file.size,
        type: file.type,
      });

      const { data, error } = await supabase.storage
        .from("demo")
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: true,
        });

      if (error) {
        console.error("Supabase storage upload error:", error);
        console.error("Error details:", {
          message: error.message,
        });

        setUploadProgress((prev) =>
          prev.map((p) =>
            p.fileName === file.name ? { ...p, status: "error" as const } : p,
          ),
        );

        // Re-throw the original error instead of swallowing it
        throw new Error(`Storage upload failed: ${error.message}`);
      }

      console.log("Upload successful, data:", data);

      // Update progress to complete
      setUploadProgress((prev) =>
        prev.map((p) =>
          p.fileName === file.name
            ? { ...p, progress: 100, status: "complete" as const }
            : p,
        ),
      );

      // Get public URL
      const { data: urlData } = supabase.storage
        .from("demo")
        .getPublicUrl(data.path);

      console.log("Public URL generated:", urlData.publicUrl);
      return urlData.publicUrl;
    } catch (error) {
      console.error("Error in uploadFile:", error);

      // Update progress to error state
      setUploadProgress((prev) =>
        prev.map((p) =>
          p.fileName === file.name ? { ...p, status: "error" as const } : p,
        ),
      );

      // Re-throw the error instead of returning null
      throw error;
    }
  }, []);

  // Process document with AI analysis
  const processDocumentAnalysis = useCallback(async (file: File) => {
    try {
      setUploadProgress((prev) =>
        prev.map((p) =>
          p.fileName === file.name
            ? { ...p, status: "processing" as const }
            : p,
        ),
      );

      const formData = new FormData();
      formData.append("file", file);

      const { data, error } = await supabase.functions.invoke(
        "process-document",
        { body: formData },
      );

      if (error) {
        console.error("Error from process-document function:", error);
        throw error;
      }

      setProcessingResult(data);

      // Parse the analysis field to get the ProcessedDocumentData
      let processedDocumentData = null;
      if (data.analysis && typeof data.analysis === "string") {
        try {
          processedDocumentData = JSON.parse(data.analysis);
        } catch (e) {
          console.warn("Could not parse analysis JSON:", e);
        }
      }

      // Return the rich structured data for storage
      return {
        // New format - rich ProcessedDocumentData structure
        processedData: processedDocumentData,

        // File package info from new format
        filePackage: data.filePackage,
        identification: data.identification,

        // Legacy compatibility fields (for existing components that might need them)
        documentType: data.identification?.documentType,
        confidence: data.identification?.confidence,
        extractedFields: data.identification?.extractedFields,
        warnings: data.identification?.warnings,
        success: data.success,
      };
    } catch (error) {
      console.error("Error processing document:", error);
      throw error;
    }
  }, []);

  // Create document in database
  const createDocument = useCallback(
    async (documentData: DocumentInsert): Promise<Document | null> => {
      try {
        const { data, error } = await supabase
          .from("documents")
          .insert([documentData])
          .select()
          .single();

        if (error) {
          console.error("Error creating document:", error);
          throw error;
        }

        return data;
      } catch (error) {
        console.error("Error in createDocument:", error);
        return null;
      }
    },
    [],
  );

  // Main function to process and store document
  const processAndStoreDocument = useCallback(
    async (options: ProcessDocumentOptions) => {
      const {
        file,
        scanMethod = "upload",
        description = "Document uploaded by user",
      } = options;

      if (!file) {
        toast({
          title: "No file selected",
          description: "Please select a file to process",
          variant: "destructive",
        });
        return null;
      }

      try {
        setIsProcessing(true);

        // Step 1: Upload file to storage
        const fileUrl = await uploadFile(file);
        if (!fileUrl) {
          throw new Error("Failed to upload file");
        }

        // Step 2: Process document with AI (optional)
        let analysisResult = null;
        try {
          analysisResult = await processDocumentAnalysis(file);
        } catch (analysisError) {
          console.warn(
            "Document analysis failed, continuing without analysis:",
            analysisError,
          );
        }

        // Step 3: Create document record in database
        // Map AI-identified types to database enum values
        const mapDocumentTypeToDatabase = (
          aiType: string,
        ): Enums<"document_type"> => {
          // Store specific AI type in metadata, use generic database enum
          switch (aiType) {
            case "bill_of_lading":
            case "delivery_order":
            case "proof_of_delivery":
            case "manifest":
              return "manifest";
            case "commercial_invoice":
            case "freight_bill":
              return "contract";
            default:
              return "general";
          }
        };

        const databaseDocumentType = analysisResult?.documentType
          ? mapDocumentTypeToDatabase(analysisResult.documentType)
          : "general";

        console.log("Document type mapping:", {
          aiIdentified: analysisResult?.documentType,
          databaseType: databaseDocumentType,
          confidence: analysisResult?.confidence,
        });

        const documentData: DocumentInsert = {
          name: file.name,
          type: databaseDocumentType,
          content_type: file.type,
          size: file.size,
          url: fileUrl,
          storage_path: `demo/${Date.now()}-${file.name}`,
          bucket_id: "demo",
          organization_id: null,
          created_by: null,
          driver_id: null,
          latitude: null,
          longitude: null,
          metadata: {
            description,
            scan_method: scanMethod,
            original_filename: file.name,

            // Store the AI-identified document type in metadata (the important part!)
            ...(analysisResult?.documentType
              ? {
                  ai_document_type: analysisResult.documentType,
                  ai_confidence: analysisResult.confidence,
                }
              : {}),

            // Store the rich ProcessedDocumentData if available
            ...(analysisResult?.processedData
              ? {
                  processedData: analysisResult.processedData,
                }
              : {}),

            // Store file package info
            ...(analysisResult?.filePackage
              ? {
                  filePackage: analysisResult.filePackage,
                }
              : {}),

            // Store identification info
            ...(analysisResult?.identification
              ? {
                  identification: analysisResult.identification,
                }
              : {}),

            // Legacy compatibility (for older components)
            ...(analysisResult
              ? {
                  analysis: analysisResult,
                }
              : {}),
          } as Json,
        };

        const document = await createDocument(documentData);

        if (!document) {
          throw new Error("Failed to create document record");
        }

        // Step 4: Update local state
        setDocuments((prev) => [document, ...prev]);

        // Step 5: Clear upload progress
        setUploadProgress((prev) =>
          prev.filter((p) => p.fileName !== file.name),
        );

        // Show success toast
        toast({
          title: "Document Processed",
          description:
            "Your document has been uploaded and processed successfully.",
        });

        return { document, analysisResult };
      } catch (error) {
        console.error("Error processing document:", error);

        // Clear failed upload from progress
        setUploadProgress((prev) =>
          prev.filter((p) => p.fileName !== file.name),
        );

        toast({
          title: "Processing Failed",
          description:
            "There was an error processing your document. Please try again.",
          variant: "destructive",
        });
        throw error;
      } finally {
        setIsProcessing(false);
      }
    },
    [toast, uploadFile, processDocumentAnalysis, createDocument],
  );

  // Get document by ID
  const getDocument = useCallback(
    async (id: string): Promise<Document | null> => {
      try {
        const { data, error } = await supabase
          .from("documents")
          .select("*")
          .eq("id", id)
          .single();

        if (error) {
          console.error("Error fetching document:", error);
          return null;
        }

        return data;
      } catch (error) {
        console.error("Error in getDocument:", error);
        return null;
      }
    },
    [],
  );

  // Delete document
  const deleteDocument = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        // First get the document to find its storage path
        const document = await getDocument(id);

        if (document?.storage_path) {
          // Delete from storage
          const { error: storageError } = await supabase.storage
            .from("demo")
            .remove([document.storage_path]);

          if (storageError) {
            console.warn("Error deleting file from storage:", storageError);
          }
        }

        // Delete from database
        const { error } = await supabase
          .from("documents")
          .delete()
          .eq("id", id);

        if (error) {
          console.error("Error deleting document:", error);
          toast({
            title: "Error deleting document",
            description: error.message,
            variant: "destructive",
          });
          return false;
        }

        // Update local state
        setDocuments((prev) => prev.filter((doc) => doc.id !== id));

        toast({
          title: "Document deleted",
          description: "The document has been successfully deleted.",
        });

        return true;
      } catch (error) {
        console.error("Error in deleteDocument:", error);
        toast({
          title: "Error",
          description: "Failed to delete document",
          variant: "destructive",
        });
        return false;
      }
    },
    [getDocument, toast],
  );

  // Clear all demo documents
  const clearAllDocuments = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Get all documents first
      const { data: docsToDelete, error: fetchError } = await supabase
        .from("documents")
        .select("storage_path");

      if (fetchError) {
        throw fetchError;
      }

      // Delete files from storage
      if (docsToDelete && docsToDelete.length > 0) {
        const storagePaths = docsToDelete
          .map((doc) => doc.storage_path)
          .filter(Boolean);

        if (storagePaths.length > 0) {
          const { error: storageError } = await supabase.storage
            .from("demo")
            .remove(storagePaths);

          if (storageError) {
            console.warn("Error deleting files from storage:", storageError);
          }
        }
      }

      // Delete all documents from database
      const { error } = await supabase.from("documents").delete().neq("id", ""); // Delete all records

      if (error) {
        throw error;
      }

      // Clear local state
      setDocuments([]);
      setUploadProgress([]);
      setProcessingResult(null);

      toast({
        title: "All documents cleared",
        description: "All documents have been successfully deleted.",
      });

      return true;
    } catch (error) {
      console.error("Error clearing documents:", error);
      toast({
        title: "Error",
        description: "Failed to clear all documents",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Reset processing state
  const resetProcessing = useCallback(() => {
    setProcessingResult(null);
    setIsProcessing(false);
    setUploadProgress([]);
  }, []);

  // Re-analyze an existing document and update its metadata
  const reAnalyzeDocument = useCallback(
    async (documentId: string, file: File): Promise<Document | null> => {
      try {
        // Step 1: Get current document to preserve existing metadata
        const currentDoc = await getDocument(documentId);
        if (!currentDoc) {
          throw new Error("Document not found");
        }

        console.log("Current document for reanalysis:", {
          id: currentDoc.id,
          bucket_id: currentDoc.bucket_id,
          name: currentDoc.name,
        });

        // Step 2: Process document analysis
        const analysisResult = await processDocumentAnalysis(file);

        if (!analysisResult) {
          throw new Error("Analysis failed to return results");
        }

        // Step 3: Prepare metadata update based on analysis result format
        let metadataUpdate: Record<string, unknown>;

        // Use the new structured format
        if (analysisResult.processedData) {
          metadataUpdate = {
            processedData: analysisResult.processedData,
            filePackage: analysisResult.filePackage,
            identification: analysisResult.identification,
            analysis: analysisResult, // Keep full result for legacy compatibility
          };
        } else {
          // Legacy format fallback
          const legacyAnalysis =
            typeof analysisResult === "string"
              ? JSON.parse(analysisResult)
              : analysisResult;
          metadataUpdate = {
            analysis: legacyAnalysis,
          };
        }

        const currentMetadata =
          typeof currentDoc.metadata === "object" &&
          currentDoc.metadata !== null
            ? (currentDoc.metadata as Record<string, unknown>)
            : {};

        // Add AI document type to metadata for the update as well
        const enhancedMetadataUpdate = {
          ...metadataUpdate,
          // Store the AI-identified document type in metadata
          ...(analysisResult?.documentType
            ? {
                ai_document_type: analysisResult.documentType,
                ai_confidence: analysisResult.confidence,
              }
            : {}),
        };

        // Step 4: Update document in database with the new metadata
        const { data: updatedDoc, error } = await supabase
          .from("documents")
          .update({
            metadata: {
              ...currentMetadata,
              ...enhancedMetadataUpdate,
            } as Json,
          })
          .eq("id", documentId)
          .select()
          .single();

        if (error) {
          throw error;
        }

        // Step 5: Update local state if this document is in our current list
        setDocuments((prev) =>
          prev.map((doc) => (doc.id === documentId ? updatedDoc : doc)),
        );

        return updatedDoc;
      } catch (error) {
        console.error("Error re-analyzing document:", error);
        throw error;
      }
    },
    [processDocumentAnalysis, getDocument],
  );

  return {
    // Data
    documents,
    uploadProgress,
    processingResult,

    // Loading states
    isLoading,
    isProcessing,

    // Main operations
    processAndStoreDocument,
    processDocumentAnalysis,
    reAnalyzeDocument,
    fetchDocuments,
    getDocument,
    deleteDocument,
    clearAllDocuments,

    // Utilities
    resetProcessing,
  };
}
