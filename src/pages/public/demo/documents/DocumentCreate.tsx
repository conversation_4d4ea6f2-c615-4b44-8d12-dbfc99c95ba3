import { useState } from "react";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { ArrowLeft, Info } from "lucide-react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router";
import * as z from "zod";

import { DocumentTypeBadge } from "@/components/common/types/DocumentType";
import { FileUploadField } from "@/components/forms/fields/FileUpload";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";

// Form schema validation
const documentSchema = z.object({
  name: z.string().min(3, "Document name must be at least 3 characters"),
  type: z.enum(
    ["manifest", "contract", "general", "verification", "other"] as const,
    {
      required_error: "Please select a document type",
    },
  ),
  description: z.string().optional(),
  file: z.instanceof(File, { message: "Please upload a file" }).optional(),
  metadata: z.record(z.string()).optional(),
});

type DocumentFormValues = z.infer<typeof documentSchema>;

// Document type options
const documentTypes: {
  value: "manifest" | "contract" | "general" | "verification" | "other";
  label: string;
}[] = [
  { value: "manifest", label: "Shipping Manifest" },
  { value: "contract", label: "Contract" },
  { value: "general", label: "General Document" },
  { value: "verification", label: "Verification Document" },
  { value: "other", label: "Other Document" },
];

export default function DocumentCreate() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<DocumentFormValues>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      name: "",
      type: "general",
      description: "",
      metadata: {},
    },
  });

  const selectedType = form.watch("type");

  const onSubmit = async (values: DocumentFormValues) => {
    setIsSubmitting(true);

    try {
      // Here we would normally handle the API call to create the document
      console.log("Document data:", values);

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: "Document Created",
        description: "Your document has been successfully created.",
      });

      navigate("/demo/documents");
    } catch (error) {
      console.error("Error creating document:", error);
      toast({
        title: "Creation Failed",
        description:
          "There was an error creating your document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigate("/demo/documents")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create Document</h1>
          <p className="text-muted-foreground">
            Create a new document with the details below
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
              <CardDescription>
                Fill in the details for your new document
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter document name" {...field} />
                        </FormControl>
                        <FormDescription>
                          Give your document a descriptive name
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select document type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {documentTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center gap-2">
                                  <DocumentTypeBadge type={type.value} />
                                  <span>{type.label}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the type of document you're creating
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter document description"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Add additional details about this document
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Render type-specific metadata fields based on document type */}
                  {selectedType === "manifest" && (
                    <div className="space-y-4 rounded-md border p-4">
                      <h3 className="font-medium">Manifest Details</h3>
                      {/* Add manifest-specific fields */}
                      <FormField
                        control={form.control}
                        name="metadata.shipmentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Shipment ID</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter shipment ID"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  {selectedType === "contract" && (
                    <div className="space-y-4 rounded-md border p-4">
                      <h3 className="font-medium">Contract Details</h3>
                      {/* Add contract-specific fields */}
                      <FormField
                        control={form.control}
                        name="metadata.partyA"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Party A</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter first party name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="metadata.partyB"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Party B</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter second party name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="file"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>File (Optional)</FormLabel>
                        <FormControl>
                          <FileUploadField
                            name="file"
                            description="Drag and drop your document here, or click to browse"
                            maxSize={10} // 10MB
                            accept={[
                              "application/pdf",
                              "image/jpeg",
                              "image/png",
                              "text/plain",
                              "text/csv",
                            ]}
                            showPreview={true}
                          />
                        </FormControl>
                        <FormDescription>
                          Supported file types: PDF, JPEG, PNG, CSV, TXT (Max:
                          10MB)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Alert variant="default" className="bg-muted/50">
                    <Info className="h-4 w-4" />
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      This is a demo version. Documents created here will not be
                      permanently stored.
                    </AlertDescription>
                  </Alert>

                  <div className="flex justify-end gap-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate("/demo/documents")}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? "Creating..." : "Create Document"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Document Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">File Requirements</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>Maximum file size: 10MB</li>
                  <li>Supported formats: PDF, JPEG, PNG, CSV, TXT</li>
                  <li>Clear, legible documents are more easily processed</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Document Types</h3>
                <ul className="text-muted-foreground ml-6 list-disc text-sm">
                  <li>
                    <strong>Manifest:</strong> Lists all items in a shipment
                  </li>
                  <li>
                    <strong>Contract:</strong> Legal agreement between parties
                  </li>
                  <li>
                    <strong>Verification:</strong> Identity or cargo
                    verification
                  </li>
                  <li>
                    <strong>General:</strong> Standard business documents
                  </li>
                  <li>
                    <strong>Other:</strong> Miscellaneous document types
                  </li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Document Privacy</h3>
                <p className="text-muted-foreground text-sm">
                  By default, all documents are private and only visible to you
                  and system administrators.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
