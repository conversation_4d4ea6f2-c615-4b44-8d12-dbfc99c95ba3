import type { Meta, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import DocumentDetails from "./DocumentDetails";

const meta: Meta<typeof DocumentDetails> = {
  title: "Demo/Pages/DocumentDetails",
  component: DocumentDetails,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "demo-document-123" },
      },
      routing: { path: "/demo/documents/:id" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
