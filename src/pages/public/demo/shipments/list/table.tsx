"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import { Link } from "react-router";

import type { ActionContext, TableConfig } from "@/components/tables/table";

import { ShipmentModeBadge } from "@/components/common/types/ShipmentMode";
import { ShipmentSourceBadge } from "@/components/common/types/ShipmentSource";
import { ShipmentStatusBadge } from "@/components/common/types/ShipmentStatus";
import { SearchText } from "@/components/search";
import Currency from "@/components/shared/Currency";
import TimeAgo from "@/components/shared/TimeAgo";
import { createTypedExportCSVAction } from "@/components/tables/actions";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import Table from "@/components/tables/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { formatWeight } from "@/lib/formatters";

const i18n = {
  en: {
    noData: "There are no shipments yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search shipments...",
    },
    headers: {
      id: "ID",
      status: "Status",
      mode: "Mode",
      source: "Source",
      route: "Route",
      driver: "Driver",
      weight: "Weight",
      valuation: "Valuation",
      created_at: "Created",
      actions: "Actions",
    },
  },
  links: {
    shipments: "/demo/shipments/[id]",
  },
};

export const groupName = "shipment";

export type ShipmentType = {
  id: string;
  status: string;
  mode: string;
  source: string;
  weight?: number;
  valuation?: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  driver?: {
    id: string;
    first_name: string;
    last_name: string;
    avatar?: string;
  };
  organization?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  stops?: Array<{
    id: string;
    sequence_number: number;
    type: string;
    location?: {
      id: string;
      formatted: string;
      latitude?: number;
      longitude?: number;
    };
  }>;
};

export type ShipmentData = {
  shipments: ShipmentType[];
  total: number;
};

interface ListShipmentsProps extends PropsWithChildren {
  loading?: boolean;
  shipments?: ShipmentData;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onDelete?: (id: string) => Promise<boolean>;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

// Helper function to format route from stops
const formatRoute = (stops?: ShipmentType["stops"]) => {
  if (!stops || stops.length === 0) return "No route defined";

  const sortedStops = stops.sort(
    (a, b) => a.sequence_number - b.sequence_number,
  );
  const origin = sortedStops[0]?.location?.formatted;
  const destination = sortedStops[sortedStops.length - 1]?.location?.formatted;

  if (origin && destination && origin !== destination) {
    return `${origin} → ${destination}`;
  }

  return origin || destination || "Unknown route";
};

export default function ListShipments({
  group = groupName,
  loading = false,
  shipments,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  onDelete,
  children,
}: ListShipmentsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!shipments) return undefined;
    return {
      items: shipments.shipments,
      total: shipments.total,
    };
  }, [shipments]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<ShipmentType>(
            [
              "id",
              "status",
              "mode",
              "source",
              "weight",
              "valuation",
              "driver",
              "organization",
            ],
            {
              filename: "shipments_export.csv",
              label: "Export Selected Shipments",
              resolvers: {
                driver: (driver: unknown) => {
                  if (
                    driver &&
                    typeof driver === "object" &&
                    "first_name" in driver &&
                    "last_name" in driver
                  ) {
                    return `${(driver.first_name as string) || ""} ${(driver.last_name as string) || ""}`.trim();
                  }
                  return "";
                },
                organization: (org: unknown) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return (org.name as string) || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Shipment Actions",
            render: (context: ActionContext<ShipmentType>) => {
              if (context.type === "row") {
                return (
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" asChild>
                      <Link
                        to={i18n.links.shipments.replace(
                          "[id]",
                          context.row.id,
                        )}
                      >
                        View
                      </Link>
                    </Button>
                    {onDelete && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(context.row.id)}
                      >
                        Delete
                      </Button>
                    )}
                  </div>
                );
              }
              return null;
            },
          },
        ],
        [onDelete],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "id",
              accessorKey: "id",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.id}
                />
              ),
              cell: ({ row }) => (
                <Link
                  to={i18n.links.shipments.replace("[id]", row.original.id)}
                  className="font-medium hover:underline"
                >
                  #{row.getValue("id")?.toString().substring(0, 8)}
                </Link>
              ),
              enableHiding: false,
            },
            {
              id: "status",
              accessorKey: "status",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.status}
                />
              ),
              cell: ({ row }) => (
                <ShipmentStatusBadge status={row.getValue("status")} />
              ),
            },
            {
              id: "mode",
              accessorKey: "mode",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.mode}
                />
              ),
              cell: ({ row }) => (
                <ShipmentModeBadge mode={row.getValue("mode")} />
              ),
            },
            {
              id: "source",
              accessorKey: "source",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.source}
                />
              ),
              cell: ({ row }) => (
                <ShipmentSourceBadge source={row.getValue("source")} />
              ),
            },
            {
              id: "route",
              accessorKey: "stops",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.route}
                />
              ),
              cell: ({ row }) => {
                const route = formatRoute(row.original.stops);
                return (
                  <div
                    className="max-w-[200px] truncate text-sm sm:max-w-md"
                    title={route}
                  >
                    {route}
                  </div>
                );
              },
            },
            {
              id: "driver",
              accessorKey: "driver",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.driver}
                />
              ),
              cell: ({ row }) => {
                const driver = row.original.driver;
                if (!driver) {
                  return (
                    <Badge variant="outline" className="text-xs">
                      Unassigned
                    </Badge>
                  );
                }
                return (
                  <div className="flex min-w-0 items-center gap-2">
                    {driver.avatar && (
                      <img
                        src={driver.avatar}
                        alt={`${driver.first_name} ${driver.last_name}`}
                        className="h-5 w-5 flex-shrink-0 rounded-full sm:h-6 sm:w-6"
                      />
                    )}
                    <span className="truncate text-sm">
                      {driver.first_name} {driver.last_name}
                    </span>
                  </div>
                );
              },
            },
            {
              id: "weight",
              accessorKey: "weight",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.weight}
                />
              ),
              cell: ({ row }) => {
                const weight = row.original.weight;
                if (!weight)
                  return (
                    <span className="text-muted-foreground text-xs sm:text-sm">
                      N/A
                    </span>
                  );
                return (
                  <span className="font-mono text-xs tabular-nums sm:text-sm">
                    {formatWeight(weight)}
                  </span>
                );
              },
            },
            {
              id: "valuation",
              accessorKey: "valuation",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.valuation}
                />
              ),
              cell: ({ row }) => {
                const valuation = row.original.valuation;
                if (!valuation)
                  return (
                    <span className="text-muted-foreground text-xs sm:text-sm">
                      N/A
                    </span>
                  );
                return (
                  <Currency
                    loading={loading}
                    amount={valuation}
                    className="font-mono text-xs tabular-nums sm:text-sm"
                  />
                );
              },
            },
            {
              id: "created_at",
              accessorKey: "created_at",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.created_at}
                />
              ),
              cell: ({ row }) => (
                <TimeAgo
                  loading={loading}
                  date={new Date(row.getValue("created_at"))}
                  className="text-muted-foreground text-xs sm:text-sm"
                />
              ),
            },
          ] as ColumnDef<ShipmentType, ShipmentType[]>[],
        [loading],
      )}
    >
      {children}
    </Table>
  );
}
