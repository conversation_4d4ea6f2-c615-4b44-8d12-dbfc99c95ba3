import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";

import { AnalyticsSection } from "./AnalyticsSection";

const meta: Meta<typeof AnalyticsSection> = {
  title: "Demo/Components/AnalyticsSection",
  component: AnalyticsSection,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const defaultAnalyticsData = {
  trends: [
    { period: "28.10", value: 8 },
    { period: "29.10", value: 12 },
    { period: "30.10", value: 6 },
    { period: "31.10", value: 15 },
    { period: "01.11", value: 10 },
    { period: "02.11", value: 18 },
    { period: "03.11", value: 14 },
  ],
  efficiency: {
    percentage: 96,
    chartData: [65, 78, 90, 85, 92, 96, 88, 94],
  },
  chat: {
    messages: [
      {
        id: "1",
        sender: "Hi!",
        message: "What is your question?",
        timestamp: "now",
      },
    ],
    unreadCount: 2,
  },
};

const lowPerformanceData = {
  trends: [
    { period: "28.10", value: 3 },
    { period: "29.10", value: 5 },
    { period: "30.10", value: 2 },
    { period: "31.10", value: 7 },
    { period: "01.11", value: 4 },
    { period: "02.11", value: 6 },
    { period: "03.11", value: 5 },
  ],
  efficiency: {
    percentage: 67,
    chartData: [45, 52, 60, 55, 62, 67, 58, 64],
  },
  chat: {
    messages: [
      {
        id: "1",
        sender: "Support",
        message: "How can I help you today?",
        timestamp: "5 min ago",
      },
    ],
    unreadCount: 5,
  },
};

const highPerformanceData = {
  trends: [
    { period: "28.10", value: 18 },
    { period: "29.10", value: 22 },
    { period: "30.10", value: 16 },
    { period: "31.10", value: 25 },
    { period: "01.11", value: 20 },
    { period: "02.11", value: 28 },
    { period: "03.11", value: 24 },
  ],
  efficiency: {
    percentage: 98,
    chartData: [85, 88, 95, 92, 96, 98, 94, 97],
  },
  chat: {
    messages: [
      {
        id: "1",
        sender: "Admin",
        message: "Great performance this week!",
        timestamp: "2 hours ago",
      },
    ],
    unreadCount: 0,
  },
};

const busyChatData = {
  trends: defaultAnalyticsData.trends,
  efficiency: defaultAnalyticsData.efficiency,
  chat: {
    messages: [
      {
        id: "1",
        sender: "Driver",
        message: "Arriving at destination in 10 minutes",
        timestamp: "2 min ago",
      },
      {
        id: "2",
        sender: "Dispatcher",
        message: "Copy that, thank you for the update",
        timestamp: "1 min ago",
      },
    ],
    unreadCount: 15,
  },
};

const noActivityData = {
  trends: [
    { period: "28.10", value: 0 },
    { period: "29.10", value: 1 },
    { period: "30.10", value: 0 },
    { period: "31.10", value: 2 },
    { period: "01.11", value: 1 },
    { period: "02.11", value: 0 },
    { period: "03.11", value: 1 },
  ],
  efficiency: {
    percentage: 45,
    chartData: [20, 25, 30, 28, 35, 45, 38, 42],
  },
  chat: {
    messages: [],
    unreadCount: 0,
  },
};

export const Default: Story = {
  args: {
    data: defaultAnalyticsData,
  },
};

export const LowPerformance: Story = {
  args: {
    data: lowPerformanceData,
  },
};

export const HighPerformance: Story = {
  args: {
    data: highPerformanceData,
  },
};

export const BusyChat: Story = {
  args: {
    data: busyChatData,
  },
};

export const NoActivity: Story = {
  args: {
    data: noActivityData,
  },
};

export const PeakSeason: Story = {
  args: {
    data: {
      trends: [
        { period: "01.12", value: 35 },
        { period: "02.12", value: 42 },
        { period: "03.12", value: 38 },
        { period: "04.12", value: 45 },
        { period: "05.12", value: 40 },
        { period: "06.12", value: 48 },
        { period: "07.12", value: 44 },
      ],
      efficiency: {
        percentage: 89,
        chartData: [75, 82, 88, 85, 89, 92, 87, 90],
      },
      chat: {
        messages: [
          {
            id: "1",
            sender: "Operations",
            message: "Peak season protocols in effect",
            timestamp: "30 min ago",
          },
        ],
        unreadCount: 8,
      },
    },
  },
};
