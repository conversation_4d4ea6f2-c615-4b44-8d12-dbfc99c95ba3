import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import { AlertsSection } from "./AlertsSection";

const meta: Meta<typeof AlertsSection> = {
  title: "Demo/Components/AlertsSection",
  component: AlertsSection,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const singleAlert = [
  {
    id: "1",
    type: "Geofencing alert",
    message:
      "Truck crossed geofence at Warehouse A. Driver arrival notification sent to staff.",
    time: "13:48",
    severity: "medium" as const,
  },
];

const multipleAlerts = [
  {
    id: "1",
    type: "Traffic Delay",
    message:
      "Heavy traffic detected on Route I-95. Estimated delay: 25 minutes.",
    time: "14:15",
    severity: "low" as const,
  },
  {
    id: "2",
    type: "Maintenance Required",
    message:
      "Vehicle AL-223965406 requires scheduled maintenance in 500 miles.",
    time: "13:45",
    severity: "medium" as const,
  },
  {
    id: "3",
    type: "Emergency Stop",
    message: "Driver initiated emergency stop. Immediate assistance requested.",
    time: "13:20",
    severity: "high" as const,
  },
];

const criticalAlerts = [
  {
    id: "1",
    type: "Vehicle Breakdown",
    message:
      "Critical engine failure detected. Vehicle immobilized on Highway 101.",
    time: "12:30",
    severity: "high" as const,
  },
  {
    id: "2",
    type: "Security Breach",
    message: "Unauthorized access attempt detected on cargo container lock.",
    time: "12:15",
    severity: "high" as const,
  },
];

const routineAlerts = [
  {
    id: "1",
    type: "Fuel Level",
    message: "Fuel level at 25%. Next fuel station in 45 miles.",
    time: "15:30",
    severity: "low" as const,
  },
  {
    id: "2",
    type: "Speed Monitoring",
    message: "Vehicle exceeded speed limit by 5 mph for 2 minutes.",
    time: "15:15",
    severity: "low" as const,
  },
  {
    id: "3",
    type: "Rest Break",
    message: "Driver has been driving for 4 hours. Rest break recommended.",
    time: "14:45",
    severity: "low" as const,
  },
];

export const Default: Story = {
  args: {
    alerts: singleAlert,
  },
};

export const MultipleAlerts: Story = {
  args: {
    alerts: multipleAlerts,
  },
};

export const CriticalAlerts: Story = {
  args: {
    alerts: criticalAlerts,
  },
};

export const RoutineAlerts: Story = {
  args: {
    alerts: routineAlerts,
  },
};

export const NoAlerts: Story = {
  args: {
    alerts: [],
  },
};

export const ManyAlerts: Story = {
  args: {
    alerts: [
      ...multipleAlerts,
      ...criticalAlerts,
      ...routineAlerts,
      {
        id: "7",
        type: "Weather Warning",
        message: "Severe weather conditions ahead. Consider alternate route.",
        time: "16:00",
        severity: "medium" as const,
      },
      {
        id: "8",
        type: "Delivery Update",
        message: "Customer requested delivery time change to 18:00.",
        time: "15:45",
        severity: "low" as const,
      },
    ],
  },
};
