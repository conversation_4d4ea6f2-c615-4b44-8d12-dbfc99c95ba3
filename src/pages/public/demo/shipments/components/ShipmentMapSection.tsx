import { useEffect, useRef } from "react";
import mapboxgl from "mapbox-gl";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
// Import Mapbox CSS
import "mapbox-gl/dist/mapbox-gl.css";

interface ShipmentData {
  distance: string;
  time: string;
  optimization: number;
  route?: {
    origin: [number, number];
    destination: [number, number];
    waypoints?: [number, number][];
  };
}

interface ShipmentMapSectionProps {
  shipment: ShipmentData;
  mapboxToken?: string;
}

export function ShipmentMapSection({
  shipment,
  mapboxToken = process.env.REACT_APP_MAPBOX_TOKEN || "your-mapbox-token-here",
}: ShipmentMapSectionProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);

  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    // Set Mapbox access token
    mapboxgl.accessToken = mapboxToken;

    // Default coordinates (Los Angeles to Las Vegas route)
    const defaultRoute = {
      origin: [-118.2437, 34.0522] as [number, number],
      destination: [-115.1398, 36.1699] as [number, number],
      waypoints: [
        [-117.922, 34.1084] as [number, number],
        [-116.5454, 35.0456] as [number, number],
      ],
    };

    const route = shipment.route || defaultRoute;

    // Initialize map with disabled interactions
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/mapbox/light-v11",
      center: [
        (route.origin[0] + route.destination[0]) / 2,
        (route.origin[1] + route.destination[1]) / 2,
      ],
      zoom: 6,
      // Disable all user interactions
      interactive: false,
      scrollZoom: false,
      boxZoom: false,
      dragRotate: false,
      dragPan: false,
      keyboard: false,
      doubleClickZoom: false,
      touchZoomRotate: false,
    });

    map.current.on("load", () => {
      if (!map.current) return;

      // Add markers for origin and destination
      new mapboxgl.Marker({ color: "#3b82f6" })
        .setLngLat(route.origin)
        .addTo(map.current);

      new mapboxgl.Marker({ color: "#ef4444" })
        .setLngLat(route.destination)
        .addTo(map.current);

      // Add waypoint markers if they exist
      if (route.waypoints) {
        route.waypoints.forEach((waypoint) => {
          new mapboxgl.Marker({ color: "#f59e0b" })
            .setLngLat(waypoint)
            .addTo(map.current!);
        });
      }

      // Create a route line
      const routeCoordinates = [
        route.origin,
        ...(route.waypoints || []),
        route.destination,
      ];

      map.current.addSource("route", {
        type: "geojson",
        data: {
          type: "Feature",
          properties: {},
          geometry: {
            type: "LineString",
            coordinates: routeCoordinates,
          },
        },
      });

      map.current.addLayer({
        id: "route",
        type: "line",
        source: "route",
        layout: {
          "line-join": "round",
          "line-cap": "round",
        },
        paint: {
          "line-color": "#3b82f6",
          "line-width": 4,
          "line-opacity": 0.8,
        },
      });

      // Fit map to show entire route
      const bounds = new mapboxgl.LngLatBounds();
      routeCoordinates.forEach((coord) => bounds.extend(coord));
      map.current.fitBounds(bounds, { padding: 100 });
    });

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, [shipment.route, mapboxToken]);

  return (
    <article className="relative">
      {/* Mapbox Map Container */}
      <Card className="h-96">
        <CardContent className="relative h-full overflow-hidden rounded-lg p-0">
          {/* Mapbox container */}
          <div
            ref={mapContainer}
            className="h-full w-full"
            style={{ position: "relative" }}
          />

          {/* Frozen map overlay to prevent any interaction */}
          <div className="absolute inset-0 z-10 cursor-not-allowed" />

          {/* Top-left overlay card - Distance/Time Tracking */}
          <Card className="border-border/50 bg-card/95 absolute top-4 left-4 z-20 w-80 border-2 shadow-lg backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="mb-3 flex gap-2">
                <Badge className="bg-primary">Live Tracking</Badge>
                <Badge variant="outline" className="text-xs">
                  shipment
                </Badge>
                <Badge variant="outline" className="text-xs">
                  real-time
                </Badge>
              </div>
              <div>
                <p className="text-muted-foreground mb-1 text-sm">
                  Distance to destination
                </p>
                <p className="text-foreground text-2xl font-bold">
                  {shipment.distance}{" "}
                  <span className="text-muted-foreground text-lg font-medium">
                    / {shipment.time}
                  </span>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Bottom-right overlay card - Route Optimization */}
          <Card className="border-border/50 bg-card/95 absolute right-4 bottom-4 z-20 w-80 border-2 shadow-lg backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="mb-3">
                <p className="text-muted-foreground mb-2 text-sm">
                  Route efficiency & optimization
                </p>
                <div className="flex items-center gap-3">
                  <p className="text-foreground text-2xl font-bold">
                    {shipment.optimization}%
                  </p>
                  <div className="bg-muted h-3 flex-1 rounded-full">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ease-in-out ${
                        shipment.optimization >= 90
                          ? "bg-green-500"
                          : shipment.optimization >= 70
                            ? "bg-primary"
                            : "bg-orange-500"
                      }`}
                      style={{ width: `${shipment.optimization}%` }}
                    />
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button size="sm" className="px-3 py-1 text-xs">
                  Re-optimize
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="px-3 py-1 text-xs"
                >
                  Route Details
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Map controls disabled indicator */}
          <div className="bg-card/90 text-muted-foreground absolute top-4 right-4 z-20 rounded-md px-2 py-1 text-xs">
            Map controls disabled
          </div>
        </CardContent>
      </Card>
    </article>
  );
}
