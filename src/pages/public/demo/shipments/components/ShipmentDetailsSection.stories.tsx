import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import { ShipmentDetailsSection } from "./ShipmentDetailsSection";

const meta: Meta<typeof ShipmentDetailsSection> = {
  title: "Demo/Components/ShipmentDetailsSection",
  component: ShipmentDetailsSection,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockShipmentData = {
  driver: {
    name: "<PERSON>",
    id: "1241A4A12119323518",
    location: "Ukraine",
    avatar: "/api/placeholder/40/40",
    rating: 4.8,
  },
  parcels: [
    {
      type: "Novaposhta parcels",
      status: "New bags paid",
      quantity: 12,
      destination: "Rivne",
    },
    {
      type: "Hazardous Chemicals",
      status: "Delivered",
      quantity: 8,
      destination: "Kyiv",
    },
  ],
  status: "Delivered",
  arrival: "28.10.23",
  price: 520.45,
};

export const Default: Story = {
  args: {
    shipment: mockShipmentData,
  },
};

export const InTransit: Story = {
  args: {
    shipment: {
      ...mockShipmentData,
      status: "In Transit",
      driver: {
        ...mockShipmentData.driver,
        name: "Sarah <PERSON>",
        location: "Poland",
        rating: 4.9,
      },
      price: 750.25,
    },
  },
};

export const Loading: Story = {
  args: {
    shipment: {
      ...mockShipmentData,
      status: "Loading",
      driver: {
        ...mockShipmentData.driver,
        name: "David Martinez",
        location: "Spain",
        rating: 4.6,
      },
      parcels: [
        {
          type: "Electronics",
          status: "Loading in progress",
          quantity: 24,
          destination: "Madrid",
        },
        {
          type: "Fragile Items",
          status: "Pending",
          quantity: 6,
          destination: "Barcelona",
        },
      ],
      arrival: "15.11.23",
      price: 1200.0,
    },
  },
};

export const HighValue: Story = {
  args: {
    shipment: {
      ...mockShipmentData,
      status: "Priority",
      driver: {
        ...mockShipmentData.driver,
        name: "Alex Thompson",
        location: "Germany",
        rating: 5.0,
      },
      parcels: [
        {
          type: "Medical Supplies",
          status: "Expedited",
          quantity: 50,
          destination: "Berlin",
        },
        {
          type: "Pharmaceuticals",
          status: "Temperature Controlled",
          quantity: 12,
          destination: "Munich",
        },
      ],
      price: 2500.75,
    },
  },
};
