import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import { TruckCapacitySection } from "./TruckCapacitySection";

const meta: Meta<typeof TruckCapacitySection> = {
  title: "Demo/Components/TruckCapacitySection",
  component: TruckCapacitySection,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockTruckData = {
  id: "AL - 223965406",
  capacity: 86,
  maxLoad: "8,453 KG",
  status: "On-Route" as const,
};

export const Default: Story = {
  args: {
    truck: mockTruckData,
  },
};

export const Loading: Story = {
  args: {
    truck: {
      ...mockTruckData,
      id: "TX - 445789123",
      capacity: 45,
      maxLoad: "12,000 KG",
      status: "Loading",
    },
  },
};

export const FullCapacity: Story = {
  args: {
    truck: {
      ...mockTruckData,
      id: "CA - 987654321",
      capacity: 98,
      maxLoad: "15,750 KG",
      status: "On-Route",
    },
  },
};

export const LowCapacity: Story = {
  args: {
    truck: {
      ...mockTruckData,
      id: "NY - 123456789",
      capacity: 22,
      maxLoad: "6,200 KG",
      status: "Loading",
    },
  },
};

export const Delivered: Story = {
  args: {
    truck: {
      ...mockTruckData,
      id: "FL - 555888999",
      capacity: 95,
      maxLoad: "9,800 KG",
      status: "Delivered",
    },
  },
};

export const EmptyTruck: Story = {
  args: {
    truck: {
      ...mockTruckData,
      id: "WA - 111222333",
      capacity: 0,
      maxLoad: "10,000 KG",
      status: "Loading",
    },
  },
};
