import {
  Clock,
  Filter,
  MapPin,
  Navigation,
  Package,
  Phone,
  Search,
  Truck,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ActiveOrder {
  id: string;
  type: string;
  company: string;
  location: string;
  destination: string;
  time: string;
  distance: string;
  icon: string;
}

interface LoadMetrics {
  currentLocation: string;
  lastStop: string;
  distance: string;
  currentSpeed: string;
  route: string;
  timeAgo: string;
}

interface DriverDetails {
  name: string;
  avatar: string;
  experience: string;
  license: string;
  id: string;
  phone: string;
}

interface LoadSectionProps {
  activeOrders: ActiveOrder[];
  metrics: LoadMetrics;
  driver: DriverDetails;
}

const mockData = {
  activeOrders: [
    {
      id: "123867",
      type: "Food",
      company: "Viz Zodiac Colony",
      location: "Miami, FL",
      destination: "Park view",
      time: "12 Jan",
      distance: "14 Jan",
      icon: "🍕",
    },
    {
      id: "639081",
      type: "Groupage Cargo",
      company: "SCV Medical Center",
      location: "San Jose, CA",
      destination: "25, Monetary Road",
      time: "12 Jan",
      distance: "14 Jan",
      icon: "📦",
    },
    {
      id: "153973",
      type: "Construction Materials",
      company: "125, 58th Colony",
      location: "Miami, FL",
      destination: "71, Long Island",
      time: "12 Jan",
      distance: "14 Jan",
      icon: "🏗️",
    },
    {
      id: "153890",
      type: "Flowers",
      company: "514, Norman town",
      location: "Brooklyn, NY",
      destination: "36, Bronze Avenue",
      time: "12 Jan",
      distance: "14 Jan",
      icon: "🌸",
    },
    {
      id: "136839",
      type: "Food",
      company: "992 Zodiac Colony",
      location: "Miami, FL",
      destination: "71, Park view",
      time: "12 Jan",
      distance: "14 Jan",
      icon: "🍕",
    },
  ],
  metrics: {
    currentLocation: "Route I - 75",
    lastStop: "9h ago",
    distance: "700/750 km",
    currentSpeed: "80 kmph",
    route: "Route I - 75",
    timeAgo: "9h ago",
  },
  driver: {
    name: "Herman Oslo",
    avatar: "/api/placeholder/40/40",
    experience: "25 Years",
    license: "CDE",
    id: "2415-65-7867",
    phone: "***********",
  },
};

export function LoadSection() {
  const { activeOrders, metrics, driver } = mockData;

  return (
    <article className="space-y-4 sm:space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold sm:text-xl">Load Overview</h2>
        <Badge variant="secondary" className="text-sm">
          {activeOrders.length} Active Orders
        </Badge>
      </div>

      <div className="grid gap-4 sm:gap-6 lg:grid-cols-4">
        {/* Active Orders Sidebar */}
        <div className="lg:col-span-1">
          <Card className="h-auto lg:h-[600px]">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center justify-between text-base sm:text-lg">
                <span>{activeOrders.length} Active Orders</span>
              </CardTitle>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                  <Input placeholder="Search" className="pl-10" />
                </div>
                <Button variant="outline" size="icon" className="flex-shrink-0">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-[300px] space-y-1 overflow-y-auto lg:max-h-[480px]">
                {activeOrders.map((order, index) => (
                  <div
                    key={order.id}
                    className={`hover:bg-muted/50 cursor-pointer border-l-4 p-3 transition-colors sm:p-4 ${
                      index === 0
                        ? "border-l-primary bg-primary/5"
                        : "border-l-border"
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 text-lg">{order.icon}</div>
                      <div className="min-w-0 flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <span className="text-sm font-semibold">
                            ID: {order.id}
                          </span>
                        </div>
                        <p className="text-muted-foreground mb-1 text-xs">
                          {order.type}
                        </p>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-xs">
                            <MapPin className="text-muted-foreground h-3 w-3 flex-shrink-0" />
                            <span className="text-foreground truncate">
                              {order.company}, {order.location}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-xs">
                            <Navigation className="text-muted-foreground h-3 w-3 flex-shrink-0" />
                            <span className="text-foreground truncate">
                              {order.destination}
                            </span>
                          </div>
                        </div>
                        <div className="text-muted-foreground mt-2 flex justify-between text-xs">
                          <span>{order.time}</span>
                          <span>{order.distance}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Load Details Grid */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="route" className="space-y-4 sm:space-y-6">
            <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
              <TabsTrigger value="route" className="text-xs sm:text-sm">
                Route
              </TabsTrigger>
              <TabsTrigger value="driver" className="text-xs sm:text-sm">
                Driver
              </TabsTrigger>
              <TabsTrigger value="load" className="text-xs sm:text-sm">
                Load
              </TabsTrigger>
              <TabsTrigger value="metrics" className="text-xs sm:text-sm">
                Metrics
              </TabsTrigger>
            </TabsList>

            <TabsContent value="route" className="space-y-4 sm:space-y-6">
              {/* Route Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">
                    Route Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <p className="text-muted-foreground text-sm">Origin</p>
                      <p className="font-medium">Miami, FL</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-muted-foreground text-sm">
                        Destination
                      </p>
                      <p className="font-medium">New York, NY</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-muted-foreground text-sm">Distance</p>
                      <p className="font-medium">1,090 mi</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-muted-foreground text-sm">
                        Estimated Time
                      </p>
                      <p className="font-medium">18h 30m</p>
                    </div>
                  </div>

                  {/* Route Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium">65%</span>
                    </div>
                    <div className="bg-muted h-2 w-full rounded-full">
                      <div className="bg-primary h-2 w-[65%] rounded-full" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Map Placeholder */}
              <Card>
                <CardContent className="bg-muted flex h-48 items-center justify-center sm:h-64">
                  <div className="text-center">
                    <MapPin className="text-muted-foreground mx-auto mb-2 h-8 w-8 sm:h-12 sm:w-12" />
                    <p className="text-muted-foreground text-sm">Route Map</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="driver" className="space-y-4 sm:space-y-6">
              {/* Driver Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">
                    Driver Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-center">
                    <Avatar className="h-12 w-12 flex-shrink-0 sm:h-16 sm:w-16">
                      <AvatarImage src={driver.avatar} />
                      <AvatarFallback>
                        {driver.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <h3 className="text-base font-semibold sm:text-lg">
                        {driver.name}
                      </h3>
                      <p className="text-muted-foreground text-sm">
                        {driver.id}
                      </p>
                      <div className="mt-2 flex flex-col gap-2 text-sm sm:flex-row sm:items-center sm:gap-4">
                        <span className="text-muted-foreground">
                          Experience: {driver.experience}
                        </span>
                        <span className="text-muted-foreground">
                          License: {driver.license}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-shrink-0 gap-2"
                    >
                      <Phone className="h-4 w-4" />
                      <span className="hidden sm:inline">Call</span>
                    </Button>
                  </div>

                  <div className="bg-muted grid grid-cols-3 gap-2 rounded-lg p-3 sm:gap-4 sm:p-4">
                    <div className="text-center">
                      <p className="text-primary text-lg font-bold sm:text-2xl">
                        4.8
                      </p>
                      <p className="text-muted-foreground text-xs">Rating</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold sm:text-2xl">127</p>
                      <p className="text-muted-foreground text-xs">Trips</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold sm:text-2xl">98%</p>
                      <p className="text-muted-foreground text-xs">On-time</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="load" className="space-y-4 sm:space-y-6">
              {/* Load Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base sm:text-lg">
                    Load Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <p className="text-muted-foreground text-sm">
                        Total Weight
                      </p>
                      <p className="text-xl font-bold sm:text-2xl">8,453 KG</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-muted-foreground text-sm">
                        Capacity Used
                      </p>
                      <p className="text-xl font-bold sm:text-2xl">86%</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Load Breakdown</h4>
                    {activeOrders.slice(0, 3).map((order) => (
                      <div
                        key={order.id}
                        className="bg-muted flex items-center justify-between rounded-lg p-3"
                      >
                        <div className="flex min-w-0 flex-1 items-center gap-3">
                          <div className="flex-shrink-0 text-lg">
                            {order.icon}
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="truncate text-sm font-medium">
                              {order.type}
                            </p>
                            <p className="text-muted-foreground truncate text-xs">
                              {order.company}
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant="outline"
                          className="flex-shrink-0 text-xs"
                        >
                          ID: {order.id}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="metrics" className="space-y-4 sm:space-y-6">
              {/* Current Metrics */}
              <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                      <Clock className="h-4 w-4 sm:h-5 sm:w-5" />
                      Current Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground text-sm">
                        Location
                      </span>
                      <span className="text-sm font-medium">
                        {metrics.currentLocation}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground text-sm">
                        Last Update
                      </span>
                      <span className="text-sm font-medium">
                        {metrics.timeAgo}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground text-sm">
                        Speed
                      </span>
                      <span className="text-sm font-medium">
                        {metrics.currentSpeed}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground text-sm">
                        Distance
                      </span>
                      <span className="text-sm font-medium">
                        {metrics.distance}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                      <Truck className="h-4 w-4 sm:h-5 sm:w-5" />
                      Vehicle Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="text-center">
                      <div className="bg-muted mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full sm:h-20 sm:w-20">
                        <Truck className="text-muted-foreground h-6 w-6 sm:h-8 sm:w-8" />
                      </div>
                      <Badge className="mb-2">On Route</Badge>
                      <p className="text-muted-foreground text-xs">
                        Vehicle operational
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </article>
  );
}
