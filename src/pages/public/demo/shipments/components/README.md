# Dashboard Components

This directory contains modular dashboard components that can be used independently or together to build comprehensive logistics dashboards.

## Components

### ShipmentMapSection ✨ **NEW**

A real Mapbox-powered map component for shipment tracking with frozen controls (non-interactive).

#### Features

- **Real Mapbox Integration**: Uses Mapbox GL JS for professional mapping
- **Frozen Controls**: All map interactions are disabled for display-only purposes
- **Route Visualization**: Shows origin, destination, waypoints, and route lines
- **Overlay Information**: Distance, time, and optimization metrics
- **Color-Coded Optimization**: Green (90%+), <PERSON> (70-89%), <PERSON> (<70%)

#### Setup

1. **Mapbox Token**: Set your Mapbox access token as an environment variable:

   ```bash
   REACT_APP_MAPBOX_TOKEN=your_mapbox_public_token_here
   ```

2. **Props Interface**:

   ```typescript
   interface ShipmentData {
     distance: string; // e.g., "270mi"
     time: string; // e.g., "4h 15min"
     optimization: number; // 0-100
     route?: {
       origin: [number, number]; // [longitude, latitude]
       destination: [number, number]; // [longitude, latitude]
       waypoints?: [number, number][]; // Optional stops
     };
   }
   ```

3. **Usage**:

   ```tsx
   import { ShipmentMapSection } from "./components";

   <ShipmentMapSection
     shipment={{
       distance: "270mi",
       time: "4h 15min",
       optimization: 85,
       route: {
         origin: [-118.2437, 34.0522], // Los Angeles
         destination: [-115.1398, 36.1699], // Las Vegas
       },
     }}
   />;
   ```

#### Map Controls Disabled

- Pan, zoom, rotate interactions are completely disabled
- Cursor shows "not-allowed" when hovering over map
- Visual indicator shows "Map controls disabled"
- Transparent overlay prevents any map interaction

### Other Components

- **LoadSection**: Order management and driver metrics
- **TrackingMapSection**: Placeholder map with decorative elements
- **ShipmentDetailsSection**: Driver info and parcel details
- **TruckCapacitySection**: Vehicle capacity and status
- **AlertsSection**: Notifications and alerts management
- **AnalyticsSection**: Performance trends and chat widget

## Development

Each component has comprehensive Storybook stories showcasing different states and scenarios:

```bash
npm run storybook
```

Navigate to `Dashboard/` to view all component variations.

## Dependencies

- `mapbox-gl`: Already installed (`^3.13.0`)
- Mapbox GL CSS is automatically imported
- All other dependencies are standard UI components
