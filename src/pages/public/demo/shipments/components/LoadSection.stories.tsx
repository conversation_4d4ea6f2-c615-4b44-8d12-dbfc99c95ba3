import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import { LoadSection } from "./LoadSection";

const meta: Meta<typeof LoadSection> = {
  title: "Demo/Components/LoadSection",
  component: LoadSection,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const EmptyState: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "The LoadSection component with default mock data showing active orders and load management.",
      },
    },
  },
};

export const HighActivity: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "LoadSection during peak hours with multiple active orders and high driver utilization.",
      },
    },
  },
};

export const DriverFocused: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "LoadSection emphasizing driver information and performance metrics.",
      },
    },
  },
};

export const RouteOptimization: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "LoadSection highlighting route planning and optimization features.",
      },
    },
  },
};

export const LoadManagement: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "LoadSection focusing on cargo management and capacity utilization.",
      },
    },
  },
};
