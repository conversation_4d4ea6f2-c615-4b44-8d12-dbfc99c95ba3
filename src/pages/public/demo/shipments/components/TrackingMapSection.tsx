import { MapPin } from "lucide-react";

import LocationMap from "@/components/maps/LocationMap";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface TrackingData {
  distance: string;
  time: string;
  optimization: number;
}

interface TrackingMapSectionProps {
  tracking: TrackingData;
}

export function TrackingMapSection({ tracking }: TrackingMapSectionProps) {
  return (
    <article className="relative">
      {/* Interactive Map Background */}
      <Card className="h-96">
        <CardContent className="from-muted/20 to-muted/40 relative h-full overflow-hidden rounded-lg bg-gradient-to-br p-0">
          {/* Map Placeholder Content */}
          <LocationMap />
          {/* Top-left overlay card - Distance/Time Tracking */}
          <Card className="border-border/50 bg-card/95 absolute top-4 left-4 w-80 border-2 shadow-lg backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="mb-3 flex gap-2">
                <Badge className="bg-primary">Tracking</Badge>
                <Badge variant="outline" className="text-xs">
                  traffic jams
                </Badge>
                <Badge variant="outline" className="text-xs">
                  PO
                </Badge>
              </div>
              <div>
                <p className="text-muted-foreground mb-1 text-sm">
                  Distance to arrival
                </p>
                <p className="text-foreground text-2xl font-bold">
                  {tracking.distance}{" "}
                  <span className="text-muted-foreground text-lg font-medium">
                    / {tracking.time}
                  </span>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Bottom-right overlay card - Traffic Optimization */}
          <Card className="border-border/50 bg-card/95 absolute right-4 bottom-4 w-80 border-2 shadow-lg backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="mb-3">
                <p className="text-muted-foreground mb-2 text-sm">
                  Traffic and route optimization
                </p>
                <div className="flex items-center gap-3">
                  <p className="text-foreground text-2xl font-bold">
                    {tracking.optimization}%
                  </p>
                  <div className="bg-muted h-3 flex-1 rounded-full">
                    <div
                      className="bg-primary h-3 rounded-full transition-all duration-500 ease-in-out"
                      style={{ width: `${tracking.optimization}%` }}
                    />
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button size="sm" className="px-3 py-1 text-xs">
                  Optimize
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="px-3 py-1 text-xs"
                >
                  View all
                </Button>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </article>
  );
}
