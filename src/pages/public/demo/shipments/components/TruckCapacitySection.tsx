import { Truck } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface TruckData {
  id: string;
  capacity: number;
  maxLoad: string;
  status: "On-Route" | "Loading" | "Delivered";
}

interface TruckCapacitySectionProps {
  truck: TruckData;
}

export function TruckCapacitySection({ truck }: TruckCapacitySectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Current truck capacity
          <Button variant="link" size="sm">
            Read more
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-center">
        {/* Truck Illustration Placeholder */}
        <div className="bg-muted flex items-center justify-center rounded-lg p-8">
          <div className="text-center">
            <Truck className="text-muted-foreground mx-auto mb-2 h-16 w-16" />
            <div className="text-primary text-3xl font-bold">
              {truck.capacity}%
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm">{truck.id}</span>
            <Badge>{truck.status}</Badge>
          </div>
          <div className="flex justify-between">
            <span className="text-sm">Max Load</span>
            <span className="text-sm font-medium">{truck.maxLoad}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
