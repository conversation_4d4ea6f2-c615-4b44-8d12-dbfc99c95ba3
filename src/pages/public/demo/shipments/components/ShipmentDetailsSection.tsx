import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DriverInfo {
  name: string;
  id: string;
  location: string;
  avatar: string;
  rating: number;
}

interface ParcelInfo {
  type: string;
  status: string;
  quantity: number;
  destination: string;
}

interface ShipmentData {
  driver: DriverInfo;
  parcels: ParcelInfo[];
  status: string;
  arrival: string;
  price: number;
}

interface ShipmentDetailsSectionProps {
  shipment: ShipmentData;
}

export function ShipmentDetailsSection({
  shipment,
}: ShipmentDetailsSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Shipment details
          <Button variant="link" size="sm">
            Read more
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <Avatar>
            <AvatarImage src={shipment.driver.avatar} />
            <AvatarFallback>
              {shipment.driver.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <p className="font-semibold">{shipment.driver.name}</p>
            <p className="text-muted-foreground text-sm">
              {shipment.driver.id} • {shipment.driver.location}
            </p>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-sm">Rating</span>
            <Badge variant="secondary">{shipment.driver.rating}</Badge>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-sm font-medium">{shipment.parcels[0].type}</p>
            <p className="text-muted-foreground text-xs">
              {shipment.parcels[0].status}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Parcels Loading</p>
            <p className="text-muted-foreground text-xs">
              {shipment.parcels[0].destination}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Status</p>
            <Badge className="text-xs">{shipment.status}</Badge>
          </div>
        </div>

        <div className="flex items-center justify-between border-t pt-4">
          <div>
            <p className="text-muted-foreground text-sm">Date of arrival</p>
            <p className="font-semibold">{shipment.arrival}</p>
          </div>
          <div>
            <p className="text-muted-foreground text-sm">Type of Parcels</p>
            <Badge variant="secondary">{shipment.parcels[1].type}</Badge>
          </div>
        </div>

        <div className="pt-4 text-center">
          <p className="text-2xl font-bold">$ {shipment.price}</p>
        </div>
      </CardContent>
    </Card>
  );
}
