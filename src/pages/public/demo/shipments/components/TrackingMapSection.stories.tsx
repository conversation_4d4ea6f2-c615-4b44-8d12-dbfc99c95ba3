import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import { TrackingMapSection } from "./TrackingMapSection";

const meta: Meta<typeof TrackingMapSection> = {
  title: "Demo/Components/TrackingMapSection",
  component: TrackingMapSection,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const defaultTrackingData = {
  distance: "120km",
  time: "1h 50min",
  optimization: 85,
};

export const Default: Story = {
  args: {
    tracking: defaultTrackingData,
  },
};

export const ShortDistance: Story = {
  args: {
    tracking: {
      distance: "45km",
      time: "35min",
      optimization: 92,
    },
  },
};

export const LongDistance: Story = {
  args: {
    tracking: {
      distance: "580km",
      time: "6h 15min",
      optimization: 78,
    },
  },
};

export const HighOptimization: Story = {
  args: {
    tracking: {
      distance: "200km",
      time: "2h 30min",
      optimization: 96,
    },
  },
};

export const LowOptimization: Story = {
  args: {
    tracking: {
      distance: "150km",
      time: "3h 45min",
      optimization: 52,
    },
  },
};

export const TrafficDelay: Story = {
  args: {
    tracking: {
      distance: "95km",
      time: "2h 20min",
      optimization: 34,
    },
  },
};

export const OptimalRoute: Story = {
  args: {
    tracking: {
      distance: "320km",
      time: "3h 15min",
      optimization: 98,
    },
  },
};

export const CrossCountry: Story = {
  args: {
    tracking: {
      distance: "1,250km",
      time: "14h 30min",
      optimization: 88,
    },
  },
};
