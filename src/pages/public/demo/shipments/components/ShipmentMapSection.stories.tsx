import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import { ShipmentMapSection } from "./ShipmentMapSection";

const meta: Meta<typeof ShipmentMapSection> = {
  title: "Demo/Components/ShipmentMapSection",
  component: ShipmentMapSection,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default LA to Las Vegas route
const defaultShipmentData = {
  distance: "270mi",
  time: "4h 15min",
  optimization: 85,
  route: {
    origin: [-118.2437, 34.0522] as [number, number], // Los Angeles
    destination: [-115.1398, 36.1699] as [number, number], // Las Vegas
    waypoints: [
      [-117.922, 34.1084] as [number, number], // San Bernardino
      [-116.5454, 35.0456] as [number, number], // Barstow
    ],
  },
};

export const Default: Story = {
  args: {
    shipment: defaultShipmentData,
  },
};

export const ShortRoute: Story = {
  args: {
    shipment: {
      distance: "45mi",
      time: "1h 20min",
      optimization: 92,
      route: {
        origin: [-118.2437, 34.0522] as [number, number], // Los Angeles
        destination: [-117.9583, 33.8303] as [number, number], // Anaheim
      },
    },
  },
};

export const CrossCountryRoute: Story = {
  args: {
    shipment: {
      distance: "2,800mi",
      time: "41h 30min",
      optimization: 78,
      route: {
        origin: [-118.2437, 34.0522] as [number, number], // Los Angeles
        destination: [-74.006, 40.7128] as [number, number], // New York City
        waypoints: [
          [-112.074, 33.4484] as [number, number], // Phoenix
          [-106.6504, 35.0844] as [number, number], // Albuquerque
          [-97.5164, 35.4676] as [number, number], // Oklahoma City
          [-90.0715, 35.2131] as [number, number], // Memphis
        ],
      },
    },
  },
};

export const HighOptimization: Story = {
  args: {
    shipment: {
      distance: "156mi",
      time: "2h 45min",
      optimization: 96,
      route: {
        origin: [-122.4194, 37.7749] as [number, number], // San Francisco
        destination: [-121.4689, 38.5556] as [number, number], // Sacramento
        waypoints: [
          [-122.0312, 37.9043] as [number, number], // San Rafael
        ],
      },
    },
  },
};

export const LowOptimization: Story = {
  args: {
    shipment: {
      distance: "95mi",
      time: "3h 45min",
      optimization: 34,
      route: {
        origin: [-122.4194, 37.7749] as [number, number], // San Francisco
        destination: [-121.8863, 37.3382] as [number, number], // San Jose
        waypoints: [
          [-122.2711, 37.8044] as [number, number], // Berkeley
          [-122.0312, 37.9043] as [number, number], // San Rafael (inefficient detour)
        ],
      },
    },
  },
};

export const TrafficDelay: Story = {
  args: {
    shipment: {
      distance: "68mi",
      time: "2h 50min",
      optimization: 42,
      route: {
        origin: [-118.2437, 34.0522] as [number, number], // Los Angeles
        destination: [-117.8564, 33.8156] as [number, number], // Orange County
      },
    },
  },
};

export const OptimalRoute: Story = {
  args: {
    shipment: {
      distance: "420mi",
      time: "6h 15min",
      optimization: 98,
      route: {
        origin: [-122.4194, 37.7749] as [number, number], // San Francisco
        destination: [-118.2437, 34.0522] as [number, number], // Los Angeles
        waypoints: [
          [-121.2958, 36.6002] as [number, number], // Monterey
          [-120.6596, 35.2828] as [number, number], // San Luis Obispo
        ],
      },
    },
  },
};

export const MultiStopDelivery: Story = {
  args: {
    shipment: {
      distance: "185mi",
      time: "4h 20min",
      optimization: 88,
      route: {
        origin: [-121.4689, 38.5556] as [number, number], // Sacramento
        destination: [-122.4194, 37.7749] as [number, number], // San Francisco
        waypoints: [
          [-121.7405, 38.2975] as [number, number], // Fairfield
          [-122.0312, 37.9043] as [number, number], // San Rafael
          [-122.2711, 37.8044] as [number, number], // Berkeley
        ],
      },
    },
  },
};

export const DirectRoute: Story = {
  args: {
    shipment: {
      distance: "380mi",
      time: "5h 45min",
      optimization: 94,
      route: {
        origin: [-104.9903, 39.7392] as [number, number], // Denver
        destination: [-111.891, 40.7608] as [number, number], // Salt Lake City
      },
    },
  },
};
