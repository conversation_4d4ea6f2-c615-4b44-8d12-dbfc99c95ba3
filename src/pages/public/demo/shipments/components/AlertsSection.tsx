import { AlertTriangle } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface AlertInfo {
  id: string;
  type: string;
  message: string;
  time: string;
  severity: "low" | "medium" | "high";
}

interface AlertsSectionProps {
  alerts: AlertInfo[];
}

export function AlertsSection({ alerts }: AlertsSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Alerts and Notifications
          <Badge variant="destructive">{alerts.length}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {alerts.map((alert) => (
          <div key={alert.id} className="space-y-2">
            <div className="flex items-start gap-3">
              <AlertTriangle className="mt-0.5 h-5 w-5 text-orange-500 dark:text-orange-400" />
              <div className="flex-1">
                <p className="text-sm font-medium">{alert.type}</p>
                <p className="text-muted-foreground text-sm">{alert.message}</p>
                <p className="text-muted-foreground/80 mt-1 text-xs">
                  {alert.time}
                </p>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
