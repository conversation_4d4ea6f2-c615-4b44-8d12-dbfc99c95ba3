import { MessageCircle } from "lucide-react";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

interface TrendDataPoint {
  period: string;
  value: number;
}

interface ChatMessage {
  id: string;
  sender: string;
  message: string;
  timestamp: string;
}

interface AnalyticsData {
  trends: TrendDataPoint[];
  efficiency: {
    percentage: number;
    chartData: number[];
  };
  chat: {
    messages: ChatMessage[];
    unreadCount: number;
  };
}

interface AnalyticsSectionProps {
  data: AnalyticsData;
}

export function AnalyticsSection({ data }: AnalyticsSectionProps) {
  return (
    <section className="grid gap-6 lg:grid-cols-3">
      {/* Shipment Trends */}
      <article>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Shipment trends
              <Badge variant="destructive">↓</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4 flex items-center gap-2">
              <Badge>8 shipments</Badge>
            </div>
            {/* Bar Chart Placeholder */}
            <div className="bg-muted flex h-32 items-end justify-between rounded p-4">
              {data.trends.map((point, index) => (
                <div key={index} className="flex flex-col items-center gap-1">
                  <div
                    className="bg-primary w-4 rounded"
                    style={{ height: `${(point.value / 20) * 100}%` }}
                  />
                  <span className="text-muted-foreground text-xs">
                    {point.period}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </article>

      {/* Route Efficiency */}
      <article>
        <Card className="bg-primary text-primary-foreground">
          <CardHeader>
            <CardTitle className="text-primary-foreground flex items-center justify-between">
              Route efficiency
              <Badge variant="secondary">✓</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4 text-center">
              <div className="text-primary-foreground text-6xl font-bold">
                {data.efficiency.percentage}
              </div>
              <div className="text-primary-foreground text-xl">%</div>
            </div>

            {/* Line Chart Placeholder */}
            <div className="bg-primary-foreground/10 flex h-20 items-end justify-between rounded p-4">
              {data.efficiency.chartData.map((point, index) => (
                <div
                  key={index}
                  className="bg-primary-foreground/50 w-2 rounded"
                  style={{ height: `${(point / 100) * 100}%` }}
                />
              ))}
            </div>

            <p className="text-primary-foreground/80 mt-2 text-sm">
              Send the best route to the driver's email
            </p>
          </CardContent>
        </Card>
      </article>

      {/* Chat Widget */}
      <article>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Chat
              <Badge variant="destructive">{data.chat.unreadCount}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback>U</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">Hi!</p>
              </div>
            </div>

            <div className="bg-primary text-primary-foreground ml-8 rounded-lg p-3">
              <p className="text-sm">What is your question?</p>
            </div>

            <div className="flex gap-2">
              <Input placeholder="Message" className="flex-1" />
              <Button size="icon">
                <MessageCircle className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </article>
    </section>
  );
}
