import { useParams } from "react-router";

import {
  AlertsSection,
  AnalyticsSection,
  ShipmentDetailsSection,
  TrackingMapSection,
  TruckCapacitySection,
} from "./components";

// TypeScript Interfaces
interface DriverInfo {
  name: string;
  id: string;
  location: string;
  avatar: string;
  rating: number;
}

interface ParcelInfo {
  type: string;
  status: string;
  quantity: number;
  destination: string;
}

interface AlertInfo {
  id: string;
  type: string;
  message: string;
  time: string;
  severity: "low" | "medium" | "high";
}

interface TrendDataPoint {
  period: string;
  value: number;
}

interface ChatMessage {
  id: string;
  sender: string;
  message: string;
  timestamp: string;
}

interface ShipmentData {
  id: string;
  tracking: {
    distance: string;
    time: string;
    optimization: number;
  };
  shipment: {
    driver: DriverInfo;
    parcels: ParcelInfo[];
    status: string;
    arrival: string;
    price: number;
  };
  truck: {
    id: string;
    capacity: number;
    maxLoad: string;
    status: "On-Route" | "Loading" | "Delivered";
  };
  alerts: AlertInfo[];
  trends: TrendDataPoint[];
  efficiency: {
    percentage: number;
    chartData: number[];
  };
  chat: {
    messages: ChatMessage[];
    unreadCount: number;
  };
}

// Mock shipment data (you could fetch this based on shipment ID)
const getShipmentData = (shipmentId: string): ShipmentData => ({
  id: shipmentId,
  tracking: {
    distance: "120km",
    time: "1h 50min",
    optimization: 85,
  },
  shipment: {
    driver: {
      name: "Michael Johnson",
      id: "1241A4A12119323518",
      location: "Ukraine",
      avatar: "/api/placeholder/40/40",
      rating: 4.8,
    },
    parcels: [
      {
        type: "Novaposhta parcels",
        status: "New bags paid",
        quantity: 12,
        destination: "Rivne",
      },
      {
        type: "Hazardous Chemicals",
        status: "Delivered",
        quantity: 8,
        destination: "Kyiv",
      },
    ],
    status: "Delivered",
    arrival: "28.10.23",
    price: 520.45,
  },
  truck: {
    id: `AL - ${shipmentId}`,
    capacity: 86,
    maxLoad: "8,453 KG",
    status: "On-Route",
  },
  alerts: [
    {
      id: "1",
      type: "Geofencing alert",
      message: `Truck for shipment ${shipmentId} crossed geofence at Warehouse A. Driver arrival notification sent to staff.`,
      time: "13:48",
      severity: "medium",
    },
  ],
  trends: [
    { period: "28.10", value: 8 },
    { period: "29.10", value: 12 },
    { period: "30.10", value: 6 },
    { period: "31.10", value: 15 },
    { period: "01.11", value: 10 },
    { period: "02.11", value: 18 },
    { period: "03.11", value: 14 },
  ],
  efficiency: {
    percentage: 96,
    chartData: [65, 78, 90, 85, 92, 96, 88, 94],
  },
  chat: {
    messages: [
      {
        id: "1",
        sender: "Hi!",
        message: "What is your question?",
        timestamp: "now",
      },
    ],
    unreadCount: 2,
  },
});

export default function ShipmentDetails() {
  const { id } = useParams<{ id: string }>();
  const shipmentId = id || "223965406";
  const data = getShipmentData(shipmentId);

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Shipment #{shipmentId}</h1>
        <p className="text-muted-foreground">
          Real-time tracking and details for this shipment
        </p>
      </div>

      {/* Tracking Section */}
      <TrackingMapSection tracking={data.tracking} />

      {/* Details Grid */}
      <section className="grid gap-6 lg:grid-cols-3">
        <ShipmentDetailsSection shipment={data.shipment} />
        <TruckCapacitySection truck={data.truck} />
        <AlertsSection alerts={data.alerts} />
      </section>

      {/* Analytics Section */}
      <AnalyticsSection
        data={{
          trends: data.trends,
          efficiency: data.efficiency,
          chat: data.chat,
        }}
      />
    </div>
  );
}
