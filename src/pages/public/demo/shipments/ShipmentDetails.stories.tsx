import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import ShipmentDetails from "./index";

const meta: Meta<typeof ShipmentDetails> = {
  title: "Demo/Pages/ShipmentDetails",
  component: ShipmentDetails,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "123" },
      },
      routing: { path: "/demo/shipments/:id" },
    }),
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  parameters: {
    ...meta.parameters,
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "ship-001" },
      },
      routing: { path: "/demo/shipments/:id" },
    }),
  },
};

export const InTransit: Story = {
  args: {},
  parameters: {
    ...meta.parameters,
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "ship-002" },
      },
      routing: { path: "/demo/shipments/:id" },
    }),
    docs: {
      description: {
        story:
          "Shipment details for an active in-transit shipment with real-time tracking.",
      },
    },
  },
};

export const HighValueLoad: Story = {
  args: {},
  parameters: {
    ...meta.parameters,
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "ship-hv-001" },
      },
      routing: { path: "/demo/shipments/:id" },
    }),
    docs: {
      description: {
        story:
          "Shipment details for a high-value load requiring special handling.",
      },
    },
  },
};

export const LongHaulRoute: Story = {
  args: {},
  parameters: {
    ...meta.parameters,
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { id: "ship-lh-001" },
      },
      routing: { path: "/demo/shipments/:id" },
    }),
    docs: {
      description: {
        story: "Shipment details for a long-haul route with multiple stops.",
      },
    },
  },
};
