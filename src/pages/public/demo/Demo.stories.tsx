import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import DemoDashboard from "./dashboard/index";
import { DashboardLayout } from "./layout";
import LogisticsPage from "./logistics/index";

const meta: Meta<typeof DashboardLayout> = {
  title: "Demo/Overview",
  component: DashboardLayout,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/demo" },
    }),
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const DashboardView: Story = {
  args: {
    defaultTab: "dashboard",
    children: <DemoDashboard />,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete logistics dashboard with real-time shipment tracking, analytics, and operational overview.",
      },
    },
  },
};

export const LogisticsView: Story = {
  args: {
    defaultTab: "logistics",
    children: <LogisticsPage />,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Logistics management interface for handling loads, routes, and operational planning.",
      },
    },
  },
};

export const DocumentsView: Story = {
  args: {
    defaultTab: "documents",
    children: (
      <div className="p-8">
        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="bg-muted rounded-lg p-6 text-center">
              <div className="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg">
                📄
              </div>
              <h3 className="mb-2 font-medium">Bills of Lading</h3>
              <p className="text-muted-foreground text-sm">
                Manage shipping documents
              </p>
            </div>
            <div className="bg-muted rounded-lg p-6 text-center">
              <div className="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg">
                📋
              </div>
              <h3 className="mb-2 font-medium">Manifests</h3>
              <p className="text-muted-foreground text-sm">
                Cargo manifests and lists
              </p>
            </div>
            <div className="bg-muted rounded-lg p-6 text-center">
              <div className="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg">
                🏷️
              </div>
              <h3 className="mb-2 font-medium">Labels</h3>
              <p className="text-muted-foreground text-sm">
                Shipping labels and tags
              </p>
            </div>
          </div>
          <div className="bg-muted/50 rounded-lg p-8 text-center">
            <h3 className="mb-2 text-lg font-medium">Document Scanner</h3>
            <p className="text-muted-foreground">
              Scan and process documents with AI
            </p>
          </div>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Document management system for handling shipping documents, manifests, and automated scanning.",
      },
    },
  },
};

export const VerificationsView: Story = {
  args: {
    defaultTab: "verifications",
    children: (
      <div className="p-8">
        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Driver Verifications</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 rounded-lg bg-green-50 p-3 dark:bg-green-950/20">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="font-medium">License Verification</span>
                  <span className="text-muted-foreground ml-auto text-sm">
                    Verified
                  </span>
                </div>
                <div className="flex items-center gap-3 rounded-lg bg-orange-50 p-3 dark:bg-orange-950/20">
                  <div className="h-3 w-3 rounded-full bg-orange-500"></div>
                  <span className="font-medium">Background Check</span>
                  <span className="text-muted-foreground ml-auto text-sm">
                    Pending
                  </span>
                </div>
                <div className="flex items-center gap-3 rounded-lg bg-green-50 p-3 dark:bg-green-950/20">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="font-medium">Medical Certificate</span>
                  <span className="text-muted-foreground ml-auto text-sm">
                    Verified
                  </span>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Vehicle Inspections</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 rounded-lg bg-green-50 p-3 dark:bg-green-950/20">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="font-medium">Safety Inspection</span>
                  <span className="text-muted-foreground ml-auto text-sm">
                    Passed
                  </span>
                </div>
                <div className="flex items-center gap-3 rounded-lg bg-red-50 p-3 dark:bg-red-950/20">
                  <div className="h-3 w-3 rounded-full bg-red-500"></div>
                  <span className="font-medium">Emissions Test</span>
                  <span className="text-muted-foreground ml-auto text-sm">
                    Failed
                  </span>
                </div>
                <div className="flex items-center gap-3 rounded-lg bg-green-50 p-3 dark:bg-green-950/20">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="font-medium">Insurance</span>
                  <span className="text-muted-foreground ml-auto text-sm">
                    Active
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-muted/50 rounded-lg p-8 text-center">
            <h3 className="mb-2 text-lg font-medium">Verification Process</h3>
            <p className="text-muted-foreground">
              Automated verification workflow with real-time updates
            </p>
          </div>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Verification system for driver credentials and vehicle inspections with automated workflows.",
      },
    },
  },
};
