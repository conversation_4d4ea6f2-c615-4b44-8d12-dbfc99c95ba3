import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import DemoDashboard from "./dashboard";
import { DashboardLayout } from "./layout";

const meta: Meta<typeof DashboardLayout> = {
  title: "Demo/Layout",
  component: DashboardLayout,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/demo" },
    }),
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultTab: "dashboard",
  },
};

export const WithDashboardContent: Story = {
  args: {
    defaultTab: "dashboard",
    children: <DemoDashboard />,
  },
};

export const DocumentsTab: Story = {
  args: {
    defaultTab: "documents",
    children: (
      <div className="p-8 text-center">
        <h2 className="mb-4 text-2xl font-bold">Document Management</h2>
        <p className="text-muted-foreground">
          Handle all shipping documents in one place
        </p>
      </div>
    ),
  },
};

export const VerificationsTab: Story = {
  args: {
    defaultTab: "verifications",
    children: (
      <div className="p-8 text-center">
        <h2 className="mb-4 text-2xl font-bold">Verification Center</h2>
        <p className="text-muted-foreground">
          Streamlined verification processes for drivers and vehicles
        </p>
      </div>
    ),
  },
};

export const LogisticsTab: Story = {
  args: {
    defaultTab: "logistics",
    children: (
      <div className="p-8 text-center">
        <h2 className="mb-4 text-2xl font-bold">Logistics Overview</h2>
        <p className="text-muted-foreground">
          Comprehensive load management and logistics operations
        </p>
      </div>
    ),
  },
};

export const AllTabs: Story = {
  args: {
    defaultTab: "dashboard",
    children: (
      <div className="p-8 text-center">
        <h2 className="mb-4 text-2xl font-bold">Dashboard Overview</h2>
        <p className="text-muted-foreground">
          Complete logistics dashboard with real-time tracking
        </p>
      </div>
    ),
  },
};

export const EmptyState: Story = {
  args: {
    defaultTab: "dashboard",
    children: (
      <div className="text-muted-foreground flex h-96 items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-medium">No Content</h3>
          <p className="text-sm">This tab has no content yet</p>
        </div>
      </div>
    ),
  },
};
