import {
  <PERSON><PERSON>,
  <PERSON>Sign,
  FileText,
  <PERSON>fresh<PERSON><PERSON>,
  Star,
  TrendingUp,
  Truck,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export function DriversSection() {
  return (
    <section
      id="drivers"
      className="border-t border-gray-100 py-20 dark:border-gray-800"
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-600 to-blue-700">
            <Truck className="h-8 w-8 text-white" />
          </div>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
            For Drivers — Drive, Deliver, Get Paid
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Built for life on the road, not life in the office.
          </p>
        </div>

        {/* Features Stack */}
        <div className="mx-auto max-w-5xl space-y-8">
          {/* Digital Docs */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/5 to-blue-600/5 dark:from-blue-500/10 dark:to-blue-600/10"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_20%_30%,rgba(59,130,246,0.2),transparent_50%)]"></div>

            <div className="relative rounded-3xl border border-blue-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-blue-800/30 dark:bg-gray-800/80">
              <div className="grid items-center gap-8 md:grid-cols-3">
                <div className="space-y-4 md:col-span-2">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600">
                      <FileText className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        Digital Docs
                      </h3>
                      <p className="font-medium text-blue-600 dark:text-blue-400">
                        Paperwork made simple
                      </p>
                    </div>
                  </div>
                  <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                    Upload BOLs, pickup slips, and CDL — we handle paperwork,
                    deadlines, and renewals. No more lost documents or missed
                    compliance dates.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                      Auto-renewal alerts
                    </Badge>
                    <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                      Cloud storage
                    </Badge>
                    <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                      Instant access
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="flex h-32 w-32 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/50 dark:to-blue-800/50">
                    <FileText className="h-16 w-16 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Instant Pay Pipeline */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-green-500/5 to-green-600/5 dark:from-green-500/10 dark:to-green-600/10"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_30%,rgba(34,197,94,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_80%_30%,rgba(34,197,94,0.2),transparent_50%)]"></div>

            <div className="relative rounded-3xl border border-green-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-green-800/30 dark:bg-gray-800/80">
              <div className="grid items-center gap-8 md:grid-cols-3">
                <div className="flex justify-center md:order-first">
                  <div className="flex h-32 w-32 items-center justify-center rounded-2xl bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/50 dark:to-green-800/50">
                    <DollarSign className="h-16 w-16 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="space-y-4 md:col-span-2">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-green-600">
                      <DollarSign className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        Instant Pay Pipeline
                      </h3>
                      <p className="font-medium text-green-600 dark:text-green-400">
                        Get paid when the job's done
                      </p>
                    </div>
                  </div>
                  <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                    No more waiting weeks or chasing invoices. Payment flows
                    automatically when delivery is confirmed. Choose direct
                    deposit, digital wallet, or even crypto.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                      Same-day payment
                    </Badge>
                    <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                      Multiple options
                    </Badge>
                    <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                      Zero fees
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* No More Ghost Lanes */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-500/5 to-purple-600/5 dark:from-purple-500/10 dark:to-purple-600/10"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_20%_30%,rgba(147,51,234,0.2),transparent_50%)]"></div>

            <div className="relative rounded-3xl border border-purple-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-purple-800/30 dark:bg-gray-800/80">
              <div className="grid items-center gap-8 md:grid-cols-3">
                <div className="space-y-4 md:col-span-2">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-purple-600">
                      <RefreshCw className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        No More Ghost Lanes
                      </h3>
                      <p className="font-medium text-purple-600 dark:text-purple-400">
                        AI-powered return loads
                      </p>
                    </div>
                  </div>
                  <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                    Our AI Dispatcher fills your return routes so you earn on
                    the way back. Smart matching considers your schedule,
                    preferred routes, and cargo compatibility.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                      Smart matching
                    </Badge>
                    <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                      Route optimization
                    </Badge>
                    <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                      Double your earnings
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="flex h-32 w-32 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/50 dark:to-purple-800/50">
                    <RefreshCw className="h-16 w-16 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI Voice Agents */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-orange-500/5 to-orange-600/5 dark:from-orange-500/10 dark:to-orange-600/10"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_30%,rgba(249,115,22,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_80%_30%,rgba(249,115,22,0.2),transparent_50%)]"></div>

            <div className="relative rounded-3xl border border-orange-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-orange-800/30 dark:bg-gray-800/80">
              <div className="grid items-center gap-8 md:grid-cols-3">
                <div className="flex justify-center md:order-first">
                  <div className="flex h-32 w-32 items-center justify-center rounded-2xl bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/50 dark:to-orange-800/50">
                    <Bot className="h-16 w-16 text-orange-600 dark:text-orange-400" />
                  </div>
                </div>
                <div className="space-y-4 md:col-span-2">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-orange-500 to-orange-600">
                      <Bot className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        AI Voice Agents
                      </h3>
                      <p className="font-medium text-orange-600 dark:text-orange-400">
                        Talk, don't type
                      </p>
                    </div>
                  </div>
                  <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                    From roadside help to dispatch updates — just talk to get
                    things done. Voice-activated everything means less time on
                    your phone and more time driving.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200">
                      Voice commands
                    </Badge>
                    <Badge className="border-0 bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200">
                      Hands-free
                    </Badge>
                    <Badge className="border-0 bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200">
                      24/7 support
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Gamified Progress */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-yellow-500/5 to-yellow-600/5 dark:from-yellow-500/10 dark:to-yellow-600/10"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,rgba(234,179,8,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_20%_30%,rgba(234,179,8,0.2),transparent_50%)]"></div>

            <div className="relative rounded-3xl border border-yellow-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-yellow-800/30 dark:bg-gray-800/80">
              <div className="grid items-center gap-8 md:grid-cols-3">
                <div className="space-y-4 md:col-span-2">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-yellow-500 to-yellow-600">
                      <Star className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        Gamified Progress
                      </h3>
                      <p className="font-medium text-yellow-600 dark:text-yellow-400">
                        Level up your driving career
                      </p>
                    </div>
                  </div>
                  <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                    Earn badges, hit mileage goals, and see how you rank among
                    drivers. Unlock better rates, priority loads, and exclusive
                    opportunities as you build your reputation.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
                      Achievement system
                    </Badge>
                    <Badge className="border-0 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
                      Leaderboards
                    </Badge>
                    <Badge className="border-0 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
                      Unlock rewards
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="flex h-32 w-32 items-center justify-center rounded-2xl bg-gradient-to-br from-yellow-100 to-yellow-200 dark:from-yellow-900/50 dark:to-yellow-800/50">
                    <Star className="h-16 w-16 text-yellow-600 dark:text-yellow-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Success Stories CTA */}
        <div className="relative mt-16">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-blue-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-blue-800/30 dark:bg-gray-800/80">
            <div className="text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Join Thousands of Successful Drivers
              </h3>
              <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Drivers on QuikSkope earn <strong>23% more</strong> than
                industry average with verified loads, instant payments, and
                optimized routes.
              </p>

              <div className="mb-8 grid gap-8 md:grid-cols-3">
                <div className="text-center">
                  <div className="mb-2 text-3xl font-bold text-blue-600 dark:text-blue-400">
                    $87K
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Average annual earnings
                  </p>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-3xl font-bold text-purple-600 dark:text-purple-400">
                    4.8★
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Driver satisfaction rating
                  </p>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-3xl font-bold text-blue-600 dark:text-blue-400">
                    15,000+
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Active drivers
                  </p>
                </div>
              </div>

              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-3 text-lg hover:from-blue-700 hover:to-purple-700">
                <Truck className="mr-2 h-5 w-5" />
                Join the Network
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
