import {
  CheckCircle,
  CreditCard,
  DollarSign,
  Shield,
  Smartphone,
  Star,
  Zap,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

export function PaymentsSection() {
  return (
    <section className="border-t border-gray-100 py-20 dark:border-gray-800">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-16 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-600 to-green-700">
            <CreditCard className="h-8 w-8 text-white" />
          </div>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
            Payments Without Friction
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            When the paperwork's done, payment flows.
          </p>
        </div>

        {/* Main Payment Features */}
        <div className="relative mb-16">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-green-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-green-800/30 dark:bg-gray-800/80">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 transition-transform group-hover:scale-110">
                  <DollarSign className="h-8 w-8 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Instant Payments
                </h3>
                <p className="mb-3 text-sm leading-relaxed text-gray-600 dark:text-gray-300">
                  Get paid immediately when delivery is confirmed. No waiting,
                  no chasing invoices.
                </p>
                <div className="flex flex-wrap justify-center gap-1">
                  <Badge className="border-0 bg-green-100 text-xs text-green-800 dark:bg-green-900/50 dark:text-green-200">
                    Same-day
                  </Badge>
                  <Badge className="border-0 bg-green-100 text-xs text-green-800 dark:bg-green-900/50 dark:text-green-200">
                    Zero fees
                  </Badge>
                </div>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 transition-transform group-hover:scale-110">
                  <Smartphone className="h-8 w-8 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Multiple Options
                </h3>
                <p className="mb-3 text-sm leading-relaxed text-gray-600 dark:text-gray-300">
                  Direct deposit, digital wallets, or crypto. Choose how you
                  want to get paid.
                </p>
                <div className="flex flex-wrap justify-center gap-1">
                  <Badge className="border-0 bg-blue-100 text-xs text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                    Bank transfer
                  </Badge>
                  <Badge className="border-0 bg-blue-100 text-xs text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                    Digital wallet
                  </Badge>
                </div>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-purple-600 transition-transform group-hover:scale-110">
                  <Star className="h-8 w-8 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Built-in Tipping
                </h3>
                <p className="mb-3 text-sm leading-relaxed text-gray-600 dark:text-gray-300">
                  Reward exceptional service with instant tips. Great drivers
                  get recognized.
                </p>
                <div className="flex flex-wrap justify-center gap-1">
                  <Badge className="border-0 bg-purple-100 text-xs text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                    Instant tips
                  </Badge>
                  <Badge className="border-0 bg-purple-100 text-xs text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                    Driver rewards
                  </Badge>
                </div>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-orange-500 to-orange-600 transition-transform group-hover:scale-110">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Secure & Tracked
                </h3>
                <p className="mb-3 text-sm leading-relaxed text-gray-600 dark:text-gray-300">
                  Every payment is tracked, verified, and secure. Complete
                  transparency for all parties.
                </p>
                <div className="flex flex-wrap justify-center gap-1">
                  <Badge className="border-0 bg-orange-100 text-xs text-orange-800 dark:bg-orange-900/50 dark:text-orange-200">
                    Encrypted
                  </Badge>
                  <Badge className="border-0 bg-orange-100 text-xs text-orange-800 dark:bg-orange-900/50 dark:text-orange-200">
                    Audit trail
                  </Badge>
                </div>
              </div>
            </div>

            {/* Payment Flow Visualization */}
            <div className="mt-12 border-t border-green-100 pt-8 dark:border-green-800/30">
              <h4 className="mb-8 text-center text-xl font-bold text-gray-900 dark:text-gray-100">
                How It Works
              </h4>
              <div className="flex items-center justify-center space-x-4 md:space-x-8">
                <div className="text-center">
                  <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Delivery Confirmed
                  </p>
                </div>
                <div className="h-0.5 w-8 bg-gradient-to-r from-blue-500 to-green-500"></div>
                <div className="text-center">
                  <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-green-600">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Payment Triggered
                  </p>
                </div>
                <div className="h-0.5 w-8 bg-gradient-to-r from-green-500 to-emerald-500"></div>
                <div className="text-center">
                  <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-emerald-500 to-emerald-600">
                    <DollarSign className="h-6 w-6 text-white" />
                  </div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Money Received
                  </p>
                </div>
              </div>
            </div>

            {/* Key Stats */}
            <div className="mt-12 border-t border-green-100 pt-8 dark:border-green-800/30">
              <div className="grid gap-8 text-center md:grid-cols-3">
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    &lt; 2 min
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Average payment time
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    $0
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Transaction fees
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    99.9%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Payment success rate
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Simple CTA */}
        <div className="text-center">
          <Button className="bg-gradient-to-r from-green-600 to-emerald-600 px-8 py-3 text-lg hover:from-green-700 hover:to-emerald-700">
            <CreditCard className="mr-2 h-5 w-5" />
            Get Paid Instantly
          </Button>
        </div>
      </div>
    </section>
  );
}
