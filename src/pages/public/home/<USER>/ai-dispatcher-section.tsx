import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  CheckCircle,
  Clock,
  MapPin,
  RefreshCw,
  Target,
  Zap,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

export function AiDispatcherSection() {
  return (
    <section
      id="features"
      className="border-t border-gray-100 py-20 dark:border-gray-800"
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-20 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-indigo-600 to-indigo-700">
            <Bot className="h-8 w-8 text-white" />
          </div>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
            Dispatching Made Smarter
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            The AI Dispatcher plans your loads so you don't have to.
          </p>
        </div>

        {/* Section 1: AI Load Matching & Optimization */}
        <div className="relative mb-20">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-indigo-500/5 to-blue-500/5 dark:from-indigo-500/10 dark:to-blue-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(99,102,241,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(99,102,241,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-indigo-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-indigo-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-indigo-500 to-blue-500">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                AI Load Matching & Optimization
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Advanced algorithms match loads with the perfect carriers while
                optimizing routes and maximizing profitability.
              </p>
            </div>

            <div className="grid items-center gap-12 lg:grid-cols-2">
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-indigo-500 to-blue-500">
                    <Brain className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      RAP Port Scoring System
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Our proprietary RAP (Reliability, Availability,
                      Performance) Port algorithm scores every carrier match
                      based on historical performance, current capacity, and
                      route efficiency.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-200">
                        Performance tracking
                      </Badge>
                      <Badge className="border-0 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-200">
                        Reliability scoring
                      </Badge>
                      <Badge className="border-0 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-200">
                        Capacity optimization
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-indigo-500">
                    <RefreshCw className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Return Lane Optimization
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Eliminate empty miles by automatically finding profitable
                      return loads. Our AI considers driver preferences,
                      equipment type, and delivery schedules to maximize
                      earnings.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Empty mile reduction
                      </Badge>
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Profit maximization
                      </Badge>
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Route planning
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-indigo-600 to-blue-600">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Predictive Delay Alerts
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Get ahead of problems before they happen. Weather
                      patterns, traffic data, and historical trends help predict
                      delays and suggest alternative routes or carriers.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-200">
                        Weather integration
                      </Badge>
                      <Badge className="border-0 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-200">
                        Traffic analysis
                      </Badge>
                      <Badge className="border-0 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-200">
                        Proactive alerts
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="rounded-3xl border border-indigo-100 bg-gradient-to-br from-indigo-50 to-blue-50 p-8 dark:border-indigo-800 dark:from-indigo-900/20 dark:to-blue-900/20">
                <h4 className="mb-6 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  RAP Port Matching
                </h4>
                <div className="space-y-4">
                  <div className="rounded-xl border border-indigo-100 bg-white p-4 shadow-sm dark:border-indigo-700 dark:bg-gray-800">
                    <div className="mb-3 flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-r from-green-500 to-emerald-500">
                          <span className="text-sm font-bold text-white">
                            A+
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            Elite Transport Co.
                          </span>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            RAP Score: 98/100
                          </p>
                        </div>
                      </div>
                      <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        Best Match
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="text-center">
                        <div className="font-semibold text-green-600 dark:text-green-400">
                          99%
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          On-time
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-blue-600 dark:text-blue-400">
                          $2.85
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          Per mile
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-purple-600 dark:text-purple-400">
                          2 hrs
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          Response
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-xl border border-indigo-100 bg-white p-4 shadow-sm dark:border-indigo-700 dark:bg-gray-800">
                    <div className="mb-3 flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-r from-blue-500 to-blue-600">
                          <span className="text-sm font-bold text-white">
                            A
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            Reliable Freight LLC
                          </span>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            RAP Score: 94/100
                          </p>
                        </div>
                      </div>
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Good Match
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="text-center">
                        <div className="font-semibold text-green-600 dark:text-green-400">
                          96%
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          On-time
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-blue-600 dark:text-blue-400">
                          $2.72
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          Per mile
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-purple-600 dark:text-purple-400">
                          4 hrs
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          Response
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section 2: Intelligent Load Capture & Processing */}
        <div className="relative mb-20">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_20%,rgba(34,197,94,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_20%,rgba(34,197,94,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-green-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-green-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500">
                <Camera className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Intelligent Load Capture & Processing
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Transform any load information into actionable dispatch data
                with AI-powered document processing.
              </p>
            </div>

            <div className="grid items-center gap-12 lg:grid-cols-2">
              <div className="rounded-3xl border border-green-100 bg-gradient-to-br from-green-50 to-emerald-50 p-8 dark:border-green-800 dark:from-green-900/20 dark:to-emerald-900/20">
                <h4 className="mb-6 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  AI Load Processing
                </h4>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 rounded-lg border border-green-100 bg-white p-3 dark:border-green-700 dark:bg-gray-800">
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-green-500 to-emerald-500">
                      <Camera className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        Photo Upload
                      </span>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Snap a picture of any load sheet
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rounded-lg border border-green-100 bg-white p-3 dark:border-green-700 dark:bg-gray-800">
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-emerald-500 to-green-500">
                      <Brain className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        AI Processing
                      </span>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Extract all relevant data automatically
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rounded-lg border border-green-100 bg-white p-3 dark:border-green-700 dark:bg-gray-800">
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-green-600 to-emerald-600">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        Ready to Dispatch
                      </span>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Instantly available for carrier matching
                      </p>
                    </div>
                  </div>
                </div>
                <div className="mt-6 rounded-lg bg-gradient-to-r from-green-100 to-emerald-100 p-4 dark:from-green-900/50 dark:to-emerald-900/50">
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    <Clock className="mr-1 inline h-4 w-4" />
                    Average processing time: 12 seconds
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-green-500 to-emerald-500">
                    <Camera className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Photo-to-Digital Conversion
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Simply photograph load sheets, rate confirmations, or
                      handwritten notes. Our AI extracts pickup locations,
                      delivery addresses, cargo details, and special
                      instructions with 99.7% accuracy.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-emerald-500 to-green-500">
                    <Brain className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Smart Auto-Fill Technology
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      AI recognizes patterns and auto-completes missing
                      information. Company names become full addresses, partial
                      commodity descriptions get expanded, and rates are
                      validated against market data.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-green-600 to-emerald-600">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Instant Dispatch Ready
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Processed loads are immediately available for carrier
                      matching and dispatch. No manual data entry, no delays —
                      from photo to posted in under 30 seconds.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section 3: Smart Handoffs & Multi-Party Management */}
        <div className="relative mb-20">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-500/5 to-pink-500/5 dark:from-purple-500/10 dark:to-pink-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(147,51,234,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-purple-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-purple-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500">
                <RefreshCw className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Smart Handoffs & Multi-Party Management
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Seamlessly manage complex shipments with multiple stops,
                carriers, and stakeholders.
              </p>
            </div>

            <div className="grid items-center gap-12 lg:grid-cols-2">
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-purple-500 to-pink-500">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Multi-Stop Management
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Handle complex routes with multiple pickups and
                      deliveries. Each stop is tracked independently with its
                      own verification requirements, timing windows, and special
                      instructions.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                        Route optimization
                      </Badge>
                      <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                        Stop sequencing
                      </Badge>
                      <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                        Time windows
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-pink-500 to-purple-500">
                    <RefreshCw className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Carrier Handoff Control
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      When shipments transfer between carriers, maintain
                      complete control and visibility. Automated handoff
                      protocols ensure nothing gets lost in translation between
                      different parties.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-200">
                        Seamless transfers
                      </Badge>
                      <Badge className="border-0 bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-200">
                        Automated protocols
                      </Badge>
                      <Badge className="border-0 bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-200">
                        Full visibility
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-purple-600 to-pink-600">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Chain-of-Custody Preservation
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Maintain unbroken documentation across all handoffs. Every
                      transfer is verified, documented, and added to the
                      permanent audit trail — perfect for high-value or
                      regulated cargo.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                        Audit trail
                      </Badge>
                      <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                        Compliance ready
                      </Badge>
                      <Badge className="border-0 bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                        Verification
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="rounded-3xl border border-purple-100 bg-gradient-to-br from-purple-50 to-pink-50 p-8 dark:border-purple-800 dark:from-purple-900/20 dark:to-pink-900/20">
                <h4 className="mb-6 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Multi-Party Shipment
                </h4>
                <div className="space-y-4">
                  <div className="rounded-lg border border-purple-100 bg-white p-4 shadow-sm dark:border-purple-700 dark:bg-gray-800">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        Pickup - Chicago, IL
                      </span>
                      <Badge className="border-0 bg-green-100 text-xs text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        Completed
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Carrier: Elite Transport • Verified: 2:15 PM
                    </p>
                  </div>

                  <div className="rounded-lg border border-purple-100 bg-white p-4 shadow-sm dark:border-purple-700 dark:bg-gray-800">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        Transfer - Memphis, TN
                      </span>
                      <Badge className="border-0 bg-blue-100 text-xs text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        In Progress
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Handoff: Elite → Regional Express • ETA: 6:30 PM
                    </p>
                  </div>

                  <div className="rounded-lg border border-purple-100 bg-white p-4 shadow-sm dark:border-purple-700 dark:bg-gray-800">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        Delivery - Atlanta, GA
                      </span>
                      <Badge className="border-0 bg-gray-100 text-xs text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        Scheduled
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Carrier: Regional Express • ETA: Tomorrow 10:00 AM
                    </p>
                  </div>
                </div>
                <div className="mt-6 rounded-lg bg-gradient-to-r from-purple-100 to-pink-100 p-4 dark:from-purple-900/50 dark:to-pink-900/50">
                  <p className="text-sm font-medium text-purple-800 dark:text-purple-200">
                    <CheckCircle className="mr-1 inline h-4 w-4" />
                    Chain of custody maintained across all handoffs
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="relative">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(99,102,241,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_50%_50%,rgba(99,102,241,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-indigo-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-indigo-800/30 dark:bg-gray-800/80">
            <div className="text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-indigo-600 to-purple-600">
                <Bot className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Ready for Smarter Dispatching?
              </h3>
              <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Join dispatchers who've reduced manual work by{" "}
                <strong>78%</strong> and increased load efficiency by{" "}
                <strong>34%</strong> with AI-powered automation.
              </p>

              <div className="mb-8 grid gap-8 md:grid-cols-3">
                <div className="text-center">
                  <div className="mb-2 text-3xl font-bold text-indigo-600 dark:text-indigo-400">
                    12 sec
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Average load processing
                  </p>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-3xl font-bold text-purple-600 dark:text-purple-400">
                    99.7%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Data extraction accuracy
                  </p>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-3xl font-bold text-indigo-600 dark:text-indigo-400">
                    78%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Reduction in manual work
                  </p>
                </div>
              </div>

              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-3 text-lg hover:from-indigo-700 hover:to-purple-700">
                  <Bot className="mr-2 h-5 w-5" />
                  Try Auto-Dispatch
                </Button>
                <Button
                  variant="outline"
                  className="border-indigo-300 px-8 py-3 text-lg text-indigo-700 hover:bg-indigo-50 dark:border-indigo-600 dark:text-indigo-300 dark:hover:bg-indigo-900/20"
                >
                  <Target className="mr-2 h-5 w-5" />
                  Learn About RAP Port
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
