import {
  <PERSON><PERSON>,
  CheckCircle,
  Clock,
  Eye,
  FileText,
  Globe,
  MapPin,
  Shield,
  Zap,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export function ShippersSection() {
  return (
    <section
      id="shippers"
      className="border-t border-gray-100 py-20 dark:border-gray-800"
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-20 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-600 to-purple-700">
            <Globe className="h-8 w-8 text-white" />
          </div>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
            For Shippers & Brokers — Control Without Chaos
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            From quote to dropoff, every load is verifiable, visible, and
            secure.
          </p>
        </div>

        {/* Section 1: Real-Time Logistics Intelligence */}
        <div className="relative mb-20">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-blue-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-blue-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500">
                <MapPin className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Real-Time Logistics Intelligence
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Complete visibility into every shipment with live tracking,
                predictive analytics, and automated alerts.
              </p>
            </div>

            <div className="grid items-center gap-12 lg:grid-cols-2">
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500">
                    <Eye className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Live Load Visibility
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      See current location, ETA, temp/humidity data, and driver
                      status in real-time. Get automatic alerts for delays,
                      route deviations, or environmental changes.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        GPS tracking
                      </Badge>
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Environmental sensors
                      </Badge>
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Predictive ETAs
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-cyan-500 to-blue-500">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Smart Alerts & Analytics
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      AI-powered insights predict delays before they happen. Get
                      proactive notifications about weather, traffic, or carrier
                      issues that could impact delivery.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-cyan-100 text-cyan-800 dark:bg-cyan-900/50 dark:text-cyan-200">
                        Predictive alerts
                      </Badge>
                      <Badge className="border-0 bg-cyan-100 text-cyan-800 dark:bg-cyan-900/50 dark:text-cyan-200">
                        Weather integration
                      </Badge>
                      <Badge className="border-0 bg-cyan-100 text-cyan-800 dark:bg-cyan-900/50 dark:text-cyan-200">
                        Performance analytics
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Customer Communication
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Automated customer updates with branded tracking pages.
                      Your customers stay informed without constant phone calls
                      or emails.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Branded tracking
                      </Badge>
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Auto notifications
                      </Badge>
                      <Badge className="border-0 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                        Custom messaging
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="rounded-3xl border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 p-8 dark:border-gray-700 dark:from-gray-800 dark:to-gray-900">
                <h4 className="mb-6 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Live Dashboard Preview
                </h4>
                <div className="space-y-4">
                  <div className="rounded-xl border border-gray-100 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        Load #QS-2024-001
                      </span>
                      <Badge className="border-0 bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 dark:from-green-900/50 dark:to-emerald-900/50 dark:text-green-200">
                        In Transit
                      </Badge>
                    </div>
                    <p className="mb-2 text-sm text-gray-600 dark:text-gray-400">
                      Chicago, IL → Atlanta, GA
                    </p>
                    <div className="mb-2 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>ETA: 2:30 PM EST</span>
                      <span>Temp: 38°F ✓</span>
                    </div>
                    <div className="h-2 rounded-full bg-gray-200 dark:bg-gray-700">
                      <div className="h-2 w-3/4 rounded-full bg-gradient-to-r from-green-500 to-emerald-500"></div>
                    </div>
                  </div>
                  <div className="rounded-xl border border-gray-100 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        Load #QS-2024-002
                      </span>
                      <Badge className="border-0 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 dark:from-blue-900/50 dark:to-blue-800/50 dark:text-blue-200">
                        Pickup Verified
                      </Badge>
                    </div>
                    <p className="mb-2 text-sm text-gray-600 dark:text-gray-400">
                      Dallas, TX → Phoenix, AZ
                    </p>
                    <div className="mb-2 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>ETA: Tomorrow 11:00 AM</span>
                      <span>Driver: Mike R. ⭐4.9</span>
                    </div>
                    <div className="h-2 rounded-full bg-gray-200 dark:bg-gray-700">
                      <div className="h-2 w-1/4 rounded-full bg-gradient-to-r from-blue-500 to-blue-600"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section 2: Verification & Custom Protocols */}
        <div className="relative mb-20">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_20%,rgba(34,197,94,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_20%,rgba(34,197,94,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-green-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-green-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Verification & Custom Protocols
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Configure custom verification workflows that match your
                operational requirements and compliance standards.
              </p>
            </div>

            <div className="grid items-center gap-12 lg:grid-cols-2">
              <div className="rounded-3xl border border-green-100 bg-gradient-to-br from-green-50 to-emerald-50 p-8 dark:border-green-800 dark:from-green-900/20 dark:to-emerald-900/20">
                <h4 className="mb-6 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Custom Workflow Builder
                </h4>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 rounded-lg border border-green-100 bg-white p-3 dark:border-green-700 dark:bg-gray-800">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Pickup Authorization
                    </span>
                  </div>
                  <div className="flex items-center space-x-3 rounded-lg border border-green-100 bg-white p-3 dark:border-green-700 dark:bg-gray-800">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Temperature Verification
                    </span>
                  </div>
                  <div className="flex items-center space-x-3 rounded-lg border border-green-100 bg-white p-3 dark:border-green-700 dark:bg-gray-800">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Load Inspection
                    </span>
                  </div>
                  <div className="flex items-center space-x-3 rounded-lg border border-green-100 bg-white p-3 dark:border-green-700 dark:bg-gray-800">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Delivery Confirmation
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-green-500 to-emerald-500">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Compliance Automation
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Automatically enforce industry regulations, temperature
                      controls, and custom business rules. Every protocol is
                      documented and auditable.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        DOT compliance
                      </Badge>
                      <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        FSMA certified
                      </Badge>
                      <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        Cold chain
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-emerald-500 to-green-500">
                    <FileText className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Document Management
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Custom forms, digital signatures, and automated filing.
                      Create templates for BOLs, inspection reports, or any
                      documents your business requires.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200">
                        Digital signatures
                      </Badge>
                      <Badge className="border-0 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200">
                        Form builder
                      </Badge>
                      <Badge className="border-0 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200">
                        Auto filing
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-r from-green-600 to-emerald-600">
                    <Bot className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                      Exception Handling
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                      Define escalation rules for when things don't go as
                      planned. Automatic notifications, alternate carriers, and
                      contingency protocols keep shipments moving.
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        Auto escalation
                      </Badge>
                      <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        Backup carriers
                      </Badge>
                      <Badge className="border-0 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                        Recovery plans
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Results */}
            <div className="mt-12 border-t border-green-100 pt-8 dark:border-green-800/30">
              <div className="grid gap-8 text-center md:grid-cols-3">
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    100%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Protocol compliance
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    85%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Faster processing
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    $0
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Compliance violations
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section 3: Performance Analytics & Business Intelligence */}
        <div className="relative">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-500/5 to-blue-500/5 dark:from-purple-500/10 dark:to-blue-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(147,51,234,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-purple-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-purple-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-blue-500">
                <FileText className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Performance Analytics & Business Intelligence
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Turn shipping data into business insights with comprehensive
                analytics, carrier scorecards, and predictive reporting.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-purple-600 transition-transform group-hover:scale-110">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Carrier Scorecards
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Rate carriers on delivery times, documentation quality, and
                  customer satisfaction to optimize your network.
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 transition-transform group-hover:scale-110">
                  <MapPin className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Route Analytics
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Identify your most profitable lanes, seasonal trends, and
                  optimization opportunities across your network.
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-indigo-500 to-indigo-600 transition-transform group-hover:scale-110">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Cost Intelligence
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Track all shipping costs including hidden fees, delays, and
                  damage claims for true P&L visibility.
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-cyan-500 to-cyan-600 transition-transform group-hover:scale-110">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Predictive Insights
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Forecast demand, predict capacity crunches, and get ahead of
                  market rate changes with AI-powered analytics.
                </p>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="mt-12 border-t border-purple-100 pt-8 dark:border-purple-800/30">
              <div className="grid gap-8 text-center md:grid-cols-4">
                <div>
                  <div className="mb-2 text-3xl font-bold text-purple-600 dark:text-purple-400">
                    94%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    On-time delivery
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-blue-600 dark:text-blue-400">
                    31%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Cost reduction
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-indigo-600 dark:text-indigo-400">
                    87%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Customer satisfaction
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-cyan-600 dark:text-cyan-400">
                    2.4x
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    ROI improvement
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
