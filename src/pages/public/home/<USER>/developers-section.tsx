import { Code, Database, Shield, Terminal, Webhook } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

export function DevelopersSection() {
  return (
    <section
      id="developers"
      className="bg-gray-900 py-20 text-white dark:bg-gray-950"
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-16 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-400 to-purple-400">
            <Code className="h-8 w-8 text-white" />
          </div>
          <h2 className="mb-4 text-3xl font-bold md:text-4xl">
            For Developers — Build on QuikSkope
          </h2>
          <p className="text-xl text-gray-300">
            Plug into our verification engine and logistics stack.
          </p>
        </div>

        {/* API Features */}
        <div className="relative mb-16">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/10 to-purple-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.15),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-gray-700/50 bg-gray-800/80 p-6 backdrop-blur-sm md:p-8 lg:p-12 dark:border-gray-600/50 dark:bg-gray-900/80">
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 lg:gap-8">
              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 transition-transform group-hover:scale-110">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-white">
                  Verification API
                </h3>
                <p className="mb-3 text-sm leading-relaxed text-gray-300">
                  Programmatically manage handoffs, track verification events,
                  and trigger compliance rules.
                </p>
                <div className="flex flex-wrap justify-center gap-1.5">
                  <Badge className="border-blue-700 bg-blue-900/50 text-xs text-blue-200">
                    REST API
                  </Badge>
                  <Badge className="border-blue-700 bg-blue-900/50 text-xs text-blue-200">
                    Webhooks
                  </Badge>
                </div>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-green-600 transition-transform group-hover:scale-110">
                  <Database className="h-8 w-8 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-white">
                  Shipment Ingestion
                </h3>
                <p className="mb-3 text-sm leading-relaxed text-gray-300">
                  Send load data directly to our system to initiate workflows
                  and assign carriers.
                </p>
                <div className="flex flex-wrap justify-center gap-1.5">
                  <Badge className="border-green-700 bg-green-900/50 text-xs text-green-200">
                    Bulk import
                  </Badge>
                  <Badge className="border-green-700 bg-green-900/50 text-xs text-green-200">
                    Real-time
                  </Badge>
                </div>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-purple-600 transition-transform group-hover:scale-110">
                  <Webhook className="h-8 w-8 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-white">
                  Event Hooks
                </h3>
                <p className="mb-3 text-sm leading-relaxed text-gray-300">
                  Get notified when shipments reach key milestones — pickups,
                  deliveries, document signings.
                </p>
                <div className="flex flex-wrap justify-center gap-1.5">
                  <Badge className="border-purple-700 bg-purple-900/50 text-xs text-purple-200">
                    Real-time
                  </Badge>
                  <Badge className="border-purple-700 bg-purple-900/50 text-xs text-purple-200">
                    Reliable
                  </Badge>
                </div>
              </div>
            </div>

            {/* Code Example */}
            <div className="mt-8 border-t border-gray-700 pt-6 md:mt-12 md:pt-8 dark:border-gray-600">
              <h4 className="mb-6 text-center text-lg font-bold text-white md:mb-8 md:text-xl">
                Simple Integration
              </h4>
              <div className="overflow-hidden rounded-xl border border-gray-700 bg-gray-900 dark:border-gray-600 dark:bg-gray-950">
                <div className="flex items-center space-x-2 border-b border-gray-700 px-4 py-3 dark:border-gray-600">
                  <Terminal className="h-4 w-4 text-green-400" />
                  <span className="font-mono text-sm text-green-400">
                    API Example
                  </span>
                </div>
                <div className="overflow-x-auto p-4">
                  <pre className="font-mono text-xs text-gray-300 sm:text-sm">
                    <code>{`// Track shipment verification
const response = await fetch('/api/shipments/track', {
  method: 'POST',
  headers: { 
    'Authorization': 'Bearer YOUR_API_KEY' 
  },
  body: JSON.stringify({
    shipment_id: 'QS-2024-001',
    event: 'pickup_verified',
    location: { 
      lat: 41.8781, 
      lng: -87.6298 
    }
  })
});`}</code>
                  </pre>
                </div>
              </div>
            </div>

            {/* Developer Stats */}
            <div className="mt-8 border-t border-gray-700 pt-6 md:mt-12 md:pt-8 dark:border-gray-600">
              <div className="grid gap-6 text-center sm:grid-cols-3 md:gap-8">
                <div>
                  <div className="mb-2 text-2xl font-bold text-blue-400 md:text-3xl">
                    99.9%
                  </div>
                  <p className="text-sm text-gray-300 md:text-base">
                    API uptime
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-2xl font-bold text-green-400 md:text-3xl">
                    &lt; 100ms
                  </div>
                  <p className="text-sm text-gray-300 md:text-base">
                    Average response time
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-2xl font-bold text-purple-400 md:text-3xl">
                    24/7
                  </div>
                  <p className="text-sm text-gray-300 md:text-base">
                    Developer support
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <Badge className="mb-4 border-blue-700 bg-gradient-to-r from-blue-900/50 to-purple-900/50 text-blue-200 md:mb-6">
            Coming Soon
          </Badge>
          <div className="flex flex-col justify-center gap-3 sm:flex-row sm:gap-4">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-2.5 text-base hover:from-blue-700 hover:to-purple-700 md:px-8 md:py-3 md:text-lg">
              <Code className="mr-2 h-4 w-4 md:h-5 md:w-5" />
              Get API Access
            </Button>
            <Button
              variant="outline"
              className="border-gray-600 bg-transparent px-6 py-2.5 text-base text-white hover:bg-gray-800 md:px-8 md:py-3 md:text-lg dark:border-gray-500 dark:hover:bg-gray-900"
            >
              <Terminal className="mr-2 h-4 w-4 md:h-5 md:w-5" />
              View Documentation
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
