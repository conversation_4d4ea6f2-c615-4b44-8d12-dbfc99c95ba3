import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ircle,
  DollarSign,
  FileText,
  MapPin,
  Shield,
  TrendingUp,
  Truck,
  Users,
} from "lucide-react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, ResponsiveContainer } from "recharts";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";

const fraudTypes = [
  { name: "Carrier Impersonation", value: 42, cases: 903, color: "#ef4444" },
  { name: "Load Theft", value: 28, cases: 602, color: "#f97316" },
  { name: "Document Fraud", value: 18, cases: 387, color: "#eab308" },
  { name: "Identity Theft", value: 12, cases: 258, color: "#dc2626" },
];

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
}: {
  cx?: number;
  cy?: number;
  midAngle?: number;
  innerRadius?: number;
  outerRadius?: number;
  percent?: number;
}) => {
  if (!cx || !cy || !midAngle || !innerRadius || !outerRadius || !percent) {
    return null;
  }

  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? "start" : "end"}
      dominantBaseline="central"
      fontSize="12"
      fontWeight="600"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

export function ProblemSection() {
  return (
    <section className="py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-20 text-center">
          <div className="mb-8 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-red-500 to-orange-500">
            <AlertTriangle className="h-8 w-8 text-white" />
          </div>
          <h2 className="mb-6 text-4xl font-bold text-gray-900 md:text-5xl dark:text-gray-100">
            The Fraud Epidemic in Freight
          </h2>
          <p className="mx-auto max-w-3xl text-xl leading-relaxed text-gray-600 dark:text-gray-300">
            Freight fraud doesn't just hurt shippers' bottom lines — it
            devastates drivers' livelihoods and destroys trust across the entire
            supply chain.
          </p>
        </div>

        {/* Impact on Both Audiences */}
        <div className="mb-20 grid gap-8 md:grid-cols-2">
          <Card className="border border-gray-200 transition-all hover:shadow-lg dark:border-gray-700 dark:bg-gray-800">
            <CardHeader>
              <div className="mb-4 flex items-center space-x-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-blue-600">
                  <Truck className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl text-gray-900 dark:text-gray-100">
                    Impact on Drivers
                  </CardTitle>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    How fraud hurts honest carriers
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">
                  Lost income from stolen loads
                </span>
                <span className="font-bold text-blue-600 dark:text-blue-400">
                  $45K avg
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">
                  Drivers blacklisted unfairly
                </span>
                <span className="font-bold text-blue-600 dark:text-blue-400">
                  1,200+
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">
                  Insurance premium increases
                </span>
                <span className="font-bold text-blue-600 dark:text-blue-400">
                  +23%
                </span>
              </div>
              <div className="border-t border-gray-100 pt-3 dark:border-gray-600">
                <p className="text-sm text-gray-600 italic dark:text-gray-400">
                  "I lost my truck and my reputation because someone used my DOT
                  number for a fake pickup. It took 8 months to clear my name."
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 transition-all hover:shadow-lg dark:border-gray-700 dark:bg-gray-800">
            <CardHeader>
              <div className="mb-4 flex items-center space-x-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-purple-500 to-purple-600">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl text-gray-900 dark:text-gray-100">
                    Impact on Shippers
                  </CardTitle>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    How fraud damages businesses
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">
                  Direct financial losses
                </span>
                <span className="font-bold text-purple-600 dark:text-purple-400">
                  $920M
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">
                  Customer relationships lost
                </span>
                <span className="font-bold text-purple-600 dark:text-purple-400">
                  15,000+
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 dark:text-gray-300">
                  Insurance claims rejected
                </span>
                <span className="font-bold text-purple-600 dark:text-purple-400">
                  68%
                </span>
              </div>
              <div className="border-t border-gray-100 pt-3 dark:border-gray-600">
                <p className="text-sm text-gray-600 italic dark:text-gray-400">
                  "We lost a $2M client after fraudsters picked up their load.
                  They blamed us for not verifying the carrier properly."
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* The Numbers Don't Lie - Simplified */}
        <div className="relative mb-20">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-red-500/5 to-orange-500/5 dark:from-red-500/10 dark:to-orange-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(239,68,68,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(239,68,68,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-red-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-red-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-red-500 to-orange-500">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                The Numbers Don't Lie
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Freight fraud losses have increased by{" "}
                <strong className="text-red-600 dark:text-red-400">
                  41% over the past 5 years
                </strong>
                , with 2024 marking the highest recorded losses in industry
                history.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-red-500 to-orange-500 transition-transform group-hover:scale-110">
                  <DollarSign className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  $920M
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Total 2024 losses
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-red-500 to-orange-500 transition-transform group-hover:scale-110">
                  <AlertTriangle className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  2,150
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Fraud incidents
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-red-500 to-orange-500 transition-transform group-hover:scale-110">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  $428K
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Average loss per incident
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-red-500 to-orange-500 transition-transform group-hover:scale-110">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  18 days
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Average detection time
                </p>
              </div>
            </div>

            <div className="mt-12 border-t border-red-100 pt-8 dark:border-red-800/30">
              <div className="grid gap-8 text-center md:grid-cols-3">
                <div>
                  <div className="mb-2 text-3xl font-bold text-red-600 dark:text-red-400">
                    72%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Loads never recovered
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-red-600 dark:text-red-400">
                    41%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Increase since 2020
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-red-600 dark:text-red-400">
                    68%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Insurance claims rejected
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Fraud Types - Simplified and Contained */}
        <div className="relative mb-20">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-orange-500/5 to-yellow-500/5 dark:from-orange-500/10 dark:to-yellow-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_20%,rgba(249,115,22,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_20%,rgba(249,115,22,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-orange-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-orange-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-orange-500 to-yellow-500">
                <AlertTriangle className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                Most Common Fraud Types
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Fraudsters are becoming more sophisticated, with{" "}
                <strong className="text-orange-600 dark:text-orange-400">
                  carrier impersonation
                </strong>{" "}
                now accounting for nearly half of all cases.
              </p>
            </div>

            <div className="mx-auto grid max-w-5xl items-start gap-8 lg:grid-cols-2 lg:gap-12">
              <div className="order-2 space-y-4 lg:order-1 lg:space-y-6">
                <div className="rounded-xl border border-gray-100 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                  <div className="mb-2 flex items-center space-x-3">
                    <div className="h-3 w-3 rounded-full bg-red-500"></div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      Carrier Impersonation
                    </h4>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      42%
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Fake carriers using stolen DOT numbers and forged documents
                    to pick up legitimate loads.
                  </p>
                </div>

                <div className="rounded-xl border border-gray-100 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                  <div className="mb-2 flex items-center space-x-3">
                    <div className="h-3 w-3 rounded-full bg-orange-500"></div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      Load Theft
                    </h4>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      28%
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Drivers disappearing with cargo after legitimate pickup,
                    often targeting high-value electronics.
                  </p>
                </div>

                <div className="rounded-xl border border-gray-100 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                  <div className="mb-2 flex items-center space-x-3">
                    <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      Document Fraud
                    </h4>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      18%
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Forged BOLs, fake insurance certificates, and altered
                    delivery receipts.
                  </p>
                </div>

                <div className="rounded-xl border border-gray-100 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
                  <div className="mb-2 flex items-center space-x-3">
                    <div className="h-3 w-3 rounded-full bg-red-600"></div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      Identity Theft
                    </h4>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      12%
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Stolen driver credentials and company identities used for
                    fraudulent operations.
                  </p>
                </div>
              </div>

              <div className="order-1 flex justify-center lg:order-2">
                <div className="aspect-square w-full max-w-sm">
                  <ChartContainer
                    config={{
                      fraud: {
                        label: "Fraud Cases",
                      },
                    }}
                    className="h-full w-full"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={fraudTypes}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={renderCustomizedLabel}
                          outerRadius="80%"
                          fill="#8884d8"
                          dataKey="value"
                          stroke="#fff"
                          strokeWidth={2}
                        >
                          {fraudTypes.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <ChartTooltip
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const data = payload[0].payload;
                              return (
                                <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg dark:border-gray-700 dark:bg-gray-800">
                                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                                    {data.name}
                                  </p>
                                  <p className="text-gray-600 dark:text-gray-300">
                                    {data.cases.toLocaleString()} cases
                                  </p>
                                  <p className="text-gray-500 dark:text-gray-400">
                                    {data.value}% of total
                                  </p>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Solution Section */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(34,197,94,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(34,197,94,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-green-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-green-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h3 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-gray-100">
                How QuikSkope Eliminates Fraud
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Our comprehensive verification system creates an unbreakable
                chain of trust from pickup to delivery, protecting both drivers
                and shippers.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 transition-transform group-hover:scale-110">
                  <MapPin className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  GPS Verification
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Real-time location tracking with geofenced pickup and delivery
                  zones
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 transition-transform group-hover:scale-110">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Digital Signatures
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Cryptographically secure signatures with biometric
                  verification
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 transition-transform group-hover:scale-110">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Smart Documentation
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  AI-powered document validation with tamper-proof audit trails
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 transition-transform group-hover:scale-110">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Identity Verification
                </h4>
                <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                  Multi-factor authentication with continuous driver
                  verification
                </p>
              </div>
            </div>

            {/* Results */}
            <div className="mt-12 border-t border-green-100 pt-8 dark:border-green-800/30">
              <div className="grid gap-8 text-center md:grid-cols-3">
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    99.8%
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Fraud Prevention Rate
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    $2.3M
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Saved from Fraud Attempts
                  </p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600 dark:text-green-400">
                    0
                  </div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Successful Impersonations
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
