import { Globe, Users } from "lucide-react";
import { LayoutGroup, motion } from "motion/react";
import { <PERSON> } from "react-router";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { TextRotate } from "@/pages/public/home/<USER>";

export function HeroSection() {
  return (
    <section className="relative overflow-hidden py-20 lg:py-32">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-white to-purple-50/50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-900"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.15),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(255,89,65,0.08),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(255,89,65,0.12),transparent_50%)]"></div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <Badge className="mb-6 border-0 bg-gradient-to-r from-blue-100 to-orange-100 text-gray-800 dark:from-blue-900/50 dark:to-orange-900/50 dark:text-orange-200">
            Where Verified Drivers Meet Transparent Freight
          </Badge>
          <LayoutGroup>
            <motion.h1
              className="mb-6 flex w-full flex-col items-center justify-center text-center text-4xl font-bold whitespace-pre text-gray-900 md:text-6xl dark:text-gray-100"
              layout
            >
              <motion.span
                className="pt-0.5 sm:pt-1 md:pt-2"
                layout
                transition={{ type: "spring", damping: 30, stiffness: 400 }}
              >
                A{" "}
              </motion.span>
              <TextRotate
                texts={["Smarter", "Faster", "Secure"]}
                mainClassName={cn(
                  "justify-center overflow-hidden rounded-lg bg-[#ff5941] px-2 py-0.5 text-white shadow-lg sm:px-2 sm:py-1 md:px-3 md:py-2",
                )}
                staggerFrom={"last"}
                initial={{ y: "100%" }}
                animate={{ y: 0 }}
                exit={{ y: "-120%" }}
                staggerDuration={0.025}
                splitLevelClassName="overflow-hidden pb-0.5 sm:pb-1 md:pb-1"
                transition={{ type: "spring", damping: 30, stiffness: 400 }}
                rotationInterval={2000}
              />{" "}
              <motion.span layout className="pt-0.5 sm:pt-1 md:p-0">
                Way to Move Freight
              </motion.span>
            </motion.h1>
          </LayoutGroup>
          <p className="mx-auto mb-8 max-w-3xl text-xl text-gray-600 dark:text-gray-300">
            From the open road to the control tower, QuikSkope unifies every
            shipment, handshake, and signature — with AI-driven dispatching,
            instant payments, and compliance built in.
          </p>
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-[#ff5941] shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-[#e04e37]"
            >
              <Link to="/drivers" className="flex items-center gap-2">
                <Users className="mr-2 h-5 w-5" />
                Join as a Driver
              </Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-100 dark:hover:border-orange-500/50 dark:hover:bg-gray-800"
            >
              <Link to="/demo" className="flex items-center gap-2">
                <Globe className="mr-2 h-5 w-5" />
                Explore Demo
              </Link>
            </Button>
          </div>

          {/* Subtle accent elements */}
          <div className="mt-16 flex items-center justify-center space-x-8 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 rounded-full bg-gradient-to-r from-blue-500 to-[#ff5941]"></div>
              <span>15,000+ Active Drivers</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 rounded-full bg-gradient-to-r from-[#ff5941] to-blue-500"></div>
              <span>500+ Shipping Companies</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 rounded-full bg-[#ff5941]"></div>
              <span>$2.3M Fraud Prevented</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
