import {
  Bot,
  Globe,
  MapPin,
  Star,
  TrendingUp,
  Truck,
  Users,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

export function FinalCtaSection() {
  return (
    <section className="py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Main CTA */}
        <div className="relative">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.2),transparent_50%)]"></div>

          <div className="relative rounded-3xl border border-blue-100/50 bg-white/80 p-8 backdrop-blur-sm md:p-12 dark:border-blue-800/30 dark:bg-gray-800/80">
            <div className="mb-12 text-center">
              <div className="mb-8 inline-flex h-20 w-20 items-center justify-center rounded-3xl bg-gradient-to-r from-blue-600 to-purple-600">
                <Truck className="h-10 w-10 text-white" />
              </div>
              <h2 className="mb-6 text-4xl font-bold text-gray-900 md:text-5xl dark:text-gray-100">
                Freight That Moves Itself
              </h2>
              <p className="mx-auto mb-8 max-w-3xl text-xl leading-relaxed text-gray-600 dark:text-gray-300">
                Whether you're behind the wheel or behind a desk, QuikSkope
                frees you to focus on what matters most — growing your business
                and delivering excellence.
              </p>
            </div>

            {/* Success Metrics */}
            <div className="mb-12 grid gap-8 md:grid-cols-4">
              <div className="text-center">
                <div className="mb-3 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-green-500 to-emerald-500">
                  <Star className="h-6 w-6 text-white" />
                </div>
                <div className="mb-1 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  99.8%
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Fraud prevention
                </p>
              </div>
              <div className="text-center">
                <div className="mb-3 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-blue-600">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div className="mb-1 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  23%
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Higher driver earnings
                </p>
              </div>
              <div className="text-center">
                <div className="mb-3 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-purple-500 to-purple-600">
                  <Globe className="h-6 w-6 text-white" />
                </div>
                <div className="mb-1 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  47%
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Fewer disputes
                </p>
              </div>
              <div className="text-center">
                <div className="mb-3 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-orange-500 to-orange-600">
                  <Bot className="h-6 w-6 text-white" />
                </div>
                <div className="mb-1 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  78%
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Less manual work
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 text-lg hover:from-blue-700 hover:to-purple-700">
                <Users className="mr-2 h-5 w-5" />
                Join as Driver
              </Button>
              <Button
                variant="outline"
                className="border-gray-300 px-8 py-4 text-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-100 dark:hover:bg-gray-800"
              >
                <Globe className="mr-2 h-5 w-5" />
                Schedule Demo
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="mt-12 border-t border-gray-100 pt-8 dark:border-gray-700">
              <p className="mb-4 text-center text-sm text-gray-500 dark:text-gray-400">
                Trusted by thousands of drivers and shippers
              </p>
              <div className="flex items-center justify-center space-x-8 text-gray-400 dark:text-gray-500">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span className="text-sm">15,000+ Active Drivers</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Truck className="h-4 w-4" />
                  <span className="text-sm">500+ Shipping Companies</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span className="text-sm">$2.3M Fraud Prevented</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
