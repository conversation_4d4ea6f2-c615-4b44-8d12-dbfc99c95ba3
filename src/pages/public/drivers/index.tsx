function HeroSection() {
  return (
    <section className="bg-gradient-to-br from-orange-50 to-amber-100 px-6 py-16 dark:from-orange-950/30 dark:to-amber-950/30">
      <div className="mx-auto max-w-4xl text-center">
        <header className="space-y-6">
          <h1 className="text-foreground text-4xl leading-tight font-bold md:text-6xl">
            Drive. Deliver. Get Paid. All Without the Paper Chase.
          </h1>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl leading-relaxed md:text-2xl">
            QuikSkope is your digital co-pilot — streamlining your paperwork,
            managing your credentials, and making sure every mile gets you paid
            faster.
          </p>
          <div className="flex flex-col justify-center gap-4 pt-6 sm:flex-row">
            <button className="rounded-lg bg-orange-600 px-8 py-3 font-semibold text-white transition-colors hover:bg-orange-700">
              Join the Network
            </button>
            <button className="rounded-lg border-2 border-orange-600 px-8 py-3 font-semibold text-orange-600 transition-colors hover:bg-orange-50 dark:hover:bg-orange-950/20">
              Watch a Demo
            </button>
          </div>
        </header>
      </div>
    </section>
  );
}

function DriverFocusSection() {
  return (
    <section className="bg-background px-6 py-16">
      <div className="mx-auto max-w-4xl text-center">
        <header className="space-y-6">
          <h2 className="text-foreground text-3xl font-bold md:text-4xl">
            Curated for Drivers Like You
          </h2>
          <p className="text-foreground text-xl leading-relaxed">
            You're not just hauling loads. You're running a business on the
            move.
          </p>
          <p className="text-muted-foreground text-lg leading-relaxed">
            That's why we built QuikSkope to handle the heavy admin — so you can
            stay focused on the road ahead.
          </p>
        </header>
      </div>
    </section>
  );
}

function SmarterPickupsSection() {
  return (
    <section className="bg-muted/30 px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Smarter Pickups & Deliveries — Powered by Workflow Intelligence
          </h2>
        </header>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-3 text-xl font-semibold">
              Arrive Smarter, Not Sooner
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Our system notifies shippers automatically before you arrive,
              helping them prepare your load in advance.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-3 text-xl font-semibold">
              Real-Time Wait Estimates
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              See projected wait times before you pull in. Know whether to grab
              lunch or get in line.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-3 text-xl font-semibold">
              Enabled by Real-Time Tracking
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              With optional real-time location tracking, QuikSkope keeps
              everyone in sync — dispatch, shippers, and receivers.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-3 text-xl font-semibold">
              Digital Signatures at Every Step
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              At pickup and dropoff, sign paperwork digitally on the spot — so
              the paperwork doesn't get lost in the shuffle.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-3 text-xl font-semibold">
              Verified at Every Step
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Pickup and delivery are logged with GPS, QR, and visual capture —
              no need to explain or chase signatures.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-3 text-xl font-semibold">
              Instant Sync With Dispatch
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Your status updates in real-time so nobody's left guessing.
            </p>
          </article>
        </div>
      </div>
    </section>
  );
}

function DigitalPaperworkSection() {
  return (
    <section className="bg-blue-50/50 px-6 py-16 dark:bg-blue-950/20">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Digital Paperwork That Stays Put
          </h2>
        </header>

        <div className="grid gap-8 md:grid-cols-3">
          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Auto-Organized Docs
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Snap a photo — we handle the rest. Pick-up slips, BOLs, delivery
              receipts — digitized, tagged, and ready when you need them.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Secure Doc Locker
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Your CDL, medical card, endorsements, and insurance — all stored
              securely, with reminders before they expire.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Easy Renewals
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              We'll guide you through renewals, schedule reminders, and pre-fill
              forms where possible.
            </p>
          </article>
        </div>
      </div>
    </section>
  );
}

function SmarterShipmentsSection() {
  return (
    <section className="bg-background px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Smarter Shipments — With Full Document Flow
          </h2>
          <p className="text-foreground mb-2 text-xl">
            Everything Documented. Everything Synced.
          </p>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
            From BOLs to pickup slips to seal verifications — capture it all
            digitally, right from your mobile device or supported hardware.
          </p>
        </header>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <article className="rounded-lg border border-emerald-200 bg-emerald-50/50 p-6 dark:border-emerald-800 dark:bg-emerald-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Seal Capture & Verification
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Document the seal at pickup and delivery — including visual
              confirmation and metadata — to automatically build a secure
              shipment record.
            </p>
          </article>

          <article className="rounded-lg border border-emerald-200 bg-emerald-50/50 p-6 dark:border-emerald-800 dark:bg-emerald-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Instant Paper Trail
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Each step is logged and shared with all stakeholders. That means
              no lost paperwork and better protection when disputes arise.
            </p>
          </article>

          <article className="rounded-lg border border-emerald-200 bg-emerald-50/50 p-6 dark:border-emerald-800 dark:bg-emerald-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Shipment Performance Score
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Verified documentation builds your report score — a signal of
              trust used across the network to unlock better loads and faster
              payments.
            </p>
          </article>

          <article className="rounded-lg border border-emerald-200 bg-emerald-50/50 p-6 dark:border-emerald-800 dark:bg-emerald-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Synced to Every Step of the Workflow
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Shipping documentation feeds directly into dispatch, invoicing,
              and payment — no delays, no repeats, no mystery.
            </p>
          </article>

          <article className="rounded-lg border border-emerald-200 bg-emerald-50/50 p-6 dark:border-emerald-800 dark:bg-emerald-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Smart Device Flexibility
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Use supported devices (dash-mounted, mobile, or handheld — we're
              flexible) to record and upload documents in real time.
            </p>
          </article>
        </div>
      </div>
    </section>
  );
}

function GetPaidSection() {
  return (
    <section className="bg-green-50/50 px-6 py-16 dark:bg-green-950/20">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Get Paid Without the Wait
          </h2>
        </header>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <article className="bg-card rounded-lg border-l-4 border-green-500 p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Instant Payment Pipeline
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Delivery verified? You get paid — often within minutes. No
              chasing, no calls, no invoices lost in the shuffle.
            </p>
          </article>

          <article className="bg-card rounded-lg border-l-4 border-green-500 p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Tip-Enabled Jobs
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Shippers and brokers can add tips for outstanding performance,
              professionalism, or just getting the job done right.
            </p>
          </article>

          <article className="bg-card rounded-lg border-l-4 border-green-500 p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Streamlined for Everyone
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              If the entity isn't on the platform yet, we've built a fast-track
              process to help you get paid faster — even from manual systems.
            </p>
          </article>

          <article className="bg-card rounded-lg border-l-4 border-green-500 p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Payment Transparency
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Track every cent. Know what's pending, what's processed, and when
              it's landing.
            </p>
          </article>

          <article className="bg-card rounded-lg border-l-4 border-green-500 p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              No More Middlemen
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Our system integrates directly with shippers, brokers, and fleets.
              You stay in the loop.
            </p>
          </article>
        </div>
      </div>
    </section>
  );
}

function AIDispatcherSection() {
  return (
    <section className="bg-purple-50/50 px-6 py-16 dark:bg-purple-950/20">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Meet the AI Dispatcher
          </h2>
        </header>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Your Smart Load Planner
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Our AI Dispatcher finds verified loads that match your route,
              equipment, and history.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              No More Ghost Lanes
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Backhaul routes are optimized to keep you earning — no more empty
              drives.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Maximize Your Miles
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Take on high-trust lanes on the way back from shipments, turning
              QuikSkope into a second source of income.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Personalized Recommendations
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Prefer certain lanes? Avoid cities during rush hour? We learn and
              adjust.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Balanced Workloads
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Suggests breaks, rest stops, and fuel planning — so you stay
              efficient and legal.
            </p>
          </article>
        </div>
      </div>
    </section>
  );
}

function SupportAgentsSection() {
  return (
    <section className="bg-background px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Built-In Support Agents
          </h2>
        </header>

        <div className="grid gap-8 md:grid-cols-3">
          <article className="rounded-lg border border-amber-200 bg-amber-50/50 p-6 dark:border-amber-800 dark:bg-amber-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Voice-Activated Help
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Hands-free chat for anything from document uploads to account
              questions.
            </p>
          </article>

          <article className="rounded-lg border border-amber-200 bg-amber-50/50 p-6 dark:border-amber-800 dark:bg-amber-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Roadside Assistant Agent
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Stuck, stalled, or need info fast? Our AI agent is there with
              guides, checklists, and live support options.
            </p>
          </article>

          <article className="rounded-lg border border-amber-200 bg-amber-50/50 p-6 dark:border-amber-800 dark:bg-amber-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Multilingual Support
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Spanish, English, and more. Built for the drivers who keep America
              moving.
            </p>
          </article>
        </div>
      </div>
    </section>
  );
}

function GamificationSection() {
  return (
    <section className="bg-indigo-50/50 px-6 py-16 dark:bg-indigo-950/20">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Level Up With Driver Data & Gamification
          </h2>
        </header>

        <div className="grid gap-8 md:grid-cols-3">
          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Drive, Track, Earn Badges
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Hit mileage milestones, complete safe deliveries, and get
              rewarded. It's not just driving — it's progress you can see.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              See How You Stack Up
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Compare your stats with regional and national averages. Identify
              ways to improve, earn more, and get ahead.
            </p>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Feedback-Driven Growth
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              We don't just track your data — we help you act on it with tips,
              trends, and nudges built to improve your bottom line.
            </p>
          </article>
        </div>
      </div>
    </section>
  );
}

function CallToActionSection() {
  return (
    <section className="bg-gradient-to-br from-orange-600 to-red-700 px-6 py-16">
      <div className="mx-auto max-w-4xl text-center">
        <header className="space-y-6">
          <h2 className="text-3xl font-bold text-white md:text-4xl">
            Take the Wheel of Your Career
          </h2>
          <p className="mx-auto max-w-2xl text-xl text-orange-100">
            No more faxing slips. No more being ghosted on payments. No more
            surprise expirations.
          </p>
          <p className="mx-auto max-w-2xl text-lg text-orange-100">
            QuikSkope is the driver's command center — finally, all in one app.
          </p>
          <div className="flex flex-col justify-center gap-4 pt-6 sm:flex-row">
            <button className="rounded-lg bg-white px-8 py-3 font-semibold text-orange-600 transition-colors hover:bg-gray-100">
              Sign Up Today
            </button>
            <button className="rounded-lg border-2 border-white px-8 py-3 font-semibold text-white transition-colors hover:bg-white hover:text-orange-600">
              Try the Demo Experience
            </button>
          </div>
        </header>
      </div>
    </section>
  );
}

export default function DriversPage() {
  return (
    <main>
      <HeroSection />
      <DriverFocusSection />
      <SmarterPickupsSection />
      <DigitalPaperworkSection />
      <SmarterShipmentsSection />
      <GetPaidSection />
      <AIDispatcherSection />
      <SupportAgentsSection />
      <GamificationSection />
      <CallToActionSection />
    </main>
  );
}
