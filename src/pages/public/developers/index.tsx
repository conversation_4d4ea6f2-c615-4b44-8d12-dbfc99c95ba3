import {
  Book,
  CheckCircle,
  Clock,
  Code,
  Cpu,
  Database,
  Download,
  FileText,
  Github,
  Globe,
  Link,
  Lock,
  Play,
  Settings,
  Shield,
  Terminal,
  TrendingUp,
  Users,
  Webhook,
  Zap,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

function HeroSection() {
  return (
    <section className="relative overflow-hidden py-20 lg:py-32">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900 dark:from-gray-950 dark:via-gray-900 dark:to-indigo-950"></div>
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <Badge className="mb-6 border-blue-700 bg-gradient-to-r from-blue-900/50 to-indigo-900/50 text-blue-200">
            Developer-First Logistics Platform
          </Badge>
          <h1 className="mb-6 text-4xl font-bold text-white md:text-6xl">
            Build on{" "}
            <span className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
              QuikSkope
            </span>
          </h1>
          <p className="mx-auto mb-8 max-w-3xl text-xl text-gray-300">
            Plug into our verification engine and logistics stack. Build custom
            integrations, automate workflows, and create seamless freight
            experiences with our comprehensive API suite.
          </p>
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              <Code className="mr-2 h-5 w-5" />
              Get API Access
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-gray-600 bg-transparent text-white hover:bg-gray-800 dark:hover:bg-gray-700"
            >
              <Book className="mr-2 h-5 w-5" />
              View Documentation
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

function ApiOverviewSection() {
  return (
    <section className="border-border border-t py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="relative">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]"></div>

          <div className="bg-card/80 relative rounded-3xl border border-blue-100/50 p-8 backdrop-blur-sm md:p-12 dark:border-blue-900/50">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-500">
                <Terminal className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
                Comprehensive API Suite
              </h2>
              <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
                Everything you need to integrate freight verification, tracking,
                and payments into your applications.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-3">
              <Card className="border-border border transition-all hover:border-blue-200 hover:shadow-lg dark:hover:border-blue-800">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-indigo-500">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Verification API</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Programmatically manage handoffs, track verification events,
                    and trigger compliance rules.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-blue-100 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                      REST API
                    </Badge>
                    <Badge className="border-0 bg-blue-100 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                      Webhooks
                    </Badge>
                    <Badge className="border-0 bg-blue-100 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                      Real-time
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-border border transition-all hover:border-green-200 hover:shadow-lg dark:hover:border-green-800">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-green-500 to-emerald-500">
                    <Database className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Shipment Ingestion</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Send load data directly to our system to initiate workflows
                    and assign carriers.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-green-100 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      Bulk import
                    </Badge>
                    <Badge className="border-0 bg-green-100 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      Real-time sync
                    </Badge>
                    <Badge className="border-0 bg-green-100 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      Auto-matching
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-border border transition-all hover:border-purple-200 hover:shadow-lg dark:hover:border-purple-800">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-purple-500 to-pink-500">
                    <Webhook className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Event Hooks</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Get notified when shipments reach key milestones — pickups,
                    deliveries, document signings.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="border-0 bg-purple-100 text-xs text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                      Real-time events
                    </Badge>
                    <Badge className="border-0 bg-purple-100 text-xs text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                      Reliable delivery
                    </Badge>
                    <Badge className="border-0 bg-purple-100 text-xs text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                      Custom filters
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* API Stats */}
            <div className="border-border mt-12 border-t pt-8">
              <div className="grid gap-8 text-center md:grid-cols-4">
                <div>
                  <div className="mb-2 text-3xl font-bold text-blue-600">
                    99.9%
                  </div>
                  <p className="text-muted-foreground">API uptime</p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-green-600">
                    &lt; 100ms
                  </div>
                  <p className="text-muted-foreground">Average response time</p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-purple-600">
                    24/7
                  </div>
                  <p className="text-muted-foreground">Developer support</p>
                </div>
                <div>
                  <div className="mb-2 text-3xl font-bold text-indigo-600">
                    50+
                  </div>
                  <p className="text-muted-foreground">API endpoints</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function CodeExamplesSection() {
  return (
    <section className="border-border border-t py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-gray-800 to-gray-900">
            <Code className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Simple Integration
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
            Get started in minutes with our intuitive REST API and comprehensive
            SDKs.
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* Code Example 1 */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-gray-800/5 to-gray-900/5"></div>
            <div className="border-border bg-card/80 relative rounded-3xl border p-8 backdrop-blur-sm">
              <div className="mb-4 flex items-center space-x-2">
                <Terminal className="h-5 w-5 text-green-500" />
                <span className="font-mono text-sm text-green-500">
                  Track Shipment Verification
                </span>
              </div>
              <div className="overflow-x-auto rounded-xl bg-gray-900 p-6">
                <pre className="font-mono text-sm text-gray-300">
                  {`// Track shipment verification
const response = await fetch('/api/shipments/track', {
  method: 'POST',
  headers: { 
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    shipment_id: 'QS-2024-001',
    event: 'pickup_verified',
    location: { lat: 41.8781, lng: -87.6298 },
    verification_data: {
      driver_id: 'DRV-12345',
      signature: 'base64_signature',
      photos: ['pickup_photo_1.jpg']
    }
  })
});

const result = await response.json();
console.log('Verification recorded:', result);`}
                </pre>
              </div>
            </div>
          </div>

          {/* Code Example 2 */}
          <div className="relative">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5"></div>
            <div className="bg-card/80 relative rounded-3xl border border-blue-100/50 p-8 backdrop-blur-sm dark:border-blue-900/50">
              <div className="mb-4 flex items-center space-x-2">
                <Webhook className="h-5 w-5 text-blue-500" />
                <span className="font-mono text-sm text-blue-500">
                  Webhook Event Handler
                </span>
              </div>
              <div className="overflow-x-auto rounded-xl bg-gray-900 p-6">
                <pre className="font-mono text-sm text-gray-300">
                  {`// Handle webhook events
app.post('/webhooks/quikskope', (req, res) => {
  const { event_type, shipment_id, data } = req.body;
  
  switch (event_type) {
    case 'shipment.pickup_verified':
      console.log(\`Pickup verified for \${shipment_id}\`);
      updateShipmentStatus(shipment_id, 'in_transit');
      break;
      
    case 'shipment.delivered':
      console.log(\`Delivery completed for \${shipment_id}\`);
      triggerPayment(shipment_id);
      break;
      
    case 'shipment.exception':
      console.log(\`Exception reported for \${shipment_id}\`);
      alertDispatch(shipment_id, data.exception);
      break;
  }
  
  res.status(200).send('OK');
});`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function IntegrationFeaturesSection() {
  return (
    <section className="border-border border-t py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="relative">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-green-500/5 to-emerald-500/5"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_20%,rgba(34,197,94,0.1),transparent_50%)]"></div>

          <div className="bg-card/80 relative rounded-3xl border border-green-100/50 p-8 backdrop-blur-sm md:p-12 dark:border-green-900/50">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500">
                <Settings className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
                Integration Features
              </h2>
              <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
                Powerful tools to connect QuikSkope with your existing systems
                and workflows.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <Card className="border border-green-200 bg-green-50/50 transition-all hover:shadow-lg dark:border-green-800 dark:bg-green-950/20">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-green-500 to-emerald-500">
                    <Cpu className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Real-time Data Sync</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Bi-directional sync with your TMS, ERP, and accounting
                    systems. Keep all platforms updated automatically.
                  </p>
                </CardContent>
              </Card>

              <Card className="border border-green-200 bg-green-50/50 transition-all hover:shadow-lg dark:border-green-800 dark:bg-green-950/20">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-emerald-500 to-green-500">
                    <Lock className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Secure Authentication</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    OAuth 2.0, API keys, and webhook signatures ensure your
                    integrations are secure and compliant.
                  </p>
                </CardContent>
              </Card>

              <Card className="border border-green-200 bg-green-50/50 transition-all hover:shadow-lg dark:border-green-800 dark:bg-green-950/20">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-green-600 to-emerald-600">
                    <FileText className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Document Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Upload, retrieve, and manage shipment documents
                    programmatically. Full CRUD operations with metadata.
                  </p>
                </CardContent>
              </Card>

              <Card className="border border-green-200 bg-green-50/50 transition-all hover:shadow-lg dark:border-green-800 dark:bg-green-950/20">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-emerald-500 to-green-500">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Automated Workflows</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Trigger custom business logic based on shipment events.
                    Build complex automation with simple API calls.
                  </p>
                </CardContent>
              </Card>

              <Card className="border border-green-200 bg-green-50/50 transition-all hover:shadow-lg dark:border-green-800 dark:bg-green-950/20">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-green-500 to-emerald-500">
                    <Globe className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Multi-tenant Support</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Build applications that serve multiple customers with
                    isolated data and configurable permissions.
                  </p>
                </CardContent>
              </Card>

              <Card className="border border-green-200 bg-green-50/50 transition-all hover:shadow-lg dark:border-green-800 dark:bg-green-950/20">
                <CardHeader>
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-emerald-600 to-green-600">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle>Analytics & Reporting</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Access detailed analytics on shipment performance, carrier
                    ratings, and operational metrics via API.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function DeveloperResourcesSection() {
  return (
    <section className="border-border border-t py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-600 to-indigo-600">
            <Book className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Developer Resources
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
            Everything you need to build, test, and deploy your QuikSkope
            integrations.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border-border border text-center transition-all hover:border-purple-200 hover:shadow-lg dark:hover:border-purple-800">
            <CardHeader>
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-purple-500 to-indigo-500">
                <Book className="h-6 w-6 text-white" />
              </div>
              <CardTitle>API Documentation</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Comprehensive guides, examples, and reference materials.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Book className="mr-2 h-4 w-4" />
                View Docs
              </Button>
            </CardContent>
          </Card>

          <Card className="border-border border text-center transition-all hover:border-green-200 hover:shadow-lg dark:hover:border-green-800">
            <CardHeader>
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-green-500 to-emerald-500">
                <Play className="h-6 w-6 text-white" />
              </div>
              <CardTitle>Interactive Playground</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Test API calls and explore responses in real-time.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Play className="mr-2 h-4 w-4" />
                Try Now
              </Button>
            </CardContent>
          </Card>

          <Card className="border-border border text-center transition-all hover:border-blue-200 hover:shadow-lg dark:hover:border-blue-800">
            <CardHeader>
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-blue-600">
                <Download className="h-6 w-6 text-white" />
              </div>
              <CardTitle>SDKs & Libraries</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Official SDKs for JavaScript, Python, PHP, and more.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </CardContent>
          </Card>

          <Card className="border-border hover:border-border border text-center transition-all hover:shadow-lg">
            <CardHeader>
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-gray-700 to-gray-800">
                <Github className="h-6 w-6 text-white" />
              </div>
              <CardTitle>Sample Projects</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Open-source examples and starter templates on GitHub.
              </p>
              <Button variant="outline" size="sm" className="w-full">
                <Github className="mr-2 h-4 w-4" />
                View GitHub
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

function UseCasesSection() {
  return (
    <section className="border-border border-t py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="relative">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-orange-500/5 to-red-500/5"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(249,115,22,0.1),transparent_50%)]"></div>

          <div className="bg-card/80 relative rounded-3xl border border-orange-100/50 p-8 backdrop-blur-sm md:p-12 dark:border-orange-900/50">
            <div className="mb-12 text-center">
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-orange-500 to-red-500">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
                Common Use Cases
              </h2>
              <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
                See how developers are using QuikSkope APIs to build innovative
                logistics solutions.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-3">
              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-orange-500 to-red-500 transition-transform group-hover:scale-110">
                  <Link className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-foreground mb-2 text-xl font-semibold">
                  TMS Integration
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Connect existing transportation management systems with
                  QuikSkope's verification and tracking capabilities.
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-red-500 to-pink-500 transition-transform group-hover:scale-110">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-foreground mb-2 text-xl font-semibold">
                  Customer Portals
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Build branded tracking portals that give your customers
                  real-time visibility into their shipments.
                </p>
              </div>

              <div className="group text-center">
                <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-pink-500 to-purple-500 transition-transform group-hover:scale-110">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-foreground mb-2 text-xl font-semibold">
                  Compliance Automation
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Automate compliance checks, document validation, and audit
                  trail generation for regulatory requirements.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function CallToActionSection() {
  return (
    <section className="py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="relative">
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-gray-800/5 to-gray-900/5"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(55,65,81,0.1),transparent_50%)]"></div>

          <div className="border-border bg-card/80 relative rounded-3xl border p-8 backdrop-blur-sm md:p-12">
            <div className="text-center">
              <div className="mb-8 inline-flex h-20 w-20 items-center justify-center rounded-3xl bg-gradient-to-r from-gray-800 to-gray-900">
                <Code className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-foreground mb-6 text-4xl font-bold md:text-5xl">
                Ready to Start Building?
              </h2>
              <p className="text-muted-foreground mx-auto mb-8 max-w-2xl text-xl">
                Join developers who are transforming logistics with QuikSkope's
                powerful APIs and seamless integrations.
              </p>

              <div className="mb-12 flex flex-col justify-center gap-4 sm:flex-row">
                <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-4 text-lg hover:from-blue-700 hover:to-indigo-700">
                  <Code className="mr-2 h-5 w-5" />
                  Get API Access
                </Button>
                <Button
                  variant="outline"
                  className="border-border hover:bg-muted/50 px-8 py-4 text-lg"
                >
                  <Terminal className="mr-2 h-5 w-5" />
                  View Documentation
                </Button>
              </div>

              {/* Developer Stats */}
              <div className="border-border border-t pt-8">
                <p className="text-muted-foreground mb-4 text-center text-sm">
                  Trusted by innovative development teams
                </p>
                <div className="text-muted-foreground flex items-center justify-center space-x-8">
                  <div className="flex items-center space-x-2">
                    <Code className="h-4 w-4" />
                    <span className="text-sm">50+ API Endpoints</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">99.9% Uptime</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span className="text-sm">24/7 Support</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function DevelopersPage() {
  return (
    <div>
      <HeroSection />
      <ApiOverviewSection />
      <CodeExamplesSection />
      <IntegrationFeaturesSection />
      <DeveloperResourcesSection />
      <UseCasesSection />
      <CallToActionSection />
    </div>
  );
}
