const Privacy = () => {
  return (
    <div className="mx-auto max-w-7xl px-4 py-20">
      <div className="mb-16 text-center">
        <h1 className="text-foreground mb-6 text-4xl font-bold md:text-5xl">
          Privacy Policy
        </h1>
        <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
          How we handle and protect your data
        </p>
      </div>
      <div className="glass-effect mx-auto max-w-4xl space-y-8 rounded-lg p-8">
        <div>
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            Data Collection
          </h2>
          <p className="text-muted-foreground">
            At QuikSkope, we take your privacy seriously. We collect and process
            data that is necessary to provide our services, including personal
            information, usage data, and transaction details.
          </p>
        </div>

        <div>
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            Data Usage
          </h2>
          <p className="text-muted-foreground">
            Your data is used to provide and improve our services, prevent
            fraud, and ensure the security of transactions on our platform. We
            never sell your personal information to third parties.
          </p>
        </div>

        <div>
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            Data Protection
          </h2>
          <p className="text-muted-foreground">
            We implement industry-standard security measures to protect your
            data from unauthorized access, disclosure, or misuse. This includes
            encryption, secure servers, and regular security audits.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Privacy;
