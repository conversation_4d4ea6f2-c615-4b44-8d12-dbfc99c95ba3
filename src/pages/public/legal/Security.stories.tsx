import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import {
  reactRouterParameters,
  withRouter,
} from "storybook-addon-remix-react-router";

import Security from "./Security";

const meta: Meta<typeof Security> = {
  title: "Pages/Public/Legal/Security",
  component: Security,
  decorators: [withRouter],
  parameters: {
    layout: "fullscreen",
    reactRouter: reactRouterParameters({
      location: {
        pathParams: {},
      },
      routing: { path: "/legal/security" },
    }),
  },
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
