const Security = () => {
  return (
    <div className="mx-auto max-w-7xl px-4 py-20">
      <div className="mb-16 text-center">
        <h1 className="text-foreground mb-6 text-4xl font-bold md:text-5xl">
          Security
        </h1>
        <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
          How we protect your data and operations
        </p>
      </div>
      <div className="glass-effect mx-auto max-w-4xl rounded-lg p-8">
        <div className="space-y-8">
          <div>
            <h2 className="text-foreground mb-4 text-2xl font-bold">
              Data Protection
            </h2>
            <p className="text-muted-foreground">
              We employ industry-leading encryption and security protocols to
              protect your sensitive information. All data is encrypted both in
              transit and at rest.
            </p>
          </div>

          <div>
            <h2 className="text-foreground mb-4 text-2xl font-bold">
              Infrastructure Security
            </h2>
            <p className="text-muted-foreground">
              Our infrastructure is hosted on secure, enterprise-grade cloud
              platforms with multiple layers of security and redundancy.
            </p>
          </div>

          <div>
            <h2 className="text-foreground mb-4 text-2xl font-bold">
              Access Control
            </h2>
            <p className="text-muted-foreground">
              Strict access controls and authentication mechanisms ensure that
              only authorized personnel can access sensitive systems and data.
            </p>
          </div>

          <div>
            <h2 className="text-foreground mb-4 text-2xl font-bold">
              Compliance
            </h2>
            <p className="text-muted-foreground">
              We maintain compliance with industry standards and regularly
              undergo security audits to ensure the highest level of protection.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Security;
