import { Link } from "react-router";

import { ScrollArea } from "@/components/ui/scroll-area";

const Legal = () => {
  return (
    <div className="mx-auto max-w-7xl px-4 py-20">
      <div className="mb-16 text-center">
        <h1 className="text-foreground mb-6 text-4xl font-bold md:text-5xl">
          Legal Information
        </h1>
        <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
          Important documents and policies
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        <Link
          to="/legal/terms"
          className="glass-effect hover-lift rounded-lg p-8"
        >
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            Terms of Service
          </h2>
          <ScrollArea className="h-[200px] rounded-md">
            <div className="text-muted-foreground space-y-4 pr-4">
              <p>Last updated: March 14, 2024</p>
              <p>
                Please read these Terms of Service carefully before using
                QuikSkope. By using our service, you agree to be bound by these
                terms.
              </p>
              <h3 className="text-foreground text-lg font-semibold">
                1. Services Overview
              </h3>
              <p>
                QuikSkope provides supply chain verification and security
                services. Our platform helps prevent fraud and ensure secure
                transactions in the logistics industry.
              </p>
            </div>
          </ScrollArea>
        </Link>

        <Link
          to="/legal/privacy"
          className="glass-effect hover-lift rounded-lg p-8"
        >
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            Privacy Policy
          </h2>
          <ScrollArea className="h-[200px] rounded-md">
            <div className="text-muted-foreground space-y-4 pr-4">
              <p>Last updated: March 14, 2024</p>
              <p>
                Your privacy is important to us. This Privacy Policy explains
                how we collect, use, and protect your personal information.
              </p>
              <h3 className="text-foreground text-lg font-semibold">
                1. Data Collection
              </h3>
              <p>
                We collect information necessary to provide our services,
                including but not limited to contact details, transaction data,
                and usage information.
              </p>
            </div>
          </ScrollArea>
        </Link>

        <Link
          to="/legal/security"
          className="glass-effect hover-lift rounded-lg p-8"
        >
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            Security Policy
          </h2>
          <p className="text-muted-foreground">
            Learn about our robust security measures and how we protect your
            data
          </p>
        </Link>

        <Link
          to="/legal/compliance"
          className="glass-effect hover-lift rounded-lg p-8"
        >
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            Compliance
          </h2>
          <p className="text-muted-foreground">
            Information about our compliance with industry standards and
            regulations
          </p>
        </Link>
      </div>
    </div>
  );
};

export default Legal;
