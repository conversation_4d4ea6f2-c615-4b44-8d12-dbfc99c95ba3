const Terms = () => {
  return (
    <div className="mx-auto max-w-7xl px-4 py-20">
      <div className="mb-16 text-center">
        <h1 className="text-foreground mb-6 text-4xl font-bold md:text-5xl">
          Terms of Service
        </h1>
        <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
          Our service agreement and usage terms
        </p>
      </div>
      <div className="glass-effect mx-auto max-w-4xl space-y-8 rounded-lg p-8">
        <div>
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            1. Acceptance of Terms
          </h2>
          <p className="text-muted-foreground">
            By accessing or using QuikSkope's services, you agree to be bound by
            these Terms of Service and all applicable laws and regulations.
          </p>
        </div>

        <div>
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            2. Service Description
          </h2>
          <p className="text-muted-foreground">
            QuikSkope provides supply chain verification and security services.
            Our platform helps prevent fraud and ensure secure transactions in
            the logistics industry.
          </p>
        </div>

        <div>
          <h2 className="text-foreground mb-4 text-2xl font-bold">
            3. User Obligations
          </h2>
          <p className="text-muted-foreground">
            Users must provide accurate information and maintain the security of
            their account credentials. Any suspicious activity should be
            reported immediately.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Terms;
