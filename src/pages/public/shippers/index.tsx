function HeroSection() {
  return (
    <section className="bg-gradient-to-br from-blue-50 to-indigo-100 px-6 py-16 dark:from-blue-950/30 dark:to-indigo-950/30">
      <div className="mx-auto max-w-4xl text-center">
        <header className="space-y-6">
          <h1 className="text-foreground text-4xl leading-tight font-bold md:text-6xl">
            Verified, Tracked, Delivered — With Zero Guesswork.
          </h1>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl leading-relaxed md:text-2xl">
            From dispatch to dropoff, QuikSkope brings real-time visibility,
            fraud-proof workflows, and verified carrier performance — all in one
            logistics dashboard.
          </p>
          <div className="flex flex-col justify-center gap-4 pt-6 sm:flex-row">
            <button className="rounded-lg bg-blue-600 px-8 py-3 font-semibold text-white transition-colors hover:bg-blue-700">
              Book a Demo
            </button>
            <button className="rounded-lg border-2 border-blue-600 px-8 py-3 font-semibold text-blue-600 transition-colors hover:bg-blue-50 dark:hover:bg-blue-950/20">
              Get Started
            </button>
          </div>
        </header>
      </div>
    </section>
  );
}

function LogisticsIntelligenceSection() {
  return (
    <section className="bg-background px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Logistics Intelligence at a Glance
          </h2>
          <p className="text-foreground mb-6 text-xl">
            The Dashboard Built for Modern Freight Ops
          </p>
          <ul className="text-muted-foreground mx-auto max-w-3xl space-y-2 text-lg">
            <li className="flex items-start">
              <span className="mt-3 mr-3 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></span>
              Track all your active shipments with real-time location data.
            </li>
            <li className="flex items-start">
              <span className="mt-3 mr-3 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></span>
              Instantly view driver ETA, progress, and verification status.
            </li>
            <li className="flex items-start">
              <span className="mt-3 mr-3 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></span>
              Get notified if a shipment is running late, idle, or at risk.
            </li>
          </ul>
        </header>

        <div className="grid gap-8 md:grid-cols-2">
          <article className="bg-muted/20 border-border rounded-lg border p-6">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Dynamic Load Planning
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                AI-powered recommendations for dispatching loads efficiently.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Built-in return lane optimization to reduce ghost miles.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Suggested matches based on equipment type, certifications, and
                score.
              </li>
            </ul>
          </article>

          <article className="bg-muted/20 border-border rounded-lg border p-6">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Work With Trusted Drivers — Or Bring Your Own
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                All recommended drivers come from our vetted network.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Prefer your own carriers? Easily add them to the platform.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                External carriers receive secure links to verify shipments and
                enable real-time tracking — no app downloads required.
              </li>
            </ul>
          </article>

          <article className="bg-muted/20 border-border rounded-lg border p-6">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Full Load Visibility & Incident Awareness
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Real-time tracking is built-in — no setup needed.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                View current location, route progress, and estimated delivery
                time.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Monitor shipment conditions, including temperature, humidity,
                and handling events (where supported).
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Know what type of truck is in use, how your load is being
                handled, and whether protocols are being followed.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Receive incident reports instantly — from delays to exceptions
                to deviations from protocol.
              </li>
            </ul>
          </article>

          <article className="bg-muted/20 border-border rounded-lg border p-6">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Shipment Document Management
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Keep every shipment's paperwork in one place — no more folders
                of scattered BOLs.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Upload, tag, and organize pickup slips, photos, signatures, and
                receipts in a single view.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Smarter attribution of documents connects files to each shipment
                automatically.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Supports quicker payments and better compliance by ensuring
                documentation is accurate, complete, and live.
              </li>
              <li className="flex items-start">
                <span className="bg-muted-foreground mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full"></span>
                Enables live signatures from both shipper and driver to ensure
                real-time accountability.
              </li>
            </ul>
          </article>
        </div>
      </div>
    </section>
  );
}

function VerificationProtocolSection() {
  return (
    <section className="bg-blue-50/50 px-6 py-16 dark:bg-blue-950/20">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Verification Protocol
          </h2>
        </header>

        <div className="grid gap-8 md:grid-cols-2">
          <article className="bg-card rounded-lg border border-blue-200 p-6 shadow-sm dark:border-blue-800">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Meet the Integrity Protocol
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Every pickup and dropoff is GPS-verified with supporting media.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Capture visual seal verification to ensure security.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Automatic digital signature capture from all parties.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Verification checkpoints are embedded at every handoff —
                including multi-stop and relay-style shipments.
              </li>
            </ul>
          </article>

          <article className="bg-card rounded-lg border border-blue-200 p-6 shadow-sm dark:border-blue-800">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Document Trail, Digitized
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                No more paper-chasing. Instantly view BOLs, slips, receipts, and
                driver ID.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Documents are tagged, time-stamped, and accessible in real time.
              </li>
            </ul>
          </article>

          <article className="bg-card rounded-lg border border-blue-200 p-6 shadow-sm dark:border-blue-800">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              AI-Enforced Rules & Custom Protocols
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Shippers and brokers can define their own enhanced verification
                protocols.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Custom instructions and rules are passed to our AI agents to
                enforce your operational standards.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Think of it as a virtual checklist that adapts to your
                compliance playbook.
              </li>
            </ul>
          </article>

          <article className="bg-card rounded-lg border border-blue-200 p-6 shadow-sm dark:border-blue-800">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Reduce Fraud. Increase Trust.
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Verified chains of custody ensure compliance and reduce risk.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                Audit logs keep every shipment accountable.
              </li>
            </ul>
          </article>
        </div>
      </div>
    </section>
  );
}

function PaymentsRatingsSection() {
  return (
    <section className="bg-background px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Payments & Ratings
          </h2>
        </header>

        <div className="grid gap-8 md:grid-cols-2">
          <article className="rounded-lg border border-green-200 bg-green-50/50 p-6 dark:border-green-800 dark:bg-green-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Transparent Payouts & Invoicing
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
                Pay carriers directly or send digital invoices through the
                platform.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
                Instant Payment Pipeline ensures faster payouts to drivers on
                the network.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
                Tips can be added by shippers and brokers to reward jobs well
                done.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
                Payment status is linked to document verification and shipment
                completion.
              </li>
            </ul>
          </article>

          <article className="rounded-lg border border-yellow-200 bg-yellow-50/50 p-6 dark:border-yellow-800 dark:bg-yellow-950/20">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Rating, Recognition & Trust
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-yellow-500"></span>
                Drivers earn ratings based on documentation, timeliness,
                communication, and professionalism.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-yellow-500"></span>
                RAP Port: A reputation metric that reflects proactive
                engagement, consistency, and reliability.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-yellow-500"></span>
                RAP Port is earned through first-mover actions, clean
                documentation, and successful handoffs.
              </li>
            </ul>
          </article>
        </div>
      </div>
    </section>
  );
}

function DispatchToolsSection() {
  return (
    <section className="bg-muted/30 px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Dispatch Tools & Automation
          </h2>
        </header>

        <div className="grid gap-8 md:grid-cols-2">
          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Dispatch With Confidence — Powered by Reputation
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-purple-500"></span>
                Discover and dispatch drivers based on real-time availability
                and RAP Port.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-purple-500"></span>
                Auto-match loads using performance data, route history, and
                compliance score.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-purple-500"></span>
                Route optimizer fills ghost lanes and maximizes carrier
                capacity.
              </li>
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-purple-500"></span>
                Multi-load dispatch planning for bulk scheduling and return lane
                efficiency.
              </li>
            </ul>
          </article>

          <article className="bg-card border-border rounded-lg border p-6 shadow-sm">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Smart Alerts
            </h3>
            <ul className="text-muted-foreground space-y-2">
              <li className="flex items-start">
                <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-purple-500"></span>
                Be alerted to delays, document issues, or verification
                mismatches — before they become problems.
              </li>
            </ul>
          </article>
        </div>
      </div>
    </section>
  );
}

function ComplianceSection() {
  return (
    <section className="bg-background px-6 py-16">
      <div className="mx-auto max-w-4xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Compliance & Certification Tracker
          </h2>
        </header>

        <article className="rounded-lg border border-red-200 bg-red-50/50 p-8 dark:border-red-800 dark:bg-red-950/20">
          <h3 className="text-foreground mb-4 text-xl font-semibold">
            Carrier Profiles with Live Docs
          </h3>
          <ul className="text-muted-foreground space-y-2">
            <li className="flex items-start">
              <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-red-500"></span>
              View current licenses, endorsements, medical cards, and insurance.
            </li>
            <li className="flex items-start">
              <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-red-500"></span>
              Expiry notifications ensure compliance across your fleet.
            </li>
          </ul>
        </article>
      </div>
    </section>
  );
}

function IntegrationsSection() {
  return (
    <section className="bg-muted/30 px-6 py-16">
      <div className="mx-auto max-w-4xl">
        <header className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            Seamless Integrations
          </h2>
        </header>

        <article className="bg-card border-border rounded-lg border p-8 shadow-sm">
          <h3 className="text-foreground mb-4 text-xl font-semibold">
            Connect Without Friction
          </h3>
          <ul className="text-muted-foreground space-y-2">
            <li className="flex items-start">
              <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-indigo-500"></span>
              Whether you're using legacy systems or spreadsheets, QuikSkope
              adapts.
            </li>
            <li className="flex items-start">
              <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-indigo-500"></span>
              Import your data via CSV or Excel — our AI will match and sync it
              instantly.
            </li>
            <li className="flex items-start">
              <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-indigo-500"></span>
              Connect with our growing ecosystem of logistics tools and TMS
              platforms.
            </li>
            <li className="flex items-start">
              <span className="mt-2 mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-indigo-500"></span>
              Designed to reduce setup time and speed up onboarding.
            </li>
          </ul>
        </article>
      </div>
    </section>
  );
}

function CallToActionSection() {
  return (
    <section className="bg-gradient-to-br from-indigo-600 to-blue-700 px-6 py-16">
      <div className="mx-auto max-w-4xl text-center">
        <header className="space-y-6">
          <h2 className="text-3xl font-bold text-white md:text-4xl">
            Powerful tools. Verified drivers. Zero guesswork.
          </h2>
          <p className="mx-auto max-w-2xl text-xl text-blue-100">
            QuikSkope brings security, visibility, and performance together for
            the modern shipper.
          </p>
          <div className="flex flex-col justify-center gap-4 pt-6 sm:flex-row">
            <button className="rounded-lg bg-white px-8 py-3 font-semibold text-blue-600 transition-colors hover:bg-gray-100">
              Book a Demo
            </button>
            <button className="rounded-lg border-2 border-white px-8 py-3 font-semibold text-white transition-colors hover:bg-white hover:text-blue-600">
              Start Shipping Smarter
            </button>
          </div>
        </header>
      </div>
    </section>
  );
}

export default function ShippersPage() {
  return (
    <main>
      <HeroSection />
      <LogisticsIntelligenceSection />
      <VerificationProtocolSection />
      <PaymentsRatingsSection />
      <DispatchToolsSection />
      <ComplianceSection />
      <IntegrationsSection />
      <CallToActionSection />
    </main>
  );
}
