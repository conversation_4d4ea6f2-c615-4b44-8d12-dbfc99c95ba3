import { Check, Flag, Milestone } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";

interface RoadmapItem {
  title: string;
  description: string;
  status: "completed" | "in-progress" | "planned";
  date?: string;
}

type Quarter = {
  name: string;
  items: RoadmapItem[];
};

const quarters: Quarter[] = [
  {
    name: "2025 Q1",
    items: [
      {
        title: "Initial Platform Launch",
        description:
          "Basic authentication, driver profiles, and organization management",
        status: "completed",
        date: "2025 Q1",
      },
      {
        title: "Shipment Management System",
        description: "Core shipment tracking and management capabilities",
        status: "in-progress",
        date: "2025 Q1",
      },
      {
        title: "Driver Profiles",
        description: "Enhanced driver profile management and features",
        status: "in-progress",
        date: "2025 Q1",
      },
      {
        title: "Shipment Verification",
        description: "Enhanced security measures for shipment validation",
        status: "planned",
        date: "2025 Q1",
      },
      {
        title: "Driver Real-time Tracking",
        description: "Live location tracking and route optimization",
        status: "planned",
        date: "2025 Q1",
      },
    ],
  },
  {
    name: "2025 Q2",
    items: [
      {
        title: "Driver Verification",
        description: "Identity verification and background check system",
        status: "planned",
        date: "2025 Q2",
      },
      {
        title: "Document System",
        description: "Digital document management system",
        status: "planned",
        date: "2025 Q2",
      },
      {
        title: "Document Recognition",
        description: "AI-powered document recognition and processing",
        status: "planned",
        date: "2025 Q2",
      },
    ],
  },
  {
    name: "2025 Q3",
    items: [
      {
        title: "Paper Trail System",
        description: "Comprehensive document tracking and audit capabilities",
        status: "planned",
        date: "2025 Q3",
      },
      {
        title: "Payment System Integration",
        description: "Secure payment processing and financial management",
        status: "planned",
        date: "2025 Q3",
      },
      {
        title: "Driver Tipping Feature",
        description: "In-app tipping system for rewarding excellent service",
        status: "planned",
        date: "2025 Q3",
      },
    ],
  },
];

const getStatusColor = (status: RoadmapItem["status"]) => {
  switch (status) {
    case "completed":
      return "bg-green-500/10 text-green-500";
    case "in-progress":
      return "bg-blue-500/10 text-blue-500";
    case "planned":
      return "bg-gray-500/10 text-gray-500";
  }
};

const Roadmap = () => {
  return (
    <div className="bg-background px-4 py-20">
      <div className="mx-auto max-w-5xl">
        <div className="mb-12 text-center">
          <h1 className="text-primary mb-4 text-4xl font-bold">
            Product Roadmap
          </h1>
          <p className="text-muted-foreground text-xl">
            Follow our journey as we build the future of logistics security
          </p>
        </div>

        <div className="space-y-16">
          {quarters.map((quarter, quarterIndex) => (
            <div key={quarterIndex} className="relative">
              <h2 className="text-primary mb-8 text-2xl font-semibold">
                {quarter.name}
              </h2>

              <div className="relative space-y-8">
                {/* Timeline line - positioned behind dots */}
                <div className="bg-primary/20 absolute top-0 bottom-0 left-[27px] w-[2px]" />

                {quarter.items.map((item, index) => (
                  <div key={index} className="group flex items-start gap-8">
                    {/* Timeline circle with dot inside */}
                    <div className="relative mt-4 flex items-center justify-center">
                      {/* Outer circle */}
                      <div
                        className={`absolute h-[26px] w-[26px] rounded-full border-2 ${
                          item.status === "completed"
                            ? "border-green-500"
                            : item.status === "in-progress"
                              ? "border-blue-500"
                              : "border-gray-300 group-hover:border-gray-400"
                        } `}
                      />
                      {/* Inner dot */}
                      <div
                        className={`relative z-10 h-[10px] w-[10px] rounded-full ${
                          item.status === "completed"
                            ? "bg-green-500"
                            : item.status === "in-progress"
                              ? "bg-blue-500"
                              : "bg-gray-300 group-hover:bg-gray-400"
                        } `}
                      />
                    </div>

                    <Card className="flex-1 p-6 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                      <div className="flex items-start gap-4">
                        <div
                          className={`mt-1 rounded-full p-2 ${getStatusColor(item.status)}`}
                        >
                          {item.status === "completed" ? (
                            <Check className="h-5 w-5" />
                          ) : item.status === "in-progress" ? (
                            <Flag className="h-5 w-5" />
                          ) : (
                            <Milestone className="h-5 w-5" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="mb-2 flex items-center justify-between">
                            <h3 className="text-xl font-semibold">
                              {item.title}
                            </h3>
                            <Badge
                              variant={
                                item.status === "completed"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {item.date}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </Card>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Roadmap;
