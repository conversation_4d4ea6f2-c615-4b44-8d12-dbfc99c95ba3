# Cursor AI Prompt: Logistics Platform Homepage

## Project Overview

Create a modern, driver-centric logistics platform homepage that showcases our comprehensive solution for patching industry gaps. The platform focuses on security, tracking, and paper trail management with a gamified driver experience.

## Homepage Structure & Content

### Hero Section

- **Headline**: "Complete Logistics Solution - Security, Tracking, Paper Trail"
- **Subheadline**: "The only platform that brings together all the gaps in logistics with a driver-first approach"
- **Primary CTA**: "Driver Login" (prominent button)
- **Secondary CTA**: "View Demo" (leads to map-based shipment demo)

### Core Value Propositions (3-column layout)

1. **Security First**
   - Systematic verification checks
   - Human-readable rules engine
   - LLM + multimodal vision model integration
   - Configurable integrity protocols

2. **Real-Time Tracking**
   - Live shipment monitoring
   - Automatic position updates (toggleable)
   - Interactive map interface
   - Direct dock additions

3. **Complete Paper Trail**
   - Documenso integration for digital signatures
   - Comprehensive documentation system
   - Automated reporting for all parties
   - Industry standard compliance

### Platform Features Section

- **Driver Dashboard Preview**: Show gamification elements (mileage, distance traveled, value towed)
- **Verification System**: One-step systematic checks with visual rule configuration
- **Reporting Engine**: Different views for drivers (stats, reviews, incidents) vs shippers/brokers (logistics stats, efficiency, security, paperwork coverage)
- **Roadside Agent Tool**: Integrated emergency assistance

### Demo Section

- Interactive map component showing:
  - Two-point shipment creation (start/end points)
  - Real-time tracking simulation
  - "Start Shipment" and "End Shipment" buttons
  - Live position updates toggle

## Technical Requirements

### Navigation

- Minimal header: Home, Legal, API Documentation
- Driver Login (primary action)
- Clean, professional design

### Page Structure

```
/                 # Homepage
/legal           # Legal pages
/api-docs        # API documentation
/app/dashboard   # Driver dashboard (post-login)
/app/demo        # Interactive shipment demo
/verification    # Verification flow page
/real-time       # Real-time tracking page
```

### Key Interactive Elements

1. **Map Component**: Drag-and-drop shipment points
2. **Login Flow**: Driver-only authentication
3. **Demo Mode**: Functional shipment simulation
4. **Responsive Design**: Mobile-first approach

### Design Guidelines

- Clean, modern interface
- Driver-centric UX/UI
- Prominent security/trust indicators
- Gamification elements visible in dashboard previews
- Professional color scheme suitable for logistics industry

### Content Tone

- "Raising the tide by creating industry standards"
- Focus on solving real logistics problems
- Emphasize driver empowerment and experience
- Highlight comprehensive solution approach

## Bottom To-Do Items

### Immediate Development Tasks

- [ ] **Repository Cleanup**
  - Clear current app and move to different branch
  - Delete everything except homepage, legal, and driver dashboard components
  - Set up clean project structure

- [ ] **Core Page Development**
  - Build responsive homepage with hero section and value props
  - Create legal pages template
  - Develop basic API documentation structure
  - Implement driver-only login system

- [ ] **Dashboard & Demo Implementation**
  - Mock up driver dashboard with gamification elements
  - Create interactive map demo under /app route
  - Build verification flow page with systematic checks preview
  - Develop real-time tracking interface

- [ ] **Feature Integration**
  - Integrate Documenso flow for digital signatures
  - Implement roadside agent tool in dashboard
  - Create configurable integrity protocol interface
  - Build reporting system with role-based views

- [ ] **Technical Infrastructure**
  - Set up authentication system (driver-centric)
  - Implement map functionality with two-point dropping
  - Create automatic position update system with toggle
  - Build gamification tracking (mileage, distance, value)

### Future Enhancements

- [ ] Advanced reporting analytics
- [ ] Multi-party dashboard views (shippers/brokers)
- [ ] Enhanced LLM integration for verification
- [ ] Mobile app companion
- [ ] API rate limiting and documentation
- [ ] Advanced security features

## Success Metrics

- Clean, professional homepage that clearly communicates value
- Functional driver login and dashboard
- Working demo that showcases core capabilities
- Mobile-responsive design
- Fast load times and smooth interactions
