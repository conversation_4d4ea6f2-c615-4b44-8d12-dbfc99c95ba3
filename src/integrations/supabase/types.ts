export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      contacts: {
        Row: {
          created_at: string;
          email: string;
          first_name: string;
          id: string;
          last_name: string;
          location_id: string | null;
          organization_id: string | null;
          phone_number: string | null;
          type: Database["public"]["Enums"]["contact_type"];
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          email: string;
          first_name: string;
          id?: string;
          last_name: string;
          location_id?: string | null;
          organization_id?: string | null;
          phone_number?: string | null;
          type?: Database["public"]["Enums"]["contact_type"];
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          email?: string;
          first_name?: string;
          id?: string;
          last_name?: string;
          location_id?: string | null;
          organization_id?: string | null;
          phone_number?: string | null;
          type?: Database["public"]["Enums"]["contact_type"];
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "contacts_location_id_fkey";
            columns: ["location_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "contacts_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      documents: {
        Row: {
          bucket_id: string;
          content_type: string | null;
          created_at: string;
          created_by: string | null;
          description: string | null;
          driver_id: string | null;
          id: string;
          latitude: number | null;
          longitude: number | null;
          metadata: Json | null;
          name: string;
          organization_id: string | null;
          size: number | null;
          storage_path: string;
          type: Database["public"]["Enums"]["document_type"];
          updated_at: string;
          url: string;
        };
        Insert: {
          bucket_id?: string;
          content_type?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          driver_id?: string | null;
          id?: string;
          latitude?: number | null;
          longitude?: number | null;
          metadata?: Json | null;
          name: string;
          organization_id?: string | null;
          size?: number | null;
          storage_path: string;
          type?: Database["public"]["Enums"]["document_type"];
          updated_at?: string;
          url?: string;
        };
        Update: {
          bucket_id?: string;
          content_type?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          driver_id?: string | null;
          id?: string;
          latitude?: number | null;
          longitude?: number | null;
          metadata?: Json | null;
          name?: string;
          organization_id?: string | null;
          size?: number | null;
          storage_path?: string;
          type?: Database["public"]["Enums"]["document_type"];
          updated_at?: string;
          url?: string;
        };
        Relationships: [
          {
            foreignKeyName: "documents_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "documents_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      drivers: {
        Row: {
          avatar: string | null;
          created_at: string;
          email: string;
          first_name: string;
          id: string;
          last_name: string;
          location_id: string | null;
          phone_number: string;
          score: number;
          status: Database["public"]["Enums"]["driver_status"] | null;
          tier: string;
          user_id: string | null;
          verified_at: string | null;
        };
        Insert: {
          avatar?: string | null;
          created_at?: string;
          email: string;
          first_name: string;
          id?: string;
          last_name: string;
          location_id?: string | null;
          phone_number: string;
          score?: number;
          status?: Database["public"]["Enums"]["driver_status"] | null;
          tier?: string;
          user_id?: string | null;
          verified_at?: string | null;
        };
        Update: {
          avatar?: string | null;
          created_at?: string;
          email?: string;
          first_name?: string;
          id?: string;
          last_name?: string;
          location_id?: string | null;
          phone_number?: string;
          score?: number;
          status?: Database["public"]["Enums"]["driver_status"] | null;
          tier?: string;
          user_id?: string | null;
          verified_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "drivers_location_id_fkey";
            columns: ["location_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
        ];
      };
      engagements: {
        Row: {
          created_at: string;
          driver_id: string;
          driver_notes: string | null;
          expires_at: string;
          id: string;
          organization_id: string;
          organization_notes: string | null;
          rate_cents: number | null;
          rate_currency: string | null;
          shipment_id: string;
          status: Database["public"]["Enums"]["engagement_status"];
          type: Database["public"]["Enums"]["engagement_type"];
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          driver_id: string;
          driver_notes?: string | null;
          expires_at?: string;
          id?: string;
          organization_id: string;
          organization_notes?: string | null;
          rate_cents?: number | null;
          rate_currency?: string | null;
          shipment_id: string;
          status?: Database["public"]["Enums"]["engagement_status"];
          type: Database["public"]["Enums"]["engagement_type"];
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          driver_id?: string;
          driver_notes?: string | null;
          expires_at?: string;
          id?: string;
          organization_id?: string;
          organization_notes?: string | null;
          rate_cents?: number | null;
          rate_currency?: string | null;
          shipment_id?: string;
          status?: Database["public"]["Enums"]["engagement_status"];
          type?: Database["public"]["Enums"]["engagement_type"];
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "engagements_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "engagements_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "engagements_shipment_id_fkey";
            columns: ["shipment_id"];
            isOneToOne: false;
            referencedRelation: "shipments";
            referencedColumns: ["id"];
          },
        ];
      };
      incidents: {
        Row: {
          created_at: string;
          driver_id: string | null;
          id: string;
          load_id: string | null;
          severity: Database["public"]["Enums"]["incident_severity"];
          shipment_id: string;
          status: Database["public"]["Enums"]["incident_status"];
          stop_id: string | null;
          summary: string | null;
          title: string;
          type: Database["public"]["Enums"]["incident_type"];
          verification_id: string | null;
        };
        Insert: {
          created_at?: string;
          driver_id?: string | null;
          id?: string;
          load_id?: string | null;
          severity: Database["public"]["Enums"]["incident_severity"];
          shipment_id: string;
          status?: Database["public"]["Enums"]["incident_status"];
          stop_id?: string | null;
          summary?: string | null;
          title: string;
          type: Database["public"]["Enums"]["incident_type"];
          verification_id?: string | null;
        };
        Update: {
          created_at?: string;
          driver_id?: string | null;
          id?: string;
          load_id?: string | null;
          severity?: Database["public"]["Enums"]["incident_severity"];
          shipment_id?: string;
          status?: Database["public"]["Enums"]["incident_status"];
          stop_id?: string | null;
          summary?: string | null;
          title?: string;
          type?: Database["public"]["Enums"]["incident_type"];
          verification_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "incidents_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "incidents_load_id_fkey";
            columns: ["load_id"];
            isOneToOne: false;
            referencedRelation: "loads";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "incidents_shipment_id_fkey";
            columns: ["shipment_id"];
            isOneToOne: false;
            referencedRelation: "shipments";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "incidents_stop_id_fkey";
            columns: ["stop_id"];
            isOneToOne: false;
            referencedRelation: "stops";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "incidents_verification_id_fkey";
            columns: ["verification_id"];
            isOneToOne: false;
            referencedRelation: "verifications";
            referencedColumns: ["id"];
          },
        ];
      };
      invitations: {
        Row: {
          created_at: string;
          created_by: string;
          driver_id: string | null;
          email: string;
          expires_at: string;
          id: string;
          organization_id: string | null;
          role: Database["public"]["Enums"]["members_role"] | null;
          status: Database["public"]["Enums"]["invitation_status"];
        };
        Insert: {
          created_at?: string;
          created_by: string;
          driver_id?: string | null;
          email: string;
          expires_at: string;
          id?: string;
          organization_id?: string | null;
          role?: Database["public"]["Enums"]["members_role"] | null;
          status?: Database["public"]["Enums"]["invitation_status"];
        };
        Update: {
          created_at?: string;
          created_by?: string;
          driver_id?: string | null;
          email?: string;
          expires_at?: string;
          id?: string;
          organization_id?: string | null;
          role?: Database["public"]["Enums"]["members_role"] | null;
          status?: Database["public"]["Enums"]["invitation_status"];
        };
        Relationships: [
          {
            foreignKeyName: "invitations_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "invitations_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      loads: {
        Row: {
          created_at: string;
          destination_id: string | null;
          id: string;
          label: string | null;
          notes: string | null;
          organization_id: string | null;
          origin_id: string | null;
          perishable: boolean;
          start_date: string | null;
          status: Database["public"]["Enums"]["load_status"];
          type: Database["public"]["Enums"]["load_type"];
          valuation: number | null;
          weight: number | null;
        };
        Insert: {
          created_at?: string;
          destination_id?: string | null;
          id?: string;
          label?: string | null;
          notes?: string | null;
          organization_id?: string | null;
          origin_id?: string | null;
          perishable?: boolean;
          start_date?: string | null;
          status?: Database["public"]["Enums"]["load_status"];
          type?: Database["public"]["Enums"]["load_type"];
          valuation?: number | null;
          weight?: number | null;
        };
        Update: {
          created_at?: string;
          destination_id?: string | null;
          id?: string;
          label?: string | null;
          notes?: string | null;
          organization_id?: string | null;
          origin_id?: string | null;
          perishable?: boolean;
          start_date?: string | null;
          status?: Database["public"]["Enums"]["load_status"];
          type?: Database["public"]["Enums"]["load_type"];
          valuation?: number | null;
          weight?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: "loads_destination_id_fkey";
            columns: ["destination_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "loads_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "loads_origin_id_fkey";
            columns: ["origin_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
        ];
      };
      locations: {
        Row: {
          city: string | null;
          country: string;
          created_at: string;
          driver_id: string | null;
          formatted: string;
          id: string;
          latitude: number;
          longitude: number;
          neighborhood: string | null;
          organization_id: string | null;
          postal: string | null;
          state: string | null;
          street: string | null;
          suite: string | null;
          type: Database["public"]["Enums"]["location_type"];
        };
        Insert: {
          city?: string | null;
          country: string;
          created_at?: string;
          driver_id?: string | null;
          formatted: string;
          id?: string;
          latitude: number;
          longitude: number;
          neighborhood?: string | null;
          organization_id?: string | null;
          postal?: string | null;
          state?: string | null;
          street?: string | null;
          suite?: string | null;
          type?: Database["public"]["Enums"]["location_type"];
        };
        Update: {
          city?: string | null;
          country?: string;
          created_at?: string;
          driver_id?: string | null;
          formatted?: string;
          id?: string;
          latitude?: number;
          longitude?: number;
          neighborhood?: string | null;
          organization_id?: string | null;
          postal?: string | null;
          state?: string | null;
          street?: string | null;
          suite?: string | null;
          type?: Database["public"]["Enums"]["location_type"];
        };
        Relationships: [
          {
            foreignKeyName: "locations_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "locations_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      manifests: {
        Row: {
          created_at: string;
          document_id: string | null;
          id: string;
          load_id: string | null;
          stop_id: string;
          type: Database["public"]["Enums"]["manifest_type"];
        };
        Insert: {
          created_at?: string;
          document_id?: string | null;
          id?: string;
          load_id?: string | null;
          stop_id: string;
          type?: Database["public"]["Enums"]["manifest_type"];
        };
        Update: {
          created_at?: string;
          document_id?: string | null;
          id?: string;
          load_id?: string | null;
          stop_id?: string;
          type?: Database["public"]["Enums"]["manifest_type"];
        };
        Relationships: [
          {
            foreignKeyName: "manifests_document_id_fkey";
            columns: ["document_id"];
            isOneToOne: true;
            referencedRelation: "documents";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "manifests_load_id_fkey";
            columns: ["load_id"];
            isOneToOne: false;
            referencedRelation: "loads";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "manifests_stop_id_fkey";
            columns: ["stop_id"];
            isOneToOne: true;
            referencedRelation: "stops";
            referencedColumns: ["id"];
          },
        ];
      };
      members: {
        Row: {
          created_at: string;
          email: string;
          id: string;
          organization_id: string;
          role: Database["public"]["Enums"]["members_role"];
          status: Database["public"]["Enums"]["member_status"];
          user_id: string | null;
        };
        Insert: {
          created_at?: string;
          email: string;
          id?: string;
          organization_id: string;
          role?: Database["public"]["Enums"]["members_role"];
          status?: Database["public"]["Enums"]["member_status"];
          user_id?: string | null;
        };
        Update: {
          created_at?: string;
          email?: string;
          id?: string;
          organization_id?: string;
          role?: Database["public"]["Enums"]["members_role"];
          status?: Database["public"]["Enums"]["member_status"];
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "members_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      notifications: {
        Row: {
          created_at: string;
          id: string;
          message: string;
          read_at: string | null;
          title: string;
          type: Database["public"]["Enums"]["notification_type"];
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          message: string;
          read_at?: string | null;
          title: string;
          type: Database["public"]["Enums"]["notification_type"];
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          message?: string;
          read_at?: string | null;
          title?: string;
          type?: Database["public"]["Enums"]["notification_type"];
          user_id?: string;
        };
        Relationships: [];
      };
      organizations: {
        Row: {
          avatar: string | null;
          created_at: string;
          id: string;
          industry: string;
          location_id: string | null;
          name: string;
          size: string;
          status: Database["public"]["Enums"]["organization_status"];
          type: Database["public"]["Enums"]["organization_type"];
          user_id: string | null;
        };
        Insert: {
          avatar?: string | null;
          created_at?: string;
          id?: string;
          industry: string;
          location_id?: string | null;
          name: string;
          size: string;
          status?: Database["public"]["Enums"]["organization_status"];
          type?: Database["public"]["Enums"]["organization_type"];
          user_id?: string | null;
        };
        Update: {
          avatar?: string | null;
          created_at?: string;
          id?: string;
          industry?: string;
          location_id?: string | null;
          name?: string;
          size?: string;
          status?: Database["public"]["Enums"]["organization_status"];
          type?: Database["public"]["Enums"]["organization_type"];
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "organizations_location_id_fkey";
            columns: ["location_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
        ];
      };
      positions: {
        Row: {
          driver_id: string | null;
          id: string;
          latitude: number;
          load_id: string | null;
          longitude: number;
          recorded_at: string;
          shipment_id: string;
          vehicle_id: string | null;
        };
        Insert: {
          driver_id?: string | null;
          id?: string;
          latitude: number;
          load_id?: string | null;
          longitude: number;
          recorded_at?: string;
          shipment_id: string;
          vehicle_id?: string | null;
        };
        Update: {
          driver_id?: string | null;
          id?: string;
          latitude?: number;
          load_id?: string | null;
          longitude?: number;
          recorded_at?: string;
          shipment_id?: string;
          vehicle_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "positions_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "positions_load_id_fkey";
            columns: ["load_id"];
            isOneToOne: false;
            referencedRelation: "loads";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "positions_shipment_id_fkey";
            columns: ["shipment_id"];
            isOneToOne: false;
            referencedRelation: "shipments";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "positions_vehicle_id_fkey";
            columns: ["vehicle_id"];
            isOneToOne: false;
            referencedRelation: "vehicles";
            referencedColumns: ["id"];
          },
        ];
      };
      profiles: {
        Row: {
          avatar: string | null;
          created_at: string;
          id: string;
          username: string | null;
        };
        Insert: {
          avatar?: string | null;
          created_at?: string;
          id: string;
          username?: string | null;
        };
        Update: {
          avatar?: string | null;
          created_at?: string;
          id?: string;
          username?: string | null;
        };
        Relationships: [];
      };
      qualifications: {
        Row: {
          created_at: string;
          document_id: string | null;
          driver_id: string;
          expires_at: string | null;
          id: string;
          issued_at: string;
          issuing_state: string;
          status: Database["public"]["Enums"]["qualification_status"];
          type: Database["public"]["Enums"]["qualification_type"];
          verified_at: string | null;
        };
        Insert: {
          created_at?: string;
          document_id?: string | null;
          driver_id: string;
          expires_at?: string | null;
          id?: string;
          issued_at: string;
          issuing_state: string;
          status?: Database["public"]["Enums"]["qualification_status"];
          type: Database["public"]["Enums"]["qualification_type"];
          verified_at?: string | null;
        };
        Update: {
          created_at?: string;
          document_id?: string | null;
          driver_id?: string;
          expires_at?: string | null;
          id?: string;
          issued_at?: string;
          issuing_state?: string;
          status?: Database["public"]["Enums"]["qualification_status"];
          type?: Database["public"]["Enums"]["qualification_type"];
          verified_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "qualifications_document_id_fkey";
            columns: ["document_id"];
            isOneToOne: true;
            referencedRelation: "documents";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "qualifications_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
        ];
      };
      shipments: {
        Row: {
          cancelled_at: string | null;
          completed_at: string | null;
          created_at: string;
          distance: number | null;
          driver_id: string | null;
          duration: unknown | null;
          id: string;
          load_id: string | null;
          mode: Database["public"]["Enums"]["shipment_mode"];
          organization_id: string | null;
          source: Database["public"]["Enums"]["shipment_source"];
          started_at: string | null;
          status: Database["public"]["Enums"]["shipment_status"];
          type: Database["public"]["Enums"]["shipment_type"] | null;
          valuation: number | null;
          weight: number | null;
        };
        Insert: {
          cancelled_at?: string | null;
          completed_at?: string | null;
          created_at?: string;
          distance?: number | null;
          driver_id?: string | null;
          duration?: unknown | null;
          id?: string;
          load_id?: string | null;
          mode?: Database["public"]["Enums"]["shipment_mode"];
          organization_id?: string | null;
          source?: Database["public"]["Enums"]["shipment_source"];
          started_at?: string | null;
          status: Database["public"]["Enums"]["shipment_status"];
          type?: Database["public"]["Enums"]["shipment_type"] | null;
          valuation?: number | null;
          weight?: number | null;
        };
        Update: {
          cancelled_at?: string | null;
          completed_at?: string | null;
          created_at?: string;
          distance?: number | null;
          driver_id?: string | null;
          duration?: unknown | null;
          id?: string;
          load_id?: string | null;
          mode?: Database["public"]["Enums"]["shipment_mode"];
          organization_id?: string | null;
          source?: Database["public"]["Enums"]["shipment_source"];
          started_at?: string | null;
          status?: Database["public"]["Enums"]["shipment_status"];
          type?: Database["public"]["Enums"]["shipment_type"] | null;
          valuation?: number | null;
          weight?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: "shipments_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "shipments_load_id_fkey";
            columns: ["load_id"];
            isOneToOne: false;
            referencedRelation: "loads";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "shipments_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      stops: {
        Row: {
          arrived_at: string | null;
          created_at: string;
          departed_at: string | null;
          id: string;
          label: string | null;
          latitude: number | null;
          load_id: string | null;
          location_id: string | null;
          longitude: number | null;
          next_stop_id: string | null;
          previous_stop_id: string | null;
          sequence_number: number;
          shipment_id: string;
          tags: string[] | null;
          type: Database["public"]["Enums"]["stop_type"];
        };
        Insert: {
          arrived_at?: string | null;
          created_at?: string;
          departed_at?: string | null;
          id?: string;
          label?: string | null;
          latitude?: number | null;
          load_id?: string | null;
          location_id?: string | null;
          longitude?: number | null;
          next_stop_id?: string | null;
          previous_stop_id?: string | null;
          sequence_number: number;
          shipment_id: string;
          tags?: string[] | null;
          type?: Database["public"]["Enums"]["stop_type"];
        };
        Update: {
          arrived_at?: string | null;
          created_at?: string;
          departed_at?: string | null;
          id?: string;
          label?: string | null;
          latitude?: number | null;
          load_id?: string | null;
          location_id?: string | null;
          longitude?: number | null;
          next_stop_id?: string | null;
          previous_stop_id?: string | null;
          sequence_number?: number;
          shipment_id?: string;
          tags?: string[] | null;
          type?: Database["public"]["Enums"]["stop_type"];
        };
        Relationships: [
          {
            foreignKeyName: "stops_load_id_fkey";
            columns: ["load_id"];
            isOneToOne: false;
            referencedRelation: "loads";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "stops_location_id_fkey";
            columns: ["location_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "stops_next_stop_id_fkey";
            columns: ["next_stop_id"];
            isOneToOne: false;
            referencedRelation: "stops";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "stops_previous_stop_id_fkey";
            columns: ["previous_stop_id"];
            isOneToOne: false;
            referencedRelation: "stops";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "stops_shipment_id_fkey";
            columns: ["shipment_id"];
            isOneToOne: false;
            referencedRelation: "shipments";
            referencedColumns: ["id"];
          },
        ];
      };
      vehicles: {
        Row: {
          created_at: string;
          driver_id: string | null;
          id: string;
          license_plate: string;
          make: string;
          mc_number: string;
          model: string;
          us_dot: string;
          vin: string;
          year: number;
        };
        Insert: {
          created_at?: string;
          driver_id?: string | null;
          id?: string;
          license_plate: string;
          make: string;
          mc_number: string;
          model: string;
          us_dot: string;
          vin: string;
          year: number;
        };
        Update: {
          created_at?: string;
          driver_id?: string | null;
          id?: string;
          license_plate?: string;
          make?: string;
          mc_number?: string;
          model?: string;
          us_dot?: string;
          vin?: string;
          year?: number;
        };
        Relationships: [
          {
            foreignKeyName: "vehicles_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: true;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
        ];
      };
      verifications: {
        Row: {
          created_at: string;
          document_id: string | null;
          driver_id: string | null;
          id: string;
          latitude: number | null;
          load_id: string | null;
          longitude: number | null;
          notes: string | null;
          shipment_id: string | null;
          signature_url: string | null;
          stop_id: string;
          vehicle_id: string | null;
          verified_at: string | null;
          verified_by: string | null;
        };
        Insert: {
          created_at?: string;
          document_id?: string | null;
          driver_id?: string | null;
          id?: string;
          latitude?: number | null;
          load_id?: string | null;
          longitude?: number | null;
          notes?: string | null;
          shipment_id?: string | null;
          signature_url?: string | null;
          stop_id: string;
          vehicle_id?: string | null;
          verified_at?: string | null;
          verified_by?: string | null;
        };
        Update: {
          created_at?: string;
          document_id?: string | null;
          driver_id?: string | null;
          id?: string;
          latitude?: number | null;
          load_id?: string | null;
          longitude?: number | null;
          notes?: string | null;
          shipment_id?: string | null;
          signature_url?: string | null;
          stop_id?: string;
          vehicle_id?: string | null;
          verified_at?: string | null;
          verified_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "verifications_document_id_fkey";
            columns: ["document_id"];
            isOneToOne: true;
            referencedRelation: "documents";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "verifications_driver_id_fkey";
            columns: ["driver_id"];
            isOneToOne: false;
            referencedRelation: "drivers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "verifications_load_id_fkey";
            columns: ["load_id"];
            isOneToOne: false;
            referencedRelation: "loads";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "verifications_shipment_id_fkey";
            columns: ["shipment_id"];
            isOneToOne: false;
            referencedRelation: "shipments";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "verifications_stop_id_fkey";
            columns: ["stop_id"];
            isOneToOne: true;
            referencedRelation: "stops";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "verifications_vehicle_id_fkey";
            columns: ["vehicle_id"];
            isOneToOne: false;
            referencedRelation: "vehicles";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      add_on_type: "ai_dispatcher";
      contact_type:
        | "billing"
        | "manager"
        | "dispatcher"
        | "safety_officer"
        | "maintenance"
        | "warehouse"
        | "customs"
        | "logistics"
        | "sales"
        | "support"
        | "other";
      contract_status:
        | "draft"
        | "pending"
        | "signed"
        | "expired"
        | "cancelled"
        | "void";
      contract_type:
        | "bill_of_lading"
        | "agreement"
        | "service_agreement"
        | "lease_agreement"
        | "insurance_certificate"
        | "power_of_attorney"
        | "other";
      document_type:
        | "manifest"
        | "contract"
        | "general"
        | "other"
        | "verification";
      driver_status: "active" | "inactive" | "suspended";
      engagement_status:
        | "pending"
        | "accepted"
        | "declined"
        | "expired"
        | "cancelled"
        | "completed";
      engagement_type: "driver_request" | "organization_offer";
      incident_severity: "low" | "medium" | "high" | "critical";
      incident_status: "reported" | "investigating" | "resolved" | "closed";
      incident_type:
        | "accident"
        | "delay"
        | "damage"
        | "theft"
        | "weather"
        | "mechanical"
        | "other";
      invitation_status: "pending" | "accepted" | "revoked" | "rejected";
      load_status:
        | "pending"
        | "packaged"
        | "loaded"
        | "in_transit"
        | "delivered"
        | "missing"
        | "damaged"
        | "rejected"
        | "returned"
        | "customs_hold";
      load_type:
        | "general"
        | "bulk_dry"
        | "bulk_liquid"
        | "container_20ft"
        | "container_40ft"
        | "container_reefer"
        | "container_flat_rack"
        | "container_open_top"
        | "container_tank"
        | "breakbulk"
        | "ro_ro"
        | "heavy_lift"
        | "project_cargo"
        | "dangerous_goods"
        | "temperature_controlled"
        | "livestock"
        | "vehicles"
        | "machinery"
        | "perishables"
        | "valuables"
        | "other";
      location_type:
        | "billing"
        | "commercial"
        | "industrial"
        | "government"
        | "public"
        | "residential"
        | "warehouse"
        | "distribution_center"
        | "retail"
        | "other";
      manifest_type: "cargo" | "container" | "out_of_gauge" | "customs";
      member_status: "pending" | "active" | "inactive" | "declined";
      members_role: "owner" | "admin" | "billing" | "member" | "viewer";
      notification_type:
        | "shipment_update"
        | "incident_report"
        | "system_alert"
        | "payment_update";
      organization_status: "pending" | "active" | "suspended" | "inactive";
      organization_type: "individual" | "private" | "non_profit" | "government";
      qualification_status: "pending" | "verified" | "expired" | "revoked";
      qualification_type:
        | "commercial_drivers_license"
        | "hazmat_endorsement"
        | "medical_certificate"
        | "defensive_driving_certificate"
        | "tanker_endorsement"
        | "doubles_triples_endorsement"
        | "other";
      shipment_mode: "open" | "closed";
      shipment_source: "driver" | "organization" | "system";
      shipment_status:
        | "pending"
        | "scheduled"
        | "assigned"
        | "confirmed"
        | "in_progress"
        | "completed"
        | "cancelled";
      shipment_type: "air" | "ocean" | "ground" | "other";
      stop_type:
        | "origin"
        | "destination"
        | "pickup"
        | "dropoff"
        | "rest"
        | "gas"
        | "maintenance"
        | "customs"
        | "weigh_station"
        | "other";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      add_on_type: ["ai_dispatcher"],
      contact_type: [
        "billing",
        "manager",
        "dispatcher",
        "safety_officer",
        "maintenance",
        "warehouse",
        "customs",
        "logistics",
        "sales",
        "support",
        "other",
      ],
      contract_status: [
        "draft",
        "pending",
        "signed",
        "expired",
        "cancelled",
        "void",
      ],
      contract_type: [
        "bill_of_lading",
        "agreement",
        "service_agreement",
        "lease_agreement",
        "insurance_certificate",
        "power_of_attorney",
        "other",
      ],
      document_type: [
        "manifest",
        "contract",
        "general",
        "other",
        "verification",
      ],
      driver_status: ["active", "inactive", "suspended"],
      engagement_status: [
        "pending",
        "accepted",
        "declined",
        "expired",
        "cancelled",
        "completed",
      ],
      engagement_type: ["driver_request", "organization_offer"],
      incident_severity: ["low", "medium", "high", "critical"],
      incident_status: ["reported", "investigating", "resolved", "closed"],
      incident_type: [
        "accident",
        "delay",
        "damage",
        "theft",
        "weather",
        "mechanical",
        "other",
      ],
      invitation_status: ["pending", "accepted", "revoked", "rejected"],
      load_status: [
        "pending",
        "packaged",
        "loaded",
        "in_transit",
        "delivered",
        "missing",
        "damaged",
        "rejected",
        "returned",
        "customs_hold",
      ],
      load_type: [
        "general",
        "bulk_dry",
        "bulk_liquid",
        "container_20ft",
        "container_40ft",
        "container_reefer",
        "container_flat_rack",
        "container_open_top",
        "container_tank",
        "breakbulk",
        "ro_ro",
        "heavy_lift",
        "project_cargo",
        "dangerous_goods",
        "temperature_controlled",
        "livestock",
        "vehicles",
        "machinery",
        "perishables",
        "valuables",
        "other",
      ],
      location_type: [
        "billing",
        "commercial",
        "industrial",
        "government",
        "public",
        "residential",
        "warehouse",
        "distribution_center",
        "retail",
        "other",
      ],
      manifest_type: ["cargo", "container", "out_of_gauge", "customs"],
      member_status: ["pending", "active", "inactive", "declined"],
      members_role: ["owner", "admin", "billing", "member", "viewer"],
      notification_type: [
        "shipment_update",
        "incident_report",
        "system_alert",
        "payment_update",
      ],
      organization_status: ["pending", "active", "suspended", "inactive"],
      organization_type: ["individual", "private", "non_profit", "government"],
      qualification_status: ["pending", "verified", "expired", "revoked"],
      qualification_type: [
        "commercial_drivers_license",
        "hazmat_endorsement",
        "medical_certificate",
        "defensive_driving_certificate",
        "tanker_endorsement",
        "doubles_triples_endorsement",
        "other",
      ],
      shipment_mode: ["open", "closed"],
      shipment_source: ["driver", "organization", "system"],
      shipment_status: [
        "pending",
        "scheduled",
        "assigned",
        "confirmed",
        "in_progress",
        "completed",
        "cancelled",
      ],
      shipment_type: ["air", "ocean", "ground", "other"],
      stop_type: [
        "origin",
        "destination",
        "pickup",
        "dropoff",
        "rest",
        "gas",
        "maintenance",
        "customs",
        "weigh_station",
        "other",
      ],
    },
  },
} as const;
