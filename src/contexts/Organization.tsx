import React, {
  createContext,
  useC<PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";

import { useGetOrganization } from "@/api/organizations";
import { useUser } from "@/contexts/User";
import { useToast } from "@/hooks/use-toast";

// Define types for organization
export interface Organization {
  id: string;
  name: string;
  industry: string;
  size: string;
  avatar?: string;
  logo_url?: string;
  slug?: string;
  created_at?: string;
  updated_at?: string;
  // Add other fields as needed
}

interface OrganizationContextType {
  currentOrganizationId: string | null;
  currentOrganization: Organization | null;
  isLoading: boolean;
  error: Error | null;
  switchOrganization: (organizationId: string) => void;
}

// Create the context
const OrganizationContext = createContext<OrganizationContextType | undefined>(
  undefined,
);

// Custom hook to use the organization context
export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error(
      "useOrganization must be used within an OrganizationProvider",
    );
  }
  return context;
}

// Provider component
export function OrganizationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { toast } = useToast();
  const { memberships } = useUser();

  // Store only the organization ID in state
  const [currentOrganizationId, setCurrentOrganizationId] = useState<
    string | null
  >(() => {
    // Initialize from localStorage if available
    return localStorage.getItem("currentOrganizationId");
  });

  // Fetch the current organization details
  const {
    data: currentOrganizationData,
    isLoading: isLoadingCurrentOrg,
    error: currentOrgError,
  } = useGetOrganization(currentOrganizationId || "", {
    enabled: !!currentOrganizationId,
  });

  // Set initial organization ID if not already set
  useEffect(() => {
    if (!currentOrganizationId && memberships.length > 0) {
      // Get the first organization ID from memberships
      const firstOrgId = memberships[0].organization.id;
      setCurrentOrganizationId(firstOrgId);
      localStorage.setItem("currentOrganizationId", firstOrgId);
    }
  }, [currentOrganizationId, memberships]);

  // Validate current organization ID against memberships
  useEffect(() => {
    if (currentOrganizationId && memberships.length > 0) {
      const hasAccess = memberships.some(
        (m) => m.organization.id === currentOrganizationId,
      );

      if (!hasAccess) {
        // Current org ID is invalid, reset to first available
        const firstOrgId = memberships[0].organization.id;
        setCurrentOrganizationId(firstOrgId);
        localStorage.setItem("currentOrganizationId", firstOrgId);
      }
    }
  }, [currentOrganizationId, memberships]);

  // Function to switch organizations
  const switchOrganization = useCallback(
    (organizationId: string) => {
      // Check if the organization exists in memberships
      const membership = memberships.find(
        (m) => m.organization.id === organizationId,
      );

      if (!membership) {
        toast({
          title: "Error",
          description: "Organization not found or you don't have access to it.",
          variant: "destructive",
        });
        return;
      }

      setCurrentOrganizationId(organizationId);
      localStorage.setItem("currentOrganizationId", organizationId);

      toast({
        title: "Organization Switched",
        description: `You are now viewing ${membership.organization.name}`,
      });
    },
    [memberships, toast],
  );

  return (
    <OrganizationContext.Provider
      value={useMemo(
        () => ({
          currentOrganizationId: currentOrganizationId || null,
          currentOrganization: currentOrganizationData || null,
          isLoading: isLoadingCurrentOrg,
          error: currentOrgError,
          switchOrganization,
        }),
        [
          currentOrganizationId,
          currentOrganizationData,
          isLoadingCurrentOrg,
          currentOrgError,
          switchOrganization,
        ],
      )}
    >
      {children}
    </OrganizationContext.Provider>
  );
}
