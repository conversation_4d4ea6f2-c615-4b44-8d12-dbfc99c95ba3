import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

import { useDriversCurrentShipment } from "@/api/drivers";
import {
  usePastShipments,
  useUpcomingShipments,
} from "@/api/drivers/shipments/use-driver-shipments";
import { useUser } from "@/contexts/User";

// Define the type for our context state
type DriverShipmentContextType = {
  activeShipment: Awaited<ReturnType<typeof useDriversCurrentShipment>>["data"];
  upcomingShipments: Awaited<ReturnType<typeof useUpcomingShipments>>["data"];
  pastShipments: Awaited<ReturnType<typeof usePastShipments>>["data"];
  isLoading: boolean;
  error: Error | null;
  refetchActive: () => Promise<void>;
  refetchUpcoming: () => Promise<void>;
  refetchPast: () => Promise<void>;
};

// Create the context with a default value
const DriverShipmentContext = createContext<DriverShipmentContextType>({
  activeShipment: null,
  upcomingShipments: [],
  pastShipments: [],
  isLoading: false,
  error: null,
  refetchActive: async () => {},
  refetchUpcoming: async () => {},
  refetchPast: async () => {},
});

// Create the provider component
export function DriverShipmentProvider({ children }: { children: ReactNode }) {
  const { driver } = useUser();
  const [error, setError] = useState<Error | null>(null);

  // Query for the active shipment
  const {
    data: activeShipment,
    isLoading: isLoadingActive,
    error: activeError,
    refetch: refetchActiveRaw,
  } = useDriversCurrentShipment();

  // Query for upcoming shipments
  const {
    data: upcomingShipments,
    isLoading: isLoadingUpcoming,
    error: upcomingError,
    refetch: refetchUpcomingRaw,
  } = useUpcomingShipments(driver?.id);

  // Query for past shipments
  const {
    data: pastShipments,
    isLoading: isLoadingPast,
    error: pastError,
    refetch: refetchPastRaw,
  } = usePastShipments(driver?.id);

  // Handle errors
  useEffect(() => {
    const currentError = activeError || upcomingError || pastError;
    if (currentError) {
      setError(currentError);
      console.error("Error in DriverShipmentContext:", currentError);
    } else {
      setError(null);
    }
  }, [activeError, upcomingError, pastError]);

  // Wrapper functions for refetching data
  const refetchActive = async () => {
    try {
      await refetchActiveRaw();
    } catch (err) {
      console.error("Failed to refetch active shipment:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
    }
  };

  const refetchUpcoming = async () => {
    try {
      await refetchUpcomingRaw();
    } catch (err) {
      console.error("Failed to refetch upcoming shipments:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
    }
  };

  const refetchPast = async () => {
    try {
      await refetchPastRaw();
    } catch (err) {
      console.error("Failed to refetch past shipments:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
    }
  };

  // Aggregate loading state
  const isLoading = isLoadingActive || isLoadingUpcoming || isLoadingPast;

  // Provide the context value
  const value = {
    activeShipment,
    upcomingShipments,
    pastShipments,
    isLoading,
    error,
    refetchActive,
    refetchUpcoming,
    refetchPast,
  };

  return (
    <DriverShipmentContext.Provider value={value}>
      {children}
    </DriverShipmentContext.Provider>
  );
}

// Create a hook for using the shipment context
export function useDriverShipment() {
  const context = useContext(DriverShipmentContext);
  if (context === undefined) {
    throw new Error(
      "useDriverShipment must be used within a DriverShipmentProvider",
    );
  }
  return context;
}

// Export both the provider and the hook
export default DriverShipmentContext;
