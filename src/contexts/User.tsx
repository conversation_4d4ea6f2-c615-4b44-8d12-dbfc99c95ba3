import { createContext, useContext, useMemo } from "react";

import type {
  Driver<PERSON>ro<PERSON><PERSON>,
  Member,
  Session,
  User,
} from "@/api/user/use-user-subscription";

import { useUserSubscription } from "@/api/user/use-user-subscription";

export interface UserContextType {
  session: Session | null;
  user: User | null;
  driver: DriverProfile | null;
  memberships: Member[];
  isLoading: boolean;
  isDriverLoading: boolean;
  isMembershipsLoading: boolean;
  error: string | null;
}

const UserContext = createContext<UserContextType>({
  session: null,
  user: null,
  driver: null,
  memberships: [],
  isLoading: false,
  isDriverLoading: false,
  isMembershipsLoading: false,
  error: null,
});

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { data, isLoading, isDriverLoading, isMembershipsLoading, error } =
    useUserSubscription();

  const value = useMemo(
    () => ({
      session: data?.session || null,
      user: data?.user || null,
      driver: data?.driver || null,
      memberships: data?.memberships || [],
      isLoading,
      isDriverLoading,
      isMembershipsLoading,
      error,
    }),
    [data, isLoading, isDriverLoading, isMembershipsLoading, error],
  );

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
