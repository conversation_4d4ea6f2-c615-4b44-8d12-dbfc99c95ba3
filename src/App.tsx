import { HelmetProvider } from "react-helmet-async";

import { APIProvider } from "@/api/provider";
import { ThemeProvider } from "@/components/ThemeProvider";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { OrganizationProvider } from "@/contexts/Organization";
import { UserProvider } from "@/contexts/User";
import { Router, Routes } from "@/pages";

export default function App() {
  return (
    <ThemeProvider>
      <APIProvider>
        <UserProvider>
          <OrganizationProvider>
            <TooltipProvider>
              <HelmetProvider>
                <Toaster />
                <Sonner />
                <Router>
                  <Routes />
                </Router>
              </HelmetProvider>
            </TooltipProvider>
          </OrganizationProvider>
        </UserProvider>
      </APIProvider>
    </ThemeProvider>
  );
}
