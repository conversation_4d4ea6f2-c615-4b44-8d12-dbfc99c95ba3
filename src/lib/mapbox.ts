import { supabase } from "@/supabase/client";

interface InternalSearchSuggestion {
  full_address: string;
  context: {
    street: { name: string };
    place: { name: string };
    neighborhood: { name: string };
    postcode: { name: string };
    region: { name: string };
    country: { name: string };
  };
  mapbox_id: string;
}

interface InternalGeocodingFeature {
  properties: {
    name: string;
    place_formatted: string;
    address: string;
    context: {
      id: string;
      text: string;
    }[];
  };
  context: {
    id: string;
    text: string;
  }[];
  center: [number, number];
  id: string;
}

export interface MapboxSuggestion {
  formatted: string;
  street: string;
  city: string;
  neighborhood: string | null;
  postal: string;
  state: string;
  country: string;
  mapbox_id: string;
  latitude: number;
  longitude: number;
}

export interface MapboxResponse {
  data: MapboxSuggestion[] | null;
  error: string | null;
}

export async function searchAddressSuggestions(
  query: string,
  sessionToken: string,
): Promise<MapboxResponse> {
  try {
    if (!query || query.length < 3) {
      return { data: [], error: null };
    }

    const {
      data: { MAPBOX_ACCESS_TOKEN },
      error: secretError,
    } = await supabase.functions.invoke("get-secret", {
      body: { name: "MAPBOX_ACCESS_TOKEN" },
    });

    if (secretError) {
      return { data: null, error: "Failed to get Mapbox access token" };
    }

    const endpoint = `https://api.mapbox.com/search/searchbox/v1/suggest?q=${encodeURIComponent(
      query,
    )}&access_token=${MAPBOX_ACCESS_TOKEN}&limit=5&proximity=ip&session_token=${sessionToken}`;

    const response = await fetch(endpoint);
    if (!response.ok) {
      const errorData = await response.json();
      return {
        data: null,
        error:
          errorData.error || `Failed to fetch suggestions: ${response.status}`,
      };
    }

    const data = await response.json();

    const suggestions: MapboxSuggestion[] = data.suggestions
      .filter((suggestion: InternalSearchSuggestion) => suggestion.full_address) // Filter out suggestions without addresses
      .map((suggestion: InternalSearchSuggestion) => ({
        formatted: suggestion.full_address,
        street: suggestion.context.street?.name || "",
        city: suggestion.context.place?.name || "",
        neighborhood: suggestion.context.neighborhood?.name || null,
        postal: suggestion.context.postcode?.name || "",
        state: suggestion.context.region?.name || "",
        country: suggestion.context.country?.name || "",
        mapbox_id: suggestion.mapbox_id,
        latitude: 0, // Search API doesn't provide coordinates
        longitude: 0, // Search API doesn't provide coordinates
      }));

    return { data: suggestions, error: null };
  } catch (err) {
    return {
      data: null,
      error: err instanceof Error ? err.message : "Failed to fetch suggestions",
    };
  }
}

export async function getAddressSuggestions(
  query: string,
): Promise<MapboxResponse> {
  try {
    if (!query || query.length < 3) {
      return { data: [], error: null };
    }

    const {
      data: { MAPBOX_ACCESS_TOKEN },
      error: secretError,
    } = await supabase.functions.invoke("get-secret", {
      body: { name: "MAPBOX_ACCESS_TOKEN" },
    });

    if (secretError) {
      return { data: null, error: "Failed to get Mapbox access token" };
    }

    const endpoint = `https://api.mapbox.com/geocoding/v6/${encodeURIComponent(
      query,
    )}.json?access_token=${MAPBOX_ACCESS_TOKEN}&limit=5&types=address&autocomplete=true&proximity=ip`;

    const response = await fetch(endpoint);
    if (!response.ok) {
      const errorData = await response.json();
      return {
        data: null,
        error:
          errorData.message ||
          `Failed to fetch suggestions: ${response.status}`,
      };
    }

    const data = await response.json();

    const suggestions: MapboxSuggestion[] = data.features
      .filter(
        (feature: InternalGeocodingFeature) =>
          feature.properties.name && feature.properties.place_formatted,
      )
      .map((feature: InternalGeocodingFeature) => ({
        formatted: `${feature.properties.name}, ${feature.properties.place_formatted}`,
        street: feature.properties.address || "",
        city:
          feature.context?.find((c) => c.id.startsWith("place"))?.text || "",
        neighborhood:
          feature.context?.find((c) => c.id.startsWith("neighborhood"))?.text ||
          null,
        postal:
          feature.context?.find((c) => c.id.startsWith("postcode"))?.text || "",
        state:
          feature.context?.find((c) => c.id.startsWith("region"))?.text || "",
        country:
          feature.context?.find((c) => c.id.startsWith("country"))?.text || "",
        mapbox_id: feature.id,
        latitude: feature.center[1] || 0,
        longitude: feature.center[0] || 0,
      }));

    return { data: suggestions, error: null };
  } catch (err) {
    return {
      data: null,
      error: err instanceof Error ? err.message : "Failed to fetch suggestions",
    };
  }
}
