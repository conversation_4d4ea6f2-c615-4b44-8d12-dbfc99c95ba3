import type { TimeValue } from "react-aria";

import { DateTime } from "luxon";

// set date to a different timezone setting
export function setDateForTimeZone(date: Date, timeZone: string) {
  const local = DateTime.fromISO(date.toISOString(), { zone: "local" });
  const utc = DateTime.fromISO(date.toISOString(), { zone: timeZone });

  if (utc.offset !== local.offset) {
    return utc
      .set({
        year: local.year,
        month: local.month,
        day: local.day,
        hour: local.hour,
        minute: local.minute,
      })
      .toJSDate();
  }

  return local.toJSDate();
}

export function displayDateForTimeZone(date: Date, timeZone: string) {
  const local = DateTime.fromISO(date.toISOString(), { zone: "local" });
  const utc = DateTime.fromISO(date.toISOString(), { zone: timeZone });

  if (utc.offset !== local.offset) {
    return local
      .set({
        year: utc.year,
        month: utc.month,
        day: utc.day,
        hour: utc.hour,
        minute: utc.minute,
      })
      .toJSDate();
  }

  return utc.toJSDate();
}

export function setTimeToInteger(time?: TimeValue | null): number {
  if (time) {
    return time.hour * 60 + time.minute;
  }

  return 0;
}

export function getTimeFromInteger(integer: number) {
  return {
    hour: Math.floor(integer / 60),
    minute: integer % 60,
  } as TimeValue;
}

export function computeHours(start: TimeValue, end: TimeValue) {
  const startInteger = setTimeToInteger(start);
  const endInteger = setTimeToInteger(end);

  if (endInteger < startInteger) {
    return (24 * 60 - startInteger + endInteger) / 60;
  }

  return (endInteger - startInteger) / 60;
}
