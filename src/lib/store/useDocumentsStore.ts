import { create } from "zustand";
import { persist } from "zustand/middleware";

export type DocumentDriver = {
  id: string;
  first_name: string;
  last_name: string;
  avatar: string | null;
};

export type DocumentOrganization = {
  id: string;
  name: string;
  avatar: string | null;
};

export type DocumentMetadata = {
  description?: string;
  cachePath?: string;
  analysis?: any;
  [key: string]: any; // Allow any additional metadata properties
};

export type Document = {
  id: string;
  name: string;
  type: string;
  content_type: string;
  size: number;
  created_at: string;
  updated_at: string;
  url: string;
  storage_path: string;
  metadata?: DocumentMetadata;
  driver?: DocumentDriver;
  organization?: DocumentOrganization;
};

interface DocumentsStore {
  documents: Document[];
  addDocument: (document: Document) => void;
  removeDocument: (id: string) => void;
  updateDocument: (document: Document) => void;
  clearDocuments: () => void;
  getDocument: (id: string) => Document | undefined;
  createDemoDocument: (overrides?: Partial<Document>) => Document;
}

// Demo document generator
const generateDemoDocument = (overrides?: Partial<Document>) => {
  const types = [
    "contract",
    "manifest",
    "license",
    "insurance",
    "receipt",
    "general",
    "other",
  ];
  const contentTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "text/plain",
  ];
  const names = [
    "Driver License",
    "Insurance Certificate",
    "Shipment Manifest",
    "Regulatory Compliance",
    "Vehicle Registration",
  ];

  return {
    id: `doc-${Math.random().toString(36).substring(2, 10)}`,
    name: overrides?.name || names[Math.floor(Math.random() * names.length)],
    type: overrides?.type || types[Math.floor(Math.random() * types.length)],
    content_type:
      overrides?.content_type ||
      contentTypes[Math.floor(Math.random() * contentTypes.length)],
    size: Math.floor(Math.random() * 10000000),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    url: "https://thohtvjkerkrihmbhkaz.supabase.co/storage/v1/object/public/documents/sample.pdf",
    storage_path: "documents/sample.pdf",
    metadata: {
      description:
        "This is a sample document for demonstration purposes. In a real system, this would be an actual scanned or uploaded document with proper metadata and content.",
    },
    driver: {
      id: "driver-123",
      first_name: "John",
      last_name: "Driver",
      avatar: null,
    },
    organization: {
      id: "org-123",
      name: "Transport Solutions LLC",
      avatar: null,
    },
    ...overrides,
  };
};

export const useDocumentsStore = create<DocumentsStore>()(
  persist(
    (set, get) => ({
      documents: [],
      addDocument: (document) => {
        set((state) => ({
          documents: [document, ...state.documents],
        }));
      },
      removeDocument: (id) => {
        set((state) => ({
          documents: state.documents.filter((doc) => doc.id !== id),
        }));
      },
      updateDocument: (document) => {
        set((state) => ({
          documents: state.documents.map((doc) =>
            doc.id === document.id ? document : doc,
          ),
        }));
      },
      clearDocuments: () => {
        set({ documents: [] });
      },
      getDocument: (id) => {
        return get().documents.find((document) => document.id === id);
      },
      createDemoDocument: (overrides) => {
        const document = generateDemoDocument(overrides);
        set((state) => ({
          documents: [document, ...state.documents],
        }));
        return document;
      },
    }),
    {
      name: "documents-store",
    },
  ),
);
