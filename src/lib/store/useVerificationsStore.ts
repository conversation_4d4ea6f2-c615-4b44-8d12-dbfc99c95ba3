import { create } from "zustand";
import { persist } from "zustand/middleware";

// import { v4 as uuidv4 } from 'uuid';

const uuidv4 = () => {
  // create a random uuid that is 32 characters long and starts with a letter
  return Math.random().toString(36).substring(2, 15);
};

// Types based on existing API data structure
export interface Driver {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
}

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  license_plate: string;
  us_dot?: string;
  mc_number?: string;
}

export interface Location {
  id: string;
  formatted: string;
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}

export interface Stop {
  id: string;
  label?: string;
  type: string;
  sequence_number: number;
  location?: Location;
}

export interface Document {
  id: string;
  name: string;
  url: string;
  content_type?: string;
  size?: number;
  created_at: string;
}

export interface Verification {
  id: string;
  created_at: string;
  verified_at?: string;
  verified_by?: string;
  driver_id?: string;
  vehicle_id?: string;
  document_id?: string;
  stop_id?: string;
  notes?: string;
  latitude?: number;
  longitude?: number;
  driver?: Driver;
  vehicle?: Vehicle;
  document?: Document;
  stop?: Stop;
}

export interface VerificationResult {
  success: boolean;
  verification_id: string;
  confidence_score: number;
  warnings: string[];
  info: string[];
  processing_time: {
    image_processing_ms: number;
    metadata_extraction_ms: number;
    ai_analysis_ms: number;
    total_ms: number;
  };
}

interface VerificationsState {
  verifications: Verification[];
  verificationResults: Record<string, VerificationResult>;
  addVerification: (verification: Verification) => void;
  updateVerification: (id: string, data: Partial<Verification>) => void;
  deleteVerification: (id: string) => void;
  getVerification: (id: string) => Verification | undefined;
  createDemoVerification: () => Verification;
  markVerificationComplete: (id: string, result?: VerificationResult) => void;
  getVerificationResult: (id: string) => VerificationResult | undefined;
  storeVerificationResult: (id: string, result: VerificationResult) => void;
}

// Sample data generators
const createRandomDriver = (): Driver => {
  const firstNames = ["John", "Jane", "Michael", "Sarah", "David", "Lisa"];
  const lastNames = [
    "Smith",
    "Johnson",
    "Williams",
    "Brown",
    "Jones",
    "Miller",
  ];

  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

  return {
    id: uuidv4(),
    first_name: firstName,
    last_name: lastName,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`,
    phone_number: `+1${Math.floor(1000000000 + Math.random() * **********)}`,
  };
};

const createRandomVehicle = (): Vehicle => {
  const makes = [
    "Ford",
    "Toyota",
    "Chevrolet",
    "Volvo",
    "Freightliner",
    "Peterbilt",
  ];
  const models = ["F-150", "Tacoma", "Silverado", "VNL", "Cascadia", "579"];

  const make = makes[Math.floor(Math.random() * makes.length)];
  const model = models[Math.floor(Math.random() * models.length)];

  return {
    id: uuidv4(),
    make,
    model,
    year: 2018 + Math.floor(Math.random() * 6),
    license_plate: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(1000 + Math.random() * 9000)}`,
    us_dot: `${Math.floor(100000 + Math.random() * 900000)}`,
    mc_number: `MC-${Math.floor(100000 + Math.random() * 900000)}`,
  };
};

const createRandomLocation = (): Location => {
  const cities = [
    "Chicago",
    "Los Angeles",
    "New York",
    "Houston",
    "Phoenix",
    "Dallas",
  ];
  const states = ["IL", "CA", "NY", "TX", "AZ", "TX"];

  const index = Math.floor(Math.random() * cities.length);

  return {
    id: uuidv4(),
    formatted: `123 Main St, ${cities[index]}, ${states[index]} 12345`,
    street: "123 Main St",
    city: cities[index],
    state: states[index],
    country: "USA",
    latitude: 40 + (Math.random() * 10 - 5),
    longitude: -100 + (Math.random() * 30 - 15),
  };
};

const createRandomStop = (): Stop => {
  const types = ["pickup", "delivery", "dropoff"];
  const type = types[Math.floor(Math.random() * types.length)];

  return {
    id: uuidv4(),
    label: `${type.charAt(0).toUpperCase() + type.slice(1)} Stop`,
    type,
    sequence_number: Math.floor(1 + Math.random() * 5),
    location: createRandomLocation(),
  };
};

const createRandomDocument = (): Document => {
  const docTypes = ["image/jpeg", "application/pdf", "image/png"];
  const docNames = [
    "Bill of Lading",
    "Delivery Receipt",
    "Proof of Delivery",
    "Driver ID",
  ];

  const name = docNames[Math.floor(Math.random() * docNames.length)];
  const contentType = docTypes[Math.floor(Math.random() * docTypes.length)];

  return {
    id: uuidv4(),
    name,
    url: "https://placehold.co/600x400",
    content_type: contentType,
    size: Math.floor(100000 + Math.random() * 900000),
    created_at: new Date().toISOString(),
  };
};

export const useVerificationsStore = create<VerificationsState>()(
  persist(
    (set, get) => ({
      verifications: [],
      verificationResults: {},

      addVerification: (verification) =>
        set((state) => ({
          verifications: [...state.verifications, verification],
        })),

      updateVerification: (id, data) =>
        set((state) => ({
          verifications: state.verifications.map((verification) =>
            verification.id === id
              ? { ...verification, ...data }
              : verification,
          ),
        })),

      deleteVerification: (id) =>
        set((state) => ({
          verifications: state.verifications.filter(
            (verification) => verification.id !== id,
          ),
        })),

      getVerification: (id) => {
        return get().verifications.find(
          (verification) => verification.id === id,
        );
      },

      markVerificationComplete: (id, result) => {
        const now = new Date().toISOString();

        // Update verification status
        set((state) => ({
          verifications: state.verifications.map((verification) =>
            verification.id === id
              ? { ...verification, verified_at: now, verified_by: "System" }
              : verification,
          ),
        }));

        // Store verification result if provided
        if (result) {
          set((state) => ({
            verificationResults: {
              ...state.verificationResults,
              [id]: result,
            },
          }));
        }
      },

      getVerificationResult: (id) => {
        return get().verificationResults[id];
      },

      storeVerificationResult: (id, result) => {
        set((state) => ({
          verificationResults: {
            ...state.verificationResults,
            [id]: result,
          },
        }));
      },

      createDemoVerification: () => {
        const driver = createRandomDriver();
        const vehicle = createRandomVehicle();
        const document = createRandomDocument();
        const stop = createRandomStop();

        const isVerified = Math.random() > 0.5;

        const verification: Verification = {
          id: uuidv4(),
          created_at: new Date(
            Date.now() - Math.floor(Math.random() * 10 * 24 * 60 * 60 * 1000),
          ).toISOString(),
          verified_at: isVerified
            ? new Date(
                Date.now() -
                  Math.floor(Math.random() * 5 * 24 * 60 * 60 * 1000),
              ).toISOString()
            : undefined,
          verified_by: isVerified ? "System" : undefined,
          driver_id: driver.id,
          vehicle_id: vehicle.id,
          document_id: document.id,
          stop_id: stop.id,
          notes:
            Math.random() > 0.7
              ? "Sample verification note for demonstration purposes."
              : undefined,
          latitude: stop.location?.latitude,
          longitude: stop.location?.longitude,
          driver,
          vehicle,
          document,
          stop,
        };

        set((state) => ({
          verifications: [...state.verifications, verification],
        }));

        return verification;
      },
    }),
    {
      name: "verifications-storage",
      skipHydration: false,
    },
  ),
);
