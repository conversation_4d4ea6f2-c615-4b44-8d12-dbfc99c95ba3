import { formatRelative } from "date-fns";

export function formatCurrency(
  value: number,
  {
    locale = "en-US",
    currency = "USD",
    style = "currency",
  }: {
    locale?: string;
    currency?: string;
    style?: "currency" | "decimal" | "percent";
  } = {},
) {
  return new Intl.NumberFormat(locale, {
    style,
    currency,
  }).format(value);
}

export function formatTimeStamp(d: Date) {
  return formatRelative(d, new Date());
}
