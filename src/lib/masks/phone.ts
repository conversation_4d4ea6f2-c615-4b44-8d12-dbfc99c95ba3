import type { MaskitoOptions } from "@maskito/core";

import { maskitoTransform } from "@maskito/core";

export const mask = {
  mask: [
    "+",
    "1",
    " ",
    "(",
    /\d/,
    /\d/,
    /\d/,
    ")",
    " ",
    /\d/,
    /\d/,
    /\d/,
    "-",
    /\d/,
    /\d/,
    /\d/,
    /\d/,
  ],
} as MaskitoOptions;

export function transformPhoneNumber(value: string) {
  return maskitoTransform(value, mask);
}
