# Utility Functions

This directory contains pure utility functions and helpers used throughout the application. These are functional, reusable pieces of code that handle common operations and transformations.

> **Quick Start**
>
> - Need to manipulate data? Check `utils.ts`
> - Need to format values? Check `format.ts`
> - Need to validate input? Check `validation.ts`
> - Need to handle dates? Check `dates.ts`
> - Need CSS utilities? Check `styles.ts`

> [!NOTE] The utilities in this directory are pure functions - they take inputs and return outputs without side effects. This makes them highly reusable and testable. Always check here before implementing new utility functions to avoid duplication.

## Directory Contents

```
lib/
├── utils.ts        # General utility functions
├── format.ts       # Value formatting helpers
├── validation.ts   # Input validation helpers
├── dates.ts        # Date manipulation utilities
├── styles.ts       # CSS/Tailwind helpers
└── README.md       # This file
```

## Common Utilities

### General Utilities (`utils.ts`)

```typescript
// Deep merge objects
const merged = deepMerge(objA, objB);

// Create unique IDs
const id = generateId();

// Debounce functions
const debouncedFn = debounce(fn, 300);

// Memoize expensive operations
const memoizedFn = memoize(expensiveFn);
```

### Formatting (`format.ts`)

```typescript
// Currency formatting
const price = formatCurrency(1234.56, "USD"); // "$1,234.56"

// Number formatting
const weight = formatNumber(1234.56, "kg"); // "1,234.56 kg"

// Phone number formatting
const phone = formatPhone("+12345678900"); // "(*************"

// Address formatting
const address = formatAddress({
  street: "123 Main St",
  city: "San Francisco",
  state: "CA",
}); // "123 Main St, San Francisco, CA"
```

### Validation (`validation.ts`)

```typescript
// Input validation
const isValid = validateEmail("<EMAIL>");

// Phone validation
const isValidPhone = validatePhone("+12345678900");

// Password strength
const strength = checkPasswordStrength("password123");

// Required fields
const hasRequired = checkRequiredFields(formData, ["email", "password"]);
```

### Date Utilities (`dates.ts`)

```typescript
// Format dates
const formatted = formatDate(date, "MM/DD/YYYY");

// Calculate durations
const duration = calculateDuration(startDate, endDate);

// Add time
const future = addDays(date, 7);

// Compare dates
const isBefore = isDateBefore(dateA, dateB);
```

### Style Utilities (`styles.ts`)

```typescript
// Conditional classes
const className = cn(
  "base-class",
  isActive && "active",
  isDisabled && "disabled",
);

// Dynamic styles
const styles = generateStyles({
  color: "primary",
  size: "lg",
});

// Theme values
const color = getThemeValue("color", "primary");
```

## Best Practices

1. **Function Design**
   - Keep functions pure
   - Use TypeScript types
   - Document parameters
   - Handle edge cases

2. **Error Handling**
   - Return null for invalid inputs
   - Throw errors when appropriate
   - Provide error messages
   - Handle type errors

3. **Performance**
   - Memoize expensive operations
   - Optimize loops
   - Cache results
   - Use efficient algorithms

4. **Testing**
   - Write unit tests
   - Test edge cases
   - Verify types
   - Document test cases

## Common Patterns

### Data Transformation

```typescript
// Convert array to object
const toObject = <T extends { id: string }>(array: T[]): Record<string, T> => {
  return array.reduce(
    (acc, item) => ({
      ...acc,
      [item.id]: item,
    }),
    {},
  );
};

// Group array by key
const groupBy = <T>(array: T[], key: keyof T): Record<string, T[]> => {
  return array.reduce(
    (acc, item) => ({
      ...acc,
      [item[key]]: [...(acc[item[key]] || []), item],
    }),
    {},
  );
};
```

### Value Formatting

```typescript
// Format with fallback
const formatWithFallback = (
  value: string | null | undefined,
  formatter: (v: string) => string,
  fallback: string = "-",
): string => {
  if (!value) return fallback;
  return formatter(value);
};

// Format list
const formatList = (items: string[], conjunction: string = "and"): string => {
  if (items.length === 0) return "";
  if (items.length === 1) return items[0];
  return `${items.slice(0, -1).join(", ")} ${conjunction} ${items.slice(-1)}`;
};
```

### Validation Helpers

```typescript
// Validate object shape
const validateShape = <T extends object>(
  data: unknown,
  shape: Record<keyof T, (v: unknown) => boolean>,
): data is T => {
  if (!data || typeof data !== "object") return false;
  return Object.entries(shape).every(([key, validator]) => {
    return validator((data as any)[key]);
  });
};

// Chain validations
const validate = <T>(
  value: T,
  ...validators: ((v: T) => boolean)[]
): boolean => {
  return validators.every((validator) => validator(value));
};
```

## When to Use

1. **Data Manipulation**
   - Array transformations
   - Object operations
   - Type conversions
   - Data filtering

2. **Value Formatting**
   - Number formatting
   - Date formatting
   - Currency formatting
   - Address formatting

3. **Input Validation**
   - Form validation
   - Data validation
   - Type checking
   - Format verification

4. **Style Management**
   - Class composition
   - Style generation
   - Theme handling
   - Responsive utilities
