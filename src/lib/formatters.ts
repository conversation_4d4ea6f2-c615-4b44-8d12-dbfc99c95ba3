/**
 * Format a number as currency with the specified locale and currency code
 */
export function formatCurrency(
  value: number | null | undefined,
  locale: string = "en-US",
  currencyCode: string = "USD",
): string {
  if (value === null || value === undefined) return "-";

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
}

/**
 * Format a number as a weight measurement with the specified unit
 */
export function formatWeight(
  value: number | null | undefined,
  unit: string = "kg",
  locale: string = "en-US",
): string {
  if (value === null || value === undefined) return "-";

  return `${new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value)} ${unit}`;
}

/**
 * Format a date with the specified format
 */
export function formatDate(
  date: Date | string | null | undefined,
  options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  },
  locale: string = "en-US",
): string {
  if (!date) return "-";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  return new Intl.DateTimeFormat(locale, options).format(dateObj);
}
