import { z } from "zod";

import { COMPANY_INDUSTRIES } from "@/components/forms/fields/company/CompanyIndustry";
import { COMPANY_SIZES } from "@/components/forms/fields/company/CompanySize";

export const organizationSchema = z.object({
  name: z.string().min(2, "Organization name must be at least 2 characters"),
  industry: z.enum(COMPANY_INDUSTRIES, {
    errorMap: () => ({ message: "Please select an industry" }),
  }),
  size: z.enum(COMPANY_SIZES, {
    errorMap: () => ({ message: "Please select company size" }),
  }),
  type: z.enum(["individual", "private", "non_profit", "government"] as const),
  address: z.string().optional(),
});
