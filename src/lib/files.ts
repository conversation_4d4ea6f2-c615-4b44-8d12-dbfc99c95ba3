export function downloadBlob(
  content: string,
  filename: string,
  contentType: string,
) {
  const blob = new Blob([content], { type: contentType });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", filename);
  link.click();
  link.remove();
}

export function arrayToCsv(data: (string | number)[][]) {
  return data
    .map(
      (row) =>
        row
          .map(String) // convert every value to String
          .map((v) => v.replace(/"/g, '""')) // escape double quotes
          .map((v) => `"${v}"`) // quote it
          .join(","), // comma-separated
    )
    .join("\r\n"); // rows starting on new lines
}

export function exportCSV({
  fileName = "export.csv",
  data,
  headers,
}: {
  headers?: string[];
  data: (string | number)[][];
  fileName?: string;
}) {
  const csvData = [...data];

  if (headers) {
    csvData.unshift(headers);
  }

  const csv = arrayToCsv(csvData);

  if (fileName.endsWith(".csv")) {
    downloadBlob(csv, fileName, "text/csv;charset=utf-8;");
  }

  return csv;
}
