// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";

import type { Database } from "./types";

const SUPABASE_URL = "https://thohtvjkerkrihmbhkaz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRob2h0dmprZXJrcmlobWJoa2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ2MjkzOTAsImV4cCI6MjA1MDIwNTM5MH0.MrGW4dCBQb7dI0JAM5PtMYfQ75ZR3nDyaD2ZrYpUuFY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
);
