export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      _driver_vehicles: {
        Row: {
          A: string
          B: string
        }
        Insert: {
          A: string
          B: string
        }
        Update: {
          A?: string
          B?: string
        }
        Relationships: [
          {
            foreignKeyName: "_driver_vehicles_A_fkey"
            columns: ["A"]
            isOneToOne: false
            referencedRelation: "drivers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "_driver_vehicles_B_fkey"
            columns: ["B"]
            isOneToOne: false
            referencedRelation: "vehicles"
            referencedColumns: ["id"]
          },
        ]
      }
      _load_stops: {
        Row: {
          A: string
          B: string
        }
        Insert: {
          A: string
          B: string
        }
        Update: {
          A?: string
          B?: string
        }
        Relationships: [
          {
            foreignKeyName: "_load_stops_A_fkey"
            columns: ["A"]
            isOneToOne: false
            referencedRelation: "loads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "_load_stops_B_fkey"
            columns: ["B"]
            isOneToOne: false
            referencedRelation: "stops"
            referencedColumns: ["id"]
          },
        ]
      }
      _load_verifications: {
        Row: {
          A: string
          B: string
        }
        Insert: {
          A: string
          B: string
        }
        Update: {
          A?: string
          B?: string
        }
        Relationships: [
          {
            foreignKeyName: "_load_verifications_A_fkey"
            columns: ["A"]
            isOneToOne: false
            referencedRelation: "loads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "_load_verifications_B_fkey"
            columns: ["B"]
            isOneToOne: false
            referencedRelation: "verifications"
            referencedColumns: ["id"]
          },
        ]
      }
      _prisma_migrations: {
        Row: {
          applied_steps_count: number
          checksum: string
          finished_at: string | null
          id: string
          logs: string | null
          migration_name: string
          rolled_back_at: string | null
          started_at: string
        }
        Insert: {
          applied_steps_count?: number
          checksum: string
          finished_at?: string | null
          id: string
          logs?: string | null
          migration_name: string
          rolled_back_at?: string | null
          started_at?: string
        }
        Update: {
          applied_steps_count?: number
          checksum?: string
          finished_at?: string | null
          id?: string
          logs?: string | null
          migration_name?: string
          rolled_back_at?: string | null
          started_at?: string
        }
        Relationships: []
      }
      documents: {
        Row: {
          bucket_id: string
          content_type: string | null
          created_at: string
          created_by: string | null
          deleted_at: string | null
          description: string | null
          driver_id: string | null
          id: string
          latitude: number | null
          longitude: number | null
          metadata: Json | null
          name: string
          organization_id: string | null
          size: number | null
          storage_path: string
          type: Database["public"]["Enums"]["document_type"]
          updated_at: string
          url: string
          verification_id: string | null
        }
        Insert: {
          bucket_id?: string
          content_type?: string | null
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          description?: string | null
          driver_id?: string | null
          id: string
          latitude?: number | null
          longitude?: number | null
          metadata?: Json | null
          name: string
          organization_id?: string | null
          size?: number | null
          storage_path: string
          type?: Database["public"]["Enums"]["document_type"]
          updated_at: string
          url?: string
          verification_id?: string | null
        }
        Update: {
          bucket_id?: string
          content_type?: string | null
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          description?: string | null
          driver_id?: string | null
          id?: string
          latitude?: number | null
          longitude?: number | null
          metadata?: Json | null
          name?: string
          organization_id?: string | null
          size?: number | null
          storage_path?: string
          type?: Database["public"]["Enums"]["document_type"]
          updated_at?: string
          url?: string
          verification_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "documents_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_driver_id_fkey"
            columns: ["driver_id"]
            isOneToOne: false
            referencedRelation: "drivers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_verification_id_fkey"
            columns: ["verification_id"]
            isOneToOne: false
            referencedRelation: "verifications"
            referencedColumns: ["id"]
          },
        ]
      }
      drivers: {
        Row: {
          created_at: string
          deleted_at: string | null
          id: string
          location_id: string | null
          organization_id: string | null
          score: number
          settings: Json | null
          status: Database["public"]["Enums"]["driver_status"] | null
          stripe_account_id: string | null
          stripe_customer_id: string | null
          tier: string
          updated_at: string
          user_id: string | null
          verified_at: string | null
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          id: string
          location_id?: string | null
          organization_id?: string | null
          score?: number
          settings?: Json | null
          status?: Database["public"]["Enums"]["driver_status"] | null
          stripe_account_id?: string | null
          stripe_customer_id?: string | null
          tier?: string
          updated_at: string
          user_id?: string | null
          verified_at?: string | null
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          id?: string
          location_id?: string | null
          organization_id?: string | null
          score?: number
          settings?: Json | null
          status?: Database["public"]["Enums"]["driver_status"] | null
          stripe_account_id?: string | null
          stripe_customer_id?: string | null
          tier?: string
          updated_at?: string
          user_id?: string | null
          verified_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "drivers_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "drivers_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "drivers_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          actor_id: string | null
          actor_name: string | null
          actor_type: Database["public"]["Enums"]["actor_type"]
          correlation_id: string | null
          created_at: string
          description: string | null
          device_id: string | null
          entity_id: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          event_type: Database["public"]["Enums"]["event_type"]
          id: string
          ip_address: string | null
          is_public: boolean
          is_system_generated: boolean
          latitude: number | null
          location_id: string | null
          location_name: string | null
          longitude: number | null
          metadata: Json | null
          parent_event_id: string | null
          related_entity_ids: string[] | null
          severity: Database["public"]["Enums"]["event_severity"]
          source: string | null
          title: string | null
          user_agent: string | null
        }
        Insert: {
          actor_id?: string | null
          actor_name?: string | null
          actor_type: Database["public"]["Enums"]["actor_type"]
          correlation_id?: string | null
          created_at?: string
          description?: string | null
          device_id?: string | null
          entity_id: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          event_type: Database["public"]["Enums"]["event_type"]
          id: string
          ip_address?: string | null
          is_public?: boolean
          is_system_generated?: boolean
          latitude?: number | null
          location_id?: string | null
          location_name?: string | null
          longitude?: number | null
          metadata?: Json | null
          parent_event_id?: string | null
          related_entity_ids?: string[] | null
          severity?: Database["public"]["Enums"]["event_severity"]
          source?: string | null
          title?: string | null
          user_agent?: string | null
        }
        Update: {
          actor_id?: string | null
          actor_name?: string | null
          actor_type?: Database["public"]["Enums"]["actor_type"]
          correlation_id?: string | null
          created_at?: string
          description?: string | null
          device_id?: string | null
          entity_id?: string
          entity_type?: Database["public"]["Enums"]["entity_type"]
          event_type?: Database["public"]["Enums"]["event_type"]
          id?: string
          ip_address?: string | null
          is_public?: boolean
          is_system_generated?: boolean
          latitude?: number | null
          location_id?: string | null
          location_name?: string | null
          longitude?: number | null
          metadata?: Json | null
          parent_event_id?: string | null
          related_entity_ids?: string[] | null
          severity?: Database["public"]["Enums"]["event_severity"]
          source?: string | null
          title?: string | null
          user_agent?: string | null
        }
        Relationships: []
      }
      incidents: {
        Row: {
          created_at: string
          deleted_at: string | null
          driver_id: string | null
          id: string
          load_id: string
          severity: Database["public"]["Enums"]["incident_severity"]
          shipment_id: string
          status: Database["public"]["Enums"]["incident_status"]
          stop_id: string | null
          summary: string | null
          title: string
          type: Database["public"]["Enums"]["incident_type"]
          updated_at: string
          verification_id: string | null
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          driver_id?: string | null
          id: string
          load_id: string
          severity: Database["public"]["Enums"]["incident_severity"]
          shipment_id: string
          status?: Database["public"]["Enums"]["incident_status"]
          stop_id?: string | null
          summary?: string | null
          title: string
          type: Database["public"]["Enums"]["incident_type"]
          updated_at: string
          verification_id?: string | null
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          driver_id?: string | null
          id?: string
          load_id?: string
          severity?: Database["public"]["Enums"]["incident_severity"]
          shipment_id?: string
          status?: Database["public"]["Enums"]["incident_status"]
          stop_id?: string | null
          summary?: string | null
          title?: string
          type?: Database["public"]["Enums"]["incident_type"]
          updated_at?: string
          verification_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "incidents_driver_id_fkey"
            columns: ["driver_id"]
            isOneToOne: false
            referencedRelation: "drivers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "incidents_load_id_fkey"
            columns: ["load_id"]
            isOneToOne: false
            referencedRelation: "loads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "incidents_shipment_id_fkey"
            columns: ["shipment_id"]
            isOneToOne: false
            referencedRelation: "shipments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "incidents_stop_id_fkey"
            columns: ["stop_id"]
            isOneToOne: false
            referencedRelation: "stops"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "incidents_verification_id_fkey"
            columns: ["verification_id"]
            isOneToOne: false
            referencedRelation: "verifications"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          created_at: string
          created_by: string
          deleted_at: string | null
          email: string
          expires_at: string | null
          id: string
          organization_id: string | null
          role: Database["public"]["Enums"]["members_role"] | null
          status: Database["public"]["Enums"]["invitation_status"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          deleted_at?: string | null
          email: string
          expires_at?: string | null
          id: string
          organization_id?: string | null
          role?: Database["public"]["Enums"]["members_role"] | null
          status?: Database["public"]["Enums"]["invitation_status"]
          updated_at: string
        }
        Update: {
          created_at?: string
          created_by?: string
          deleted_at?: string | null
          email?: string
          expires_at?: string | null
          id?: string
          organization_id?: string | null
          role?: Database["public"]["Enums"]["members_role"] | null
          status?: Database["public"]["Enums"]["invitation_status"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      loads: {
        Row: {
          can_mix_with_chemicals: boolean
          can_mix_with_food: boolean
          commodity_code: string | null
          contamination_risk: boolean
          created_at: string
          declared_value: number | null
          deleted_at: string | null
          delivery_date_from: string | null
          delivery_date_to: string | null
          description: string | null
          destination_id: string | null
          hazmat_class: Database["public"]["Enums"]["hazmat_class"]
          hazmat_subclass: string | null
          height: number | null
          id: string
          is_fragile: boolean
          is_oversized: boolean
          is_overweight: boolean
          is_stackable: boolean
          is_times_sensitive: boolean
          length: number | null
          loading_method: Database["public"]["Enums"]["loading_method"][] | null
          max_height: number | null
          max_length: number | null
          max_transit_time: number | null
          max_weight: number | null
          max_width: number | null
          odor_risk: boolean
          organization_id: string | null
          origin_id: string | null
          physical_state: Database["public"]["Enums"]["physical_state"]
          pickup_date_from: string | null
          pickup_date_to: string | null
          primary_category: Database["public"]["Enums"]["load_category"]
          priority: number
          prohibited_trailers:
            | Database["public"]["Enums"]["trailer_type"][]
            | null
          quantity: number
          recommended_trailers:
            | Database["public"]["Enums"]["trailer_type"][]
            | null
          requires_air_ride: boolean
          requires_certified_driver: boolean
          requires_clean_trailer: boolean
          requires_permits: boolean
          requires_special_equip: boolean
          secondary_categories:
            | Database["public"]["Enums"]["load_category"][]
            | null
          security_level: Database["public"]["Enums"]["security_level"]
          service_level: Database["public"]["Enums"]["service_level"]
          shipment_id: string | null
          special_equipment: string | null
          special_handling: string | null
          special_instructions: string | null
          status: Database["public"]["Enums"]["load_status"]
          temp_range_max: number | null
          temp_range_min: number | null
          temperature_requirement: Database["public"]["Enums"]["temperature_requirement"]
          un_number: string | null
          updated_at: string
          volume: number | null
          volume_unit: string | null
          weight: number | null
          weight_unit: string | null
          width: number | null
        }
        Insert: {
          can_mix_with_chemicals?: boolean
          can_mix_with_food?: boolean
          commodity_code?: string | null
          contamination_risk?: boolean
          created_at?: string
          declared_value?: number | null
          deleted_at?: string | null
          delivery_date_from?: string | null
          delivery_date_to?: string | null
          description?: string | null
          destination_id?: string | null
          hazmat_class?: Database["public"]["Enums"]["hazmat_class"]
          hazmat_subclass?: string | null
          height?: number | null
          id: string
          is_fragile?: boolean
          is_oversized?: boolean
          is_overweight?: boolean
          is_stackable?: boolean
          is_times_sensitive?: boolean
          length?: number | null
          loading_method?:
            | Database["public"]["Enums"]["loading_method"][]
            | null
          max_height?: number | null
          max_length?: number | null
          max_transit_time?: number | null
          max_weight?: number | null
          max_width?: number | null
          odor_risk?: boolean
          organization_id?: string | null
          origin_id?: string | null
          physical_state: Database["public"]["Enums"]["physical_state"]
          pickup_date_from?: string | null
          pickup_date_to?: string | null
          primary_category: Database["public"]["Enums"]["load_category"]
          priority?: number
          prohibited_trailers?:
            | Database["public"]["Enums"]["trailer_type"][]
            | null
          quantity?: number
          recommended_trailers?:
            | Database["public"]["Enums"]["trailer_type"][]
            | null
          requires_air_ride?: boolean
          requires_certified_driver?: boolean
          requires_clean_trailer?: boolean
          requires_permits?: boolean
          requires_special_equip?: boolean
          secondary_categories?:
            | Database["public"]["Enums"]["load_category"][]
            | null
          security_level?: Database["public"]["Enums"]["security_level"]
          service_level?: Database["public"]["Enums"]["service_level"]
          shipment_id?: string | null
          special_equipment?: string | null
          special_handling?: string | null
          special_instructions?: string | null
          status?: Database["public"]["Enums"]["load_status"]
          temp_range_max?: number | null
          temp_range_min?: number | null
          temperature_requirement: Database["public"]["Enums"]["temperature_requirement"]
          un_number?: string | null
          updated_at: string
          volume?: number | null
          volume_unit?: string | null
          weight?: number | null
          weight_unit?: string | null
          width?: number | null
        }
        Update: {
          can_mix_with_chemicals?: boolean
          can_mix_with_food?: boolean
          commodity_code?: string | null
          contamination_risk?: boolean
          created_at?: string
          declared_value?: number | null
          deleted_at?: string | null
          delivery_date_from?: string | null
          delivery_date_to?: string | null
          description?: string | null
          destination_id?: string | null
          hazmat_class?: Database["public"]["Enums"]["hazmat_class"]
          hazmat_subclass?: string | null
          height?: number | null
          id?: string
          is_fragile?: boolean
          is_oversized?: boolean
          is_overweight?: boolean
          is_stackable?: boolean
          is_times_sensitive?: boolean
          length?: number | null
          loading_method?:
            | Database["public"]["Enums"]["loading_method"][]
            | null
          max_height?: number | null
          max_length?: number | null
          max_transit_time?: number | null
          max_weight?: number | null
          max_width?: number | null
          odor_risk?: boolean
          organization_id?: string | null
          origin_id?: string | null
          physical_state?: Database["public"]["Enums"]["physical_state"]
          pickup_date_from?: string | null
          pickup_date_to?: string | null
          primary_category?: Database["public"]["Enums"]["load_category"]
          priority?: number
          prohibited_trailers?:
            | Database["public"]["Enums"]["trailer_type"][]
            | null
          quantity?: number
          recommended_trailers?:
            | Database["public"]["Enums"]["trailer_type"][]
            | null
          requires_air_ride?: boolean
          requires_certified_driver?: boolean
          requires_clean_trailer?: boolean
          requires_permits?: boolean
          requires_special_equip?: boolean
          secondary_categories?:
            | Database["public"]["Enums"]["load_category"][]
            | null
          security_level?: Database["public"]["Enums"]["security_level"]
          service_level?: Database["public"]["Enums"]["service_level"]
          shipment_id?: string | null
          special_equipment?: string | null
          special_handling?: string | null
          special_instructions?: string | null
          status?: Database["public"]["Enums"]["load_status"]
          temp_range_max?: number | null
          temp_range_min?: number | null
          temperature_requirement?: Database["public"]["Enums"]["temperature_requirement"]
          un_number?: string | null
          updated_at?: string
          volume?: number | null
          volume_unit?: string | null
          weight?: number | null
          weight_unit?: string | null
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "loads_destination_id_fkey"
            columns: ["destination_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "loads_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "loads_origin_id_fkey"
            columns: ["origin_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "loads_shipment_id_fkey"
            columns: ["shipment_id"]
            isOneToOne: false
            referencedRelation: "shipments"
            referencedColumns: ["id"]
          },
        ]
      }
      locations: {
        Row: {
          city: string | null
          country: string
          created_at: string
          deleted_at: string | null
          formatted: string
          id: string
          latitude: number
          longitude: number
          neighborhood: string | null
          postal: string | null
          sector: Database["public"]["Enums"]["location_sector"]
          state: string | null
          street: string | null
          suite: string | null
          type: Database["public"]["Enums"]["location_type"]
          updated_at: string
        }
        Insert: {
          city?: string | null
          country: string
          created_at?: string
          deleted_at?: string | null
          formatted: string
          id: string
          latitude: number
          longitude: number
          neighborhood?: string | null
          postal?: string | null
          sector?: Database["public"]["Enums"]["location_sector"]
          state?: string | null
          street?: string | null
          suite?: string | null
          type?: Database["public"]["Enums"]["location_type"]
          updated_at: string
        }
        Update: {
          city?: string | null
          country?: string
          created_at?: string
          deleted_at?: string | null
          formatted?: string
          id?: string
          latitude?: number
          longitude?: number
          neighborhood?: string | null
          postal?: string | null
          sector?: Database["public"]["Enums"]["location_sector"]
          state?: string | null
          street?: string | null
          suite?: string | null
          type?: Database["public"]["Enums"]["location_type"]
          updated_at?: string
        }
        Relationships: []
      }
      members: {
        Row: {
          created_at: string
          deleted_at: string | null
          email: string
          id: string
          organization_id: string
          role: Database["public"]["Enums"]["members_role"]
          status: Database["public"]["Enums"]["membership_status"]
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          email: string
          id: string
          organization_id: string
          role?: Database["public"]["Enums"]["members_role"]
          status?: Database["public"]["Enums"]["membership_status"]
          updated_at: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          email?: string
          id?: string
          organization_id?: string
          role?: Database["public"]["Enums"]["members_role"]
          status?: Database["public"]["Enums"]["membership_status"]
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "members_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          created_at: string
          deleted_at: string | null
          id: string
          message: string
          read_at: string | null
          title: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          id: string
          message: string
          read_at?: string | null
          title: string
          type: Database["public"]["Enums"]["notification_type"]
          updated_at: string
          user_id: string
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          id?: string
          message?: string
          read_at?: string | null
          title?: string
          type?: Database["public"]["Enums"]["notification_type"]
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          avatar: string | null
          created_at: string
          deleted_at: string | null
          email: string | null
          functions:
            | Database["public"]["Enums"]["organization_functions"][]
            | null
          id: string
          industry: string | null
          location_id: string | null
          name: string
          phone: string | null
          settings: Json | null
          size: string | null
          status: Database["public"]["Enums"]["organization_status"]
          stripe_account_id: string | null
          stripe_customer_id: string | null
          type: Database["public"]["Enums"]["organization_type"]
          updated_at: string
          website: string | null
        }
        Insert: {
          avatar?: string | null
          created_at?: string
          deleted_at?: string | null
          email?: string | null
          functions?:
            | Database["public"]["Enums"]["organization_functions"][]
            | null
          id: string
          industry?: string | null
          location_id?: string | null
          name: string
          phone?: string | null
          settings?: Json | null
          size?: string | null
          status?: Database["public"]["Enums"]["organization_status"]
          stripe_account_id?: string | null
          stripe_customer_id?: string | null
          type?: Database["public"]["Enums"]["organization_type"]
          updated_at: string
          website?: string | null
        }
        Update: {
          avatar?: string | null
          created_at?: string
          deleted_at?: string | null
          email?: string | null
          functions?:
            | Database["public"]["Enums"]["organization_functions"][]
            | null
          id?: string
          industry?: string | null
          location_id?: string | null
          name?: string
          phone?: string | null
          settings?: Json | null
          size?: string | null
          status?: Database["public"]["Enums"]["organization_status"]
          stripe_account_id?: string | null
          stripe_customer_id?: string | null
          type?: Database["public"]["Enums"]["organization_type"]
          updated_at?: string
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organizations_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      positions: {
        Row: {
          created_at: string
          deleted_at: string | null
          id: string
          latitude: number
          longitude: number
          shipment_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          id: string
          latitude: number
          longitude: number
          shipment_id: string
          updated_at: string
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          id?: string
          latitude?: number
          longitude?: number
          shipment_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "positions_shipment_id_fkey"
            columns: ["shipment_id"]
            isOneToOne: false
            referencedRelation: "shipments"
            referencedColumns: ["id"]
          },
        ]
      }
      qualifications: {
        Row: {
          created_at: string
          deleted_at: string | null
          document_id: string | null
          driver_id: string
          expires_at: string | null
          id: string
          issued_at: string
          issuing_state: string
          status: Database["public"]["Enums"]["qualification_status"]
          type: Database["public"]["Enums"]["qualification_type"]
          updated_at: string
          verified_at: string | null
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          document_id?: string | null
          driver_id: string
          expires_at?: string | null
          id: string
          issued_at: string
          issuing_state: string
          status?: Database["public"]["Enums"]["qualification_status"]
          type: Database["public"]["Enums"]["qualification_type"]
          updated_at: string
          verified_at?: string | null
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          document_id?: string | null
          driver_id?: string
          expires_at?: string | null
          id?: string
          issued_at?: string
          issuing_state?: string
          status?: Database["public"]["Enums"]["qualification_status"]
          type?: Database["public"]["Enums"]["qualification_type"]
          updated_at?: string
          verified_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "qualifications_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "qualifications_driver_id_fkey"
            columns: ["driver_id"]
            isOneToOne: false
            referencedRelation: "drivers"
            referencedColumns: ["id"]
          },
        ]
      }
      shipments: {
        Row: {
          assigned_at: string | null
          cancelled_at: string | null
          completed_at: string | null
          confirmed_at: string | null
          created_at: string
          deleted_at: string | null
          destination_id: string | null
          distance: number | null
          driver_id: string | null
          id: string
          mode: Database["public"]["Enums"]["shipment_mode"]
          organization_id: string | null
          origin_id: string | null
          source: Database["public"]["Enums"]["shipment_source"]
          started_at: string | null
          status: Database["public"]["Enums"]["shipment_status"]
          type: Database["public"]["Enums"]["shipment_type"]
          updated_at: string
          valuation: number | null
          weight: number | null
        }
        Insert: {
          assigned_at?: string | null
          cancelled_at?: string | null
          completed_at?: string | null
          confirmed_at?: string | null
          created_at?: string
          deleted_at?: string | null
          destination_id?: string | null
          distance?: number | null
          driver_id?: string | null
          id: string
          mode?: Database["public"]["Enums"]["shipment_mode"]
          organization_id?: string | null
          origin_id?: string | null
          source?: Database["public"]["Enums"]["shipment_source"]
          started_at?: string | null
          status?: Database["public"]["Enums"]["shipment_status"]
          type?: Database["public"]["Enums"]["shipment_type"]
          updated_at: string
          valuation?: number | null
          weight?: number | null
        }
        Update: {
          assigned_at?: string | null
          cancelled_at?: string | null
          completed_at?: string | null
          confirmed_at?: string | null
          created_at?: string
          deleted_at?: string | null
          destination_id?: string | null
          distance?: number | null
          driver_id?: string | null
          id?: string
          mode?: Database["public"]["Enums"]["shipment_mode"]
          organization_id?: string | null
          origin_id?: string | null
          source?: Database["public"]["Enums"]["shipment_source"]
          started_at?: string | null
          status?: Database["public"]["Enums"]["shipment_status"]
          type?: Database["public"]["Enums"]["shipment_type"]
          updated_at?: string
          valuation?: number | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "shipments_destination_id_fkey"
            columns: ["destination_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shipments_driver_id_fkey"
            columns: ["driver_id"]
            isOneToOne: false
            referencedRelation: "drivers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shipments_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shipments_origin_id_fkey"
            columns: ["origin_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      stops: {
        Row: {
          arrived_at: string | null
          created_at: string
          deleted_at: string | null
          departed_at: string | null
          id: string
          label: string | null
          latitude: number | null
          location_id: string | null
          longitude: number | null
          sequence_number: number
          shipment_id: string
          tags: string[] | null
          type: Database["public"]["Enums"]["stop_type"]
          updated_at: string
        }
        Insert: {
          arrived_at?: string | null
          created_at?: string
          deleted_at?: string | null
          departed_at?: string | null
          id: string
          label?: string | null
          latitude?: number | null
          location_id?: string | null
          longitude?: number | null
          sequence_number: number
          shipment_id: string
          tags?: string[] | null
          type?: Database["public"]["Enums"]["stop_type"]
          updated_at: string
        }
        Update: {
          arrived_at?: string | null
          created_at?: string
          deleted_at?: string | null
          departed_at?: string | null
          id?: string
          label?: string | null
          latitude?: number | null
          location_id?: string | null
          longitude?: number | null
          sequence_number?: number
          shipment_id?: string
          tags?: string[] | null
          type?: Database["public"]["Enums"]["stop_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "stops_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "stops_shipment_id_fkey"
            columns: ["shipment_id"]
            isOneToOne: false
            referencedRelation: "shipments"
            referencedColumns: ["id"]
          },
        ]
      }
      trailers: {
        Row: {
          created_at: string
          deleted_at: string | null
          id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          id: string
          updated_at: string
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          id?: string
          updated_at?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          accepted_terms_at: string | null
          avatar: string | null
          created_at: string
          deleted_at: string | null
          email: string
          first_name: string
          id: string
          last_name: string
          onboarded_at: string | null
          phone_number: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
          username: string | null
        }
        Insert: {
          accepted_terms_at?: string | null
          avatar?: string | null
          created_at?: string
          deleted_at?: string | null
          email: string
          first_name: string
          id: string
          last_name: string
          onboarded_at?: string | null
          phone_number: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at: string
          username?: string | null
        }
        Update: {
          accepted_terms_at?: string | null
          avatar?: string | null
          created_at?: string
          deleted_at?: string | null
          email?: string
          first_name?: string
          id?: string
          last_name?: string
          onboarded_at?: string | null
          phone_number?: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      vehicles: {
        Row: {
          created_at: string
          deleted_at: string | null
          id: string
          license_plate: string
          license_state: string
          make: string
          mc_number: string
          model: string
          organization_id: string | null
          updated_at: string
          us_dot: string
          vin: string
          year: number
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          id: string
          license_plate: string
          license_state: string
          make: string
          mc_number: string
          model: string
          organization_id?: string | null
          updated_at: string
          us_dot: string
          vin: string
          year: number
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          id?: string
          license_plate?: string
          license_state?: string
          make?: string
          mc_number?: string
          model?: string
          organization_id?: string | null
          updated_at?: string
          us_dot?: string
          vin?: string
          year?: number
        }
        Relationships: [
          {
            foreignKeyName: "vehicles_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      verifications: {
        Row: {
          created_at: string
          deleted_at: string | null
          driver_id: string | null
          id: string
          latitude: number | null
          longitude: number | null
          notes: string | null
          shipment_id: string | null
          stop_id: string
          updated_at: string
          vehicle_id: string | null
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          driver_id?: string | null
          id: string
          latitude?: number | null
          longitude?: number | null
          notes?: string | null
          shipment_id?: string | null
          stop_id: string
          updated_at: string
          vehicle_id?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          driver_id?: string | null
          id?: string
          latitude?: number | null
          longitude?: number | null
          notes?: string | null
          shipment_id?: string | null
          stop_id?: string
          updated_at?: string
          vehicle_id?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "verifications_driver_id_fkey"
            columns: ["driver_id"]
            isOneToOne: false
            referencedRelation: "drivers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "verifications_shipment_id_fkey"
            columns: ["shipment_id"]
            isOneToOne: false
            referencedRelation: "shipments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "verifications_stop_id_fkey"
            columns: ["stop_id"]
            isOneToOne: false
            referencedRelation: "stops"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "verifications_vehicle_id_fkey"
            columns: ["vehicle_id"]
            isOneToOne: false
            referencedRelation: "vehicles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "verifications_verified_by_fkey"
            columns: ["verified_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      actor_type:
        | "driver"
        | "dispatcher"
        | "customer"
        | "system"
        | "api"
        | "admin"
        | "sensor"
      document_type: "file" | "contract"
      driver_status: "active" | "inactive" | "suspended"
      entity_type:
        | "cargo"
        | "shipment"
        | "driver"
        | "vehicle"
        | "trailer"
        | "location"
        | "customer"
        | "verification"
        | "route"
      event_severity: "info" | "warning" | "error" | "critical"
      event_type:
        | "cargo_created"
        | "cargo_updated"
        | "cargo_assigned_to_shipment"
        | "cargo_removed_from_shipment"
        | "cargo_cancelled"
        | "pickup_scheduled"
        | "pickup_verification_required"
        | "pickup_verification_started"
        | "pickup_verified"
        | "pickup_verification_failed"
        | "pickup_completed"
        | "pickup_exception"
        | "in_transit"
        | "shipment_departed"
        | "shipment_arrived_at_stop"
        | "location_update"
        | "route_deviation"
        | "delivery_scheduled"
        | "delivery_verification_required"
        | "delivery_verification_started"
        | "delivery_verified"
        | "delivery_verification_failed"
        | "delivery_completed"
        | "delivery_exception"
        | "delivery_failed"
        | "shipment_created"
        | "shipment_updated"
        | "route_planned"
        | "route_optimized"
        | "route_changed"
        | "driver_assigned"
        | "driver_changed"
        | "trailer_assigned"
        | "trailer_changed"
        | "shipment_dispatched"
        | "shipment_completed"
        | "shipment_cancelled"
        | "delay_reported"
        | "cargo_damaged"
        | "accident_reported"
        | "breakdown_reported"
        | "weather_delay"
        | "traffic_delay"
        | "security_incident"
        | "system_auto_update"
        | "manual_override"
        | "status_sync"
        | "notification_sent"
      hazmat_class:
        | "none"
        | "class_1_explosives"
        | "class_2_gases"
        | "class_3_flammable_liquids"
        | "class_4_flammable_solids"
        | "class_5_oxidizers"
        | "class_6_toxic_substances"
        | "class_7_radioactive"
        | "class_8_corrosives"
        | "class_9_miscellaneous"
      incident_severity: "low" | "medium" | "high" | "critical"
      incident_status: "reported" | "investigating" | "resolved" | "closed"
      incident_type:
        | "verification"
        | "accident"
        | "delay"
        | "damage"
        | "theft"
        | "weather"
        | "mechanical"
        | "other"
      invitation_status:
        | "pending"
        | "accepted"
        | "revoked"
        | "rejected"
        | "expired"
      load_category:
        | "dry_goods"
        | "liquid_bulk"
        | "dry_bulk"
        | "refrigerated"
        | "frozen"
        | "perishable"
        | "hazmat"
        | "oversized"
        | "heavy_haul"
        | "high_value"
        | "fragile"
        | "automotive"
        | "construction_materials"
        | "retail_consumer"
        | "industrial_equipment"
      load_status:
        | "draft"
        | "pending"
        | "assigned"
        | "packaged"
        | "loaded"
        | "in_transit"
        | "out_for_delivery"
        | "delivered"
        | "exception"
        | "missing"
        | "rejected"
        | "returned"
        | "cancelled"
        | "customs_hold"
      loading_method:
        | "dock_high"
        | "ground_level"
        | "crane_required"
        | "forklift"
        | "hand_load"
        | "pump_load"
        | "gravity_feed"
        | "conveyor"
        | "side_load"
        | "top_load"
      location_sector:
        | "public"
        | "private"
        | "government"
        | "military"
        | "educational"
        | "healthcare"
        | "religious"
      location_type:
        | "billing"
        | "residential"
        | "retail"
        | "commercial"
        | "industrial"
        | "manufacturing"
        | "warehouse"
        | "distribution_center"
        | "port"
        | "rail_terminal"
        | "other"
      members_role: "owner" | "admin" | "billing" | "member" | "viewer"
      membership_status: "pending" | "active" | "inactive" | "declined"
      notification_type:
        | "shipment_update"
        | "incident_report"
        | "system_alert"
        | "payment_update"
      organization_functions:
        | "logistics"
        | "carrier"
        | "warehousing"
        | "distribution"
      organization_status: "pending" | "active" | "suspended" | "inactive"
      organization_type: "individual" | "private" | "non_profit" | "government"
      physical_state:
        | "solid"
        | "liquid"
        | "gas"
        | "bulk_solid"
        | "bulk_liquid"
        | "packaged"
        | "palletized"
        | "loose"
      qualification_status:
        | "pending"
        | "verified"
        | "expired"
        | "revoked"
        | "invalid"
      qualification_type:
        | "commercial_drivers_license"
        | "hazmat_endorsement"
        | "medical_certificate"
        | "defensive_driving_certificate"
        | "tanker_endorsement"
        | "doubles_triples_endorsement"
        | "other"
      security_level:
        | "standard"
        | "high_value"
        | "theft_target"
        | "secured_facility_only"
        | "bonded_carrier_required"
        | "escort_required"
      segregation_level:
        | "none"
        | "same_compartment_ok"
        | "separate_compartments"
        | "separate_trailers"
        | "cannot_ship_together"
      service_level:
        | "standard"
        | "expedited"
        | "overnight"
        | "same_day"
        | "white_glove"
      shipment_mode: "open" | "closed"
      shipment_source: "driver" | "organization" | "system"
      shipment_status:
        | "pending"
        | "scheduled"
        | "assigned"
        | "confirmed"
        | "in_progress"
        | "completed"
        | "cancelled"
      shipment_type: "air" | "ocean" | "ground" | "other"
      stop_type:
        | "pickup"
        | "drop_off"
        | "rest_stop"
        | "gas_station"
        | "maintenance_facility"
        | "customs_facility"
        | "weigh_station"
        | "other"
      temperature_requirement:
        | "ambient"
        | "refrigerated"
        | "frozen"
        | "heated"
        | "temperature_controlled"
        | "dry_ice_required"
      trailer_type:
        | "dry_van"
        | "refrigerated"
        | "flatbed"
        | "step_deck"
        | "lowboy"
        | "tanker"
        | "hopper"
        | "container"
        | "car_carrier"
        | "livestock"
        | "dump"
        | "curtain_side"
        | "conestoga"
        | "specialized"
      user_role: "system" | "admin" | "user"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      actor_type: [
        "driver",
        "dispatcher",
        "customer",
        "system",
        "api",
        "admin",
        "sensor",
      ],
      document_type: ["file", "contract"],
      driver_status: ["active", "inactive", "suspended"],
      entity_type: [
        "cargo",
        "shipment",
        "driver",
        "vehicle",
        "trailer",
        "location",
        "customer",
        "verification",
        "route",
      ],
      event_severity: ["info", "warning", "error", "critical"],
      event_type: [
        "cargo_created",
        "cargo_updated",
        "cargo_assigned_to_shipment",
        "cargo_removed_from_shipment",
        "cargo_cancelled",
        "pickup_scheduled",
        "pickup_verification_required",
        "pickup_verification_started",
        "pickup_verified",
        "pickup_verification_failed",
        "pickup_completed",
        "pickup_exception",
        "in_transit",
        "shipment_departed",
        "shipment_arrived_at_stop",
        "location_update",
        "route_deviation",
        "delivery_scheduled",
        "delivery_verification_required",
        "delivery_verification_started",
        "delivery_verified",
        "delivery_verification_failed",
        "delivery_completed",
        "delivery_exception",
        "delivery_failed",
        "shipment_created",
        "shipment_updated",
        "route_planned",
        "route_optimized",
        "route_changed",
        "driver_assigned",
        "driver_changed",
        "trailer_assigned",
        "trailer_changed",
        "shipment_dispatched",
        "shipment_completed",
        "shipment_cancelled",
        "delay_reported",
        "cargo_damaged",
        "accident_reported",
        "breakdown_reported",
        "weather_delay",
        "traffic_delay",
        "security_incident",
        "system_auto_update",
        "manual_override",
        "status_sync",
        "notification_sent",
      ],
      hazmat_class: [
        "none",
        "class_1_explosives",
        "class_2_gases",
        "class_3_flammable_liquids",
        "class_4_flammable_solids",
        "class_5_oxidizers",
        "class_6_toxic_substances",
        "class_7_radioactive",
        "class_8_corrosives",
        "class_9_miscellaneous",
      ],
      incident_severity: ["low", "medium", "high", "critical"],
      incident_status: ["reported", "investigating", "resolved", "closed"],
      incident_type: [
        "verification",
        "accident",
        "delay",
        "damage",
        "theft",
        "weather",
        "mechanical",
        "other",
      ],
      invitation_status: [
        "pending",
        "accepted",
        "revoked",
        "rejected",
        "expired",
      ],
      load_category: [
        "dry_goods",
        "liquid_bulk",
        "dry_bulk",
        "refrigerated",
        "frozen",
        "perishable",
        "hazmat",
        "oversized",
        "heavy_haul",
        "high_value",
        "fragile",
        "automotive",
        "construction_materials",
        "retail_consumer",
        "industrial_equipment",
      ],
      load_status: [
        "draft",
        "pending",
        "assigned",
        "packaged",
        "loaded",
        "in_transit",
        "out_for_delivery",
        "delivered",
        "exception",
        "missing",
        "rejected",
        "returned",
        "cancelled",
        "customs_hold",
      ],
      loading_method: [
        "dock_high",
        "ground_level",
        "crane_required",
        "forklift",
        "hand_load",
        "pump_load",
        "gravity_feed",
        "conveyor",
        "side_load",
        "top_load",
      ],
      location_sector: [
        "public",
        "private",
        "government",
        "military",
        "educational",
        "healthcare",
        "religious",
      ],
      location_type: [
        "billing",
        "residential",
        "retail",
        "commercial",
        "industrial",
        "manufacturing",
        "warehouse",
        "distribution_center",
        "port",
        "rail_terminal",
        "other",
      ],
      members_role: ["owner", "admin", "billing", "member", "viewer"],
      membership_status: ["pending", "active", "inactive", "declined"],
      notification_type: [
        "shipment_update",
        "incident_report",
        "system_alert",
        "payment_update",
      ],
      organization_functions: [
        "logistics",
        "carrier",
        "warehousing",
        "distribution",
      ],
      organization_status: ["pending", "active", "suspended", "inactive"],
      organization_type: ["individual", "private", "non_profit", "government"],
      physical_state: [
        "solid",
        "liquid",
        "gas",
        "bulk_solid",
        "bulk_liquid",
        "packaged",
        "palletized",
        "loose",
      ],
      qualification_status: [
        "pending",
        "verified",
        "expired",
        "revoked",
        "invalid",
      ],
      qualification_type: [
        "commercial_drivers_license",
        "hazmat_endorsement",
        "medical_certificate",
        "defensive_driving_certificate",
        "tanker_endorsement",
        "doubles_triples_endorsement",
        "other",
      ],
      security_level: [
        "standard",
        "high_value",
        "theft_target",
        "secured_facility_only",
        "bonded_carrier_required",
        "escort_required",
      ],
      segregation_level: [
        "none",
        "same_compartment_ok",
        "separate_compartments",
        "separate_trailers",
        "cannot_ship_together",
      ],
      service_level: [
        "standard",
        "expedited",
        "overnight",
        "same_day",
        "white_glove",
      ],
      shipment_mode: ["open", "closed"],
      shipment_source: ["driver", "organization", "system"],
      shipment_status: [
        "pending",
        "scheduled",
        "assigned",
        "confirmed",
        "in_progress",
        "completed",
        "cancelled",
      ],
      shipment_type: ["air", "ocean", "ground", "other"],
      stop_type: [
        "pickup",
        "drop_off",
        "rest_stop",
        "gas_station",
        "maintenance_facility",
        "customs_facility",
        "weigh_station",
        "other",
      ],
      temperature_requirement: [
        "ambient",
        "refrigerated",
        "frozen",
        "heated",
        "temperature_controlled",
        "dry_ice_required",
      ],
      trailer_type: [
        "dry_van",
        "refrigerated",
        "flatbed",
        "step_deck",
        "lowboy",
        "tanker",
        "hopper",
        "container",
        "car_carrier",
        "livestock",
        "dump",
        "curtain_side",
        "conestoga",
        "specialized",
      ],
      user_role: ["system", "admin", "user"],
    },
  },
} as const
