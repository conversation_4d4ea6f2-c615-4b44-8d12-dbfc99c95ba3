# Supabase Integration

This directory contains the core Supabase client configuration and generated types used throughout the application. It serves as the central point of interaction with our Supabase backend.

> **Quick Start**
>
> - Need to interact with Supa<PERSON>? Import from `@/supabase/client`
> - Need database types? Import from `@/supabase/types`
> - Client is pre-configured with auth and error handling
> - Types are auto-generated from the database schema

> [!NOTE] The Supabase client and types in this directory are fundamental to the application's data layer. Every feature that interacts with the backend depends on these files, making them critical infrastructure for the entire project.

## Directory Contents

```
supabase/
├── client.ts       # Configured Supabase client instance
├── types.ts        # Generated TypeScript types
└── README.md       # This file
```

## Client Usage

The Supabase client (`client.ts`) is pre-configured with:

- Environment-specific settings
- Authentication handling
- Error management
- Type safety

```typescript
import { supabase } from "@/supabase/client";

// Direct usage in API hooks
const { data, error } = await supabase.from("table").select().single();

// Auth operations
const {
  data: { user },
  error,
} = await supabase.auth.getUser();

// Storage operations
const { data, error } = await supabase.storage
  .from("bucket")
  .upload("path", file);
```

## Type System

The type definitions (`types.ts`) provide:

### Database Types

```typescript
import type { Tables, TablesInsert, TablesUpdate } from "@/supabase/types";

// Table row types
type Load = Tables<"loads">;
type Shipment = Tables<"shipments">;

// Insert types
type NewLoad = TablesInsert<"loads">;
type NewShipment = TablesInsert<"shipments">;

// Update types
type LoadUpdate = TablesUpdate<"loads">;
type ShipmentUpdate = TablesUpdate<"shipments">;
```

### Enum Types

```typescript
import type { Enums } from "@/supabase/types";

type LoadType = Enums<"load_type">;
type ShipmentStatus = Enums<"shipment_status">;
```

### Function Types

```typescript
import type { Functions } from "@/supabase/types";

// Edge function payload types
type Payload = Functions<"function_name">["Args"];
type Response = Functions<"function_name">["Returns"];
```

## Best Practices

1. **Client Usage**
   - Always import from `@/supabase/client`
   - Handle errors consistently
   - Use type-safe queries
   - Follow RLS policies

2. **Type Safety**
   - Use generated types for tables
   - Leverage enum types
   - Type function payloads
   - Maintain schema sync

3. **Error Handling**
   - Check for errors in responses
   - Handle auth errors appropriately
   - Provide meaningful feedback
   - Log errors when needed

4. **Performance**
   - Use appropriate select clauses
   - Implement proper filtering
   - Handle large datasets
   - Cache when beneficial

## Common Patterns

### Data Fetching

```typescript
const { data, error } = await supabase
  .from("resources")
  .select(
    `
    id,
    name,
    related:relation(id, name)
  `,
  )
  .eq("status", "active")
  .order("created_at");
```

### Mutations

```typescript
const { data, error } = await supabase
  .from("resources")
  .insert({ name: "New Resource" })
  .select()
  .single();
```

### File Operations

```typescript
const { data, error } = await supabase.storage
  .from("uploads")
  .upload(`${user.id}/file.pdf`, file, {
    contentType: "application/pdf",
  });
```

### Edge Functions

```typescript
const { data, error } = await supabase.functions.invoke("function_name", {
  body: { key: "value" },
});
```

## When to Use

1. **Direct in API Hooks**
   - Data fetching operations
   - Mutations and updates
   - File management
   - Real-time subscriptions

2. **Auth Operations**
   - User authentication
   - Session management
   - Role verification
   - Password operations

3. **Storage Operations**
   - File uploads
   - Asset management
   - Public URLs
   - File removal

4. **Edge Functions**
   - Complex operations
   - External integrations
   - Secure processes
   - Backend tasks
