import { useEffect, useMemo } from "react";
import { useSearchParams } from "react-router";

export function useSearchParamsValue(
  key: string,
  value: string,
  options: {
    auto?: boolean;
    removeOnUnmount?: boolean;
  } = {},
) {
  const [searchParams, setSearchParams] = useSearchParams();

  const { auto = true, removeOnUnmount = false } = options;

  useEffect(() => {
    if (auto) {
      setSearchParams((params) => {
        params.set(key, value);
        return params;
      });
    }

    return () => {
      if (removeOnUnmount) {
        searchParams.delete(key);
      }
    };
  }, [auto, removeOnUnmount, searchParams, setSearchParams, key, value]);

  return useMemo(() => {
    return {
      value: searchParams.get(key),
      set: (nextValue: string) => {
        setSearchParams((params) => {
          params.set(key, nextValue);
          return params;
        });
      },
    };
  }, [key, searchParams, setSearchParams]);
}

export function useSearchParamsDictionary(keys: string[]) {
  const [searchParams] = useSearchParams();
  return useMemo(() => {
    const params = searchParams;
    const entries = Object.fromEntries(
      keys.map((key) => [key, params.get(key)]),
    );
    return entries;
  }, [searchParams, keys]);
}
