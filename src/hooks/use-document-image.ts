import { useEffect, useState } from "react";

/**
 * Hook for loading and caching document images
 * Returns the image as an object URL, with loading and error states
 */
export function useDocumentImage(
  url: string | null,
  contentType?: string | null,
) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  useEffect(() => {
    // Reset states when URL changes
    setLoading(true);
    setError(null);
    setImageUrl(null);

    // Skip if no URL or not an image content type
    if (!url || !contentType?.includes("image")) {
      setLoading(false);
      return;
    }

    console.log("Processing image document:", url);

    let isMounted = true;
    const objectUrlsToRevoke: string[] = [];

    const fetchAndCacheImage = async () => {
      try {
        console.log("Checking for cached image:", url);

        // Check if caches API is available
        if (!("caches" in window)) {
          console.error("Cache API is not available in this browser");
          throw new Error("Cache API not supported");
        }

        // Open the cache
        console.log("Opening cache: 'document-images'");
        const cache = await caches.open("document-images");
        console.log("Cache opened successfully");

        // Check if image is in cache
        console.log("Checking if image exists in cache");
        let response = await cache.match(url);

        if (!response) {
          console.log("Image not in cache, fetching:", url);

          try {
            // Fetch the image
            const networkResponse = await fetch(url);
            console.log("Fetch response:", {
              ok: networkResponse.ok,
              status: networkResponse.status,
              type: networkResponse.type,
              headers: [...networkResponse.headers.entries()],
            });

            // Clone the response to store in cache
            if (networkResponse.ok) {
              console.log("Storing image in cache");
              await cache.put(url, networkResponse.clone());
              response = networkResponse;
              console.log("Image stored in cache successfully");
            } else {
              throw new Error(
                `Failed to fetch image: ${networkResponse.status}`,
              );
            }
          } catch (fetchError) {
            console.error("Network fetch error:", fetchError);
            throw fetchError;
          }
        } else {
          console.log("Using cached image for:", url);
        }

        // Debug response object
        console.log("Response object:", {
          ok: response.ok,
          status: response.status,
          type: response.type,
          headers: [...response.headers.entries()],
        });

        // Create an object URL from the response blob
        console.log("Creating blob from response");
        const blob = await response.blob();
        console.log("Blob created:", {
          size: blob.size,
          type: blob.type,
        });

        const objectUrl = URL.createObjectURL(blob);
        objectUrlsToRevoke.push(objectUrl);
        console.log("Object URL created:", objectUrl);

        if (isMounted) {
          setImageUrl(objectUrl);
          setLoading(false);
        }
      } catch (err) {
        console.error("Error in fetchAndCacheImage:", err);
        // Try a direct fallback approach if caching fails
        try {
          console.log("Attempting direct fetch fallback");
          const directResponse = await fetch(url);
          if (directResponse.ok) {
            const blob = await directResponse.blob();
            const objectUrl = URL.createObjectURL(blob);
            objectUrlsToRevoke.push(objectUrl);
            console.log("Fallback object URL created:", objectUrl);

            if (isMounted) {
              setImageUrl(objectUrl);
              setLoading(false);
            }
            return;
          }
        } catch (fallbackErr) {
          console.error("Fallback fetch also failed:", fallbackErr);
        }

        if (isMounted) {
          setError("Failed to load image");
          setLoading(false);
        }
      }
    };

    fetchAndCacheImage();

    // Cleanup function
    return () => {
      isMounted = false;
      // Revoke any object URLs to prevent memory leaks
      objectUrlsToRevoke.forEach((url) => {
        try {
          console.log("Revoking object URL:", url);
          URL.revokeObjectURL(url);
        } catch (e) {
          console.error("Error revoking URL:", e);
        }
      });
    };
  }, [url, contentType]);

  return { loading, error, imageUrl };
}
