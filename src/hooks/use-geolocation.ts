import { useCallback, useRef, useState } from "react";

interface Location {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

interface GeolocationError {
  code: number;
  message: string;
}

interface GeolocationHook {
  location: Location | null;
  error: GeolocationError | null;
  getCurrentLocation: () => Promise<void>;
  watchLocation: () => void;
  cancelWatch: () => void;
  watching: boolean;
  loading: boolean;
}

export function useGeolocation(): GeolocationHook {
  const [location, setLocation] = useState<Location | null>(null);
  const [error, setError] = useState<GeolocationError | null>(null);
  const [watching, setWatching] = useState(false);
  const [loading, setLoading] = useState(false);
  const watchId = useRef<number | null>(null);

  const handleSuccess = useCallback((position: GeolocationPosition) => {
    const { latitude, longitude, accuracy } = position.coords;
    setLocation({
      latitude,
      longitude,
      accuracy,
      timestamp: position.timestamp,
    });
    setLoading(false);
    setError(null);
  }, []);

  const handleError = useCallback((error: GeolocationPositionError) => {
    setError({
      code: error.code,
      message: error.message,
    });
    setLoading(false);
  }, []);

  const getCurrentLocation = useCallback(async () => {
    if (!navigator.geolocation) {
      setError({
        code: 0,
        message: "Geolocation is not supported by your browser",
      });
      return;
    }

    setLoading(true);

    try {
      const position = await new Promise<GeolocationPosition>(
        (resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          });
        },
      );
      handleSuccess(position);
    } catch (error) {
      handleError(error as GeolocationPositionError);
    }
  }, [handleSuccess, handleError]);

  const watchLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setError({
        code: 0,
        message: "Geolocation is not supported by your browser",
      });
      return;
    }

    setLoading(true);
    setWatching(true);

    watchId.current = navigator.geolocation.watchPosition(
      handleSuccess,
      handleError,
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0,
      },
    );
  }, [handleSuccess, handleError]);

  const cancelWatch = useCallback(() => {
    if (watchId.current) {
      navigator.geolocation.clearWatch(watchId.current);
      watchId.current = null;
      setWatching(false);
    }
  }, []);

  return {
    location,
    error,
    getCurrentLocation,
    watchLocation,
    cancelWatch,
    watching,
    loading,
  };
}
