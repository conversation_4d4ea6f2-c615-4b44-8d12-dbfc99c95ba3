import { useEffect, useState } from "react";

interface DebounceOptions {
  delay?: number;
  immediate?: boolean;
}

export function useDebounceValue<T>(
  defaultValue: T,
  options: DebounceOptions = {},
): [T, (value: T) => void] {
  const [value, setValue] = useState<T>(defaultValue);
  const [debouncedValue, setDebouncedValue] = useState<T>(defaultValue);
  const { delay = 500, immediate = false } = options;

  useEffect(() => {
    if (immediate) {
      setDebouncedValue(value);
      return;
    }

    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay, immediate]);

  return [debouncedValue, setValue];
}
