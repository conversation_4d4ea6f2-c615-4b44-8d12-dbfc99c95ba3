import { useCallback, useRef, useState } from "react";

import { MapboxResponse, searchAddressSuggestions } from "@/lib/mapbox";

export function useAddressAutocomplete() {
  const [suggestions, setSuggestions] = useState<MapboxResponse["data"]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const sessionToken = useRef<string>(`${Date.now()}`);

  const getSuggestions = useCallback(async (query: string) => {
    if (!query || query.length < 3) {
      setSuggestions([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { data, error: apiError } = await searchAddressSuggestions(
        query,
        sessionToken.current,
      );

      if (apiError) {
        throw new Error(apiError);
      }

      if (data) {
        setSuggestions(data);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch suggestions",
      );
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setError(null);
  }, []);

  return {
    suggestions,
    loading,
    error,
    getSuggestions,
    clearSuggestions,
  };
}
