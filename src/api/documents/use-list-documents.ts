import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

type DocumentType = Enums<"document_type">;

interface ListDocumentsParams extends PaginationParams {
  type?: DocumentType;
  organization_id?: string;
  driver_id?: string;
  search?: string;
}

export async function queryFn({
  type,
  organization_id,
  driver_id,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListDocumentsParams = {}) {
  let query = supabase.from("documents").select(
    `*,
      organization:organizations (
        id,
        name,
        type,
        industry,
        size,
        avatar
      ),
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number,
        avatar
      )`,
    { count: "exact" },
  );

  // Text search across relevant fields
  if (search) {
    const searchPattern = `%${search}%`;
    query = query.or(
      `name.ilike.${searchPattern},content_type.ilike.${searchPattern},organization.name.ilike.${searchPattern},driver.first_name.ilike.${searchPattern},driver.last_name.ilike.${searchPattern}`,
    );
  }

  // Apply filters
  if (type) {
    query = query.eq("type", type);
  }

  if (organization_id) {
    query = query.eq("organization_id", organization_id);
  }

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListDocuments(
  params: ListDocumentsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["documents", "list", params],
    queryFn: async () => queryFn(params),
  });
}
