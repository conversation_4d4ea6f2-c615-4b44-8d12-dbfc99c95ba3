import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export interface BuildDocumentInput {
  documentType?:
    | "manifest"
    | "contract"
    | "verification"
    | "general"
    | "bill_of_lading"
    | "other";
  title: string;
  content: string;
  metadata?: {
    shipmentId?: string;
    driverId?: string;
    organizationId?: string;
    createdBy?: string;
    [key: string]: any;
  };
  tableData?: {
    headers: string[];
    rows: string[][];
  };
  templateData?: {
    shipper?: {
      name?: string;
      address?: string;
      contact?: string;
    };
    consignee?: {
      name?: string;
      address?: string;
      contact?: string;
    };
    carrier?: {
      name?: string;
      scac?: string;
      contact?: string;
    };
    shipment?: {
      number?: string;
      date?: string;
      reference?: string;
      specialInstructions?: string;
    };
    items?: Array<{
      description: string;
      weight: string;
      pieces: number;
      packageType: string;
      hazmat: boolean;
    }>;
    signatures?: {
      shipper?: string;
      driver?: string;
      consignee?: string;
    };
    // Add more template types here as needed, mirror the types in the Edge Function
    [key: string]: any;
  };
  format?: "pdf" | "json";
  returnUrl?: boolean;
}

export interface BuildDocumentResult {
  success: boolean;
  document: {
    id: string;
    name: string;
    url: string;
    content_type: string;
    type: string;
  };
  url: string;
  message: string;
}

export async function mutationFn(
  input: BuildDocumentInput,
): Promise<BuildDocumentResult> {
  const { data, error } = await supabase.functions.invoke("build-document", {
    body: input,
  });

  if (error) throw error;
  return data;
}

export function useBuildDocument(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["documents"],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
