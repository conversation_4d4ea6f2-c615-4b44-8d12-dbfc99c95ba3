import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

type EntityType = "organization" | "profile";

interface UpdateAvatarInput {
  file: File;
  entityId: string;
  entityType: EntityType;
}

export async function mutationFn({
  file,
  entityId,
  entityType,
}: UpdateAvatarInput) {
  // Validate file type
  if (!file.type.startsWith("image/")) {
    throw new Error("File must be an image");
  }

  const fileExt = file.name.split(".").pop();
  const filePath = `${entityId}/avatar.${fileExt}`;

  // Upload file to storage
  const { error: uploadError } = await supabase.storage
    .from(entityType === "organization" ? "organizations" : "profiles")
    .upload(filePath, file, { upsert: true });

  if (uploadError) throw uploadError;

  // Get public URL
  const {
    data: { publicUrl },
  } = supabase.storage
    .from(entityType === "organization" ? "organizations" : "profiles")
    .getPublicUrl(filePath);

  // Update entity record with new avatar URL
  const { error: updateError } = await supabase
    .from(entityType === "organization" ? "organizations" : "profiles")
    .update({ avatar: publicUrl })
    .eq("id", entityId);

  if (updateError) throw updateError;

  return publicUrl;
}

export function useUpdateAvatar(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (_, variables, context) => {
      if (variables.entityType === "organization") {
        await queryClient.invalidateQueries({
          queryKey: ["organizations", "get", variables.entityId],
        });
      } else {
        await queryClient.invalidateQueries({
          queryKey: ["profiles", "get", variables.entityId],
        });
      }
      await props.onSuccess?.(_, variables, context);
    },
  });
}
