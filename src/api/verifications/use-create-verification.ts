import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"verifications">) {
  const { data, error } = await supabase
    .from("verifications")
    .insert([input])
    .select(
      `*,
      stop:stops (
        id,
        label,
        type,
        sequence_number
      ),
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number
      ),
      vehicle:vehicles (
        id,
        make,
        model,
        year,
        license_plate
      ),
      document:documents (
        id,
        name,
        url
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreateVerification(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["verifications", "list"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["verifications", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
