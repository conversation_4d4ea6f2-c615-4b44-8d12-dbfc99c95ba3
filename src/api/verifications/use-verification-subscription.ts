import { useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export function useVerificationSubscription(id?: string) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!id) return;

    const channel = supabase
      .channel(`verification:${id}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "verifications",
          filter: `id=eq.${id}`,
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ["verifications", "get", id],
          });
        },
      )
      .subscribe();

    return () => {
      void supabase.removeChannel(channel);
    };
  }, [id, queryClient]);
}
