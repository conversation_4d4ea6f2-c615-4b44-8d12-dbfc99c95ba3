import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface ListVerificationsParams extends PaginationParams {
  stop_id?: string;
  driver_id?: string;
  vehicle_id?: string;
  shipment_id?: string;
  load_id?: string;
  is_verified?: boolean;
  has_document?: boolean;
  search?: string;
}

export async function queryFn({
  stop_id,
  driver_id,
  vehicle_id,
  shipment_id,
  load_id,
  is_verified,
  has_document,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListVerificationsParams = {}) {
  let query = supabase.from("verifications").select(
    `*,
      stop:stops (
        id,
        label,
        type,
        sequence_number,
        location:locations (
          id,
          formatted,
          street,
          city,
          state,
          country,
          latitude,
          longitude
        )
      ),
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number
      ),
      vehicle:vehicles (
        id,
        make,
        model,
        year,
        license_plate
      ),
      document:documents (
        id,
        name,
        url,
        content_type,
        size,
        created_at
      )`,
    { count: "exact" },
  );

  if (stop_id) {
    query = query.eq("stop_id", stop_id);
  }

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (vehicle_id) {
    query = query.eq("vehicle_id", vehicle_id);
  }

  if (shipment_id) {
    query = query.eq("shipment_id", shipment_id);
  }

  if (load_id) {
    query = query.eq("load_id", load_id);
  }

  if (is_verified !== undefined) {
    query = query.not("verified_at", "is", is_verified ? null : "not null");
  }

  if (has_document !== undefined) {
    query = query.not("document_id", "is", has_document ? null : "not null");
  }

  if (search) {
    query = query.or(
      `notes.ilike.%${search}%,driver.first_name.ilike.%${search}%,driver.last_name.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListVerifications(
  params: ListVerificationsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["verifications", "list", params],
    queryFn: async () => queryFn(params),
  });
}
