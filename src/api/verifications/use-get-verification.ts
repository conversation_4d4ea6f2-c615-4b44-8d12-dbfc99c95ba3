import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("verifications")
    .select(
      `*,
      stop:stops (
        id,
        label,
        type,
        sequence_number,
        location:locations (
          id,
          formatted,
          street,
          city,
          state,
          country,
          latitude,
          longitude
        )
      ),
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number
      ),
      vehicle:vehicles (
        id,
        make,
        model,
        year,
        license_plate
      ),
      document:documents (
        id,
        name,
        url,
        content_type,
        size,
        created_at
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetVerification(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["verifications", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
