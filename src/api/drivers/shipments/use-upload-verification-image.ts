import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface UploadVerificationImageInput {
  verification_id: string;
  file: File;
}

export async function mutationFn({
  verification_id,
  file,
}: UploadVerificationImageInput) {
  // Validate file type
  if (!file.type.startsWith("image/")) {
    throw new Error("File must be an image");
  }

  const fileExt = file.name.split(".").pop();
  const filePath = `${verification_id}/image.${fileExt}`;

  // Upload file to storage
  const { error: uploadError } = await supabase.storage
    .from("verifications")
    .upload(filePath, file, { upsert: true });

  if (uploadError) throw uploadError;

  // Get public URL
  const {
    data: { publicUrl },
  } = supabase.storage.from("verifications").getPublicUrl(filePath);

  // Create document record
  const { data, error } = await supabase
    .from("documents")
    .insert([
      {
        name: file.name,
        type: "verification",
        url: publicUrl,
        content_type: file.type,
        size: file.size,
        storage_path: filePath,
      },
    ])
    .select()
    .single();

  if (error) throw error;
  return data;
}

export function useUploadVerificationImage(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: [
          "drivers",
          "shipments",
          "verifications",
          "get",
          variables.verification_id,
        ],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
