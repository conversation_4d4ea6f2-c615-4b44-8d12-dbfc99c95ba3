import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"positions">) {
  const { data, error } = await supabase
    .from("positions")
    .insert([input])
    .select()
    .single();

  if (error) throw error;
  return data;
}

export function useCreatePosition(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
  });
}
