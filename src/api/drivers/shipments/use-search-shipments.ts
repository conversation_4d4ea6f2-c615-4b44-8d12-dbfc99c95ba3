import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface SearchShipmentsParams extends PaginationParams {
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in miles
  };
  weight_range?: {
    min?: number;
    max?: number;
  };
  type?: Enums<"shipment_type">;
  date_range?: {
    start: Date;
    end: Date;
  };
  search?: string;
}

export async function queryFn({
  location,
  weight_range,
  type,
  date_range,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: SearchShipmentsParams = {}) {
  let query = supabase.from("shipments").select(
    `*,
    driver:drivers (
      id,
      first_name,
      last_name,
      avatar
    ),
    organization:organizations (
      id,
      name,
      avatar
    ),
    stops:stops (
      id,
      sequence_number,
      type,
      label,
      arrived_at,
      departed_at,
      location:locations (
        id,
        formatted,
        latitude,
        longitude
      )
    )`,
    { count: "exact" },
  );

  // Filter by location radius
  // if (location) {
  //   const { latitude, longitude, radius } = location;
  //   query = query.rpc("nearby_shipments", {
  //     lat: latitude,
  //     lng: longitude,
  //     radius_miles: radius,
  //   });
  // }

  // Filter by weight range
  if (weight_range) {
    if (weight_range.min !== undefined) {
      query = query.gte("weight", weight_range.min);
    }
    if (weight_range.max !== undefined) {
      query = query.lte("weight", weight_range.max);
    }
  }

  // Filter by load type
  if (type) {
    query = query.eq("type", type);
  }

  // Filter by date range
  if (date_range) {
    query = query
      .gte("created_at", date_range.start.toISOString())
      .lte("created_at", date_range.end.toISOString());
  }

  // Text search
  if (search) {
    query = query.or(
      `organization.name.ilike.%${search}%,stops.label.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
    return [];
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useSearchShipments(
  params: SearchShipmentsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["drivers", "shipments", "search", params],
    queryFn: async () => queryFn(params),
  });
}
