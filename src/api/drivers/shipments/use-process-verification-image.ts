import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";
import { useUploadVerificationImage } from "./use-upload-verification-image";

interface ProcessVerificationImageInput {
  verification_id: string;
  file: File;
  latitude: number;
  longitude: number;
  metadata: {
    device_id?: string;
    timestamp: string;
    accuracy?: number;
    shipping_info?: {
      manifestNumber?: string;
      mcId?: string;
      usdotId?: string;
      licensePlate?: string;
      licenseState?: string;
    };
  };
}

export interface VerificationResult {
  success: boolean;
  verification_id: string;
  verified: boolean;
  confidence_score: number;
  warnings: string[];
  info: string[];
  errors?: string[];
  processing_time: {
    image_processing_ms: number;
    metadata_extraction_ms: number;
    ai_analysis_ms: number;
    exif_analysis_ms: number;
    location_analysis_ms: number;
    total_ms: number;
  };
}

export async function mutationFn({
  verification_id,
  file,
  latitude,
  longitude,
  metadata,
}: ProcessVerificationImageInput): Promise<VerificationResult> {
  // First, upload the image to storage
  const { data: uploadError } = await supabase.storage
    .from("verifications")
    .upload(`${verification_id}/image.${file.name.split(".").pop()}`, file, {
      upsert: true,
    });

  if (uploadError) throw uploadError;

  // Get the public URL of the uploaded image
  const {
    data: { publicUrl },
  } = supabase.storage
    .from("verifications")
    .getPublicUrl(`${verification_id}/image.${file.name.split(".").pop()}`);

  // Now process the verification with the image URL
  const { data, error } = await supabase.functions.invoke(
    "verification-process",
    {
      body: {
        verification_id,
        image_url: publicUrl,
        latitude,
        longitude,
        metadata,
      },
    },
  );

  if (error) throw error;
  return data;
}

export function useProcessVerificationImage(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
  });
}
