import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";
import { useVerificationSubscription } from "./use-verification-subscription";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("verifications")
    .select(
      `*,
      document:documents (
        id,
        name,
        url
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetVerification(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  // Subscribe to real-time updates
  useVerificationSubscription(id);

  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["drivers", "shipments", "verifications", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
