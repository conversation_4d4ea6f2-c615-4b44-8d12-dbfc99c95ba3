import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface AssignShipmentInput {
  id: string;
  driver_id: string;
}

export async function mutationFn({ id, driver_id }: AssignShipmentInput) {
  const { data, error } = await supabase
    .from("shipments")
    .update({ driver_id, status: "assigned" })
    .eq("id", id)
    .select(
      `*,
      driver:drivers (
        id,
        first_name,
        last_name,
        avatar
      ),
      organization:organizations (
        id,
        name,
        avatar
      ),
      stops:stops (
        id,
        sequence_number,
        type,
        label,
        arrived_at,
        departed_at,
        location:locations (
          id,
          formatted,
          latitude,
          longitude
        )
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useAssignShipment(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["drivers", "shipments", "list"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["drivers", "shipments", "search"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["drivers", "shipments", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
