import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { supabase } from "@/supabase/client";

interface ListDriverShipmentsParams extends PaginationParams {
  driver_id: string;
  status?: Enums<"shipment_status">;
  search?: string;
}

export async function queryFn({
  driver_id,
  status,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListDriverShipmentsParams) {
  let query = supabase.from("shipments").select(
    `*,
    driver:drivers (
      id,
      first_name,
      last_name,
      avatar
    ),
    organization:organizations (
      id,
      name,
      avatar
    ),
    stops:stops (
      id,
      sequence_number,
      type,
      label,
      arrived_at,
      departed_at,
      location:locations (
        id,
        formatted,
        latitude,
        longitude
      )
    )`,
    { count: "exact" },
  );

  query = query.eq("driver_id", driver_id);

  if (status) {
    query = query.eq("status", status);
  }

  if (search) {
    query = query.or(
      `organization.name.ilike.%${search}%,stops.label.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) throw error;

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useDriverShipments(
  params: ListDriverShipmentsParams,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["drivers", "shipments", "list", params],
    queryFn: async () => queryFn(params),
  });
}

export async function upcomingShipmentsQueryFn(driverId: string) {
  const { data, error } = await supabase
    .from("shipments")
    .select(
      `
      *,
      stops (
        *,
        location:locations (*)
      ),
      loads (*)
    `,
    )
    .eq("driver_id", driverId)
    .gt("started_at", new Date().toISOString())
    .order("started_at", { ascending: true });

  if (error) throw error;
  return data;
}

export function useUpcomingShipments(driverId?: string) {
  return useQuery<Awaited<ReturnType<typeof upcomingShipmentsQueryFn>>, Error>({
    queryKey: ["driver", driverId, "shipments", "upcoming"],
    queryFn: () => upcomingShipmentsQueryFn(driverId!),
    enabled: !!driverId,
  });
}

export async function pastShipmentsQueryFn(driverId: string) {
  const { data, error } = await supabase
    .from("shipments")
    .select(
      `
      *,
      stops (
        *,
        location:locations (*)
      ),
      loads (*)
    `,
    )
    .eq("driver_id", driverId)
    .not("completed_at", "is", null)
    .order("completed_at", { ascending: false });

  if (error) throw error;
  return data;
}

export function usePastShipments(driverId?: string) {
  return useQuery<Awaited<ReturnType<typeof pastShipmentsQueryFn>>, Error>({
    queryKey: ["driver", driverId, "shipments", "past"],
    queryFn: () => pastShipmentsQueryFn(driverId!),
    enabled: !!driverId,
  });
}
