import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface CompleteVerificationInput {
  id: string;
  document_id: string;
  latitude?: number;
  longitude?: number;
  verificationResults?: {
    verified: boolean;
    confidence_score: number;
    warnings: string[];
    info: string[];
    errors?: string[];
    processing_time: {
      image_processing_ms: number;
      metadata_extraction_ms: number;
      ai_analysis_ms: number;
      exif_analysis_ms: number;
      location_analysis_ms: number;
      total_ms: number;
    };
  };
}

export async function mutationFn({
  id,
  document_id,
  latitude,
  longitude,
  verificationResults,
}: CompleteVerificationInput) {
  // Call the finalize-verification edge function
  const { data, error } = await supabase.functions.invoke(
    "finalize-verification",
    {
      body: {
        verificationId: id,
        shipmentId: id, // In this case, using the same ID for both
        documentId: document_id,
        latitude,
        longitude,
        verificationResults,
      },
    },
  );

  if (error) throw error;
  return data;
}

export function useCompleteVerification(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: [
          "drivers",
          "shipments",
          "verifications",
          "get",
          variables.id,
        ],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
