import { useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export function useVerificationSubscription(verificationId?: string) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!verificationId) return;

    // Subscribe to realtime changes for the specific verification
    const channel = supabase
      .channel("schema-db-changes")
      .on(
        "postgres_changes",
        {
          event: "*", // Listen to all events (INSERT, UPDATE, DELETE)
          schema: "public",
          table: "verifications",
          filter: `id=eq.${verificationId}`,
        },
        (payload) => {
          console.log("Verification updated:", payload);
          // Invalidate the verification query to trigger a refetch
          queryClient.invalidateQueries({
            queryKey: ["verification", verificationId],
          });
        },
      )
      .subscribe();

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(channel);
    };
  }, [verificationId, queryClient]);
}
