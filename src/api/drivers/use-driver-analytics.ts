import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export interface DriverAnalytics {
  totalShipments: number;
  totalMiles: number;
  totalTonnage: number;
  weeklyEarnings: number;
  weeklyMiles: number;
  weeklyTonnage: number;
  routesCompleted: number;
  activeIncidents: number;
  score: number;
}

export async function fetchDriverAnalytics(
  driverId: string,
): Promise<DriverAnalytics> {
  if (!driverId) {
    throw new Error("Driver ID is required");
  }

  // Get all shipments
  const { data: shipments, error: shipmentsError } = await supabase
    .from("shipments")
    .select(
      `
      id,
      distance,
      weight,
      completed_at,
      load_id,
      load:loads (
        weight
      )
    `,
    )
    .eq("driver_id", driverId);

  if (shipmentsError) {
    throw shipmentsError;
  }

  // Get positions to calculate actual distance
  const { data: positions, error: positionsError } = await supabase
    .from("positions")
    .select("latitude, longitude, recorded_at")
    .eq("driver_id", driverId)
    .order("recorded_at", { ascending: true });

  if (positionsError) {
    throw positionsError;
  }

  // Calculate total miles from positions
  let totalMilesFromPositions = 0;
  let weeklyMilesFromPositions = 0;

  if (positions && positions.length > 1) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    for (let i = 1; i < positions.length; i++) {
      const prev = positions[i - 1];
      const curr = positions[i];
      const distance = getDistanceFromLatLonInKm(
        prev.latitude,
        prev.longitude,
        curr.latitude,
        curr.longitude,
      );

      totalMilesFromPositions += distance;

      // Check if position is from the last week
      if (new Date(curr.recorded_at) >= oneWeekAgo) {
        weeklyMilesFromPositions += distance;
      }
    }
  }

  // Convert km to miles
  const kmToMiles = 0.621371;
  totalMilesFromPositions = totalMilesFromPositions * kmToMiles;
  weeklyMilesFromPositions = weeklyMilesFromPositions * kmToMiles;

  // Calculate total distance from shipments as fallback
  const totalMilesFromShipments = shipments
    ? shipments.reduce(
        (sum, shipment) => sum + (Number(shipment.distance) || 0),
        0,
      )
    : 0;

  // Use position-based distance if available, otherwise use shipment distance
  const totalMiles =
    totalMilesFromPositions > 0
      ? Math.round(totalMilesFromPositions)
      : Math.round(totalMilesFromShipments);

  // Calculate weekly miles (last 7 days)
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const weeklyShipments = shipments
    ? shipments.filter(
        (s) => s.completed_at && new Date(s.completed_at) >= oneWeekAgo,
      )
    : [];

  const weeklyMilesFromShipments = weeklyShipments.reduce(
    (sum, shipment) => sum + (Number(shipment.distance) || 0),
    0,
  );

  const weeklyMiles =
    weeklyMilesFromPositions > 0
      ? Math.round(weeklyMilesFromPositions)
      : Math.round(weeklyMilesFromShipments);

  // Calculate tonnage
  const totalTonnage = shipments
    ? shipments.reduce(
        (sum, shipment) =>
          sum + (Number(shipment.load?.weight) || Number(shipment.weight) || 0),
        0,
      )
    : 0;

  const weeklyTonnage = weeklyShipments.reduce(
    (sum, shipment) =>
      sum + (Number(shipment.load?.weight) || Number(shipment.weight) || 0),
    0,
  );

  return {
    routesCompleted: shipments?.length || 0,
    activeIncidents: 0,
    score: 0,
    totalShipments: shipments?.length || 0,
    totalMiles,
    totalTonnage: Math.round(totalTonnage),
    weeklyEarnings: 0, // We'll implement this later
    weeklyMiles,
    weeklyTonnage: Math.round(weeklyTonnage),
  };
}

export function useDriverAnalytics(driverId?: string) {
  return useQuery({
    queryKey: ["driver", driverId, "analytics"],
    queryFn: () => fetchDriverAnalytics(driverId!),
    enabled: !!driverId,
  });
}

// Helper function to calculate distance between two points
function getDistanceFromLatLonInKm(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in km
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}
