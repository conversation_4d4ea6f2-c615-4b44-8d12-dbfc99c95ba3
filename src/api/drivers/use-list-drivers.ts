import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

type DriverStatus = Enums<"driver_status">;

interface ListDriversParams extends PaginationParams {
  status?: DriverStatus;
  search?: string;
}

export async function queryFn({
  status,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListDriversParams = {}) {
  let query = supabase.from("drivers").select(
    `*,
    vehicles (
      id,
      make,
      model,
      year,
      license_plate
    ),
    qualifications (
      id,
      type,
      status,
      expires_at
    )`,
    { count: "exact" },
  );

  if (status) {
    query = query.eq("status", status);
  }

  if (search) {
    query = query.or(
      `first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListDrivers(
  params: ListDriversParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["drivers", "list", params],
    queryFn: async () => queryFn(params),
  });
}
