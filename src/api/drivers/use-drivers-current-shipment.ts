import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { useUser } from "@/contexts/User";
import { supabase } from "@/supabase/client";

export async function queryFn(driverId: string) {
  const { data: shipment, error } = await supabase
    .from("shipments")
    .select(
      `*,
      load:loads(*),
      verifications (
        verified_at
      ),
      stops(*, 
        location:locations(*)
      )`,
    )
    .eq("driver_id", driverId)
    .eq("status", "in_progress")
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return shipment;
}

export function useDriversCurrentShipment(
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn" | "enabled"
  > = {},
) {
  const { user, driver, isLoading } = useUser();

  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    enabled: !!driver && !!user && !isLoading,
    queryKey: ["drivers", "current-shipment", driver?.id],
    queryFn: () => queryFn(driver?.id),
  });
}
