import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DeleteDriverInput {
  id: string;
}

export async function mutationFn({ id }: DeleteDriverInput) {
  const { error } = await supabase.from("drivers").delete().eq("id", id);

  if (error) throw error;
}

export function useDeleteDriver(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["drivers", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["drivers", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
