import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("drivers")
    .select(
      `*,
      vehicles (
        id,
        make,
        model,
        year,
        license_plate
      ),
      qualifications (
        id,
        type,
        status,
        expires_at
      ),
      shipments (
        id,
        completed_at,
        status
      )`,
    )
    .eq("id", id)
    .single();

  if (error) throw error;
  return data;
}

export function useGetDriver(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["drivers", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
