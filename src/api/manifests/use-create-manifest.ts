import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"manifests">) {
  const { data, error } = await supabase
    .from("manifests")
    .insert([input])
    .select(
      `*,
      stop:stops (
        id,
        type,
        label
      ),
      document:documents (
        id,
        name,
        url
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreateManifest(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["manifests", "list"] });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
