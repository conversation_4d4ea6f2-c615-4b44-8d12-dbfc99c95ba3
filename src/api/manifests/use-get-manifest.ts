import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("manifests")
    .select(
      `*,
      stop:stops (
        id,
        sequence_number,
        type,
        label,
        arrived_at,
        departed_at,
        location:locations (
          id,
          formatted,
          street,
          city,
          state,
          country
        )
      ),
      load:loads (
        id,
        label,
        type,
        status,
        perishable,
        weight,
        valuation
      ),
      document:documents (
        id,
        name,
        url,
        content_type,
        size,
        created_at,
        created_by,
        metadata
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetManifest(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["manifests", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
