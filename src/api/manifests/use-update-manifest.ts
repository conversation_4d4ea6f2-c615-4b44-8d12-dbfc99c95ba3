import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesUpdate<"manifests">) {
  const { data, error } = await supabase
    .from("manifests")
    .update(input)
    .eq("id", input.id)
    .select(
      `*,
      stop:stops (
        id,
        type,
        label
      ),
      document:documents (
        id,
        name,
        url
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useUpdateManifest(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["manifests", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["manifests", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
