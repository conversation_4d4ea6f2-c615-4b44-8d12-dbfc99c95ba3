import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface ListManifestsParams extends PaginationParams {
  stop_id?: string;
  load_id?: string;
  type?: Enums<"manifest_type">;
  has_document?: boolean;
  search?: string;
}

export async function queryFn({
  stop_id,
  load_id,
  type,
  has_document,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListManifestsParams = {}) {
  let query = supabase.from("manifests").select(
    `*,
      stop:stops (
        id,
        sequence_number,
        type,
        label,
        location:locations (
          id,
          formatted
        )
      ),
      load:loads (
        id,
        label,
        type,
        status
      ),
      document:documents (
        id,
        name,
        url,
        content_type,
        size,
        created_at
      )`,
    { count: "exact" },
  );

  if (stop_id) {
    query = query.eq("stop_id", stop_id);
  }

  if (load_id) {
    query = query.eq("load_id", load_id);
  }

  if (type) {
    query = query.eq("type", type);
  }

  if (has_document !== undefined) {
    if (has_document) {
      query = query.not("document_id", "is", null);
    } else {
      query = query.is("document_id", null);
    }
  }

  if (search) {
    query = query.or(
      `document.name.ilike.%${search}%,load.label.ilike.%${search}%,stop.label.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListManifests(
  params: ListManifestsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["manifests", "list", params],
    queryFn: async () => queryFn(params),
  });
}
