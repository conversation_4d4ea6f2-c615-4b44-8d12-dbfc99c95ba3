import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DeleteQualificationInput {
  id: string;
  driver_id: string;
}

export async function mutationFn({ id }: DeleteQualificationInput) {
  const { error } = await supabase.from("qualifications").delete().eq("id", id);

  if (error) throw error;
}

export function useDeleteQualification(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: [
          "qualifications",
          "list",
          { driver_id: variables.driver_id },
        ],
      });
      await queryClient.invalidateQueries({
        queryKey: ["qualifications", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
