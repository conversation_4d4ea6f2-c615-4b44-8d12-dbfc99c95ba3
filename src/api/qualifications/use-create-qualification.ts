import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"qualifications">) {
  const { data, error } = await supabase
    .from("qualifications")
    .insert([input])
    .select(
      `*,
      document:documents (
        id,
        name,
        url
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreateQualification(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["qualifications", "list", { driver_id: data.driver_id }],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
