import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesUpdate<"qualifications">) {
  const { data, error } = await supabase
    .from("qualifications")
    .update(input)
    .eq("id", input.id)
    .select(
      `*,
      document:documents (
        id,
        name,
        url
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useUpdateQualification(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["qualifications", "list", { driver_id: data.driver_id }],
      });
      await queryClient.invalidateQueries({
        queryKey: ["qualifications", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
