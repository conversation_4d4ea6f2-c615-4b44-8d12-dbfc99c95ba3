import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

const DEFAULT_PAGE_SIZE = 10;

interface ListQualificationsParams extends PaginationParams {
  driver_id?: string;
  type?: Enums<"qualification_type">;
  status?: Enums<"qualification_status">;
  search?: string;
}

export async function queryFn({
  driver_id,
  type,
  status,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListQualificationsParams = {}) {
  let query = supabase.from("qualifications").select(
    `*,
      document:documents (
        id,
        name,
        url
      )`,
    { count: "exact" },
  );

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (type) {
    query = query.eq("type", type);
  }

  if (status) {
    query = query.eq("status", status);
  }

  if (search) {
    query = query.or(
      `issuing_state.ilike.%${search}%,document.name.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListQualifications(
  params: ListQualificationsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["qualifications", "list", params],
    queryFn: () => queryFn(params),
  });
}
