import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface ListLocationsParams extends PaginationParams {
  type?: Enums<"location_type">;
  organization_id?: string;
  driver_id?: string;
  search?: string;
}

export async function queryFn({
  type,
  organization_id,
  driver_id,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListLocationsParams = {}) {
  let query = supabase.from("locations").select(`*`, { count: "exact" });

  if (type) {
    query = query.eq("type", type);
  }

  if (organization_id) {
    query = query.eq("organization_id", organization_id);
  }

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (search) {
    query = query.or(
      `formatted.ilike.%${search}%,street.ilike.%${search}%,city.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListLocations(
  params: ListLocationsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["locations", "list", params],
    queryFn: async () => queryFn(params),
  });
}
