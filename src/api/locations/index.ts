export {
  queryFn as listLocationsQueryFn,
  useListLocations,
} from "./use-list-locations";
export {
  queryFn as getLocationQueryFn,
  useGetLocation,
} from "./use-get-location";
export {
  mutationFn as createLocationMutationFn,
  useCreateLocation,
} from "./use-create-location";
export {
  mutationFn as updateLocationMutationFn,
  useUpdateLocation,
} from "./use-update-location";
export {
  mutationFn as deleteLocationMutationFn,
  useDeleteLocation,
} from "./use-delete-location";
