import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesUpdate<"organizations">) {
  const { data, error } = await supabase
    .from("organizations")
    .update(input)
    .eq("id", input.id)
    .select(
      `*,
      location:locations (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useUpdateOrganization(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["organizations", "list"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["organizations", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
