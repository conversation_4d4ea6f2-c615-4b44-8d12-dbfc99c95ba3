import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DeleteMemberInput {
  id: string;
  organization_id: string;
}

export async function mutationFn({ id }: DeleteMemberInput) {
  const { error } = await supabase.from("members").delete().eq("id", id);

  if (error) throw error;
}

export function useDeleteMember(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: [
          "organizations",
          "members",
          "list",
          { organization_id: variables.organization_id },
        ],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
