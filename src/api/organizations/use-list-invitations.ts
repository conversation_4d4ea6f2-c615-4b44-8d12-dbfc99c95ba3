import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { supabase } from "@/supabase/client";
import { validate } from "../utils";

interface ListInvitationsParams extends PaginationParams {
  organization_id: string;
  status?: Enums<"invitation_status">;
  role?: Enums<"members_role">;
  search?: string;
}

export async function queryFn({
  organization_id,
  status,
  role,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListInvitationsParams) {
  let query = supabase.from("invitations").select(
    `*,
    organization:organizations (
      id,
      name,
      industry,
      type,
      avatar
    )`,
    { count: "exact" },
  );

  query = query.eq("organization_id", organization_id);

  if (status) {
    query = query.eq("status", status);
  }

  if (role) {
    query = query.eq("role", role);
  }

  if (search) {
    query = query.ilike("email", `%${search}%`);
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
    return [];
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListInvitations(
  params: ListInvitationsParams,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["organizations", "invitations", "list", params],
    queryFn: async () => queryFn(params),
  });
}
