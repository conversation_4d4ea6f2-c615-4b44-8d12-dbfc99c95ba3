import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface ListOrganizationsParams extends PaginationParams {
  status?: Enums<"organization_status">;
  type?: Enums<"organization_type">;
  search?: string;
}

export async function queryFn({
  status,
  type,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListOrganizationsParams = {}) {
  let query = supabase.from("organizations").select(
    `*,
    location:locations (
      id,
      formatted,
      street,
      city,
      state,
      country,
      latitude,
      longitude
    ),
    members:organization_members (
      id,
      role,
      created_at,
      user:users (
        id,
        email,
        first_name,
        last_name,
        avatar
      )
    ),
    invitations:organization_invitations (
      id,
      email,
      role,
      status,
      created_at,
      expires_at
    )`,
    { count: "exact" },
  );

  if (status) {
    query = query.eq("status", status);
  }

  if (type) {
    query = query.eq("type", type);
  }

  if (search) {
    query = query.or(
      `name.ilike.%${search}%,industry.ilike.%${search}%,contact_email.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListOrganizations(
  params: ListOrganizationsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["organizations", "list", params],
    queryFn: async () => queryFn(params),
  });
}
