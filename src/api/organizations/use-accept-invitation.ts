import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface AcceptInvitationInput {
  id: string;
  organization_id: string;
}

export async function mutationFn({ id }: AcceptInvitationInput) {
  const { data, error } = await supabase
    .from("invitations")
    .update({ status: "accepted" })
    .eq("id", id)
    .select(
      `*,
      organization:organizations (
        id,
        name,
        industry,
        type,
        avatar
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useAcceptInvitation(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: [
          "organizations",
          "invitations",
          "list",
          { organization_id: variables.organization_id },
        ],
      });
      await queryClient.invalidateQueries({
        queryKey: ["organizations", "members", "list"],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
