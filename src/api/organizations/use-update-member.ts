import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesUpdate<"members">) {
  const { data, error } = await supabase
    .from("members")
    .update(input)
    .eq("id", input.id)
    .select(
      `*,
      user:users (
        id,
        email,
        first_name,
        last_name,
        avatar
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useUpdateMember(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: [
          "organizations",
          "members",
          "list",
          { organization_id: data.organization_id },
        ],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
