import {
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export async function mutationFn({ id }: { id: string }) {
  const { data: user } = await supabase.auth.getUser();
  if (!user.user) throw new Error("Not authenticated");

  const { error } = await supabase
    .from("members")
    .delete()
    .eq("organization_id", id)
    .eq("user_id", user.user.id);

  if (error) throw error;
}

export function useLeaveOrganization(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (_, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["members", "list", { organization_id: variables.id }],
      });
      await props.onSuccess?.(_, variables, context);
    },
  });
}
