import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"invitations">) {
  const { data, error } = await supabase
    .from("invitations")
    .insert([input])
    .select(
      `*,
      organization:organizations (
        id,
        name,
        industry,
        type,
        avatar
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreateInvitation(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: [
          "organizations",
          "invitations",
          "list",
          { organization_id: data.organization_id },
        ],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
