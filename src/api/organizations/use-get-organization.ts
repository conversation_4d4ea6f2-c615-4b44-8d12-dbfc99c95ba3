import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";
import { validate } from "../utils";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("organizations")
    .select(
      `*,
      location:locations (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude
      ),
      members:organization_members (
        id,
        role,
        created_at,
        user:users (
          id,
          email,
          first_name,
          last_name,
          avatar
        )
      ),
      invitations:organization_invitations (
        id,
        email,
        role,
        status,
        created_at,
        expires_at
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetOrganization(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["organizations", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
