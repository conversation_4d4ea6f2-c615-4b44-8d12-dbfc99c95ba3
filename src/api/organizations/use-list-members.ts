import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { supabase } from "@/supabase/client";
import { validate } from "../utils";

interface ListMembersParams extends PaginationParams {
  organization_id: string;
  role?: Enums<"members_role">;
  search?: string;
}

export async function queryFn({
  organization_id,
  role,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListMembersParams) {
  let query = supabase.from("members").select(
    `*,
    user:users (
      id,
      email,
      first_name,
      last_name,
      avatar
    )
    `,
    { count: "exact" },
  );

  query = query.eq("organization_id", organization_id);

  if (role) {
    query = query.eq("role", role);
  }

  if (search) {
    query = query.or(
      `user.first_name.ilike.%${search}%,user.last_name.ilike.%${search}%,user.email.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
    return [];
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListMembers(
  params: ListMembersParams,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["organizations", "members", "list", params],
    queryFn: async () => queryFn(params),
  });
}
