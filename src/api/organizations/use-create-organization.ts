import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"organizations">) {
  const { data, error } = await supabase
    .from("organizations")
    .insert([input])
    .select(
      `*,
      location:locations!organizations_location_id_fkey (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreateOrganization(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      TablesInsert<"organizations">
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    TablesInsert<"organizations">
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["organizations", "list"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["organizations", "get", data.id],
      });

      // Also invalidate user memberships to refresh the organization list
      await queryClient.invalidateQueries({
        queryKey: ["user"],
      });

      await props.onSuccess?.(data, variables, context);
    },
  });
}
