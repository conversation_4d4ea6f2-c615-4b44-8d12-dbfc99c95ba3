import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface ListVehiclesParams extends PaginationParams {
  driver_id?: string;
  search?: string;
}

export async function queryFn({
  driver_id,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListVehiclesParams = {}) {
  let query = supabase.from("vehicles").select(
    `*,
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number
      )`,
    { count: "exact" },
  );

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (search) {
    query = query.or(
      `make.ilike.%${search}%,model.ilike.%${search}%,license_plate.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListVehicles(
  params: ListVehiclesParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["vehicles", "list", params],
    queryFn: async () => queryFn(params),
  });
}
