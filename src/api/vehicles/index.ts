export {
  queryFn as listVehiclesQueryFn,
  useListVehicles,
} from "./use-list-vehicles";
export { queryFn as getVehicleQueryFn, useGetVehicle } from "./use-get-vehicle";
export {
  mutationFn as createVehicleMutationFn,
  useCreateVehicle,
} from "./use-create-vehicle";
export {
  mutationFn as updateVehicleMutationFn,
  useUpdateVehicle,
} from "./use-update-vehicle";
export {
  mutationFn as deleteVehicleMutationFn,
  useDeleteVehicle,
} from "./use-delete-vehicle";
