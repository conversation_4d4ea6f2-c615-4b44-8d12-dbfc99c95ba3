import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("incidents")
    .select(
      `*,
      shipment:shipments (
        id,
        status,
        mode,
        started_at,
        completed_at
      ),
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number
      ),
      stop:stops (
        id,
        label,
        type,
        sequence_number
      ),
      load:loads (
        id,
        label,
        type,
        status
      ),
      verification:verifications (
        id,
        verified_at,
        verified_by,
        notes
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetIncident(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["incidents", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
