import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"incidents">) {
  const { data, error } = await supabase
    .from("incidents")
    .insert([input])
    .select(
      `*,
      shipment:shipments (
        id,
        status
      ),
      driver:drivers (
        id,
        first_name,
        last_name
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreateIncident(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["incidents", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["incidents", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
