import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface ListIncidentsParams extends PaginationParams {
  status?: Enums<"incident_status">;
  type?: Enums<"incident_type">;
  severity?: Enums<"incident_severity">;
  shipment_id?: string;
  driver_id?: string;
  search?: string;
}

export async function queryFn({
  status,
  type,
  severity,
  shipment_id,
  driver_id,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListIncidentsParams = {}) {
  let query = supabase.from("incidents").select(
    `*,
      shipment:shipments (
        id,
        status
      ),
      driver:drivers (
        id,
        first_name,
        last_name
      ),
      stop:stops (
        id,
        label
      ),
      load:loads (
        id,
        label
      ),
      verification:verifications (
        id,
        verified_at
      )`,
    { count: "exact" },
  );

  if (status) {
    query = query.eq("status", status);
  }

  if (type) {
    query = query.eq("type", type);
  }

  if (severity) {
    query = query.eq("severity", severity);
  }

  if (shipment_id) {
    query = query.eq("shipment_id", shipment_id);
  }

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (search) {
    query = query.ilike("title", `%${search}%`);
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListIncidents(
  params: ListIncidentsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["incidents", "list", params],
    queryFn: async () => queryFn(params),
  });
}
