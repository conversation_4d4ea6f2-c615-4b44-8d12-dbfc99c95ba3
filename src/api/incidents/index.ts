export {
  queryFn as listIncidentsQueryFn,
  useListIncidents,
} from "./use-list-incidents";
export {
  queryFn as getIncidentQueryFn,
  useGetIncident,
} from "./use-get-incident";
export {
  mutationFn as createIncidentMutationFn,
  useCreateIncident,
} from "./use-create-incident";
export {
  mutationFn as updateIncidentMutationFn,
  useUpdateIncident,
} from "./use-update-incident";
export {
  mutationFn as deleteIncidentMutationFn,
  useDeleteIncident,
} from "./use-delete-incident";
