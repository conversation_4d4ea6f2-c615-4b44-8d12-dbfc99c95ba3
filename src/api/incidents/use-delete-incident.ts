import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DeleteIncidentInput {
  id: string;
}

export async function mutationFn({ id }: DeleteIncidentInput) {
  const { error } = await supabase.from("incidents").delete().eq("id", id);

  if (error) throw error;
}

export function useDeleteIncident(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["incidents", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["incidents", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
