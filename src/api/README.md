# API Layer

This directory contains our data fetching layer using React Query to interface with Supabase. The API layer provides a clean, type-safe way to interact with our backend data while handling caching, loading states, and error management.

> [!NOTE] > **Knowledge Prompt:**
> "The API layer uses React Query + Supabase. Queries are organized by domain (console, drivers, user). All query hooks follow the `use-` prefix convention, handle errors appropriately, and return `{ data, error, isLoading }`. Mutations should invalidate relevant queries on success. The layer handles database queries, edge functions, and Postgres functions through Supabase. The API files are located at "~/src/api/\*_/_.ts".

> [!IMPORTANT]
> The Supabase client is imported from `@/supabase/client`:
>
> ```typescript
> import { supabase } from "@/supabase/client";
> ```
>
> These should only be used for input parameters to queries and mutations.
>
> All Supabase types are imported from `@/supabase/types`:
>
> ```typescript
> import type {
>   Enums,
>   Tables,
>   TablesInsert,
>   TablesUpdate,
> } from "@/supabase/types";
>
> // Usage examples:
> type Contact = Tables<"contacts">; // Table type
> type NewContact = TablesInsert<"contacts">; // Insert type
> type ContactUpdate = TablesUpdate<"contacts">; // Update type
> type ContactType = Enums<"contact_type">; // Enum type
> ```
>
> For dynamic selections (in queries and mutations), let TypeScript infer the return type:
>
> ```typescript
> // Let TypeScript infer complex selection types
> const { data } = await supabase
>   .from("contacts")
>   .select(
>     `
>     id,
>     email,
>     organization:organizations (
>       id,
>       name
>     )
>   `,
>   )
>   .returns<{
>     // No need to specify - TypeScript will infer!
>     id: string;
>     email: string;
>     organization: {
>       id: string;
>       name: string;
>     };
>   }>();
>
> // Instead, use type inference with 'typeof'
> type ContactWithOrg = (typeof data)[number]; // Inferred type from selection
> ```

## Directory Structure

```
api/
├── client.ts            # React Query client configuration
├── provider.tsx         # React Query provider component
├── index.ts             # API exports and namespace organization
├── console/             # Console-specific queries
├── drivers/             # Driver-specific queries
├── locations/           # Location-specific queries
├── organizations/       # Organization-specific queries
├── shipments/           # Shipment-specific queries
├── user/                # User-related queries
```

## Conventions

### File Naming

- Query hook files should be named with kebab-case
- All query hook files should be prefixed with `use-`
- File name should match the hook name
- Examples:
  - `use-active-shipments.ts` → `useActiveShipments`
  - `use-analytics-snapshot.ts` → `useAnalyticsSnapshot`
  - `use-dashboard-metrics.ts` → `useDashboardMetrics`

### Hook Naming

- All query hooks should be prefixed with `use`
- Names should clearly indicate the data being fetched
- Example: `useActiveShipments`, `useAnalyticsSnapshot`

### Query Structure

Each query hook should follow this pattern:

```typescript
// Define the query function outside the hook for better type inference and reusability
export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("table_name")
    .select(
      `
      field1,
      field2,
      relation1 (
        nested_field1,
        nested_field2
      )
    `,
    )
    .eq("id", id)
    .single();

  if (error) throw error;
  return data;
}

export function useQueryName(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["namespace", "operation-name", id],
    queryFn: () => queryFn(id),
  });
}
```

Key conventions for queries:

- Extract query logic into a separate `queryFn` function
- Export `queryFn` to allow reuse outside of hooks
- Use TypeScript's `Awaited<ReturnType<typeof queryFn>>` for precise type inference
- Keep the query function focused on data fetching
- Handle errors consistently by throwing them

### List Query Pattern

List queries follow a standardized pattern for consistency:

```typescript
interface ListResourceParams extends PaginationParams {
  // Resource-specific filters with proper types
  type?: Enums<"resource_type">;
  organization_id?: string;
  search?: string;
}

export async function queryFn({
  // Resource-specific params first
  type,
  organization_id,
  // Standard pagination params last
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListResourceParams = {}) {
  let query = supabase.from("resources").select(
    `*,
      organization:organizations (
        id,
        name
      )`,
    { count: "exact" },
  );

  // Apply filters
  if (type) {
    query = query.eq("type", type);
  }

  if (organization_id) {
    query = query.eq("organization_id", organization_id);
  }

  // Apply search if provided
  if (search) {
    query = query.or(`field1.ilike.%${search}%,field2.ilike.%${search}%`);
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) throw error;

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListResources(
  params: ListResourceParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["resources", "list", params],
    queryFn: async () => queryFn(params),
  });
}
```

### Mutations

Mutations follow a similar pattern with the mutation function extracted:

```typescript
export async function mutationFn(input: TablesInsert<"resource">) {
  const { data, error } = await supabase
    .from("resource")
    .insert([input])
    .select()
    .single();

  if (error) throw error;
  return data;
}

export function useCreateResource(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["resources", "list"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["resources", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}

// For delete operations:
interface DeleteResourceInput {
  id: string;
}

export async function mutationFn({ id }: DeleteResourceInput) {
  const { error } = await supabase.from("resource").delete().eq("id", id);

  if (error) throw error;
}

export function useDeleteResource(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["resources", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["resources", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
```

Key mutation conventions:

- Extract mutation logic into a separate `mutationFn` function
- Export `mutationFn` to allow reuse outside of hooks
- Use TypeScript's utility types for precise type inference:
  - `Awaited<ReturnType<typeof mutationFn>>` for return type
  - `Parameters<typeof mutationFn>[0]` for input type
- Keep mutation function focused on data modification
- Handle errors consistently by throwing them
- Use proper cache invalidation in `onSuccess`

### Query Keys

Query keys follow a strict pattern for cache management:

```typescript
// List queries
["resources", "list", { filters, search, pagination }][
  // Get single resource
  ("resources", "get", id)
][
  // Get resource with relations
  ("resources", "get", id, { with: ["relation1", "relation2"] })
][
  // Nested resource lists
  ("organizations", "resources", "list", orgId, { type, status })
][
  // Nested resource single item
  ("organizations", "resources", "get", orgId, resourceId)
][
  // Domain specific operations
  ("console", "metrics", "daily")
][("console", "metrics", "monthly", { year: 2024 })];
```

Key conventions for query keys:

- Use array syntax for all keys
- First element is always the resource/domain name in plural form
- Second element is the operation type:
  - `"list"` for fetching multiple items
  - `"get"` for fetching single items
  - Custom operation names for specific domain operations
- Additional elements follow this order:
  1. Operation type (`"list"`, `"get"`, etc.)
  2. Required IDs (resource ID, parent ID, etc.)
  3. Options object (filters, search, pagination) as last element
- For nested resources:
  1. Parent resource name
  2. Child resource name
  3. Operation type
  4. Parent ID
  5. Child ID (if applicable)
  6. Options object (if applicable)
- Keep keys as minimal as possible while ensuring cache separation

### Error Handling

- Always check for Supabase errors and throw them if present
- Let React Query handle error states
- Return error state from the hook for component-level handling

### Type Safety

- Use Supabase generated types from `@/supabase/types`:
  - `Tables<"resource">` for base table types
  - `TablesInsert<"resource">` for insert operations
  - `TablesUpdate<"resource">` for update operations
  - `Enums<"enum_name">` for enum types
- Avoid creating duplicate types for database entities
- Export interface types only for:
  - Query parameters (e.g., `ListResourceParams`)
  - Mutation inputs (e.g., `DeleteResourceInput`)
  - Response transformations
- Use TypeScript for all query hooks
- Leverage type inference with `satisfies` keyword where appropriate

### Special Note on User Queries

The user subscription system (`useUserSubscription`) is a special case that:

- Manages authentication state via Supabase Auth
- Handles real-time session updates
- Fetches associated user data:
  - Driver profile (if exists)
  - Organization memberships
  - User permissions

This subscription system serves as the foundation for user-specific queries and should be accessed through the UserContext rather than directly.

```typescript
// Example of user-aware query
export function useProtectedResource() {
  const { user, isLoading } = useUser(); // Access via context

  return useQuery({
    enabled: !!user && !isLoading, // Only run when user is available
    queryKey: ["protected-resource", user?.id],
    queryFn: async () => {
      // Query implementation
    },
  });
}
```

### Exports

All queries are exported through the `api` object in `index.ts`, organized by namespace:

```typescript
export const api = {
  drivers: {
    currentShipment: useDriversCurrentShipment,
  },
  console: {
    activeShipments: useActiveShipments,
    analyticsSnapshot: useAnalyticsSnapshot,
    // ...
  },
};
```

## Usage Example

```typescript
import { activeShipments } from "@/api/console/use-active-shipments";

function MyComponent() {
  const { data, error, isLoading } = activeShipments({
    onSuccess: (data) => {
      console.log(data);
    },
  });

  if (isLoading) return <Loading />;
  if (error) return <Error message={error.message} />;

  return (
    <div>
      {/* Render data */}
    </div>
  );
}
```

## Benefits

- **Centralized Data Fetching**: All API calls are organized in one place
- **Caching**: React Query handles caching automatically
- **Real-time Updates**: Easy integration with Supabase real-time features
- **Type Safety**: Full TypeScript support with Supabase generated types
- **Error Handling**: Consistent error handling patterns
- **Loading States**: Built-in loading state management

### Serverless Functions

The API layer also handles calls to Supabase Edge Functions and Postgres Functions:

```typescript
interface ProcessShipmentInput {
  shipmentId: string;
  options?: {
    priority?: boolean;
    notify?: boolean;
  };
}

// Edge Function Example
export function useProcessShipment(
  props: Omit<
    UseMutationOptions<void, Error, ProcessShipmentInput>,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<void, Error, ProcessShipmentInput>({
    ...props,
    mutationFn: async (input: ProcessShipmentInput) => {
      const { error } = await supabase.functions.invoke("process-shipment", {
        body: input,
      });

      if (error) throw error;
    },
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["shipment", variables.shipmentId],
      });
      await props.onSuccess?.(data, variables, context);
    },
    onError: async (error, variables, context) => {
      await props.onError?.(error, variables, context);
    },
  });
}

// Postgres Function Example
export function useCalculateRouteMetrics(
  routeId: string,
  props: Omit<
    UseQueryOptions<RouteMetrics, Error, RouteMetrics>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<RouteMetrics, Error>({
    ...props,
    queryKey: ["route-metrics", routeId],
    queryFn: async () => {
      const { data, error } = await supabase.rpc("calculate_route_metrics", {
        route_id: routeId,
      });

      if (error) throw error;
      return data;
    },
  });
}
```

Key conventions for serverless functions:

- Edge Function Mutations:
  - Follow standard mutation pattern with props
  - Use properly typed input interfaces
  - Keep callbacks focused on cache management
  - Use async/await for callbacks
- Postgres Function Queries:
  - Follow standard query pattern with props
  - Use proper return type inference
  - Follow query key conventions
  - Handle errors consistently
- Both should:
  - Follow the same error handling pattern
  - Be called through the API layer
  - Update relevant cache on success
