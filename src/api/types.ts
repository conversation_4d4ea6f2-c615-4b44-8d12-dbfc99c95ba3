export interface PaginationParams {
  pageIndex?: number;
  pageSize?: number;
}

export interface PaginationMetadata {
  pageIndex: number;
  pageSize: number;
  totalPages: number;
  total: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
}

export interface ListQueryParams extends PaginationParams {
  query?: string;
}

export interface UseListQueryResult<T> {
  items: T[];
  total: number;
  isLoading: boolean;
  error: Error | null;
}
