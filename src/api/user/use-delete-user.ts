import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export async function mutationFn(id?: string) {
  const { error } = await supabase.auth.admin.deleteUser(
    id ?? (await supabase.auth.getUser()).data.user?.id,
  );

  if (error) throw error;
}

export function useDeleteUser(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
  });
}
