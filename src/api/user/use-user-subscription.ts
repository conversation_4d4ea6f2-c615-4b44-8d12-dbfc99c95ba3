import type { Session, User } from "@supabase/supabase-js";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { supabase } from "@/supabase/client";
import { Tables } from "@/supabase/types";

export type { Session, User };
export type DriverProfile = Tables<"drivers">;
export type Member = Tables<"members"> & {
  organization: Pick<
    Tables<"organizations">,
    "id" | "name" | "industry" | "size"
  >;
};

async function getDriverProfile(id: string) {
  try {
    const { data, error } = await supabase
      .from("drivers")
      .select("*")
      .eq("user_id", id)
      .single();

    return { data, error };
  } catch (err) {
    console.error("Error in getDriverProfile:", err);
    return { data: null, error: err };
  }
}

async function getMemberships(email: string) {
  try {
    const { data, error } = await supabase
      .from("members")
      .select(
        `*,
        organization:organizations (
          id,
          name,
          industry,
          size
        )`,
      )
      .eq("email", email);

    return { data, error };
  } catch (err) {
    console.error("Error in getMemberships:", err);
    return { data: [], error: err };
  }
}

export function useUserSubscription() {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [driver, setDriver] = useState<DriverProfile | null>(null);
  const [memberships, setMemberships] = useState<Member[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDriverLoading, setIsDriverLoading] = useState(false);
  const [isMembershipsLoading, setIsMembershipsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs to prevent multiple API calls
  const driverLoadedRef = useRef(false);
  const membershipsLoadedRef = useRef(false);
  const driverLoadingRef = useRef(false);
  const membershipsLoadingRef = useRef(false);

  // Memoized lazy load driver profile
  const loadDriverProfile = useCallback(async (userId: string) => {
    if (driverLoadingRef.current || driverLoadedRef.current) return;

    driverLoadingRef.current = true;
    setIsDriverLoading(true);
    try {
      const { data: driverData, error: driverError } =
        await getDriverProfile(userId);

      if (driverError && driverError.code !== "PGRST116") {
        console.error("Error fetching driver profile:", driverError);
      }

      setDriver(driverData);
      driverLoadedRef.current = true;
    } catch (err) {
      console.error("Driver profile fetch failed:", err);
      setDriver(null);
    } finally {
      setIsDriverLoading(false);
      driverLoadingRef.current = false;
    }
  }, []);

  // Memoized lazy load memberships
  const loadMemberships = useCallback(async (email: string) => {
    if (membershipsLoadingRef.current || membershipsLoadedRef.current) return;

    membershipsLoadingRef.current = true;
    setIsMembershipsLoading(true);
    try {
      const { data: membershipData, error: membershipError } =
        await getMemberships(email);

      if (membershipError) {
        console.error("Error fetching memberships:", membershipError);
      }

      setMemberships(membershipData ?? []);
      membershipsLoadedRef.current = true;
    } catch (err) {
      console.error("Memberships fetch failed:", err);
      setMemberships([]);
    } finally {
      setIsMembershipsLoading(false);
      membershipsLoadingRef.current = false;
    }
  }, []);

  useEffect(() => {
    let isMounted = true;
    let sessionTimeout: NodeJS.Timeout;

    const setSessionData = async (session: Session | null) => {
      if (!isMounted) return;

      // Fast timeout for session data only (3 seconds max)
      sessionTimeout = setTimeout(() => {
        if (isMounted) {
          console.warn("Session loading timed out, proceeding anyway");
          setIsLoading(false);
        }
      }, 3000);

      try {
        if (session) {
          setSession(session);
          setUser(session.user ?? null);
          setError(null);

          // Set loading to false immediately after session/user data is available
          setIsLoading(false);
          clearTimeout(sessionTimeout);

          // Reset refs for new session to allow fresh data loading
          driverLoadedRef.current = false;
          membershipsLoadedRef.current = false;

          // Lazy load additional data in background if user exists
          if (session.user) {
            // Load driver profile (function has its own guards)
            loadDriverProfile(session.user.id);

            // Load memberships if email exists (function has its own guards)
            if (session.user.email) {
              loadMemberships(session.user.email);
            }
          }
        } else {
          // No session - clear all data and stop loading
          if (isMounted) {
            setSession(null);
            setUser(null);
            setDriver(null);
            setMemberships([]);
            setError(null);
            setIsLoading(false);
            clearTimeout(sessionTimeout);

            // Reset refs so data can be loaded for next user
            driverLoadedRef.current = false;
            membershipsLoadedRef.current = false;
            driverLoadingRef.current = false;
            membershipsLoadingRef.current = false;
          }
        }
      } catch (err) {
        console.error("Unexpected error in setSessionData:", err);
        if (isMounted) {
          setError("An unexpected error occurred while loading session");
          setIsLoading(false);
          clearTimeout(sessionTimeout);
        }
      }
    };

    // Get initial session
    const initializeSession = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        await setSessionData(data.session);
      } catch (error) {
        console.error("Error getting session:", error);
        if (isMounted) {
          setError("Failed to initialize session");
          setIsLoading(false);
        }
      }
    };

    initializeSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, session) => {
      try {
        await setSessionData(session);
      } catch (error) {
        console.error("Error handling auth state change:", error);
        if (isMounted) {
          setError("Error handling authentication change");
          setIsLoading(false);
        }
      }
    });

    return () => {
      isMounted = false;
      clearTimeout(sessionTimeout);
      subscription.unsubscribe();
    };
  }, []); // Empty dependency array - only run once on mount

  return useMemo(
    () => ({
      data: {
        user,
        driver,
        memberships,
        session,
      },
      isLoading, // Main loading state (session/user only)
      isDriverLoading, // Separate loading state for driver data
      isMembershipsLoading, // Separate loading state for memberships
      error,
    }),
    [
      user,
      driver,
      memberships,
      session,
      isLoading,
      isDriverLoading,
      isMembershipsLoading,
      error,
    ],
  );
}
