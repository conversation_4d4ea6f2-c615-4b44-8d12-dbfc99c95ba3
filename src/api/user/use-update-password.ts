import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface UpdatePasswordInput {
  password: string;
}

export async function mutationFn({ password }: UpdatePasswordInput) {
  const { error } = await supabase.auth.updateUser({
    password,
  });

  if (error) throw error;
}

export function useUpdatePassword(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
  });
}
