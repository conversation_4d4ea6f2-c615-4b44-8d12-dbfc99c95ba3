import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";
import { validate } from "../utils";

export async function queryFn() {
  const { data, error } = await supabase
    .from("notifications")
    .select("*")
    .eq("read_at", null)
    .order("created_at", { ascending: false });

  if (error) {
    validate(error, { allowNotFound: true });
    return [];
  }
  return data || [];
}

export function useListUnreadNotifications(
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["user", "notifications", "unread"],
    queryFn,
  });
}
