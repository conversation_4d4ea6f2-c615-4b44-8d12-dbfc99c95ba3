import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export async function mutationFn({ scope }: { scope?: "global" } = {}) {
  const { error } = await supabase.auth.signOut({ scope });
  if (error) throw error;
}

export function useLogout(
  props: Omit<
    UseMutationOptions<void, Error, { scope?: "global" } | undefined>,
    "mutationFn"
  > = {},
) {
  return useMutation<void, Error, { scope?: "global" } | undefined>({
    ...props,
    mutationFn,
  });
}
