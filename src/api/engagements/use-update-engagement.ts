import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

interface UpdateEngagementInput extends TablesUpdate<"engagements"> {
  notes?: string;
  isDriver: boolean;
}

export async function mutationFn({
  notes,
  isDriver,
  ...input
}: UpdateEngagementInput) {
  const notesField = isDriver ? "driver_notes" : "organization_notes";

  const { data, error } = await supabase
    .from("engagements")
    .update({ ...input, [notesField]: notes })
    .eq("id", input.id)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export function useUpdateEngagement(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["engagements", "list"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["engagements", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
