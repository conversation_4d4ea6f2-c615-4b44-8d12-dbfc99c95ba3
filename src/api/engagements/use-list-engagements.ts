import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

type EngagementStatus = Enums<"engagement_status">;

interface ListEngagementsParams extends PaginationParams {
  shipment_id?: string;
  driver_id?: string;
  organization_id?: string;
  stop_id?: string;
  vehicle_id?: string;
  verification_status?: "verified" | "unverified";
  has_documents?: boolean;
  status?: EngagementStatus[];
  search?: string;
}

export async function queryFn({
  shipment_id,
  driver_id,
  organization_id,
  stop_id,
  vehicle_id,
  verification_status,
  has_documents,
  status,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListEngagementsParams = {}) {
  let query = supabase.from("engagements").select(
    `*,
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number,
        score,
        tier,
        verified_at,
        location:locations (
          id,
          formatted,
          latitude,
          longitude
        ),
        qualifications:qualifications (
          id,
          type,
          status,
          issued_at,
          expires_at,
          verified_at
        )
      ),
      organization:organizations (
        id,
        name,
        type,
        industry,
        size,
        location:locations (
          id,
          formatted,
          latitude,
          longitude
        )
      ),
      shipment:shipments (
        id,
        status,
        mode,
        source,
        weight,
        valuation,
        distance,
        duration,
        started_at,
        completed_at,
        cancelled_at,
        load:loads (
          id,
          type,
          label,
          notes,
          perishable,
          weight,
          valuation
        ),
        stops:stops (
          id,
          sequence_number,
          type,
          label,
          arrived_at,
          departed_at,
          location:locations (
            id,
            formatted,
            latitude,
            longitude
          )
        ),
        documents:documents (
          id,
          name,
          type,
          url,
          created_at
        )
      )`,
    { count: "exact" },
  );

  // Text search across relevant fields
  if (search) {
    const searchPattern = `%${search}%`;
    query = query.or(
      `driver.first_name.ilike.${searchPattern},driver.last_name.ilike.${searchPattern},driver.email.ilike.${searchPattern},organization.name.ilike.${searchPattern},shipment.id.ilike.${searchPattern}`,
    );
  }

  // Apply filters
  if (shipment_id) {
    query = query.eq("shipment_id", shipment_id);
  }

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (organization_id) {
    query = query.eq("organization_id", organization_id);
  }

  if (stop_id) {
    query = query.eq("shipment.stops.id", stop_id);
  }

  if (vehicle_id) {
    query = query.eq("shipment.vehicle_id", vehicle_id);
  }

  if (verification_status === "verified") {
    query = query.not("driver.verified_at", "is", null);
  } else if (verification_status === "unverified") {
    query = query.is("driver.verified_at", null);
  }

  if (has_documents === true) {
    query = query.gt("shipment.documents.length", 0);
  } else if (has_documents === false) {
    query = query.eq("shipment.documents.length", 0);
  }

  if (status && status.length > 0) {
    query = query.in("status", status);
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListEngagements(
  params: ListEngagementsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["engagements", "list", params],
    queryFn: async () => queryFn(params),
    enabled: !!(
      params.shipment_id ||
      params.driver_id ||
      params.organization_id
    ),
  });
}
