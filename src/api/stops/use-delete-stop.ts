import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DeleteStopInput {
  id: string;
  shipment_id: string;
}

export async function mutationFn({ id }: DeleteStopInput) {
  const { data, error } = await supabase
    .from("stops")
    .delete()
    .eq("id", id)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export function useDeleteStop(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["stops", "list", { shipment_id: variables.shipment_id }],
      });
      await queryClient.invalidateQueries({
        queryKey: ["stops", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
