import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { supabase } from "@/supabase/client";
import { validate } from "../utils";

interface ListStopsParams extends PaginationParams {
  shipment_id?: string;
  load_id?: string;
  location_id?: string;
  type?: Enums<"stop_type">;
  has_arrived?: boolean;
  has_departed?: boolean;
  search?: string;
}

export async function queryFn({
  shipment_id,
  load_id,
  location_id,
  type,
  has_arrived,
  has_departed,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListStopsParams = {}) {
  let query = supabase.from("stops").select(
    `*,
      location:locations (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude
      ),
      verifications:verifications (
        id,
        verified_at,
        verified_by,
        notes
      )`,
    { count: "exact" },
  );

  if (shipment_id) {
    query = query.eq("shipment_id", shipment_id);
  }

  if (load_id) {
    query = query.eq("load_id", load_id);
  }

  if (location_id) {
    query = query.eq("location_id", location_id);
  }

  if (type) {
    query = query.eq("type", type);
  }

  if (has_arrived !== undefined) {
    query = query.not("arrived_at", "is", has_arrived ? null : "not null");
  }

  if (has_departed !== undefined) {
    query = query.not("departed_at", "is", has_departed ? null : "not null");
  }

  if (search) {
    query = query.textSearch("label", search);
  }

  const { data, count, error } = await query
    .order("sequence_number", { ascending: true })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
    return [];
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListStops(
  params: ListStopsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["stops", "list", params],
    queryFn: async () => queryFn(params),
  });
}
