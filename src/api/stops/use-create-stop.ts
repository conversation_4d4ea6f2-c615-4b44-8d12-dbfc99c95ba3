import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"stops">) {
  const { data, error } = await supabase
    .from("stops")
    .insert([input])
    .select(
      `*,
      location:locations (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude
      ),
      verifications:verifications (
        id,
        verified_at,
        verified_by,
        notes
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreateStop(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ["stops", "list", { shipment_id: data.shipment_id }],
      });
      await queryClient.invalidateQueries({
        queryKey: ["stops", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
