import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("stops")
    .select(
      `*,
      location:locations (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude
      ),
      verifications:verifications (
        id,
        verified_at,
        verified_by,
        notes
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetStop(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["stops", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
