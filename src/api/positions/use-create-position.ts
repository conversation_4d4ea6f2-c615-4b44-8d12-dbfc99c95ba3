import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesInsert<"positions">) {
  const { data, error } = await supabase
    .from("positions")
    .insert([input])
    .select(
      `*,
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number
      ),
      vehicle:vehicles (
        id,
        make,
        model,
        year,
        license_plate
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useCreatePosition(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["positions", "list"] });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
