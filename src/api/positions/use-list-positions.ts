import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { supabase } from "@/supabase/client";
import { validate } from "../utils";

interface ListPositionsParams extends PaginationParams {
  shipment_id?: string;
  driver_id?: string;
  vehicle_id?: string;
  load_id?: string;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  time_range?: {
    start: Date;
    end: Date;
  };
}

export async function queryFn({
  shipment_id,
  driver_id,
  vehicle_id,
  load_id,
  bounds,
  time_range,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListPositionsParams = {}) {
  let query = supabase.from("positions").select(
    `*,
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number
      ),
      vehicle:vehicles (
        id,
        make,
        model,
        year,
        license_plate
      )`,
    { count: "exact" },
  );

  if (shipment_id) {
    query = query.eq("shipment_id", shipment_id);
  }

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (vehicle_id) {
    query = query.eq("vehicle_id", vehicle_id);
  }

  if (load_id) {
    query = query.eq("load_id", load_id);
  }

  if (bounds) {
    query = query
      .gte("latitude", bounds.south)
      .lte("latitude", bounds.north)
      .gte("longitude", bounds.west)
      .lte("longitude", bounds.east);
  }

  if (time_range) {
    query = query
      .gte("created_at", time_range.start.toISOString())
      .lte("created_at", time_range.end.toISOString());
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
    return [];
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListPositions(
  params: ListPositionsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["positions", "list", params],
    queryFn: async () => queryFn(params),
  });
}
