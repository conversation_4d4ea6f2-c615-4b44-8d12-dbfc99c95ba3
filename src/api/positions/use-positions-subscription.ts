import { useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface PositionsSubscriptionParams {
  shipment_id?: string;
  driver_id?: string;
  vehicle_id?: string;
  load_id?: string;
}

export function usePositionsSubscription({
  shipment_id,
  driver_id,
  vehicle_id,
  load_id,
}: PositionsSubscriptionParams = {}) {
  const queryClient = useQueryClient();

  useEffect(() => {
    const filter = {
      ...(shipment_id && { shipment_id: `eq.${shipment_id}` }),
      ...(driver_id && { driver_id: `eq.${driver_id}` }),
      ...(vehicle_id && { vehicle_id: `eq.${vehicle_id}` }),
      ...(load_id && { load_id: `eq.${load_id}` }),
    };

    const channelId = `positions:${Object.values(filter).join(":")}`;
    const channel = supabase
      .channel(channelId)
      .on(
        // TODO: Use the correct event type
        // "postgres_changes",
        "system",
        {
          event: "*",
          schema: "public",
          table: "positions",
          filter,
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: [
              "positions",
              "list",
              {
                shipment_id,
                driver_id,
                vehicle_id,
                load_id,
              },
            ],
          });
        },
      )
      .subscribe();

    return () => {
      void supabase.removeChannel(channel);
    };
  }, [queryClient, shipment_id, driver_id, vehicle_id, load_id]);
}
