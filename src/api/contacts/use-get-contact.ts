import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("contacts")
    .select(
      `*,
      organization:organizations (
        id,
        name
      ),
      location:locations (
        id,
        formatted
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetContact(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["contacts", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
