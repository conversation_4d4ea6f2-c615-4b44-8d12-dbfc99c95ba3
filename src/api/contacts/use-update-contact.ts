import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(input: TablesUpdate<"contacts">) {
  const { data, error } = await supabase
    .from("contacts")
    .update(input)
    .eq("id", input.id)
    .select(
      `*,
      organization:organizations (
        id,
        name
      ),
      location:locations (
        id,
        formatted
      )`,
    )
    .single();

  if (error) throw error;
  return data;
}

export function useUpdateContact(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["contacts", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["contacts", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
