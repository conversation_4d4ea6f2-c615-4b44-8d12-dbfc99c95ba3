import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export async function queryFn() {
  const { data, error } = await supabase
    .from("incidents")
    .select(
      `*,
          shipment:shipments (
            id,
            status
          ),
          driver:drivers (
            id,
            first_name,
            last_name
          ),
          stop:stops (
            id,
            label,
            type,
            sequence_number
          )`,
    )
    .eq("status", "reported")
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data || [];
}

export function usePendingIncidents(
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["console", "incidents", "pending"],
    queryFn,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
  });
}
