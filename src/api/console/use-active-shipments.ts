import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

export async function queryFn() {
  const { data, error } = await supabase
    .from("shipments")
    .select(
      `*,
        driver:drivers (
          id,
          first_name,
          last_name
        ),
        organization:organizations (
          id,
          name,
          avatar
        ),
        load:loads (
          id,
          title,
          description,
          weight,
          type
        ),
        stops:stops (
          id,
          sequence_number,
          type,
          label,
          arrived_at,
          departed_at,
          location:locations (
            id,
            formatted,
            latitude,
            longitude
          )
        )`,
    )
    .eq("status", "in_progress")
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data || [];
}

export function useActiveShipments(
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["console", "shipments", "active"],
    queryFn,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
  });
}
