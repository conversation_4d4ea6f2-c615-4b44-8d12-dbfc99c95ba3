import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface AnalyticsSnapshot {
  total_shipments: number;
  active_shipments: number;
  completed_shipments: number;
  cancelled_shipments: number;
  total_drivers: number;
  active_drivers: number;
  total_organizations: number;
  active_organizations: number;
  total_incidents: number;
  open_incidents: number;
  total_revenue: number;
  average_shipment_value: number;
}

export async function queryFn(
  organizationId?: string,
): Promise<AnalyticsSnapshot> {
  // If no organization ID provided, return default values
  if (!organizationId) {
    return {
      total_shipments: 0,
      active_shipments: 0,
      completed_shipments: 0,
      cancelled_shipments: 0,
      total_drivers: 0,
      active_drivers: 0,
      total_organizations: 0,
      active_organizations: 0,
      total_incidents: 0,
      open_incidents: 0,
      total_revenue: 0,
      average_shipment_value: 0,
    };
  }

  // Get counts for each metric filtered by organization
  const [
    { count: totalShipments },
    { count: activeShipments },
    { count: completedShipments },
    { count: cancelledShipments },
    { count: totalDrivers },
    { count: activeDrivers },
    { count: totalIncidents },
    { count: openIncidents },
  ] = await Promise.all([
    supabase
      .from("shipments")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId),
    supabase
      .from("shipments")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId)
      .eq("status", "in_progress"),
    supabase
      .from("shipments")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId)
      .eq("status", "completed"),
    supabase
      .from("shipments")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId)
      .eq("status", "cancelled"),
    supabase
      .from("drivers")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId),
    supabase
      .from("drivers")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId)
      .eq("status", "active"),
    supabase
      .from("incidents")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId),
    supabase
      .from("incidents")
      .select("*", { count: "exact" })
      .eq("organization_id", organizationId)
      .eq("status", "reported"),
  ]);

  // Get revenue metrics
  const { data: shipments } = await supabase
    .from("shipments")
    .select("valuation")
    .eq("organization_id", organizationId);

  const totalRevenue = shipments?.reduce(
    (sum, shipment) => sum + (shipment.valuation || 0),
    0,
  );

  const averageShipmentValue =
    totalShipments > 0 ? totalRevenue / totalShipments : 0;

  return {
    total_shipments: totalShipments || 0,
    active_shipments: activeShipments || 0,
    completed_shipments: completedShipments || 0,
    cancelled_shipments: cancelledShipments || 0,
    total_drivers: totalDrivers || 0,
    active_drivers: activeDrivers || 0,
    total_organizations: 1, // Current organization
    active_organizations: 1, // Current organization is active
    total_incidents: totalIncidents || 0,
    open_incidents: openIncidents || 0,
    total_revenue: totalRevenue || 0,
    average_shipment_value: averageShipmentValue || 0,
  };
}

export function useAnalyticsSnapshot(
  organizationId?: string,
  props: Omit<
    UseQueryOptions<AnalyticsSnapshot, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<AnalyticsSnapshot, Error>({
    ...props,
    queryKey: ["console", "analytics", "snapshot", organizationId],
    queryFn: () => queryFn(organizationId),
    enabled: !!organizationId && props.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
