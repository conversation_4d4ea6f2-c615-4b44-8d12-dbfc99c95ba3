import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DashboardMetrics {
  totalShipments: number;
  activeShipments: number;
  totalDrivers: number;
  activeDrivers: number;
  totalIncidents: number;
  openIncidents: number;
  totalTonnage: number;
  weeklyTonnage: number;
  totalEarnings: number;
  weeklyEarnings: number;
}

export async function queryFn() {
  const [
    { count: totalShipments },
    { count: activeShipments },
    { count: totalDrivers },
    { count: activeDrivers },
    { count: totalIncidents },
    { count: openIncidents },
  ] = await Promise.all([
    supabase.from("shipments").select("*", { count: "exact" }),
    supabase
      .from("shipments")
      .select("*", { count: "exact" })
      .eq("status", "in_progress"),
    supabase.from("drivers").select("*", { count: "exact" }),
    supabase
      .from("drivers")
      .select("*", { count: "exact" })
      .eq("status", "active"),
    supabase.from("incidents").select("*", { count: "exact" }),
    supabase
      .from("incidents")
      .select("*", { count: "exact" })
      .eq("status", "reported"),
  ]);

  return {
    totalShipments: totalShipments || 0,
    activeShipments: activeShipments || 0,
    totalDrivers: totalDrivers || 0,
    activeDrivers: activeDrivers || 0,
    totalIncidents: totalIncidents || 0,
    openIncidents: openIncidents || 0,
    totalTonnage: 0,
    weeklyTonnage: 0,
    totalEarnings: 0,
    weeklyEarnings: 0,
  } satisfies DashboardMetrics;
}

export function useDashboardMetrics(
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["console", "metrics", "dashboard"],
    queryFn,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
  });
}
