import { PostgrestError } from "@supabase/supabase-js";

/**
 * Validates a Supabase response error and handles common error cases
 * @param error The PostgrestError from a Supabase query
 * @param options Configuration options for error handling
 * @returns null if error should be ignored, otherwise throws the error
 */
export function validate(
  error: PostgrestError | null,
  options: {
    /**
     * If true, PGRST116 (not found) errors will return null instead of throwing
     * @default false
     */
    allowNotFound?: boolean;
  } = {},
) {
  if (!error) return;

  // Handle "not found" errors (PGRST116)
  if (error.code === "PGRST116" && options.allowNotFound) {
    return null;
  }

  throw error;
}
