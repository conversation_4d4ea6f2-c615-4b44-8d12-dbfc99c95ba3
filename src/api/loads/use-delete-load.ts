import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DeleteLoadInput {
  id: string;
}

export async function mutationFn({ id }: DeleteLoadInput) {
  const { error } = await supabase.from("loads").delete().eq("id", id);

  if (error) throw error;
}

export function useDeleteLoad(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["loads", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["loads", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
