import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(data: TablesUpdate<"loads">) {
  const { data: load, error } = await supabase
    .from("loads")
    .update(data)
    .eq("id", data.id)
    .select(
      `
      *,
      organization:organizations (
        id,
        name
      ),
      origin:locations!loads_origin_id_fkey (
        id,
        formatted
      ),
      destination:locations!loads_destination_id_fkey (
        id,
        formatted
      )
    `,
    )
    .single();

  if (error) throw error;
  return load;
}

export function useUpdateLoad(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["loads", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["loads", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
