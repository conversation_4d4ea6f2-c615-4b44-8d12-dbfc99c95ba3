import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(data: TablesInsert<"loads">) {
  const { data: load, error } = await supabase
    .from("loads")
    .insert([data])
    .select(
      `*,
      organization:organizations (
        id,
        name
      ),
      origin:locations!loads_origin_id_fkey (
        id,
        formatted
      ),
      destination:locations!loads_destination_id_fkey (
        id,
        formatted
      )`,
    )
    .single();

  if (error) throw error;
  return load;
}

export function useCreateLoad(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["loads", "list"] });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
