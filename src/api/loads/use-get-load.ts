import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";
import { validate } from "../utils";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("loads")
    .select(
      `
      *,
      organization:organizations (
        id,
        name,
        industry,
        type
      ),
      origin:locations!loads_origin_id_fkey (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude,
        type
      ),
      destination:locations!loads_destination_id_fkey (
        id,
        formatted,
        street,
        city,
        state,
        country,
        latitude,
        longitude,
        type
      ),
      shipments (
        id,
        status,
        started_at,
        completed_at
      )
    `,
    )
    .eq("id", id)
    .maybeSingle();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }

  return data;
}

export function useGetLoad(
  id?: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["loads", "get", id],
    queryFn: async () => {
      if (!id) return null;
      return queryFn(id);
    },
    enabled: !!id,
  });
}
