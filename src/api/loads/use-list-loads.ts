import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

type LoadStatus = Enums<"load_status">;
type LoadType = Enums<"load_type">;

interface ListLoadsParams extends PaginationParams {
  status?: LoadStatus;
  type?: LoadType;
  organization_id?: string;
  perishable?: boolean;
  origin_id?: string;
  destination_id?: string;
  search?: string;
}

export async function queryFn({
  status,
  type,
  organization_id,
  perishable,
  origin_id,
  destination_id,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListLoadsParams = {}) {
  let query = supabase.from("loads").select(
    `
      *,
      organization:organizations (
        id,
        name
      ),
      origin:locations!loads_origin_id_fkey (
        id,
        formatted,
        latitude,
        longitude
      ),
      destination:locations!loads_destination_id_fkey (
        id,
        formatted,
        latitude,
        longitude
      )
    `,
    { count: "exact" },
  );

  if (status) {
    query = query.eq("status", status);
  }

  if (type) {
    query = query.eq("type", type);
  }

  if (organization_id) {
    query = query.eq("organization_id", organization_id);
  }

  if (perishable !== undefined) {
    query = query.eq("perishable", perishable);
  }

  if (origin_id) {
    query = query.eq("origin_id", origin_id);
  }

  if (destination_id) {
    query = query.eq("destination_id", destination_id);
  }

  if (search) {
    query = query.or(`label.ilike.%${search}%,notes.ilike.%${search}%`);
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListLoads(
  params: ListLoadsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["loads", "list", params],
    queryFn: async () => queryFn(params),
  });
}
