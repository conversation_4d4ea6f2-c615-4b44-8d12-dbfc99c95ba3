import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { supabase } from "@/supabase/client";

interface DeleteShipmentInput {
  id: string;
}

export async function mutationFn({ id }: DeleteShipmentInput) {
  const { error } = await supabase.from("shipments").delete().eq("id", id);

  if (error) throw error;
}

export function useDeleteShipment(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  > = {},
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["shipments", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["shipments", "get", variables.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
