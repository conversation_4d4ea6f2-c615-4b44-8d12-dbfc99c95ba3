import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

export async function queryFn(id: string) {
  const { data, error } = await supabase
    .from("shipments")
    .select(
      `*,
      driver:drivers (
        id,
        first_name,
        last_name,
        email,
        phone_number,
        avatar,
        score,
        tier,
        verified_at,
        location:locations (
          id,
          formatted,
          latitude,
          longitude
        ),
        qualifications:qualifications (
          id,
          type,
          status,
          issued_at,
          expires_at,
          verified_at
        )
      ),
      organization:organizations (
        id,
        name,
        type,
        industry,
        size,
        avatar,
        location:locations (
          id,
          formatted,
          latitude,
          longitude
        )
      ),
      load:loads (
        id,
        type,
        label,
        notes,
        perishable,
        weight,
        valuation
      ),
      stops:stops (
        id,
        sequence_number,
        type,
        label,
        notes,
        arrived_at,
        departed_at,
        location:locations (
          id,
          formatted,
          street,
          city,
          state,
          country,
          latitude,
          longitude
        ),
        verifications:verifications (
          id,
          verified_at,
          verified_by,
          notes
        )
      ),
      documents:documents (
        id,
        name,
        type,
        url,
        content_type,
        size,
        created_at,
        created_by,
        metadata
      )`,
    )
    .eq("id", id)
    .single();

  if (error) {
    validate(error, { allowNotFound: true });
    return null;
  }
  return data;
}

export function useGetShipment(
  id: string,
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["shipments", "get", id],
    enabled: !!id,
    queryFn: () => queryFn(id),
  });
}
