import type { UseQueryOptions } from "@tanstack/react-query";

import { useQuery } from "@tanstack/react-query";

import type { PaginatedResponse, PaginationParams } from "@/api/types";
import type { Enums } from "@/supabase/types";

import { DEFAULT_PAGE_SIZE } from "@/api/constants";
import { validate } from "@/api/utils";
import { supabase } from "@/supabase/client";

interface ListShipmentsParams extends PaginationParams {
  status?: Enums<"shipment_status"> | Enums<"shipment_status">[];
  mode?: Enums<"shipment_mode">;
  source?: Enums<"shipment_source">;
  driver_id?: string;
  organization_id?: string;
  load_id?: string;
  date_range?: {
    start: Date;
    end: Date;
  };
  weight_range?: {
    min: number;
    max: number;
  };
  valuation_range?: {
    min: number;
    max: number;
  };
  search?: string;
}

export async function queryFn({
  status,
  mode,
  source,
  driver_id,
  organization_id,
  load_id,
  date_range,
  weight_range,
  valuation_range,
  search,
  pageIndex = 0,
  pageSize = DEFAULT_PAGE_SIZE,
}: ListShipmentsParams = {}) {
  let query = supabase.from("shipments").select(
    `*,
      driver:drivers (
        id,
        first_name,
        last_name,
        avatar,
        score,
        tier,
        verified_at
      ),
      organization:organizations (
        id,
        name,
        type,
        industry,
        size,
        avatar
      ),
      load:loads (
        id,
        type,
        label,
        perishable,
        weight,
        valuation
      ),
      stops:stops (
        id,
        sequence_number,
        type,
        label,
        arrived_at,
        departed_at,
        location:locations (
          id,
          formatted,
          latitude,
          longitude
        )
      )`,
    { count: "exact" },
  );

  if (status) {
    if (Array.isArray(status)) {
      query = query.in("status", status);
    } else {
      query = query.eq("status", status);
    }
  }

  if (mode) {
    query = query.eq("mode", mode);
  }

  if (source) {
    query = query.eq("source", source);
  }

  if (driver_id) {
    query = query.eq("driver_id", driver_id);
  }

  if (organization_id) {
    query = query.eq("organization_id", organization_id);
  }

  if (load_id) {
    query = query.eq("load_id", load_id);
  }

  if (date_range) {
    query = query
      .gte("created_at", date_range.start.toISOString())
      .lte("created_at", date_range.end.toISOString());
  }

  if (weight_range) {
    query = query
      .gte("weight", weight_range.min)
      .lte("weight", weight_range.max);
  }

  if (valuation_range) {
    query = query
      .gte("valuation", valuation_range.min)
      .lte("valuation", valuation_range.max);
  }

  if (search) {
    query = query.or(
      `driver.first_name.ilike.%${search}%,driver.last_name.ilike.%${search}%,organization.name.ilike.%${search}%,load.label.ilike.%${search}%`,
    );
  }

  const { data, count, error } = await query
    .order("created_at", { ascending: false })
    .range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);

  if (error) {
    validate(error, { allowNotFound: true });
  }

  return {
    items: data || [],
    total: count || 0,
  } satisfies PaginatedResponse<(typeof data)[number]>;
}

export function useListShipments(
  params: ListShipmentsParams = {},
  props: Omit<
    UseQueryOptions<Awaited<ReturnType<typeof queryFn>>, Error>,
    "queryKey" | "queryFn"
  > = {},
) {
  return useQuery<Awaited<ReturnType<typeof queryFn>>, Error>({
    ...props,
    queryKey: ["shipments", "list", params],
    queryFn: async () => queryFn(params),
  });
}
