import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesInsert } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn(data: TablesInsert<"shipments">) {
  const { data: shipment, error } = await supabase
    .from("shipments")
    .insert([{ ...data, status: "pending" }])
    .select(
      `*,
      organization:organizations (
        id,
        name
      ),
      driver:users (
        id,
        name
      )`,
    )
    .single();

  if (error) throw error;
  return shipment;
}

export function useCreateShipment(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["shipments", "list"] });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
