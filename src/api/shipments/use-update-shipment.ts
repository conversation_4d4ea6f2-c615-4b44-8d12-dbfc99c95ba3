import type { UseMutationOptions } from "@tanstack/react-query";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import type { TablesUpdate } from "@/supabase/types";

import { supabase } from "@/supabase/client";

export async function mutationFn({
  id,
  ...data
}: TablesUpdate<"shipments"> & { id: string }) {
  const { data: shipment, error } = await supabase
    .from("shipments")
    .update(data)
    .eq("id", id)
    .select(
      `*,
      organization:organizations (
        id,
        name
      ),
      driver:users (
        id,
        name
      )`,
    )
    .single();

  if (error) throw error;
  return shipment;
}

export function useUpdateShipment(
  props: Omit<
    UseMutationOptions<
      Awaited<ReturnType<typeof mutationFn>>,
      Error,
      Parameters<typeof mutationFn>[0]
    >,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();

  return useMutation<
    Awaited<ReturnType<typeof mutationFn>>,
    Error,
    Parameters<typeof mutationFn>[0]
  >({
    ...props,
    mutationFn,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ["shipments", "list"] });
      await queryClient.invalidateQueries({
        queryKey: ["shipments", "get", data.id],
      });
      await props.onSuccess?.(data, variables, context);
    },
  });
}
