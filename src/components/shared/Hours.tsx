import { cn } from "@/lib/utils";
import { Skeleton } from "../ui/skeleton";

const i18n = {
  en: {
    hours: "Hours",
  },
};

export function Hours({
  loading = false,
  hours,
  className,
  label = i18n.en.hours,
}: {
  loading?: boolean;
  hours: number;
  className?: string;
  label?: string;
}) {
  return (
    <p className="text-center">
      {loading ? (
        <Skeleton className={cn("h-6 w-24", className)} />
      ) : (
        <>
          <span className={cn("text-6xl font-bold", className)}>{hours}</span>{" "}
          <span className="inline-block w-full">{label}</span>
        </>
      )}
    </p>
  );
}
