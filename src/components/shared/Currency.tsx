import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/i18n";

export default function Currency({
  loading = false,
  value,
  amount = value ?? 0,
  currency = "USD",
  children = formatCurrency(amount, { currency }),
  className,
}: {
  loading?: boolean;
  value?: number;
  amount?: number;
  currency?: string;
  children?: React.ReactNode;
  className?: string;
}) {
  return (
    <span className={className}>
      {loading ? <Skeleton className="h-6 w-24" /> : children}
    </span>
  );
}
