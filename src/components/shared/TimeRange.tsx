import type { DateTimeProps } from "@/components/shared/DateTime";

import DateTime from "@/components/shared/DateTime";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    prepositions: {
      to: "to",
    },
  },
};

export default function TimeRange({
  loading = false,
  startTime,
  endTime,
  className,
  showTime = false,
}: {
  loading?: boolean;
  startTime: DateTimeProps["time"];
  endTime?: DateTimeProps["time"];
  className?: string;
  showTime?: boolean;
}) {
  return (
    <p className="text-end">
      <DateTime
        loading={loading}
        className={cn("text-3xl font-semibold", className)}
        time={startTime}
        showTime={showTime}
      />
      {endTime && (
        <>
          <br />
          <span className="text-xl font-semibold">
            {i18n.en.prepositions.to}
          </span>
          <DateTime
            loading={loading}
            className={cn("w-[144px] text-3xl font-semibold", className)}
            time={endTime}
            showTime={showTime}
          />
        </>
      )}
    </p>
  );
}
