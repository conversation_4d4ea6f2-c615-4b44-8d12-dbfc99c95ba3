import React from "react";

interface ObjectDisplayProps {
  value: any;
  depth?: number;
  maxDepth?: number;
}

/**
 * A component to properly display complex objects
 * Recursively renders objects and arrays as readable strings
 */
export default function ObjectDisplay({
  value,
  depth = 0,
  maxDepth = 3,
}: ObjectDisplayProps) {
  if (value === null || value === undefined) {
    return <span className="text-muted-foreground">None</span>;
  }

  // Handle primitive values
  if (typeof value !== "object") {
    return <span>{String(value)}</span>;
  }

  // Prevent too deep nesting
  if (depth >= maxDepth) {
    return <span>{JSON.stringify(value)}</span>;
  }

  // Handle arrays
  if (Array.isArray(value)) {
    if (value.length === 0) {
      return <span className="text-muted-foreground">Empty array</span>;
    }

    return (
      <ul className="ml-4 list-disc space-y-1">
        {value.map((item, idx) => (
          <li key={idx}>
            <ObjectDisplay value={item} depth={depth + 1} maxDepth={maxDepth} />
          </li>
        ))}
      </ul>
    );
  }

  // Handle objects
  const keys = Object.keys(value);
  if (keys.length === 0) {
    return <span className="text-muted-foreground">Empty object</span>;
  }

  return (
    <div className="space-y-1">
      {keys.map((key) => (
        <div key={key} className="ml-2">
          <span className="font-medium capitalize">
            {key.replace(/([A-Z])/g, " $1").trim()}:{" "}
          </span>
          <ObjectDisplay
            value={value[key]}
            depth={depth + 1}
            maxDepth={maxDepth}
          />
        </div>
      ))}
    </div>
  );
}
