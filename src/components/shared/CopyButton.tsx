import { useCallback } from "react";
import { CopyIcon } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default function CopyButton({
  hidden = false,
  size = "md",
  label = "Copy",
  text,
}: {
  hidden?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | null;
  label?: string;
  text?: string;
}) {
  return (
    <Button
      size="icon"
      variant="outline"
      className={cn(
        "opacity-0 transition-opacity group-hover:opacity-100 group-focus:opacity-100 group-focus-visible:opacity-100 focus:opacity-100",
        {
          hidden: hidden || !text,
          "size-5": size === "sm",
          "size-6": size === "md",
          "size-7": size === "lg",
          "size-8": size === "xl",
        },
      )}
      onClick={useCallback(
        async (event: { preventDefault: () => void }) => {
          if (text) {
            try {
              event.preventDefault();
              await navigator.clipboard.writeText(text);
              toast("Copied to clipboard");
            } catch (error) {
              toast.error("Failed to copy to clipboard", {
                description:
                  error instanceof Error ? error.message : "Unknown error",
              });
            }
          }
        },
        [text],
      )}
    >
      <CopyIcon className="size-3" />
      <span className="sr-only">{label}</span>
    </Button>
  );
}
