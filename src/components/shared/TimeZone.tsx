import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

export default function TimeZone({
  loading,
  timeZone,
}: {
  loading?: boolean;
  timeZone?: string | null;
}) {
  return (
    <Badge variant="outline" className="w-fit text-xs text-nowrap">
      {loading ? (
        <Skeleton className="h-4 w-20" />
      ) : (
        timeZone?.replace("_", " ").replace("/", " | ")
      )}
    </Badge>
  );
}
