import { useMemo } from "react";
import { format } from "date-fns";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

export interface DateTimeProps {
  loading?: boolean;
  className?: string;
  mode?: "plain" | "calendar";
  size?: "sm" | "md" | "lg" | "xl";
  format?: string;
  date?: Date | null;
  time?: {
    hour: number;
    minute: number;
    second?: number;
    millisecond?: number;
  } | null;
  showTime?: boolean;
}

export default function DateTime({
  loading = false,
  className,
  mode = "plain",
  size = "md",
  format: formatType,
  date,
  time,
  showTime = false,
}: DateTimeProps) {
  const { content, value } = useMemo(() => {
    let content;
    let value: string;

    if (time) {
      const today = new Date();
      today.setHours(time.hour);
      today.setMinutes(time.minute);
      value = `${time.hour.toFixed(2)}:${time.minute.toFixed(2)}`;
      content = format(today, formatType ?? "h:mm a");
    } else {
      if (date instanceof Date) {
        value = date.toISOString();
      } else {
        const today = new Date();
        value = today.toISOString();
      }

      if (mode === "calendar") {
        const [mmdd, year] = format(value, "PPP").split(", ");
        const [month] = mmdd?.split(" ") ?? [];
        const day = new Date(value).getDate();
        content = (
          <div className="flex aspect-square size-fit flex-col items-center justify-center">
            <span className="font-semibold">{month}</span>
            <span className="text-6xl font-bold">{day}</span>
            <span className="font-semibold">{year}</span>
            {showTime && (
              <span className="border-border rounded-lg border p-2 font-semibold">
                {format(value, "h:mm a")}
              </span>
            )}
          </div>
        );
      } else {
        content = format(value, formatType ?? "PPP");
      }
    }

    return {
      content,
      value,
    };
  }, [time, formatType, date, mode, showTime]);

  return (
    <time
      dateTime={value}
      className={cn(
        {
          "text-2xl": size === "xl",
          "text-xl": size === "lg",
          "text-lg": size === "md",
          "text-sm": size === "sm",
        },
        className,
      )}
    >
      {loading ? (
        <Skeleton className={cn("h-6 w-24", size === "xl" && "h-8 w-32")} />
      ) : (
        content
      )}
    </time>
  );
}
