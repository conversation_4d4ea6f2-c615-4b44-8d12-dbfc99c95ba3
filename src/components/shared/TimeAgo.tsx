import { formatDistanceToNow } from "date-fns";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

export interface TimeAgoProps {
  loading?: boolean;
  date: Date;
  className?: string;
}

const TimeAgo = ({ loading, date, className }: TimeAgoProps) => {
  return (
    <span className={cn(className)}>
      {loading ? (
        <Skeleton className="size-4" />
      ) : (
        formatDistanceToNow(date, { addSuffix: true })
      )}
    </span>
  );
};

export { TimeAgo };
export default TimeAgo;
