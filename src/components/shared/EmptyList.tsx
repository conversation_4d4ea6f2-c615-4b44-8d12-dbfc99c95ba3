import type { PropsWithChildren } from "react";

import { cn } from "@/lib/utils";

export default function EmptyList({
  title,
  description,
  className,
  children,
}: PropsWithChildren<{
  title: string;
  description?: string;
  className?: string;
}>) {
  return (
    <div
      className={cn(
        "flex h-full flex-1 items-center justify-center rounded-lg border border-dashed shadow-xs",
        className,
      )}
    >
      <div className="flex flex-col items-center gap-1 p-4 text-center">
        <h3 className="text-2xl font-bold tracking-tight">{title}</h3>
        {description && (
          <p className="text-muted-foreground text-sm">{description}</p>
        )}
        {children && <div className="mt-6 mb-2 w-full">{children}</div>}
      </div>
    </div>
  );
}
