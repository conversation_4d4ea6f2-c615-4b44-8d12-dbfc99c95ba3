import type { DialogProps } from "@radix-ui/react-dialog";
import type { PropsWithChildren } from "react";

import { useCallback, useState } from "react";
import { CirclePlusIcon, PenBoxIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@/components/ui/button";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    noDescription: "a form to create or edit a resource",
    noTitle: "Create or edit a resource Dialog",
    actions: {
      add: "Add ",
    },
  },
};

export interface DialogFormProps<
  FormProps,
  FormValues = Record<string, string | number | null | undefined>,
> extends PropsWithChildren<Pick<DialogProps, "open" | "onOpenChange">> {
  Component: React.ComponentType<
    | FormProps
    | (FormProps & {
        onSubmit: (values: FormValues) => void | Promise<void>;
      })
  >;
  Icon?: React.ComponentType<{ className?: string }>;
  onSubmit: (values: FormValues) => void | Promise<void>;
  onError?: (error: unknown) => void | Promise<void>;
  size?: ButtonProps["size"];
  variant?: ButtonProps["variant"];
  className?: string;
  title?: string;
  description?: string;
  label?: string;
  type?: "add" | "update" | "delete";
  closeOnError?: boolean;
  disabled?: boolean;
  useTrigger?: boolean;
  isEditing?: boolean;
}

export default function DialogForm<T, TT>({
  onSubmit,
  onError,
  size,
  type,
  variant,
  className,
  title,
  description,
  label,
  children,
  Component,
  Icon = type === "add"
    ? CirclePlusIcon
    : type === "update"
      ? PenBoxIcon
      : type === "delete"
        ? TrashIcon
        : CirclePlusIcon,
  open,
  onOpenChange,
  closeOnError = false,
  disabled = false,
  useTrigger = true,
  isEditing = false,
  ...props
}: DialogFormProps<T, TT> & T) {
  const isMobile = useMediaQuery("(max-width: 640px)");
  const [isOpen, setIsOpen] = useState<boolean>(open ?? false);

  const openChange = useCallback(
    function openChange(_open: boolean) {
      if (onOpenChange) {
        onOpenChange(_open);
      }
      setIsOpen(_open);
    },
    [onOpenChange],
  );

  const trigger = children ?? (
    <Button
      disabled={disabled}
      size={size}
      variant={variant}
      className={cn("md:min-w-36", className)}
    >
      <Icon className={cn("md:mr-2", size === "sm" ? "size-3.5" : "size-5")} />
      <span className="sr-only font-semibold md:not-sr-only">
        {label ?? i18n.en.actions.add}
      </span>
    </Button>
  );

  if (isMobile) {
    return (
      <Sheet onOpenChange={openChange} open={open ?? isOpen}>
        {useTrigger && <SheetTrigger asChild>{trigger}</SheetTrigger>}
        <SheetContent className="overflow-scroll">
          <SheetHeader className="mb-8">
            {title ? (
              <SheetTitle className="text-lg font-semibold">{title}</SheetTitle>
            ) : (
              <SheetTitle className="sr-only text-lg font-semibold">
                {i18n.en.noTitle}
              </SheetTitle>
            )}
            {description ? (
              <SheetDescription className="text-muted-foreground">
                {description}
              </SheetDescription>
            ) : (
              <DialogDescription className="text-muted-foreground sr-only">
                {i18n.en.noDescription}
              </DialogDescription>
            )}
          </SheetHeader>

          <Component
            {...(props as T)}
            open={open ?? isOpen}
            isEditing={isEditing}
            onSubmit={async (values: TT) => {
              try {
                await onSubmit(values);
                openChange(false);
              } catch (error) {
                await onError?.(error);
                if (closeOnError === true) {
                  openChange(false);
                }
              }
            }}
          />
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog onOpenChange={openChange} open={open ?? isOpen}>
      {useTrigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="max-h-[80vh] overflow-scroll">
        <DialogHeader>
          {title ? (
            <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
          ) : (
            <DialogTitle className="sr-only text-lg font-semibold">
              {i18n.en.noTitle}
            </DialogTitle>
          )}
          {description ? (
            <DialogDescription className="text-muted-foreground">
              {description}
            </DialogDescription>
          ) : (
            <DialogDescription className="text-muted-foreground sr-only">
              {i18n.en.noDescription}
            </DialogDescription>
          )}
        </DialogHeader>

        <Component
          {...(props as T)}
          open={open ?? isOpen}
          isEditing={isEditing}
          onSubmit={async (values: TT) => {
            try {
              await onSubmit(values);
              openChange(false);
            } catch (error) {
              await onError?.(error);
              if (closeOnError === true) {
                openChange(false);
              }
            }
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
