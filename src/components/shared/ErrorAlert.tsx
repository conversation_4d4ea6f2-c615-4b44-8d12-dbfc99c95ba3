import { TriangleAlertIcon } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export function ErrorAlert({
  error,
}: {
  error:
    | (Error & { digest?: string })
    | {
        message: string;
      };
}) {
  return (
    <Alert variant="destructive">
      <TriangleAlertIcon className="size-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{error.message}</AlertDescription>
    </Alert>
  );
}
