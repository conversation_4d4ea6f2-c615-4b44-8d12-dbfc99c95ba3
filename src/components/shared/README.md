# Shared Components

This directory contains reusable components that combine functionality with presentation. These range from simple utility components like copy buttons to complex systems like tables and search interfaces.

> **Quick Start**
>
> - Need a reusable component with both behavior and UI? Place it here
> - Components can be simple (CopyButton) or complex (DataTable)
> - Can include their own hooks and utilities
> - Should be generic enough to use across different features
> - Document props and usage patterns in component files

> [!NOTE] Shared components are the middle ground between pure UI components and full widgets. They encapsulate specific behaviors and their presentation, but remain generic enough to be used across different contexts. Unlike widgets which are complete features, shared components are building blocks that can be composed into larger features or views.

## Component Types

1. Utility Components:
   - `CopyButton` - Copy text with feedback
   - `ShareButton` - Share functionality with options
   - `DownloadButton` - File download with progress
   - `FilterButton` - Generic filter UI with logic

2. Complex Systems:
   - `DataTable` - Full table system with sorting/filtering
   - `SearchBar` - Search with suggestions/history
   - `FileUploader` - Upload with preview/progress
   - `FilterSystem` - Advanced filtering interface

3. Interactive Elements:
   - `DragHandle` - Drag and drop functionality
   - `InlineEdit` - Edit-in-place capability
   - `SortHandle` - Sorting interface
   - `ExpandToggle` - Expansion controls

## File Structure

```
shared/
├── buttons/         # Interactive buttons with behavior
├── data/           # Data display components
├── inputs/         # Enhanced input components
├── layout/         # Layout-related components
├── feedback/       # User feedback components
└── README.md       # This file
```

## Component Pattern

Shared components typically follow this pattern:

```typescript
interface MyComponentProps {
  // Generic props that work across contexts
  onAction?: (data: any) => void;
  children?: React.ReactNode;
}

export function MyComponent({ onAction, children }: MyComponentProps) {
  // Local state/behavior
  const [state, setState] = useState();

  // Component-specific hooks
  const { handleAction } = useMyComponentLogic();

  // Render with both functionality and presentation
  return (
    <div className="...">
      <button onClick={handleAction}>
        {children}
      </button>
      {/* Feedback UI */}
    </div>
  );
}
```

## Best Practices

1. **Component Design**
   - Keep props generic and reusable
   - Include sensible defaults
   - Support customization via props
   - Document usage patterns

2. **Functionality**
   - Encapsulate related behavior
   - Provide callback props for key events
   - Include necessary feedback UI
   - Handle loading/error states

3. **Composition**
   - Accept children when appropriate
   - Support style overrides
   - Allow content customization
   - Enable feature toggling

4. **Accessibility**
   - Include proper ARIA attributes
   - Support keyboard navigation
   - Provide visual feedback
   - Consider mobile interactions

5. **Performance**
   - Memoize when beneficial
   - Optimize event handlers
   - Lazy load complex features
   - Minimize re-renders

## Examples

### Simple Component

```typescript
export function CopyButton({ text, onCopy }: CopyButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    onCopy?.(text);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Button onClick={handleCopy}>
      {copied ? "Copied!" : "Copy"}
    </Button>
  );
}
```

### Complex Component

```typescript
export function DataTable<T>({
  data,
  columns,
  sorting,
  filtering,
}: DataTableProps<T>) {
  // Complex state management
  const table = useTable({ data, columns, sorting, filtering });

  // Rich interactive features
  return (
    <div className="data-table">
      <TableHeader {...table.header} />
      <TableBody {...table.body} />
      <TableFooter {...table.footer} />
    </div>
  );
}
```

## When to Create a Shared Component

Create a shared component when you have:

1. Functionality that's needed in multiple places
2. UI patterns that repeat across features
3. Complex behavior that should be encapsulated
4. Interactive elements that need consistent behavior
