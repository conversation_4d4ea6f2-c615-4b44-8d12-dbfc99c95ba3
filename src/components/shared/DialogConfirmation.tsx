import type { AlertDialogProps } from "@radix-ui/react-alert-dialog";
import type { PropsWithChildren } from "react";

import { useCallback, useState } from "react";
import { TrashIcon } from "lucide-react";

import type { ButtonProps } from "@/components/ui/button";

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    label: "Delete",
    actions: {
      cancel: "Cancel",
      delete: "Delete",
    },
  },
};

export interface DialogConfirmationProps
  extends PropsWithChildren<Pick<AlertDialogProps, "open" | "onOpenChange">>,
    Omit<ButtonProps, "onClick" | "onError"> {
  Icon?: React.ComponentType<{ className?: string }>;
  onClick: () => void | Promise<void>;
  onSuccess?: () => void | Promise<void>;
  onError?: (error: unknown) => void | Promise<void>;
  size?: ButtonProps["size"];
  className?: string;
  title?: string;
  description?: string;
  label?: string;
  action?: string;
  cancel?: string;
  useTrigger?: boolean;
}

export default function DialogConfirmation({
  size,
  className,
  variant = "destructive",
  title,
  description,
  label = i18n.en.label,
  action = i18n.en.actions.delete,
  cancel = i18n.en.actions.cancel,
  children,
  open,
  onOpenChange,
  Icon = TrashIcon,
  onClick,
  onSuccess,
  onError,
  useTrigger = true,
  ...props
}: DialogConfirmationProps) {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(open ?? false);

  const openChange = useCallback(
    function openChange(_open: boolean) {
      if (onOpenChange) {
        onOpenChange(_open);
      }
      setIsOpen(_open);
    },
    [onOpenChange],
  );

  return (
    <AlertDialog onOpenChange={openChange} open={open ?? isOpen}>
      {useTrigger && (
        <AlertDialogTrigger asChild>
          {children ?? (
            <Button
              variant={variant}
              size={size}
              className={cn("md:min-w-36", className)}
            >
              <Icon
                className={cn("md:mr-2", size === "sm" ? "size-3.5" : "size-5")}
              />
              <span className="sr-only font-semibold md:not-sr-only">
                {label}
              </span>
            </Button>
          )}
        </AlertDialogTrigger>
      )}

      <AlertDialogContent>
        <AlertDialogTitle>{title}</AlertDialogTitle>
        <AlertDialogDescription>{description}</AlertDialogDescription>
        <div className="flex w-full justify-end gap-4">
          <AlertDialogCancel disabled={isSubmitting}>
            {cancel}
          </AlertDialogCancel>
          <Button
            {...props}
            disabled={isSubmitting}
            variant={variant}
            onClick={useCallback(async () => {
              setIsSubmitting(true);
              try {
                await onClick();
                await onSuccess?.();
              } catch (error) {
                await onError?.(error);
              } finally {
                setIsSubmitting(false);
                openChange(false);
              }
            }, [onClick, openChange, onSuccess, onError])}
          >
            {action}
          </Button>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
