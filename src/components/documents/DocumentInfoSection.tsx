import DocumentExtractedFields from "@/components/documents/DocumentExtractedFields";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formatTimeStamp } from "@/lib/i18n";

interface DocumentInfoSectionProps {
  document: {
    id: string;
    name: string;
    type: string;
    content_type?: string | null;
    created_at: string;
    updated_at?: string;
    size?: number;
    metadata?: any;
  };
}

export default function DocumentInfoSection({
  document,
}: DocumentInfoSectionProps) {
  const documentCreatedDate = new Date(document.created_at);
  const documentUpdatedDate = document.updated_at
    ? new Date(document.updated_at)
    : null;

  // Extract fields from document metadata if available
  const extractedFields = document.metadata?.analysis
    ? typeof document.metadata.analysis === "string"
      ? tryParseJSON(document.metadata.analysis)?.extractedFields
      : document.metadata.analysis.extractedFields
    : null;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Document Information</CardTitle>
          <CardDescription>Details about this document</CardDescription>
        </CardHeader>
        <CardContent>
          <dl className="space-y-4">
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Type
              </dt>
              <dd className="text-sm">{document.type}</dd>
            </div>
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Format
              </dt>
              <dd className="text-sm">{document.content_type || "Unknown"}</dd>
            </div>
            {document.size && (
              <div>
                <dt className="text-muted-foreground text-sm font-medium">
                  Size
                </dt>
                <dd className="text-sm">{formatFileSize(document.size)}</dd>
              </div>
            )}
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Uploaded
              </dt>
              <dd className="text-sm">
                {formatTimeStamp(documentCreatedDate)}
              </dd>
            </div>
            {documentUpdatedDate && (
              <div>
                <dt className="text-muted-foreground text-sm font-medium">
                  Last Updated
                </dt>
                <dd className="text-sm">
                  {formatTimeStamp(documentUpdatedDate)}
                </dd>
              </div>
            )}
          </dl>
        </CardContent>
      </Card>

      {extractedFields && Object.keys(extractedFields).length > 0 && (
        <DocumentExtractedFields fields={extractedFields} />
      )}
    </div>
  );
}

// Helper function to safely parse JSON
function tryParseJSON(jsonString: string) {
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.error("Failed to parse document analysis", e);
    return null;
  }
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
