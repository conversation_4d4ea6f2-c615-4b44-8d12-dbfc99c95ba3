import { useEffect, useState } from "react";
import { <PERSON><PERSON>ex<PERSON>, Loader2 } from "lucide-react";
import { toast } from "sonner";

import { useBuildDocument } from "@/api/documents/use-build-document";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useIsMobile } from "@/hooks/use-mobile";
// Import templates
import { BillOfLadingForm } from "./templates";

interface CreateDocumentDialogProps {
  shipmentId?: string;
  driverId?: string;
  organizationId?: string;
  onDocumentCreated?: (document: any) => void;
  children?: React.ReactNode;
}

export default function CreateDocumentDialog({
  shipmentId,
  driverId,
  organizationId,
  onDocumentCreated,
  children,
}: CreateDocumentDialogProps) {
  const isMobile = useIsMobile();
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("bill_of_lading");
  const [title, setTitle] = useState("");

  // Template data state
  const [templateData, setTemplateData] = useState({
    shipper: {
      name: "",
      address: "",
      contact: "",
    },
    consignee: {
      name: "",
      address: "",
      contact: "",
    },
    carrier: {
      name: "",
      scac: "",
      contact: "",
    },
    shipment: {
      number: "",
      date: new Date().toISOString().split("T")[0],
      reference: "",
      specialInstructions: "",
    },
    items: [
      {
        description: "",
        weight: "",
        pieces: 1,
        packageType: "Box",
        hazmat: false,
      },
    ],
  });

  const { mutate: buildDocument, isPending } = useBuildDocument({
    onSuccess: (data) => {
      toast.success("Document created successfully");
      if (onDocumentCreated) {
        onDocumentCreated(data.document);
      }

      // Reset form and close dialog
      resetForm();
      setOpen(false);
    },
    onError: (error) => {
      toast.error(`Error creating document: ${error.message}`);
    },
  });

  // Reset the form when dialog is opened/closed
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const resetForm = () => {
    setTitle("");
    setActiveTab("bill_of_lading");
    setTemplateData({
      shipper: { name: "", address: "", contact: "" },
      consignee: { name: "", address: "", contact: "" },
      carrier: { name: "", scac: "", contact: "" },
      shipment: {
        number: "",
        date: new Date().toISOString().split("T")[0],
        reference: "",
        specialInstructions: "",
      },
      items: [
        {
          description: "",
          weight: "",
          pieces: 1,
          packageType: "Box",
          hazmat: false,
        },
      ],
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!title) {
      toast.error("Please provide a document title");
      return;
    }

    if (
      activeTab === "bill_of_lading" &&
      (!templateData.shipper.name || !templateData.consignee.name)
    ) {
      toast.error(
        "Please fill out all required fields (shipper and consignee)",
      );
      return;
    }

    const requestData: any = {
      title,
      documentType: activeTab,
      content: `Document for ${title}`,
      metadata: {
        shipmentId,
        driverId,
        organizationId,
        createdBy: "user",
      },
    };

    // Add template data based on document type
    if (activeTab === "bill_of_lading") {
      requestData.templateData = templateData;
    }

    buildDocument(requestData);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button>
            <FileText className="mr-2 h-4 w-4" /> Create Document
          </Button>
        )}
      </DialogTrigger>
      <DialogContent
        className={`${isMobile ? "h-[90vh] max-w-full rounded-t-lg" : "max-h-[90vh] sm:max-w-4xl"} overflow-y-auto`}
      >
        <DialogHeader>
          <DialogTitle>Create New Document</DialogTitle>
          <DialogDescription>
            Generate a new document from a template or create a custom document.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Document Title</Label>
            <Input
              id="title"
              placeholder="Enter document title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />
          </div>

          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="w-full"
          >
            <TabsList
              className={`grid ${isMobile ? "grid-cols-1" : "grid-cols-2"} mb-4 w-full`}
            >
              <TabsTrigger
                value="bill_of_lading"
                className="px-2 py-1.5 text-sm"
              >
                Bill of Lading
              </TabsTrigger>
              {/* Add more TabsTriggers here as we add more document types */}
            </TabsList>

            <TabsContent value="bill_of_lading" className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Shipper Information</h3>
                  <div className="space-y-2">
                    <Label htmlFor="shipper-name">Shipper Name *</Label>
                    <Input
                      id="shipper-name"
                      value={templateData.shipper.name}
                      onChange={(e) =>
                        setTemplateData({
                          ...templateData,
                          shipper: {
                            ...templateData.shipper,
                            name: e.target.value,
                          },
                        })
                      }
                      placeholder="Enter shipper name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shipper-address">Address</Label>
                    <Input
                      id="shipper-address"
                      value={templateData.shipper.address}
                      onChange={(e) =>
                        setTemplateData({
                          ...templateData,
                          shipper: {
                            ...templateData.shipper,
                            address: e.target.value,
                          },
                        })
                      }
                      placeholder="Enter shipper address"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shipper-contact">Contact</Label>
                    <Input
                      id="shipper-contact"
                      value={templateData.shipper.contact}
                      onChange={(e) =>
                        setTemplateData({
                          ...templateData,
                          shipper: {
                            ...templateData.shipper,
                            contact: e.target.value,
                          },
                        })
                      }
                      placeholder="Phone or email"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Consignee Information</h3>
                  <div className="space-y-2">
                    <Label htmlFor="consignee-name">Consignee Name *</Label>
                    <Input
                      id="consignee-name"
                      value={templateData.consignee.name}
                      onChange={(e) =>
                        setTemplateData({
                          ...templateData,
                          consignee: {
                            ...templateData.consignee,
                            name: e.target.value,
                          },
                        })
                      }
                      placeholder="Enter consignee name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consignee-address">Address</Label>
                    <Input
                      id="consignee-address"
                      value={templateData.consignee.address}
                      onChange={(e) =>
                        setTemplateData({
                          ...templateData,
                          consignee: {
                            ...templateData.consignee,
                            address: e.target.value,
                          },
                        })
                      }
                      placeholder="Enter consignee address"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consignee-contact">Contact</Label>
                    <Input
                      id="consignee-contact"
                      value={templateData.consignee.contact}
                      onChange={(e) =>
                        setTemplateData({
                          ...templateData,
                          consignee: {
                            ...templateData.consignee,
                            contact: e.target.value,
                          },
                        })
                      }
                      placeholder="Phone or email"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Add more TabsContent here as we add more document types */}
          </Tabs>

          <DialogFooter className="flex flex-col gap-2 sm:flex-row">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="w-full sm:w-auto"
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Document
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
