import { ArrowLeft, Download } from "lucide-react";
import { useNavigate } from "react-router";

import { DocumentMenu } from "@/components/actions/DocumentMenu";
import DocumentType from "@/components/common/DocumentType";
import { Button } from "@/components/ui/button";
import { formatTimeStamp } from "@/lib/i18n";

interface DocumentHeaderProps {
  document: {
    id: string;
    name: string;
    type: string;
    content_type?: string | null;
    url: string;
    created_at: string;
    metadata?: any;
    storage_path?: string;
  };
  onNavigateBack?: () => void;
  onSuccess?: () => void;
}

export default function DocumentHeader({
  document,
  onNavigateBack,
  onSuccess,
}: DocumentHeaderProps) {
  const navigate = useNavigate();
  const documentCreatedDate = new Date(document.created_at);

  const handleBack = () => {
    if (onNavigateBack) {
      onNavigateBack();
    } else {
      navigate("/app/drivers/documents");
    }
  };

  return (
    <div className="mb-6 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{document.name}</h1>
          <div className="text-muted-foreground flex items-center gap-2">
            <DocumentType type={document.content_type || ""} size={16} />
            <span>{document.type}</span>
            <span>•</span>
            <span>Uploaded {formatTimeStamp(documentCreatedDate)}</span>
          </div>
        </div>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" asChild>
          <a href={document.url} download target="_blank" rel="noreferrer">
            <Download className="mr-2 h-4 w-4" />
            Download
          </a>
        </Button>
        <DocumentMenu
          document={document}
          variant="outline"
          size="md"
          onSuccess={onSuccess}
        />
      </div>
    </div>
  );
}
