import ObjectDisplay from "@/components/shared/ObjectDisplay";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

interface DocumentExtractedFieldsProps {
  fields: Record<string, any>;
}

export default function DocumentExtractedFields({
  fields,
}: DocumentExtractedFieldsProps) {
  if (!fields || Object.keys(fields).length === 0) {
    return null;
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Extracted Fields</CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-4">
          {Object.entries(fields).map(([key, value]) => (
            <div key={key}>
              <dt className="text-muted-foreground text-sm font-medium capitalize">
                {key
                  .replace(/([A-Z])/g, " $1")
                  .replace(/_/g, " ")
                  .trim()}
              </dt>
              <dd className="text-sm break-words">
                <ObjectDisplay value={value} />
              </dd>
            </div>
          ))}
        </dl>
      </CardContent>
    </Card>
  );
}
