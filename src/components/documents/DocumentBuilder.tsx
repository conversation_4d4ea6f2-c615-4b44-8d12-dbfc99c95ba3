import { useState } from "react";
import { Check, Download, FileText, Loader2 } from "lucide-react";
import { toast } from "sonner";

import { useBuildDocument } from "@/api/documents/use-build-document";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
// Import templates correctly with named import
import { BillOfLadingForm } from "./templates/BillOfLadingForm";

interface DocumentBuilderProps {
  shipmentId?: string;
  driverId?: string;
  organizationId?: string;
  onDocumentCreated?: (document: any) => void;
}

export default function DocumentBuilder({
  shipmentId,
  driverId,
  organizationId,
  onDocumentCreated,
}: DocumentBuilderProps) {
  const [activeTab, setActiveTab] = useState<string>("general");
  const [title, setTitle] = useState("");
  const [documentType, setDocumentType] = useState<string>("general");
  const [content, setContent] = useState("");

  // For bill of lading template
  const [templateData, setTemplateData] = useState({
    shipper: {
      name: "",
      address: "",
      contact: "",
    },
    consignee: {
      name: "",
      address: "",
      contact: "",
    },
    carrier: {
      name: "",
      scac: "",
      contact: "",
    },
    shipment: {
      number: "",
      date: new Date().toISOString().split("T")[0],
      reference: "",
      specialInstructions: "",
    },
    items: [
      {
        description: "",
        weight: "",
        pieces: 1,
        packageType: "Box",
        hazmat: false,
      },
    ],
  });

  const { mutate: buildDocument, isPending } = useBuildDocument({
    onSuccess: (data) => {
      toast.success("Document created successfully");
      if (onDocumentCreated) {
        onDocumentCreated(data.document);
      }

      // Reset form
      setTitle("");
      setContent("");

      // Reset template data
      setTemplateData({
        shipper: { name: "", address: "", contact: "" },
        consignee: { name: "", address: "", contact: "" },
        carrier: { name: "", scac: "", contact: "" },
        shipment: {
          number: "",
          date: new Date().toISOString().split("T")[0],
          reference: "",
          specialInstructions: "",
        },
        items: [
          {
            description: "",
            weight: "",
            pieces: 1,
            packageType: "Box",
            hazmat: false,
          },
        ],
      });
    },
    onError: (error) => {
      toast.error(`Error creating document: ${error.message}`);
    },
  });

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setDocumentType(value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (documentType === "general" && (!title || !content)) {
      toast.error("Please fill out all required fields");
      return;
    }

    if (
      documentType === "bill_of_lading" &&
      (!title || !templateData.shipper.name || !templateData.consignee.name)
    ) {
      toast.error(
        "Please fill out all required fields (title, shipper, and consignee)",
      );
      return;
    }

    const requestData: any = {
      title,
      documentType,
      metadata: {
        shipmentId,
        driverId,
        organizationId,
        createdBy: "user",
      },
    };

    if (documentType === "general") {
      requestData.content = content;
    } else if (documentType === "bill_of_lading") {
      requestData.content = `Bill of Lading for ${templateData.shipper.name} to ${templateData.consignee.name}`;
      requestData.templateData = templateData;
    }

    buildDocument(requestData);
  };

  // Handle template data updates
  const updateTemplateData = (section: string, field: string, value: any) => {
    setTemplateData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [field]: value,
      },
    }));
  };

  // Add new item to bill of lading
  const addItem = () => {
    setTemplateData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          description: "",
          weight: "",
          pieces: 1,
          packageType: "Box",
          hazmat: false,
        },
      ],
    }));
  };

  // Update item in bill of lading
  const updateItem = (index: number, field: string, value: any) => {
    setTemplateData((prev) => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };
      return { ...prev, items: newItems };
    });
  };

  // Remove item from bill of lading
  const removeItem = (index: number) => {
    if (templateData.items.length <= 1) {
      toast.error("At least one item is required");
      return;
    }

    setTemplateData((prev) => {
      const newItems = [...prev.items];
      newItems.splice(index, 1);
      return { ...prev, items: newItems };
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create Document</CardTitle>
        <CardDescription>
          Generate a new document with your content
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Document Title</Label>
            <Input
              id="title"
              placeholder="Enter document title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />
          </div>

          <Tabs
            value={activeTab}
            onValueChange={handleTabChange}
            className="w-full"
          >
            <TabsList className="mb-4 grid grid-cols-2">
              <TabsTrigger value="general">General Document</TabsTrigger>
              <TabsTrigger value="bill_of_lading">Bill of Lading</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="content">Document Content</Label>
                <Textarea
                  id="content"
                  placeholder="Enter document content here..."
                  className="min-h-[200px]"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  required={documentType === "general"}
                />
              </div>
            </TabsContent>

            <TabsContent value="bill_of_lading" className="space-y-6">
              {/* Shipper Information */}
              <div className="space-y-3 rounded-md border p-4">
                <h3 className="text-lg font-semibold">Shipper Information</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="shipper-name">Name</Label>
                    <Input
                      id="shipper-name"
                      placeholder="Shipper name"
                      value={templateData.shipper.name}
                      onChange={(e) =>
                        updateTemplateData("shipper", "name", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shipper-contact">Contact</Label>
                    <Input
                      id="shipper-contact"
                      placeholder="Contact information"
                      value={templateData.shipper.contact}
                      onChange={(e) =>
                        updateTemplateData("shipper", "contact", e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="shipper-address">Address</Label>
                    <Textarea
                      id="shipper-address"
                      placeholder="Complete address"
                      value={templateData.shipper.address}
                      onChange={(e) =>
                        updateTemplateData("shipper", "address", e.target.value)
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Consignee Information */}
              <div className="space-y-3 rounded-md border p-4">
                <h3 className="text-lg font-semibold">Consignee Information</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="consignee-name">Name</Label>
                    <Input
                      id="consignee-name"
                      placeholder="Consignee name"
                      value={templateData.consignee.name}
                      onChange={(e) =>
                        updateTemplateData("consignee", "name", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="consignee-contact">Contact</Label>
                    <Input
                      id="consignee-contact"
                      placeholder="Contact information"
                      value={templateData.consignee.contact}
                      onChange={(e) =>
                        updateTemplateData(
                          "consignee",
                          "contact",
                          e.target.value,
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="consignee-address">Address</Label>
                    <Textarea
                      id="consignee-address"
                      placeholder="Complete address"
                      value={templateData.consignee.address}
                      onChange={(e) =>
                        updateTemplateData(
                          "consignee",
                          "address",
                          e.target.value,
                        )
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Carrier Information */}
              <div className="space-y-3 rounded-md border p-4">
                <h3 className="text-lg font-semibold">Carrier Information</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="carrier-name">Carrier Name</Label>
                    <Input
                      id="carrier-name"
                      placeholder="Carrier name"
                      value={templateData.carrier.name}
                      onChange={(e) =>
                        updateTemplateData("carrier", "name", e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="carrier-scac">SCAC</Label>
                    <Input
                      id="carrier-scac"
                      placeholder="SCAC code"
                      value={templateData.carrier.scac}
                      onChange={(e) =>
                        updateTemplateData("carrier", "scac", e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="carrier-contact">Contact</Label>
                    <Input
                      id="carrier-contact"
                      placeholder="Contact information"
                      value={templateData.carrier.contact}
                      onChange={(e) =>
                        updateTemplateData("carrier", "contact", e.target.value)
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Shipment Information */}
              <div className="space-y-3 rounded-md border p-4">
                <h3 className="text-lg font-semibold">Shipment Information</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="shipment-number">Shipment Number</Label>
                    <Input
                      id="shipment-number"
                      placeholder="Shipment #"
                      value={templateData.shipment.number}
                      onChange={(e) =>
                        updateTemplateData("shipment", "number", e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shipment-date">Date</Label>
                    <Input
                      id="shipment-date"
                      type="date"
                      value={templateData.shipment.date}
                      onChange={(e) =>
                        updateTemplateData("shipment", "date", e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shipment-reference">Reference</Label>
                    <Input
                      id="shipment-reference"
                      placeholder="Reference #"
                      value={templateData.shipment.reference}
                      onChange={(e) =>
                        updateTemplateData(
                          "shipment",
                          "reference",
                          e.target.value,
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shipment-instructions">
                      Special Instructions
                    </Label>
                    <Input
                      id="shipment-instructions"
                      placeholder="Special handling instructions"
                      value={templateData.shipment.specialInstructions}
                      onChange={(e) =>
                        updateTemplateData(
                          "shipment",
                          "specialInstructions",
                          e.target.value,
                        )
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Items */}
              <div className="space-y-3 rounded-md border p-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Items</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addItem}
                  >
                    Add Item
                  </Button>
                </div>

                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Description</TableHead>
                        <TableHead>Weight</TableHead>
                        <TableHead>Pieces</TableHead>
                        <TableHead>Package Type</TableHead>
                        <TableHead>Hazmat</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {templateData.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Input
                              placeholder="Description"
                              value={item.description}
                              onChange={(e) =>
                                updateItem(index, "description", e.target.value)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              placeholder="Weight"
                              value={item.weight}
                              onChange={(e) =>
                                updateItem(index, "weight", e.target.value)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="1"
                              value={item.pieces}
                              onChange={(e) =>
                                updateItem(
                                  index,
                                  "pieces",
                                  parseInt(e.target.value),
                                )
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <Select
                              value={item.packageType}
                              onValueChange={(value) =>
                                updateItem(index, "packageType", value)
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Box">Box</SelectItem>
                                <SelectItem value="Pallet">Pallet</SelectItem>
                                <SelectItem value="Crate">Crate</SelectItem>
                                <SelectItem value="Drum">Drum</SelectItem>
                                <SelectItem value="Bundle">Bundle</SelectItem>
                                <SelectItem value="Bag">Bag</SelectItem>
                                <SelectItem value="Carton">Carton</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <Select
                              value={item.hazmat ? "yes" : "no"}
                              onValueChange={(value) =>
                                updateItem(index, "hazmat", value === "yes")
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="yes">Yes</SelectItem>
                                <SelectItem value="no">No</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeItem(index)}
                              disabled={templateData.items.length <= 1}
                            >
                              Remove
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end pt-4">
            <Button type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Document
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
