import { BillOfLadingForm } from "./BillOfLadingForm";

// Document templates for rendering processed documents
export { DocumentTemplate } from "./DocumentTemplate";
export {
  BillOfLadingTemplate,
  billOfLadingSchema,
} from "./BillOfLadingTemplate";
export {
  DeliveryOrderTemplate,
  deliveryOrderSchema,
} from "./DeliveryOrderTemplate";
export { FuelReceiptTemplate, fuelReceiptSchema } from "./FuelReceiptTemplate";
export {
  ProofOfDeliveryTemplate,
  proofOfDeliverySchema,
} from "./ProofOfDeliveryTemplate";
export {
  TemperatureLogTemplate,
  temperatureLogSchema,
  createSampleFoodTransportData,
} from "./TemperatureLogTemplate";
export {
  WeightTicketTemplate,
  weightTicketSchema,
} from "./WeightTicketTemplate";
export {
  DocumentTemplateRouter,
  getSupportedDocumentTypes,
} from "./DocumentTemplateRouter";

// Types
export type {
  ProcessedDocumentData,
  DocumentSchema,
  FieldDefinition,
  UISection,
} from "./DocumentTemplate";

// Template forms for data entry
export {
  BillOfLadingForm,
  // Add more form exports as we create more template components
};
