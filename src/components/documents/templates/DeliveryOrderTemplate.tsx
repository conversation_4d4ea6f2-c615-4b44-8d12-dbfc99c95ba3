import React from "react";
import {
  <PERSON><PERSON>,
  CheckCircle,
  FileText,
  MapPin,
  Package,
  Shield,
  Truck,
} from "lucide-react";

import type { DocumentSchema, ProcessedDocumentData } from "./DocumentTemplate";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DocumentTemplate } from "./DocumentTemplate";

// Delivery Order specific schema (aligned with backend schema)
const DELIVERY_ORDER_SCHEMA: DocumentSchema = {
  documentType: "delivery_order",
  category: "transportation",
  subtype: "standard",
  version: "1.0.0",
  description:
    "Delivery Order - instruction from carrier/agent to release cargo to consignee",
  fields: [
    // Document identifier and basic info
    {
      key: "document_number",
      label: "Document Number",
      type: "string",
      description: "Primary document identification number",
    },
    {
      key: "document_date",
      label: "Document Date",
      type: "date",
      description: "Date the document was created or issued",
    },
    {
      key: "bol_reference",
      label: "BOL Reference",
      type: "string",
      description: "Reference to original Bill of Lading",
    },
    {
      key: "issuing_agent",
      label: "Issuing Agent",
      type: "string",
      description: "Carrier or agent issuing the delivery order",
    },
    {
      key: "issuing_agent_address",
      label: "Issuing Agent Address",
      type: "address",
      description: "Address of the issuing agent or carrier",
    },

    // Collection/Pickup Information
    {
      key: "collection_address",
      label: "Collection Address",
      type: "address",
      description: "Address where cargo is collected/picked up from",
    },
    {
      key: "collection_location_details",
      label: "Collection Location Details",
      type: "string",
      description:
        "Additional details about collection location (terminal, etc.)",
    },

    // Consignee/recipient information
    {
      key: "consignee_company",
      label: "Consignee Company",
      type: "string",
      description: "Company authorized to receive cargo",
    },
    {
      key: "consignee_address",
      label: "Consignee Address",
      type: "address",
      description: "Complete address for consignee",
    },
    {
      key: "notify_party",
      label: "Notify Party",
      type: "string",
      description: "Party to be notified of cargo arrival",
    },

    // Hauler/Transporter Information
    {
      key: "hauler_name",
      label: "Hauler Name",
      type: "string",
      description: "Name of hauler/transporter company",
    },
    {
      key: "hauler_address",
      label: "Hauler Address",
      type: "address",
      description: "Address of the hauler/transporter",
    },

    // Cargo information
    {
      key: "cargo_description",
      label: "Cargo Description",
      type: "string",
      description: "Description of cargo to be released",
    },
    {
      key: "cargo_weight",
      label: "Cargo Weight",
      type: "weight",
      description: "Weight of the cargo (with unit)",
    },
    {
      key: "cargo_measurements",
      label: "Cargo Measurements",
      type: "string",
      description: "Dimensions and measurements of cargo",
    },

    // Container/Equipment information
    {
      key: "container_numbers",
      label: "Container Numbers",
      type: "string",
      description: "Container or equipment numbers",
    },
    {
      key: "seal_numbers",
      label: "Seal Numbers",
      type: "string",
      description: "Seal numbers on containers",
    },
    {
      key: "equipment_reference",
      label: "Equipment Reference",
      type: "string",
      description: "Equipment reference or ACLU number",
    },

    // Vessel/voyage information
    {
      key: "vessel_name",
      label: "Vessel Name",
      type: "string",
      description: "Name of vessel carrying the cargo",
    },
    {
      key: "voyage_number",
      label: "Voyage Number",
      type: "string",
      description: "Voyage or trip number",
    },
    {
      key: "pod_origin",
      label: "Port of Discharge/Origin",
      type: "string",
      description: "Port where cargo was discharged or originated from",
    },
    {
      key: "import_details",
      label: "Import Details",
      type: "string",
      description: "Import service and voyage details",
    },

    // Delivery information
    {
      key: "delivery_location",
      label: "Delivery Location",
      type: "string",
      description: "Location where cargo should be delivered",
    },
    {
      key: "delivery_date_time",
      label: "Delivery Date/Time",
      type: "string",
      description: "Scheduled delivery date and time",
    },

    // Customs and regulatory information
    {
      key: "customs_clearance",
      label: "Customs Clearance",
      type: "string",
      description: "Customs clearance status or number",
    },

    // Special instructions and conditions
    {
      key: "release_conditions",
      label: "Release Conditions",
      type: "string",
      description: "Conditions for cargo release",
    },
    {
      key: "special_instructions",
      label: "Special Instructions",
      type: "string",
      description: "Special handling or delivery instructions",
    },
    {
      key: "trucking_charges_note",
      label: "Trucking Charges Note",
      type: "string",
      description: "Information about trucking charges",
    },

    // Authorization
    {
      key: "authorized_by",
      label: "Authorized By",
      type: "string",
      description: "Person authorizing the release",
    },
    {
      key: "release_date",
      label: "Release Date",
      type: "date",
      description: "Date cargo is authorized for release",
    },
  ],
  requiredFields: [
    "document_number",
    "document_date",
    "bol_reference",
    "issuing_agent",
    "consignee_company",
    "cargo_description",
    "delivery_location",
  ],
  optionalFields: [
    "issuing_agent_address",
    "collection_address",
    "collection_location_details",
    "consignee_address",
    "notify_party",
    "hauler_name",
    "hauler_address",
    "cargo_weight",
    "cargo_measurements",
    "container_numbers",
    "seal_numbers",
    "equipment_reference",
    "vessel_name",
    "voyage_number",
    "pod_origin",
    "import_details",
    "delivery_date_time",
    "customs_clearance",
    "release_conditions",
    "special_instructions",
    "trucking_charges_note",
    "authorized_by",
    "release_date",
  ],
  uiMapping: {
    layout: "delivery_order_layout",
    sections: [
      {
        name: "document_header",
        label: "Document Information",
        fields: [
          "document_number",
          "document_date",
          "bol_reference",
          "issuing_agent",
        ],
        order: 1,
      },
      {
        name: "collection_info",
        label: "Collection/Pickup Details",
        fields: ["collection_address", "collection_location_details"],
        order: 2,
      },
      {
        name: "delivery_info",
        label: "Delivery Information",
        fields: [
          "consignee_company",
          "consignee_address",
          "delivery_location",
          "delivery_date_time",
          "notify_party",
        ],
        order: 3,
      },
      {
        name: "hauler_info",
        label: "Hauler/Transporter",
        fields: ["hauler_name", "hauler_address"],
        order: 4,
      },
      {
        name: "cargo_details",
        label: "Cargo Information",
        fields: [
          "cargo_description",
          "cargo_weight",
          "cargo_measurements",
          "container_numbers",
          "seal_numbers",
        ],
        order: 5,
      },
      {
        name: "vessel_info",
        label: "Vessel & Voyage",
        fields: [
          "vessel_name",
          "voyage_number",
          "pod_origin",
          "import_details",
        ],
        order: 6,
      },
      {
        name: "customs_regulatory",
        label: "Customs & Regulatory",
        fields: ["customs_clearance", "equipment_reference"],
        order: 7,
      },
      {
        name: "authorization",
        label: "Authorization & Release",
        fields: ["authorized_by", "release_date", "trucking_charges_note"],
        order: 8,
      },
      {
        name: "conditions_instructions",
        label: "Conditions & Instructions",
        fields: ["release_conditions", "special_instructions"],
        collapsible: true,
        order: 9,
      },
    ],
  },
};

interface DeliveryOrderTemplateProps {
  documentData: ProcessedDocumentData;
  className?: string;
  showQuickSummary?: boolean;
}

export function DeliveryOrderTemplate({
  documentData,
  className = "",
  showQuickSummary = true,
}: DeliveryOrderTemplateProps) {
  const { extractedData } = documentData;

  // Enhanced quick summary component for Delivery Order highlights
  const QuickSummary = () => {
    const doNumber = extractedData.document_number;
    const bolRef = extractedData.bol_reference;
    const consignee = extractedData.consignee_company;
    const issuer = extractedData.issuing_agent;
    const hauler = extractedData.hauler_name;
    const containerNumbers = extractedData.container_numbers;
    const cargoDescription = extractedData.cargo_description;
    const deliveryLocation = extractedData.delivery_location;
    const collectionAddress = extractedData.collection_address;
    const customsClearance = extractedData.customs_clearance;

    return (
      <Card className="mb-6 border-green-200 bg-green-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg text-green-800">
            <Truck className="h-5 w-5" />
            Cargo Release Authorization
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Primary document information */}
          <div className="grid gap-4 md:grid-cols-3">
            {/* DO Number */}
            <div className="flex items-center gap-3">
              <FileText className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">DO Number</div>
                <div className="text-muted-foreground text-sm">
                  {String(doNumber) || "N/A"}
                </div>
              </div>
            </div>

            {/* BOL Reference */}
            <div className="flex items-center gap-3">
              <Shield className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">BOL Reference</div>
                <div className="text-muted-foreground text-sm">
                  {String(bolRef) || "N/A"}
                </div>
              </div>
            </div>

            {/* Issuing Agent */}
            <div className="flex items-center gap-3">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">Issued By</div>
                <div className="text-muted-foreground text-sm">
                  {String(issuer) || "N/A"}
                </div>
              </div>
            </div>
          </div>

          {/* Address flow information */}
          {(collectionAddress || deliveryLocation) && (
            <div className="mt-4 border-t border-green-200 pt-4">
              <div className="grid gap-4 md:grid-cols-2">
                {collectionAddress && (
                  <div className="flex items-start gap-3">
                    <MapPin className="mt-1 h-4 w-4 text-green-600" />
                    <div>
                      <div className="text-xs tracking-wide text-green-600 uppercase">
                        Collection From
                      </div>
                      <div className="text-sm font-medium text-green-800">
                        {String(collectionAddress).split("\n")[0]}
                      </div>
                    </div>
                  </div>
                )}

                {deliveryLocation && (
                  <div className="flex items-start gap-3">
                    <MapPin className="mt-1 h-4 w-4 text-green-600" />
                    <div>
                      <div className="text-xs tracking-wide text-green-600 uppercase">
                        Delivery To
                      </div>
                      <div className="text-sm font-medium text-green-800">
                        {String(deliveryLocation)}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Key cargo and operational details */}
          <div className="mt-4 border-t border-green-200 pt-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Consignee */}
              {consignee && (
                <div>
                  <div className="text-xs tracking-wide text-green-600 uppercase">
                    Consignee
                  </div>
                  <div className="text-lg font-bold text-green-800">
                    {String(consignee)}
                  </div>
                </div>
              )}

              {/* Hauler */}
              {hauler && (
                <div>
                  <div className="text-xs tracking-wide text-green-600 uppercase">
                    Hauler/Transporter
                  </div>
                  <div className="text-lg font-bold text-green-800">
                    {String(hauler)}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Container and cargo information */}
          {(containerNumbers || cargoDescription) && (
            <div className="mt-4 border-t border-green-200 pt-4">
              <div className="grid gap-4 md:grid-cols-2">
                {containerNumbers && (
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Container:</span>
                    <Badge variant="outline" className="border-green-400">
                      {String(containerNumbers)}
                    </Badge>
                  </div>
                )}

                {cargoDescription && (
                  <div>
                    <span className="text-sm font-medium">Cargo: </span>
                    <span className="text-sm">
                      {String(cargoDescription).length > 50
                        ? `${String(cargoDescription).substring(0, 50)}...`
                        : String(cargoDescription)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Customs and regulatory status */}
          {customsClearance && (
            <div className="mt-4 border-t border-green-200 pt-4">
              <div className="flex items-center gap-2">
                <Anchor className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Customs Status:</span>
                <Badge variant="outline" className="border-green-400">
                  {String(customsClearance)}
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={className}>
      {showQuickSummary && <QuickSummary />}

      <DocumentTemplate
        documentData={documentData}
        schema={DELIVERY_ORDER_SCHEMA}
      />
    </div>
  );
}

// Export for use in document routing
export const deliveryOrderSchema = DELIVERY_ORDER_SCHEMA;
