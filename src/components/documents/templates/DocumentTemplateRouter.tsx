import React from "react";
import { <PERSON><PERSON><PERSON>riangle, FileText, Package, Shield, Truck } from "lucide-react";

import type { ProcessedDocumentData } from "./DocumentTemplate";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { BillOfLadingTemplate } from "./BillOfLadingTemplate";
import { DeliveryOrderTemplate } from "./DeliveryOrderTemplate";
import { DocumentTemplate } from "./DocumentTemplate";
import { FuelReceiptTemplate } from "./FuelReceiptTemplate";
import { ProofOfDeliveryTemplate } from "./ProofOfDeliveryTemplate";
import { TemperatureLogTemplate } from "./TemperatureLogTemplate";
import { WeightTicketTemplate } from "./WeightTicketTemplate";

interface DocumentTemplateRouterProps {
  documentData: ProcessedDocumentData;
  className?: string;
  fallbackToGeneric?: boolean;
}

/**
 * Smart router that automatically selects the appropriate document template
 * based on the document type and subtype from the processed data.
 * Supports both traditional documents and physical items (seals, placards, etc.)
 */
export function DocumentTemplateRouter({
  documentData,
  className = "",
  fallbackToGeneric = true,
}: DocumentTemplateRouterProps) {
  console.log("DocumentTemplateRouter received documentData:", documentData);

  const { documentClass, subtype, processingType, itemCategory } = documentData;

  // Determine if this is a physical item vs document
  const isPhysicalItem =
    itemCategory === "physical_item" || isPhysicalItemType(documentClass);

  // Generate a routing key for template selection
  const getRoutingKey = () => {
    if (!documentClass) return "unknown";

    const canonical = getCanonicalDocumentType(documentClass);
    if (subtype) {
      return `${canonical}.${subtype}`;
    }
    return canonical;
  };

  // Add debugging
  const routingKey = getRoutingKey();
  const hasSpecialized = hasSpecializedTemplate(documentClass, subtype);

  console.log("DocumentTemplateRouter Debug:", {
    originalDocumentClass: documentClass,
    canonicalType: documentClass
      ? getCanonicalDocumentType(documentClass)
      : undefined,
    subtype,
    processingType,
    itemCategory,
    isPhysicalItem,
    routingKey,
    hasSpecializedTemplate: hasSpecialized,
    willUseGeneric: !hasSpecialized && fallbackToGeneric,
    willRenderSpecialized: hasSpecialized,
    templateChoice: hasSpecialized
      ? `Specialized template for ${getCanonicalDocumentType(documentClass || "")}`
      : fallbackToGeneric
        ? "Generic template (fallback)"
        : "No template",
  });

  // Route to specific template based on document type
  const renderSpecializedTemplate = () => {
    if (!documentClass) {
      console.log("No documentClass provided, using generic template");
      return null;
    }

    const canonical = getCanonicalDocumentType(documentClass);
    console.log(`Routing to specialized template for: ${canonical}`);

    switch (canonical) {
      case "bill_of_lading":
        return <BillOfLadingTemplate documentData={documentData} />;
      case "delivery_order":
        return <DeliveryOrderTemplate documentData={documentData} />;
      case "proof_of_delivery":
        return <ProofOfDeliveryTemplate documentData={documentData} />;
      case "fuel_receipt":
        return <FuelReceiptTemplate documentData={documentData} />;
      case "temperature_log":
        return <TemperatureLogTemplate documentData={documentData} />;
      case "weight_ticket":
        return <WeightTicketTemplate documentData={documentData} />;
      case "container_seal":
      case "trailer_seal":
        return <SealTemplate documentData={documentData} />;
      case "dot_number_placard":
      case "mc_number_placard":
        return <RegulatoryPlacardTemplate documentData={documentData} />;
      case "vehicle_id_plate":
      case "license_plate":
      case "container_number_plate":
        return <IdentificationPlateTemplate documentData={documentData} />;
      default:
        console.log(`No specialized template found for: ${canonical}`);
        return null;
    }
  };

  // Try to render specialized template first
  const specializedTemplate = renderSpecializedTemplate();

  if (specializedTemplate) {
    return specializedTemplate;
  }

  // Fallback to generic template if no specialized template exists
  if (fallbackToGeneric) {
    return (
      <div className={className}>
        {/* Show notice about template type and item category */}
        <Alert className="mb-6 border-amber-200 bg-amber-50">
          <div className="flex items-center gap-2">
            {isPhysicalItem ? (
              <Package className="h-4 w-4" />
            ) : (
              <FileText className="h-4 w-4" />
            )}
            <AlertTriangle className="h-4 w-4" />
          </div>
          <AlertDescription className="text-amber-800">
            <div className="mb-2 flex items-center gap-2">
              <strong>Generic Template:</strong>
              <Badge variant="outline" className="text-xs">
                {isPhysicalItem ? "Physical Item" : "Document"}
              </Badge>
              {documentClass && (
                <Badge variant="secondary" className="text-xs">
                  {documentClass}
                </Badge>
              )}
            </div>
            No specialized template available for "{documentClass}"
            {subtype && ` (${subtype})`}. Using enhanced generic renderer
            {isPhysicalItem && " optimized for physical items"}.
            {processingType === "legacy" &&
              " Consider creating a schema for enhanced processing."}
          </AlertDescription>
        </Alert>

        <DocumentTemplate
          documentData={documentData}
          isPhysicalItem={isPhysicalItem}
        />
      </div>
    );
  }

  // If no fallback, show error message
  return (
    <div className={className}>
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-red-800">
          <strong>Template Not Found:</strong> No template available for
          {isPhysicalItem ? " physical item" : " document"} type "
          {documentClass}"{subtype && ` with subtype "${subtype}"`}.
        </AlertDescription>
      </Alert>
    </div>
  );
}

// Document type mapping - handle similar document types but keep new ones separate
const getCanonicalDocumentType = (documentClass: string): string => {
  const mappings: Record<string, string> = {
    // Bill of Lading variations (keep delivery_order separate now)
    shipping_order: "bill_of_lading", // Only some shipping orders map to BOL
    ocean_bill_of_lading: "bill_of_lading",
    house_bill_of_lading: "bill_of_lading",
    master_bill_of_lading: "bill_of_lading",
    sea_waybill: "bill_of_lading",
    bol: "bill_of_lading",
    b_l: "bill_of_lading",

    // Delivery Order variations (keep separate from BOL)
    do: "delivery_order",
    cargo_release: "delivery_order",
    release_order: "delivery_order",

    // Proof of Delivery variations
    pod: "proof_of_delivery",
    delivery_receipt: "proof_of_delivery",
    delivery_confirmation: "proof_of_delivery",
    signed_receipt: "proof_of_delivery",

    // Fuel receipt variations
    fuel_bill: "fuel_receipt",
    gas_receipt: "fuel_receipt",
    diesel_receipt: "fuel_receipt",
    bunker_receipt: "fuel_receipt",

    // Temperature log variations
    temp_log: "temperature_log",
    temperature_recorder: "temperature_log",
    cold_chain_log: "temperature_log",
    reefer_log: "temperature_log",

    // Weight ticket variations
    scale_ticket: "weight_ticket",
    weigh_ticket: "weight_ticket",
    weight_certificate: "weight_ticket",
    certified_weight: "weight_ticket",

    // Seal variations
    seal: "container_seal",
    security_seal: "container_seal",
    bolt_seal: "container_seal",
    plastic_seal: "container_seal",
    metal_seal: "container_seal",

    // DOT/MC variations
    usdot: "dot_number_placard",
    dot_number: "dot_number_placard",
    mc_number: "mc_number_placard",
    motor_carrier: "mc_number_placard",

    // Vehicle/container ID variations
    unit_number: "vehicle_id_plate",
    truck_number: "vehicle_id_plate",
    trailer_number: "vehicle_id_plate",
    container_id: "container_number_plate",
    container_number: "container_number_plate",
  };

  const canonical = mappings[documentClass.toLowerCase()];
  console.log(
    `Document type mapping: ${documentClass} → ${canonical || documentClass}`,
  );
  return canonical || documentClass;
};

// Determine if a document type represents a physical item
function isPhysicalItemType(documentClass?: string): boolean {
  if (!documentClass) return false;

  const physicalItemTypes = [
    "container_seal",
    "trailer_seal",
    "seal",
    "dot_number_placard",
    "mc_number_placard",
    "vehicle_id_plate",
    "license_plate",
    "container_number_plate",
    "usdot",
    "dot_number",
    "mc_number",
    "unit_number",
    "truck_number",
  ];

  return physicalItemTypes.some(
    (type) =>
      documentClass.toLowerCase().includes(type) ||
      type.includes(documentClass.toLowerCase()),
  );
}

function hasSpecializedTemplate(
  documentClass?: string,
  subtype?: string,
): boolean {
  if (!documentClass) return false;

  const canonical = getCanonicalDocumentType(documentClass);

  // Currently implemented specialized templates
  const specializedTemplates = [
    "bill_of_lading",
    "delivery_order",
    "proof_of_delivery",
    "fuel_receipt",
    "temperature_log",
    "weight_ticket",
    // TODO: Add these physical item templates as we implement them
    // "container_seal",
    // "dot_number_placard",
    // "mc_number_placard",
    // "vehicle_id_plate",
  ];

  const hasTemplate = specializedTemplates.includes(canonical);
  console.log(`Checking specialized template for ${canonical}: ${hasTemplate}`);

  return hasTemplate;
}

// Physical item placeholder components (to be implemented in Phase 1B)
// These use the generic DocumentTemplate with hints for now

const SealTemplate = ({
  documentData,
}: {
  documentData: ProcessedDocumentData;
}) => (
  <DocumentTemplate
    documentData={documentData}
    templateHint="physical_seal"
    isPhysicalItem={true}
  />
);

const RegulatoryPlacardTemplate = ({
  documentData,
}: {
  documentData: ProcessedDocumentData;
}) => (
  <DocumentTemplate
    documentData={documentData}
    templateHint="regulatory_placard"
    isPhysicalItem={true}
  />
);

const IdentificationPlateTemplate = ({
  documentData,
}: {
  documentData: ProcessedDocumentData;
}) => (
  <DocumentTemplate
    documentData={documentData}
    templateHint="identification_plate"
    isPhysicalItem={true}
  />
);

/**
 * Get a list of all supported document types with their templates
 */
export function getSupportedDocumentTypes(): Array<{
  documentClass: string;
  subtype?: string;
  displayName: string;
  description: string;
  category: "document" | "physical_item";
}> {
  return [
    // Specialized Templates (fully implemented)
    {
      documentClass: "bill_of_lading",
      subtype: "standard",
      displayName: "Bill of Lading",
      description: "Legal contract between shipper and carrier",
      category: "document",
    },
    {
      documentClass: "fuel_receipt",
      subtype: "standard",
      displayName: "Fuel Receipt",
      description: "Fuel purchase receipt for expense tracking",
      category: "document",
    },

    // Enhanced Generic Templates (with intelligent routing)
    {
      documentClass: "delivery_order",
      displayName: "Delivery Order",
      description: "Authorization for cargo release to consignee",
      category: "document",
    },
    {
      documentClass: "proof_of_delivery",
      displayName: "Proof of Delivery",
      description: "Confirmation document that cargo was delivered",
      category: "document",
    },
    {
      documentClass: "temperature_log",
      displayName: "Temperature Log",
      description: "Cold chain monitoring record",
      category: "document",
    },
    {
      documentClass: "weight_ticket",
      displayName: "Weight Ticket",
      description: "Official vehicle/cargo weight documentation",
      category: "document",
    },

    // Physical Items
    {
      documentClass: "container_seal",
      displayName: "Container Seal",
      description: "Security seal for container/trailer doors",
      category: "physical_item",
    },
    {
      documentClass: "dot_number_placard",
      displayName: "DOT Number Placard",
      description: "US DOT registration number on vehicle",
      category: "physical_item",
    },
    {
      documentClass: "mc_number_placard",
      displayName: "MC Number Placard",
      description: "Motor Carrier authority number on vehicle",
      category: "physical_item",
    },
    {
      documentClass: "vehicle_id_plate",
      displayName: "Vehicle ID Plate",
      description: "Truck/trailer identification plate or sticker",
      category: "physical_item",
    },
  ];
}
