import React from "react";

import type { ProcessedDocumentData } from ".";

import { BillOfLadingTemplate } from ".";

const mockBillOfLading: ProcessedDocumentData = {
  documentClass: "bill_of_lading",
  processingType: "schema-driven",
  schemaVersion: "1.0.0",
  extractedData: {
    document_number: "BOL-123456",
    document_date: "2024-06-22",
    shipper_company: "Acme Shippers Inc.",
    shipper_address: "123 Main St, Springfield, IL",
    shipper_contact: "John <PERSON>",
    consignee_company: "Widgets Unlimited",
    consignee_address: "456 Elm St, Shelbyville, IL",
    carrier_company: "FastFreight LLC",
    carrier_scac: "FFLT",
    origin_city: "Springfield",
    destination_city: "Shelbyville",
    commodity_description: "Industrial widgets",
    total_pieces: 10,
    total_weight: 2000,
    freight_class: "50",
    freight_charges: 1500.0,
    hazmat_flag: false,
    special_instructions: "Handle with care.",
  },
};

export default {
  title: "Documents/Templates/BillOfLadingTemplate",
  component: BillOfLadingTemplate,
};

export const Default = () => (
  <BillOfLadingTemplate documentData={mockBillOfLading} />
);
