import React from "react";
import {
  Activity,
  AlertTriangle,
  Clock,
  Package,
  Shield,
  Thermometer,
  Truck,
} from "lucide-react";

import type { DocumentSchema, ProcessedDocumentData } from "./DocumentTemplate";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DocumentTemplate } from "./DocumentTemplate";

// Temperature Log specific schema (matches our backend schema v1.1.0)
const TEMPERATURE_LOG_SCHEMA: DocumentSchema = {
  documentType: "temperature_log",
  category: "regulatory",
  subtype: "standard",
  version: "1.1.0",
  description:
    "Temperature Log/Recorder - monitoring document for temperature-sensitive cargo during transport, supporting multi-zone and time-series temperature tracking",
  fields: [
    {
      key: "document_number",
      label: "Log Number",
      type: "string",
      description: "Temperature log identification number",
    },
    {
      key: "document_date",
      label: "Log Date",
      type: "date",
      description: "Date the temperature log was created",
    },
    {
      key: "recorder_id",
      label: "Recorder ID",
      type: "string",
      description: "Temperature recording device identifier",
    },
    {
      key: "equipment_type",
      label: "Equipment Type",
      type: "string",
      description: "Type of refrigeration equipment",
    },
    {
      key: "container_number",
      label: "Container Number",
      type: "string",
      description: "Container or trailer number",
    },
    {
      key: "bol_reference",
      label: "BOL Reference",
      type: "string",
      description: "Reference to associated Bill of Lading",
    },
    {
      key: "commodity",
      label: "Commodity",
      type: "string",
      description: "Description of temperature-sensitive goods",
    },
    {
      key: "set_point_temp",
      label: "Set Point Temperature",
      type: "string",
      description: "Target temperature setting",
    },
    {
      key: "temperature_unit",
      label: "Temperature Unit",
      type: "string",
      description: "Temperature measurement unit (F/C)",
    },
    // Multi-row data fields
    {
      key: "temperature_readings",
      label: "Temperature Readings",
      type: "array",
      description: "Array of individual temperature readings with timestamps",
    },
    {
      key: "monitoring_zones",
      label: "Monitoring Zones",
      type: "array",
      description:
        "Different temperature zones or compartments being monitored",
    },
    {
      key: "monitored_items",
      label: "Monitored Items",
      type: "array",
      description: "Individual items or containers being temperature monitored",
    },
    {
      key: "temperature_summary",
      label: "Temperature Summary",
      type: "object",
      description: "Aggregated temperature statistics from all readings",
    },
    {
      key: "alarm_events",
      label: "Alarm Events",
      type: "array",
      description: "Individual alarm events with timestamps and details",
    },
    // Existing single aggregate readings
    {
      key: "min_temp_recorded",
      label: "Minimum Temperature",
      type: "string",
      description: "Lowest temperature recorded during transport",
    },
    {
      key: "max_temp_recorded",
      label: "Maximum Temperature",
      type: "string",
      description: "Highest temperature recorded during transport",
    },
    {
      key: "avg_temp_recorded",
      label: "Average Temperature",
      type: "string",
      description: "Average temperature during transport",
    },
    {
      key: "start_date_time",
      label: "Start Date/Time",
      type: "string",
      description: "Start of temperature monitoring period",
    },
    {
      key: "end_date_time",
      label: "End Date/Time",
      type: "string",
      description: "End of temperature monitoring period",
    },
    {
      key: "duration",
      label: "Duration",
      type: "string",
      description: "Total monitoring duration",
    },
    {
      key: "recording_interval",
      label: "Recording Interval",
      type: "string",
      description: "Time interval between temperature readings",
    },
    {
      key: "alarms_count",
      label: "Number of Alarms",
      type: "number",
      description: "Total number of temperature alarms",
    },
    {
      key: "excursion_details",
      label: "Excursion Details",
      type: "string",
      description: "Details of any temperature excursions",
    },
    {
      key: "origin_location",
      label: "Origin Location",
      type: "string",
      description: "Starting location for temperature monitoring",
    },
    {
      key: "destination_location",
      label: "Destination Location",
      type: "string",
      description: "Ending location for temperature monitoring",
    },
    {
      key: "compliance_status",
      label: "Compliance Status",
      type: "boolean",
      description: "Whether temperature was maintained within requirements",
    },
    {
      key: "certified_by",
      label: "Certified By",
      type: "string",
      description: "Person or organization certifying the temperature log",
    },
    {
      key: "notes",
      label: "Notes",
      type: "string",
      description: "Additional notes or observations",
    },
    // Transport and logistics information
    {
      key: "shipper",
      label: "Shipper",
      type: "string",
      description: "Company or entity shipping the goods",
    },
    {
      key: "consignee",
      label: "Consignee",
      type: "string",
      description: "Company or entity receiving the goods",
    },
    {
      key: "driver_signature",
      label: "Driver Signature",
      type: "string",
      description: "Driver signature or name",
    },
    {
      key: "delivery_time",
      label: "Delivery Time",
      type: "string",
      description: "Date and time of delivery",
    },
  ],
  requiredFields: [
    "document_number",
    "document_date",
    "recorder_id",
    "commodity",
    "set_point_temp",
    "start_date_time",
    "end_date_time",
  ],
  optionalFields: [
    "equipment_type",
    "container_number",
    "bol_reference",
    "temperature_unit",
    "temperature_readings",
    "monitoring_zones",
    "monitored_items",
    "temperature_summary",
    "alarm_events",
    "min_temp_recorded",
    "max_temp_recorded",
    "avg_temp_recorded",
    "duration",
    "recording_interval",
    "alarms_count",
    "excursion_details",
    "origin_location",
    "destination_location",
    "compliance_status",
    "certified_by",
    "notes",
    "shipper",
    "consignee",
    "driver_signature",
    "delivery_time",
  ],
  uiMapping: {
    layout: "temperature_log_layout",
    sections: [
      {
        name: "document_info",
        label: "Document Information",
        fields: ["document_number", "document_date", "bol_reference"],
        order: 1,
      },
      {
        name: "transport_info",
        label: "Transport Information",
        fields: ["shipper", "consignee", "driver_signature", "delivery_time"],
        order: 2,
      },
      {
        name: "equipment",
        label: "Equipment & Container",
        fields: ["recorder_id", "equipment_type", "container_number"],
        order: 3,
      },
      {
        name: "commodity",
        label: "Commodity",
        fields: ["commodity"],
        order: 4,
      },
      {
        name: "temperature_settings",
        label: "Temperature Settings",
        fields: ["set_point_temp", "temperature_unit", "recording_interval"],
        order: 5,
      },
      {
        name: "multi_row_data",
        label: "Temperature Data",
        fields: ["temperature_readings", "monitoring_zones", "monitored_items"],
        order: 6,
      },
      {
        name: "summary_data",
        label: "Summary Statistics",
        fields: [
          "temperature_summary",
          "min_temp_recorded",
          "max_temp_recorded",
          "avg_temp_recorded",
        ],
        order: 7,
      },
      {
        name: "time_period",
        label: "Monitoring Period",
        fields: ["start_date_time", "end_date_time", "duration"],
        order: 8,
      },
      {
        name: "alarms",
        label: "Alarms & Compliance",
        fields: [
          "alarms_count",
          "alarm_events",
          "excursion_details",
          "compliance_status",
        ],
        order: 9,
      },
      {
        name: "locations",
        label: "Route Information",
        fields: ["origin_location", "destination_location"],
        order: 10,
      },
      {
        name: "certification",
        label: "Certification & Notes",
        fields: ["certified_by", "notes"],
        collapsible: true,
        order: 11,
      },
    ],
  },
};

interface TemperatureLogTemplateProps {
  documentData: ProcessedDocumentData;
  className?: string;
  showQuickSummary?: boolean;
}

export function TemperatureLogTemplate({
  documentData,
  className = "",
  showQuickSummary = true,
}: TemperatureLogTemplateProps) {
  const { extractedData } = documentData;

  // Quick summary component for Temperature Log highlights
  const QuickSummary = () => {
    const setPointTemp = extractedData.set_point_temp;
    const minTemp = extractedData.min_temp_recorded;
    const maxTemp = extractedData.max_temp_recorded;
    const tempUnit = extractedData.temperature_unit || "°F";
    const alarmsCount = extractedData.alarms_count as number;
    const compliance = extractedData.compliance_status;
    const hasAlarms = alarmsCount > 0;
    const isCompliant = compliance === true;
    const recorderId = extractedData.recorder_id;
    const commodity = extractedData.commodity;

    // Check for multi-row data
    const temperatureReadings =
      extractedData.temperature_readings as Array<unknown>;
    const monitoringZones = extractedData.monitoring_zones as Array<unknown>;
    const alarmEvents = extractedData.alarm_events as Array<unknown>;
    const temperatureSummary = extractedData.temperature_summary as Record<
      string,
      unknown
    >;

    const hasMultiRowData = !!(
      temperatureReadings?.length ||
      monitoringZones?.length ||
      alarmEvents?.length
    );

    return (
      <Card
        className={`mb-6 ${hasAlarms ? "border-red-200 bg-red-50" : isCompliant ? "border-green-200 bg-green-50" : "border-blue-200 bg-blue-50"}`}
      >
        <CardHeader className="pb-3">
          <CardTitle
            className={`flex items-center gap-2 text-lg ${hasAlarms ? "text-red-800" : isCompliant ? "text-green-800" : "text-blue-800"}`}
          >
            <Thermometer className="h-5 w-5" />
            Cold Chain Monitoring
            {hasAlarms && (
              <Badge variant="destructive" className="ml-2">
                {alarmsCount} ALARM{alarmsCount !== 1 ? "S" : ""}
              </Badge>
            )}
            {isCompliant && !hasAlarms && (
              <Badge
                variant="outline"
                className="ml-2 border-green-400 text-green-700"
              >
                COMPLIANT
              </Badge>
            )}
            {hasMultiRowData && (
              <Badge
                variant="outline"
                className="ml-2 border-blue-400 text-blue-700"
              >
                Multi-Zone
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {/* Set Point Temperature */}
            <div className="flex items-center gap-3">
              <Thermometer
                className={`h-4 w-4 ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Set Point</div>
                <div
                  className={`font-mono text-lg ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
                >
                  {setPointTemp ? `${String(setPointTemp)}${tempUnit}` : "N/A"}
                </div>
              </div>
            </div>

            {/* Temperature Range */}
            <div className="flex items-center gap-3">
              <Activity
                className={`h-4 w-4 ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Range</div>
                <div
                  className={`font-mono text-sm ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
                >
                  {minTemp && maxTemp
                    ? `${String(minTemp)} to ${String(maxTemp)}${tempUnit}`
                    : temperatureSummary?.minTemperature &&
                        temperatureSummary?.maxTemperature
                      ? `${temperatureSummary.minTemperature} to ${temperatureSummary.maxTemperature}${tempUnit}`
                      : "N/A"}
                </div>
              </div>
            </div>

            {/* Recorder ID */}
            <div className="flex items-center gap-3">
              <Clock
                className={`h-4 w-4 ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Recorder</div>
                <div className="text-muted-foreground text-sm">
                  {String(recorderId) || "N/A"}
                </div>
              </div>
            </div>

            {/* Compliance Status */}
            <div className="flex items-center gap-3">
              <Shield
                className={`h-4 w-4 ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Status</div>
                <div
                  className={`text-sm ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-muted-foreground"}`}
                >
                  {hasAlarms
                    ? "Violation"
                    : isCompliant
                      ? "Compliant"
                      : "Unknown"}
                </div>
              </div>
            </div>
          </div>

          {/* Commodity and Period */}
          <div className="mt-4 grid gap-4 border-t border-blue-200 pt-4 md:grid-cols-2">
            {commodity && (
              <div>
                <div
                  className={`text-xs tracking-wide uppercase ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
                >
                  Commodity
                </div>
                <div
                  className={`text-lg font-bold ${hasAlarms ? "text-red-800" : isCompliant ? "text-green-800" : "text-blue-800"}`}
                >
                  {String(commodity)}
                </div>
              </div>
            )}

            {(extractedData.start_date_time || extractedData.end_date_time) && (
              <div>
                <div
                  className={`text-xs tracking-wide uppercase ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
                >
                  Monitoring Period
                </div>
                <div
                  className={`text-lg font-bold ${hasAlarms ? "text-red-800" : isCompliant ? "text-green-800" : "text-blue-800"}`}
                >
                  {extractedData.start_date_time
                    ? new Date(
                        String(extractedData.start_date_time),
                      ).toLocaleDateString()
                    : "Start"}
                  {" → "}
                  {extractedData.end_date_time
                    ? new Date(
                        String(extractedData.end_date_time),
                      ).toLocaleDateString()
                    : "End"}
                  {extractedData.duration && (
                    <span className="text-muted-foreground ml-2 text-sm">
                      ({String(extractedData.duration)})
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Transport Information */}
          {(extractedData.shipper ||
            extractedData.consignee ||
            extractedData.driver_signature) && (
            <div className="mt-4 border-t border-blue-200 pt-4">
              <div className="grid gap-4 md:grid-cols-3">
                {extractedData.shipper && (
                  <div className="flex items-center gap-2">
                    <Truck className="h-4 w-4 text-blue-600" />
                    <div>
                      <div className="text-muted-foreground text-xs font-medium tracking-wide uppercase">
                        Shipper
                      </div>
                      <div className="text-sm font-medium">
                        {String(extractedData.shipper)}
                      </div>
                    </div>
                  </div>
                )}

                {extractedData.consignee && (
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-blue-600" />
                    <div>
                      <div className="text-muted-foreground text-xs font-medium tracking-wide uppercase">
                        Consignee
                      </div>
                      <div className="text-sm font-medium">
                        {String(extractedData.consignee)}
                      </div>
                    </div>
                  </div>
                )}

                {extractedData.driver_signature && (
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-blue-600" />
                    <div>
                      <div className="text-muted-foreground text-xs font-medium tracking-wide uppercase">
                        Driver
                      </div>
                      <div className="text-sm font-medium">
                        {String(extractedData.driver_signature)}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {extractedData.delivery_time && (
                <div className="mt-2">
                  <div className="text-muted-foreground text-xs font-medium tracking-wide uppercase">
                    Delivery Time
                  </div>
                  <div className="text-sm font-medium">
                    {String(extractedData.delivery_time)}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Multi-Row Data Summary */}
          {hasMultiRowData && (
            <div className="mt-4 border-t border-blue-200 pt-4">
              <div className="grid gap-4 md:grid-cols-4">
                {temperatureReadings && (
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Readings:</span>
                    <Badge variant="outline" className="border-blue-400">
                      {temperatureReadings.length}
                    </Badge>
                  </div>
                )}

                {monitoringZones && (
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Zones:</span>
                    <Badge variant="outline" className="border-blue-400">
                      {monitoringZones.length}
                    </Badge>
                  </div>
                )}

                {alarmEvents && (
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <span className="text-sm font-medium">Events:</span>
                    <Badge variant="outline" className="border-red-400">
                      {alarmEvents.length}
                    </Badge>
                  </div>
                )}

                {temperatureSummary && (
                  <div className="flex items-center gap-2">
                    <Thermometer className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Summary:</span>
                    <Badge variant="outline" className="border-green-400">
                      Available
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Alarm Details */}
          {hasAlarms && (
            <div className="mt-4 border-t border-red-200 pt-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="mt-0.5 h-4 w-4 text-red-600" />
                <div>
                  <div className="text-sm font-medium text-red-800">
                    Temperature Violations Detected:
                  </div>
                  <div className="text-sm text-red-700">
                    {alarmsCount} alarm{alarmsCount !== 1 ? "s" : ""} recorded
                    during monitoring period
                    {extractedData.excursion_details && (
                      <>
                        <br />
                        {String(extractedData.excursion_details)}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Log Number Highlight */}
          {extractedData.document_number && (
            <div className="mt-4 border-t border-blue-200 pt-4">
              <div
                className={`text-xs tracking-wide uppercase ${hasAlarms ? "text-red-600" : isCompliant ? "text-green-600" : "text-blue-600"}`}
              >
                Temperature Log Number
              </div>
              <div
                className={`font-mono text-2xl font-bold ${hasAlarms ? "text-red-800" : isCompliant ? "text-green-800" : "text-blue-800"}`}
              >
                {String(extractedData.document_number)}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Monitored Items Table component for displaying food items and their temperatures
  const MonitoredItemsTable = () => {
    const monitoredItems = extractedData.monitored_items as Array<unknown>;

    if (
      !monitoredItems ||
      !Array.isArray(monitoredItems) ||
      monitoredItems.length === 0
    ) {
      return null;
    }

    // Function to safely extract temperature readings from an item
    const getTemperatureReadings = (item: unknown): number[] => {
      if (!item || typeof item !== "object") return [];

      const itemObj = item as Record<string, unknown>;
      const tempReadings =
        itemObj.temperature_readings || itemObj.temperatures || itemObj.temp;

      if (Array.isArray(tempReadings)) {
        return tempReadings
          .map((t) => (typeof t === "number" ? t : parseFloat(String(t))))
          .filter((t) => !isNaN(t));
      }

      if (
        typeof tempReadings === "number" ||
        typeof tempReadings === "string"
      ) {
        const parsed = parseFloat(String(tempReadings));
        return !isNaN(parsed) ? [parsed] : [];
      }

      return [];
    };

    // Function to get item description
    const getItemDescription = (item: unknown): string => {
      if (!item || typeof item !== "object") return "Unknown Item";

      const itemObj = item as Record<string, unknown>;
      return String(
        itemObj.item_description ||
          itemObj.description ||
          itemObj.name ||
          itemObj.product ||
          "Unknown Item",
      );
    };

    // Function to check if temperatures are within acceptable range
    const getComplianceStatus = (
      temperatures: number[],
      setPoint?: string,
    ): "compliant" | "warning" | "violation" => {
      if (temperatures.length === 0) return "warning";

      const setPointTemp = setPoint ? parseFloat(setPoint) : null;
      if (!setPointTemp) return "warning";

      const tolerance = 5; // 5 degree tolerance
      const hasViolation = temperatures.some(
        (temp) => Math.abs(temp - setPointTemp) > tolerance,
      );

      return hasViolation ? "violation" : "compliant";
    };

    const setPointTemp = extractedData.set_point_temp as string;
    const tempUnit = (extractedData.temperature_unit as string) || "°F";

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Package className="h-5 w-5 text-blue-600" />
            Monitored Items & Temperature Readings
            <Badge variant="outline" className="ml-2 border-blue-400">
              {monitoredItems.length} Items
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-semibold">
                    Item Description
                  </TableHead>
                  <TableHead className="text-center font-semibold">
                    Temperature Readings
                  </TableHead>
                  <TableHead className="text-center font-semibold">
                    Min/Max
                  </TableHead>
                  <TableHead className="text-center font-semibold">
                    Status
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {monitoredItems.map((item, index) => {
                  const description = getItemDescription(item);
                  const temperatures = getTemperatureReadings(item);
                  const complianceStatus = getComplianceStatus(
                    temperatures,
                    setPointTemp,
                  );

                  const minTemp =
                    temperatures.length > 0 ? Math.min(...temperatures) : null;
                  const maxTemp =
                    temperatures.length > 0 ? Math.max(...temperatures) : null;

                  return (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-gray-500" />
                          {description}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {temperatures.length > 0 ? (
                          <div className="flex flex-wrap justify-center gap-1">
                            {temperatures.map((temp, tempIndex) => (
                              <Badge
                                key={tempIndex}
                                variant="outline"
                                className={`font-mono text-xs ${
                                  complianceStatus === "violation"
                                    ? "border-red-400 text-red-700"
                                    : complianceStatus === "compliant"
                                      ? "border-green-400 text-green-700"
                                      : "border-yellow-400 text-yellow-700"
                                }`}
                              >
                                {temp.toFixed(1)}
                                {tempUnit}
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            No readings
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        {minTemp !== null && maxTemp !== null ? (
                          <div className="font-mono text-sm">
                            <div>
                              {minTemp.toFixed(1)}
                              {tempUnit}
                            </div>
                            <div className="text-muted-foreground">to</div>
                            <div>
                              {maxTemp.toFixed(1)}
                              {tempUnit}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">
                            N/A
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge
                          variant={
                            complianceStatus === "violation"
                              ? "destructive"
                              : "outline"
                          }
                          className={`${
                            complianceStatus === "compliant"
                              ? "border-green-400 text-green-700"
                              : complianceStatus === "warning"
                                ? "border-yellow-400 text-yellow-700"
                                : ""
                          }`}
                        >
                          {complianceStatus === "compliant"
                            ? "✓ Compliant"
                            : complianceStatus === "warning"
                              ? "⚠ Unknown"
                              : "✗ Violation"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {setPointTemp && (
            <div className="text-muted-foreground mt-4 flex items-center gap-2 text-sm">
              <Thermometer className="h-4 w-4" />
              Set Point Temperature:{" "}
              <span className="font-mono font-semibold">
                {setPointTemp}
                {tempUnit}
              </span>
              <span className="ml-2">• Tolerance: ±5{tempUnit}</span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={className}>
      {showQuickSummary && <QuickSummary />}

      <MonitoredItemsTable />

      <DocumentTemplate
        documentData={documentData}
        schema={TEMPERATURE_LOG_SCHEMA}
      />
    </div>
  );
}

// Export for use in document routing
export const temperatureLogSchema = TEMPERATURE_LOG_SCHEMA;

// Sample data generator for testing the enhanced template
export const createSampleFoodTransportData = (): ProcessedDocumentData => ({
  documentClass: "temperature_log",
  processingType: "specialized",
  schemaVersion: "1.1.0",
  extractedData: {
    document_number: "***********-001",
    document_date: "2024-06-22",
    recorder_id: "TEMP-DEVICE-12345",
    equipment_type: "Truck Reefer Unit",
    container_number: "TRK-789",
    commodity: "Mixed Frozen Food Products",
    set_point_temp: "0",
    temperature_unit: "°F",
    shipper: "Wonder - Central Kitchen",
    consignee: "Wonder - North East Philly",
    driver_signature: "John Smith",
    delivery_time: "06/22/25, 8:00PM",
    start_date_time: "2024-06-22T06:00:00Z",
    end_date_time: "2024-06-22T20:00:00Z",
    duration: "14 hours",
    monitored_items: [
      {
        item_description: "Chicken Beef",
        temperature_readings: [27.2, 30.1, 28.5],
        item_type: "frozen",
        compliance_status: "compliant",
      },
      {
        item_description: "Pizza crust",
        temperature_readings: [36.7, 39.0, 37.1],
        item_type: "frozen",
        compliance_status: "warning",
      },
      {
        item_description: "Frozen Vegetables",
        temperature_readings: [2.1, 1.8, 2.5],
        item_type: "frozen",
        compliance_status: "compliant",
      },
    ],
    temperature_readings: [
      { timestamp: "06:00", temperature: 27.2, zone: "main" },
      { timestamp: "08:00", temperature: 30.1, zone: "main" },
      { timestamp: "10:00", temperature: 28.5, zone: "main" },
      { timestamp: "12:00", temperature: 36.7, zone: "main" },
      { timestamp: "14:00", temperature: 39.0, zone: "main" },
      { timestamp: "16:00", temperature: 37.1, zone: "main" },
      { timestamp: "18:00", temperature: 2.1, zone: "main" },
      { timestamp: "20:00", temperature: 2.5, zone: "main" },
    ],
    min_temp_recorded: "1.8",
    max_temp_recorded: "39.0",
    avg_temp_recorded: "25.4",
    alarms_count: 2,
    excursion_details:
      "Temperature exceeded set point for 2 hours during transport",
    compliance_status: false,
    origin_location: "Central Kitchen Warehouse",
    destination_location: "Northeast Philadelphia Store",
  },
  confidence: {
    overall: 92,
    temperatureReadings: 95,
    multiRowData: 90,
    deviceInfo: 85,
    compliance: 95,
    timeframe: 80,
    summaryData: 85,
    transportInfo: 90,
    driverInfo: 85,
  },
});
