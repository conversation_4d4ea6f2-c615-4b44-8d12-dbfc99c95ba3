import { useState } from "react";
import { Plus, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useIsMobile } from "@/hooks/use-mobile";

export interface ShipmentItem {
  description: string;
  weight: string;
  pieces: number;
  packageType: string;
  hazmat: boolean;
}

export interface BillOfLadingData {
  shipper: {
    name: string;
    address: string;
    contact: string;
  };
  consignee: {
    name: string;
    address: string;
    contact: string;
  };
  carrier: {
    name: string;
    scac: string;
    contact: string;
  };
  shipment: {
    number: string;
    date: string;
    reference: string;
    specialInstructions: string;
  };
  items: ShipmentItem[];
}

export interface BillOfLadingFormProps {
  data: BillOfLadingData;
  onChange: (data: BillOfLadingData) => void;
  onSubmit?: (data: BillOfLadingData) => void;
}

const packageTypes = [
  "Box",
  "Pallet",
  "Crate",
  "Drum",
  "Carton",
  "Bundle",
  "Roll",
  "Bag",
  "Other",
];

export const BillOfLadingForm = ({
  data,
  onChange,
  onSubmit,
}: BillOfLadingFormProps) => {
  const isMobile = useIsMobile();

  const handleChange = (
    section: keyof BillOfLadingData,
    field: string,
    value: any,
  ) => {
    onChange({
      ...data,
      [section]: {
        ...data[section],
        [field]: value,
      },
    });
  };

  const handleItemChange = (
    index: number,
    field: keyof ShipmentItem,
    value: any,
  ) => {
    const updatedItems = [...data.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    };

    onChange({
      ...data,
      items: updatedItems,
    });
  };

  const addItem = () => {
    onChange({
      ...data,
      items: [
        ...data.items,
        {
          description: "",
          weight: "",
          pieces: 1,
          packageType: "Box",
          hazmat: false,
        },
      ],
    });
  };

  const removeItem = (index: number) => {
    if (data.items.length <= 1) return;

    const updatedItems = [...data.items];
    updatedItems.splice(index, 1);

    onChange({
      ...data,
      items: updatedItems,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSubmit) {
      onSubmit(data);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Shipment Info */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Shipment Information</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="shipment-number">Shipment Number *</Label>
            <Input
              id="shipment-number"
              value={data.shipment.number}
              onChange={(e) =>
                handleChange("shipment", "number", e.target.value)
              }
              placeholder="Enter shipment number"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shipment-date">Date</Label>
            <Input
              id="shipment-date"
              type="date"
              value={data.shipment.date}
              onChange={(e) => handleChange("shipment", "date", e.target.value)}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="shipment-reference">Reference Number</Label>
          <Input
            id="shipment-reference"
            value={data.shipment.reference}
            onChange={(e) =>
              handleChange("shipment", "reference", e.target.value)
            }
            placeholder="Optional reference number"
          />
        </div>
      </div>

      <Separator />

      {/* Shipper Info */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Shipper Information</h3>
        <div className="space-y-2">
          <Label htmlFor="shipper-name">Shipper Name *</Label>
          <Input
            id="shipper-name"
            value={data.shipper.name}
            onChange={(e) => handleChange("shipper", "name", e.target.value)}
            placeholder="Enter shipper name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="shipper-address">Address</Label>
          <Textarea
            id="shipper-address"
            value={data.shipper.address}
            onChange={(e) => handleChange("shipper", "address", e.target.value)}
            placeholder="Enter shipper address"
            rows={2}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="shipper-contact">Contact Information</Label>
          <Input
            id="shipper-contact"
            value={data.shipper.contact}
            onChange={(e) => handleChange("shipper", "contact", e.target.value)}
            placeholder="Phone or email"
          />
        </div>
      </div>

      <Separator />

      {/* Consignee Info */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Consignee Information</h3>
        <div className="space-y-2">
          <Label htmlFor="consignee-name">Consignee Name *</Label>
          <Input
            id="consignee-name"
            value={data.consignee.name}
            onChange={(e) => handleChange("consignee", "name", e.target.value)}
            placeholder="Enter consignee name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="consignee-address">Address</Label>
          <Textarea
            id="consignee-address"
            value={data.consignee.address}
            onChange={(e) =>
              handleChange("consignee", "address", e.target.value)
            }
            placeholder="Enter consignee address"
            rows={2}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="consignee-contact">Contact Information</Label>
          <Input
            id="consignee-contact"
            value={data.consignee.contact}
            onChange={(e) =>
              handleChange("consignee", "contact", e.target.value)
            }
            placeholder="Phone or email"
          />
        </div>
      </div>

      <Separator />

      {/* Carrier Info */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Carrier Information</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="carrier-name">Carrier Name</Label>
            <Input
              id="carrier-name"
              value={data.carrier.name}
              onChange={(e) => handleChange("carrier", "name", e.target.value)}
              placeholder="Enter carrier name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="carrier-scac">SCAC Code</Label>
            <Input
              id="carrier-scac"
              value={data.carrier.scac}
              onChange={(e) => handleChange("carrier", "scac", e.target.value)}
              placeholder="Enter SCAC code"
              maxLength={4}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="carrier-contact">Contact Information</Label>
          <Input
            id="carrier-contact"
            value={data.carrier.contact}
            onChange={(e) => handleChange("carrier", "contact", e.target.value)}
            placeholder="Phone or email"
          />
        </div>
      </div>

      <Separator />

      {/* Freight Items */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Freight Items</h3>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addItem}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            <span>Add Item</span>
          </Button>
        </div>

        {data.items.map((item, index) => (
          <div
            key={index}
            className="bg-muted/10 space-y-4 rounded-md border p-4"
          >
            <div className="mb-2 flex items-start justify-between">
              <h4 className="font-medium">Item {index + 1}</h4>
              {data.items.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem(index)}
                  className="text-destructive hover:text-destructive h-8 px-2"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor={`item-${index}-description`}>Description</Label>
              <Input
                id={`item-${index}-description`}
                value={item.description}
                onChange={(e) =>
                  handleItemChange(index, "description", e.target.value)
                }
                placeholder="Item description"
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor={`item-${index}-pieces`}>Pieces</Label>
                <Input
                  id={`item-${index}-pieces`}
                  type="number"
                  min="1"
                  value={item.pieces}
                  onChange={(e) =>
                    handleItemChange(
                      index,
                      "pieces",
                      parseInt(e.target.value) || 1,
                    )
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`item-${index}-weight`}>Weight (lbs)</Label>
                <Input
                  id={`item-${index}-weight`}
                  value={item.weight}
                  onChange={(e) =>
                    handleItemChange(index, "weight", e.target.value)
                  }
                  placeholder="Weight in lbs"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`item-${index}-package-type`}>
                  Package Type
                </Label>
                <Select
                  value={item.packageType}
                  onValueChange={(value) =>
                    handleItemChange(index, "packageType", value)
                  }
                >
                  <SelectTrigger id={`item-${index}-package-type`}>
                    <SelectValue placeholder="Select package type" />
                  </SelectTrigger>
                  <SelectContent>
                    {packageTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-2 flex items-center space-x-2">
              <Checkbox
                id={`item-${index}-hazmat`}
                checked={item.hazmat}
                onCheckedChange={(checked) =>
                  handleItemChange(index, "hazmat", checked === true)
                }
              />
              <Label
                htmlFor={`item-${index}-hazmat`}
                className="cursor-pointer text-sm font-normal"
              >
                Hazardous Material
              </Label>
            </div>
          </div>
        ))}
      </div>

      <Separator />

      {/* Special Instructions */}
      <div className="space-y-2">
        <Label htmlFor="special-instructions">Special Instructions</Label>
        <Textarea
          id="special-instructions"
          value={data.shipment.specialInstructions}
          onChange={(e) =>
            handleChange("shipment", "specialInstructions", e.target.value)
          }
          placeholder="Enter any special handling instructions or notes"
          rows={3}
        />
      </div>

      {onSubmit && (
        <div className="flex justify-end pt-4">
          <Button type="submit" className="w-full md:w-auto">
            Generate Document
          </Button>
        </div>
      )}
    </form>
  );
};
