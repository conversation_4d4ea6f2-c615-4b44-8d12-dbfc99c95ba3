import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  MapPin,
  Package,
  User,
} from "lucide-react";

import type { DocumentSchema, ProcessedDocumentData } from "./DocumentTemplate";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DocumentTemplate } from "./DocumentTemplate";

// Proof of Delivery specific schema (matches our backend schema)
const PROOF_OF_DELIVERY_SCHEMA: DocumentSchema = {
  documentType: "proof_of_delivery",
  category: "transportation",
  subtype: "standard",
  version: "1.0.0",
  description:
    "Proof of Delivery - confirmation that cargo/shipment was delivered to consignee",
  fields: [
    {
      key: "document_number",
      label: "POD Number",
      type: "string",
      description: "Proof of Delivery document number",
    },
    {
      key: "document_date",
      label: "Document Date",
      type: "date",
      description: "Date the POD document was created",
    },
    {
      key: "delivery_date",
      label: "Delivery Date",
      type: "date",
      description: "Date when delivery was completed",
    },
    {
      key: "delivery_time",
      label: "Delivery Time",
      type: "string",
      description: "Time when delivery was completed",
    },
    {
      key: "tracking_number",
      label: "Tracking Number",
      type: "string",
      description: "Shipment tracking or reference number",
    },
    {
      key: "bol_number",
      label: "BOL Number",
      type: "string",
      description: "Bill of Lading reference number",
    },
    {
      key: "pod_number",
      label: "POD Number",
      type: "string",
      description: "Proof of Delivery document number",
    },
    {
      key: "consignee",
      label: "Consignee",
      type: "string",
      description: "Company or person who received the shipment",
    },
    {
      key: "receiver_name",
      label: "Received By",
      type: "string",
      description: "Name of person who received the shipment",
    },
    {
      key: "receiver_title",
      label: "Receiver Title",
      type: "string",
      description: "Title or position of person who received shipment",
    },
    {
      key: "delivery_address",
      label: "Delivery Address",
      type: "address",
      description: "Address where delivery was made",
    },
    {
      key: "pieces_delivered",
      label: "Pieces Delivered",
      type: "number",
      description: "Number of pieces/packages delivered",
    },
    {
      key: "weight_delivered",
      label: "Weight Delivered",
      type: "weight",
      description: "Total weight of delivered items",
    },
    {
      key: "cargo_description",
      label: "Cargo Description",
      type: "string",
      description: "Description of delivered cargo",
    },
    {
      key: "delivery_condition",
      label: "Delivery Condition",
      type: "string",
      description: "Condition of cargo upon delivery",
    },
    {
      key: "exceptions",
      label: "Exceptions",
      type: "string",
      description: "Any exceptions or damage noted",
    },
    {
      key: "signature",
      label: "Signature",
      type: "string",
      description: "Signature of person who received shipment",
      sensitive: true,
    },
    {
      key: "driver_name",
      label: "Driver Name",
      type: "string",
      description: "Name of driver who made delivery",
    },
    {
      key: "carrier_name",
      label: "Carrier",
      type: "string",
      description: "Name of carrier/shipping company",
    },
    {
      key: "special_instructions",
      label: "Special Instructions",
      type: "string",
      description: "Special delivery instructions that were followed",
    },
  ],
  requiredFields: [
    "delivery_date",
    "tracking_number",
    "consignee",
    "receiver_name",
    "delivery_address",
    "signature",
  ],
  optionalFields: [
    "document_number",
    "document_date",
    "delivery_time",
    "bol_number",
    "pod_number",
    "receiver_title",
    "pieces_delivered",
    "weight_delivered",
    "cargo_description",
    "delivery_condition",
    "exceptions",
    "driver_name",
    "carrier_name",
    "special_instructions",
  ],
  uiMapping: {
    layout: "proof_of_delivery_layout",
    sections: [
      {
        name: "document_info",
        label: "Document Information",
        fields: [
          "document_number",
          "document_date",
          "tracking_number",
          "bol_number",
          "pod_number",
        ],
        order: 1,
      },
      {
        name: "delivery_confirmation",
        label: "Delivery Confirmation",
        fields: ["delivery_date", "delivery_time", "delivery_address"],
        order: 2,
      },
      {
        name: "recipient",
        label: "Recipient Information",
        fields: ["consignee", "receiver_name", "receiver_title", "signature"],
        order: 3,
      },
      {
        name: "shipment_details",
        label: "Shipment Details",
        fields: ["pieces_delivered", "weight_delivered", "cargo_description"],
        order: 4,
      },
      {
        name: "delivery_info",
        label: "Delivery Information",
        fields: ["driver_name", "carrier_name", "delivery_condition"],
        order: 5,
      },
      {
        name: "exceptions",
        label: "Exceptions & Notes",
        fields: ["exceptions", "special_instructions"],
        collapsible: true,
        order: 6,
      },
    ],
  },
};

interface ProofOfDeliveryTemplateProps {
  documentData: ProcessedDocumentData;
  className?: string;
  showQuickSummary?: boolean;
}

export function ProofOfDeliveryTemplate({
  documentData,
  className = "",
  showQuickSummary = true,
}: ProofOfDeliveryTemplateProps) {
  const { extractedData } = documentData;

  // Quick summary component for POD highlights
  const QuickSummary = () => {
    const deliveryDate = extractedData.delivery_date;
    const receiverName = extractedData.receiver_name;
    const consignee = extractedData.consignee;
    const trackingNumber = extractedData.tracking_number;
    const hasSignature = !!extractedData.signature;
    const hasExceptions = !!extractedData.exceptions;
    const piecesDelivered = extractedData.pieces_delivered as number;

    return (
      <Card
        className={`mb-6 ${hasExceptions ? "border-amber-200 bg-amber-50" : "border-blue-200 bg-blue-50"}`}
      >
        <CardHeader className="pb-3">
          <CardTitle
            className={`flex items-center gap-2 text-lg ${hasExceptions ? "text-amber-800" : "text-blue-800"}`}
          >
            {hasExceptions ? (
              <AlertTriangle className="h-5 w-5" />
            ) : (
              <CheckCircle className="h-5 w-5" />
            )}
            Delivery Confirmation
            {hasExceptions && (
              <Badge variant="destructive" className="ml-2">
                Exceptions Noted
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {/* Delivery Status */}
            <div className="flex items-center gap-3">
              <CheckCircle
                className={`h-4 w-4 ${hasExceptions ? "text-amber-600" : "text-blue-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Status</div>
                <div className="text-muted-foreground text-sm">
                  {hasSignature ? "Delivered" : "Pending"}
                  {hasExceptions && " (Issues)"}
                </div>
              </div>
            </div>

            {/* Tracking */}
            <div className="flex items-center gap-3">
              <Package
                className={`h-4 w-4 ${hasExceptions ? "text-amber-600" : "text-blue-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Tracking</div>
                <div className="text-muted-foreground text-sm">
                  {String(trackingNumber) || "N/A"}
                </div>
              </div>
            </div>

            {/* Receiver */}
            <div className="flex items-center gap-3">
              <User
                className={`h-4 w-4 ${hasExceptions ? "text-amber-600" : "text-blue-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Received By</div>
                <div className="text-muted-foreground text-sm">
                  {String(receiverName) || "N/A"}
                </div>
              </div>
            </div>

            {/* Pieces */}
            {piecesDelivered && (
              <div>
                <div className="text-sm font-medium">Pieces</div>
                <div
                  className={`font-mono text-lg ${hasExceptions ? "text-amber-600" : "text-blue-600"}`}
                >
                  {piecesDelivered}
                </div>
              </div>
            )}
          </div>

          {/* Delivery Details */}
          <div className="mt-4 grid gap-4 border-t border-blue-200 pt-4 md:grid-cols-2">
            {deliveryDate && (
              <div>
                <div
                  className={`text-xs tracking-wide uppercase ${hasExceptions ? "text-amber-600" : "text-blue-600"}`}
                >
                  Delivery Date
                </div>
                <div
                  className={`text-lg font-bold ${hasExceptions ? "text-amber-800" : "text-blue-800"}`}
                >
                  {new Date(String(deliveryDate)).toLocaleDateString()}
                  {extractedData.delivery_time && (
                    <span className="text-muted-foreground ml-2 text-sm">
                      at {String(extractedData.delivery_time)}
                    </span>
                  )}
                </div>
              </div>
            )}

            {consignee && (
              <div>
                <div
                  className={`text-xs tracking-wide uppercase ${hasExceptions ? "text-amber-600" : "text-blue-600"}`}
                >
                  Consignee
                </div>
                <div
                  className={`text-lg font-bold ${hasExceptions ? "text-amber-800" : "text-blue-800"}`}
                >
                  {String(consignee)}
                </div>
              </div>
            )}
          </div>

          {/* Signature and Address */}
          <div className="mt-4 border-t border-blue-200 pt-4">
            <div className="grid gap-4 md:grid-cols-2">
              {hasSignature && (
                <div className="flex items-center gap-2">
                  <CheckCircle
                    className={`h-4 w-4 ${hasExceptions ? "text-amber-600" : "text-green-600"}`}
                  />
                  <span className="text-sm font-medium">Signature:</span>
                  <Badge
                    variant="outline"
                    className={
                      hasExceptions ? "border-amber-400" : "border-green-400"
                    }
                  >
                    Confirmed
                  </Badge>
                </div>
              )}

              {extractedData.delivery_address && (
                <div className="flex items-center gap-2">
                  <MapPin
                    className={`h-4 w-4 ${hasExceptions ? "text-amber-600" : "text-blue-600"}`}
                  />
                  <span className="text-sm font-medium">Location:</span>
                  <span className="text-muted-foreground text-sm">
                    {String(extractedData.delivery_address)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Exceptions Warning */}
          {hasExceptions && (
            <div className="mt-4 border-t border-amber-200 pt-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="mt-0.5 h-4 w-4 text-amber-600" />
                <div>
                  <div className="text-sm font-medium text-amber-800">
                    Exceptions Noted:
                  </div>
                  <div className="text-sm text-amber-700">
                    {String(extractedData.exceptions)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={className}>
      {showQuickSummary && <QuickSummary />}

      <DocumentTemplate
        documentData={documentData}
        schema={PROOF_OF_DELIVERY_SCHEMA}
      />
    </div>
  );
}

// Export for use in document routing
export const proofOfDeliverySchema = PROOF_OF_DELIVERY_SCHEMA;
