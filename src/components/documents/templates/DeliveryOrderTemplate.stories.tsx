import React from "react";

import type { ProcessedDocumentData } from ".";

import { DeliveryOrderTemplate } from ".";

const mockDeliveryOrder: ProcessedDocumentData = {
  documentClass: "delivery_order",
  processingType: "schema-driven",
  schemaVersion: "1.0.0",
  extractedData: {
    document_number: "DO-78910",
    document_date: "2024-06-22",
    bol_reference: "BOL-123456",
    issuing_agent: "Oceanic Shipping",
    issuing_agent_address: "789 Port Ave, Bay City, CA",
    collection_address: "Warehouse 5, Dock 3, Bay City, CA",
    collection_location_details: "Terminal 2",
    consignee_company: "Widgets Unlimited",
    consignee_address: "456 Elm St, Shelbyville, IL",
    notify_party: "Acme Shippers Inc.",
    hauler_name: "FastFreight LLC",
    hauler_address: "123 Main St, Springfield, IL",
    cargo_description: "Industrial widgets",
    cargo_weight: 2000,
    cargo_measurements: "10 pallets",
    container_numbers: "CONT-5555",
    seal_numbers: "SEAL-9999",
    equipment_reference: "EQ-2024-01",
    vessel_name: "SS Widget",
    voyage_number: "VY-2024-07",
    pod_origin: "Bay City Port",
    import_details: "Customs cleared",
    delivery_location: "456 Elm St, Shelbyville, IL",
    delivery_date_time: "2024-06-23 10:00",
    customs_clearance: "Cleared",
    release_conditions: "None",
  },
};

export default {
  title: "Documents/Templates/DeliveryOrderTemplate",
  component: DeliveryOrderTemplate,
};

export const Default = () => (
  <DeliveryOrderTemplate documentData={mockDeliveryOrder} />
);
