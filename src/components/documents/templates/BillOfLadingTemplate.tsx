import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MapPin, Package, Truck } from "lucide-react";

import type { DocumentSchema, ProcessedDocumentData } from "./DocumentTemplate";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DocumentTemplate } from "./DocumentTemplate";

// Bill of Lading specific schema (matches our backend schema)
const BOL_SCHEMA: DocumentSchema = {
  documentType: "bill_of_lading",
  category: "transportation",
  subtype: "standard",
  version: "1.0.0",
  description: "Standard Bill of Lading document for freight shipments",
  fields: [
    {
      key: "document_number",
      label: "BOL Number",
      type: "string",
      description: "Primary document identification number",
    },
    {
      key: "document_date",
      label: "Document Date",
      type: "date",
      description: "Date the document was created or issued",
    },
    {
      key: "shipper_company",
      label: "Shipper Company",
      type: "string",
      description: "Name of the shipper company",
    },
    {
      key: "shipper_address",
      label: "Shipper Address",
      type: "address",
      description: "Complete address for shipper",
    },
    {
      key: "shipper_contact",
      label: "Shipper Contact",
      type: "string",
      description: "Contact person at shipper location",
    },
    {
      key: "consignee_company",
      label: "Consignee Company",
      type: "string",
      description: "Name of the consignee company",
    },
    {
      key: "consignee_address",
      label: "Consignee Address",
      type: "address",
      description: "Complete address for consignee",
    },
    {
      key: "carrier_company",
      label: "Carrier Company",
      type: "string",
      description: "Name of the carrier company",
    },
    {
      key: "carrier_scac",
      label: "Carrier SCAC",
      type: "string",
      description: "Standard Carrier Alpha Code for the carrier",
    },
    {
      key: "origin_city",
      label: "Origin City",
      type: "string",
      description: "City where shipment originates",
    },
    {
      key: "destination_city",
      label: "Destination City",
      type: "string",
      description: "City where shipment is delivered",
    },
    {
      key: "commodity_description",
      label: "Commodity Description",
      type: "string",
      description: "Description of goods being shipped",
    },
    {
      key: "total_pieces",
      label: "Total Pieces",
      type: "number",
      description: "Total number of pieces/units",
    },
    {
      key: "total_weight",
      label: "Total Weight",
      type: "weight",
      description: "Total weight of items",
    },
    {
      key: "freight_class",
      label: "Freight Class",
      type: "string",
      description: "NMFC freight classification",
    },
    {
      key: "freight_charges",
      label: "Freight Charges",
      type: "currency",
      description: "Total freight charges",
    },
    {
      key: "hazmat_flag",
      label: "Hazardous Materials",
      type: "boolean",
      description: "Whether shipment contains hazardous materials",
    },
    {
      key: "special_instructions",
      label: "Special Instructions",
      type: "string",
      description: "Special handling or delivery instructions",
    },
  ],
  requiredFields: [
    "document_number",
    "document_date",
    "shipper_company",
    "shipper_address",
    "consignee_company",
    "consignee_address",
    "carrier_company",
    "origin_city",
    "destination_city",
    "commodity_description",
    "total_pieces",
    "total_weight",
  ],
  optionalFields: [
    "shipper_contact",
    "carrier_scac",
    "freight_class",
    "freight_charges",
    "hazmat_flag",
    "special_instructions",
  ],
  uiMapping: {
    layout: "standard_bol_layout",
    sections: [
      {
        name: "document_info",
        label: "Document Information",
        fields: ["document_number", "document_date"],
        order: 1,
      },
      {
        name: "parties",
        label: "Parties",
        fields: [
          "shipper_company",
          "shipper_address",
          "shipper_contact",
          "consignee_company",
          "consignee_address",
          "carrier_company",
          "carrier_scac",
        ],
        order: 2,
      },
      {
        name: "routing",
        label: "Routing",
        fields: ["origin_city", "destination_city"],
        order: 3,
      },
      {
        name: "cargo",
        label: "Cargo Details",
        fields: [
          "commodity_description",
          "total_pieces",
          "total_weight",
          "freight_class",
          "freight_charges",
        ],
        order: 4,
      },
      {
        name: "special",
        label: "Special Handling",
        fields: ["hazmat_flag", "special_instructions"],
        collapsible: true,
        order: 5,
      },
    ],
  },
};

interface BillOfLadingTemplateProps {
  documentData: ProcessedDocumentData;
  className?: string;
  showQuickSummary?: boolean;
}

export function BillOfLadingTemplate({
  documentData,
  className = "",
  showQuickSummary = true,
}: BillOfLadingTemplateProps) {
  const { extractedData } = documentData;

  // Quick summary component for BOL-specific highlights
  const QuickSummary = () => {
    const hasHazmat = extractedData.hazmat_flag === true;
    const totalPieces = extractedData.total_pieces as number;
    const totalWeight = extractedData.total_weight as number;
    const freight = extractedData.freight_charges as number;

    return (
      <Card className="mb-6 border-blue-200 bg-blue-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg text-blue-800">
            <Truck className="h-5 w-5" />
            Shipment Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {/* Route */}
            <div className="flex items-center gap-3">
              <MapPin className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium">Route</div>
                <div className="text-muted-foreground text-sm">
                  {String(extractedData.origin_city) || "N/A"} →{" "}
                  {String(extractedData.destination_city) || "N/A"}
                </div>
              </div>
            </div>

            {/* Cargo */}
            <div className="flex items-center gap-3">
              <Package className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium">Cargo</div>
                <div className="text-muted-foreground text-sm">
                  {totalPieces ? `${totalPieces} pieces` : "N/A"}
                  {totalWeight && ` • ${totalWeight} lbs`}
                </div>
              </div>
            </div>

            {/* Freight */}
            {freight && (
              <div>
                <div className="text-sm font-medium">Freight Charges</div>
                <div className="font-mono text-lg text-green-600">
                  ${freight.toFixed(2)}
                </div>
              </div>
            )}

            {/* Hazmat Warning */}
            {hasHazmat && (
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <div>
                  <Badge variant="destructive" className="text-xs">
                    HAZMAT
                  </Badge>
                  <div className="mt-1 text-xs text-red-600">
                    Special handling required
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* BOL Number Highlight */}
          {extractedData.document_number && (
            <div className="mt-4 border-t border-blue-200 pt-4">
              <div className="text-xs tracking-wide text-blue-600 uppercase">
                Bill of Lading Number
              </div>
              <div className="font-mono text-2xl font-bold text-blue-800">
                {String(extractedData.document_number)}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={className}>
      {showQuickSummary && <QuickSummary />}

      <DocumentTemplate documentData={documentData} schema={BOL_SCHEMA} />
    </div>
  );
}

// Export for use in document routing
export const billOfLadingSchema = BOL_SCHEMA;
