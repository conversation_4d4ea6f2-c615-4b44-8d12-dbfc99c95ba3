import React from "react";
import { <PERSON><PERSON><PERSON>riangle, Award, Scale, Shield, Truck } from "lucide-react";

import type { DocumentSchema, ProcessedDocumentData } from "./DocumentTemplate";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DocumentTemplate } from "./DocumentTemplate";

// Weight Ticket specific schema (matches our backend schema)
const WEIGHT_TICKET_SCHEMA: DocumentSchema = {
  documentType: "weight_ticket",
  category: "regulatory",
  subtype: "standard",
  version: "1.0.0",
  description:
    "Weight Ticket - official documentation of vehicle and cargo weights for compliance and billing",
  fields: [
    {
      key: "document_number",
      label: "Ticket Number",
      type: "string",
      description: "Weight ticket identification number",
    },
    {
      key: "document_date",
      label: "Weigh Date",
      type: "date",
      description: "Date when weighing occurred",
    },
    {
      key: "weigh_time",
      label: "Weigh Time",
      type: "string",
      description: "Time when weighing occurred",
    },
    {
      key: "scale_location",
      label: "Scale Location",
      type: "string",
      description: "Location or name of the weighing facility",
    },
    {
      key: "scale_operator",
      label: "Scale Operator",
      type: "string",
      description: "Person operating the scale",
    },
    {
      key: "truck_number",
      label: "Truck Number",
      type: "string",
      description: "Truck or tractor identification number",
    },
    {
      key: "trailer_number",
      label: "Trailer Number",
      type: "string",
      description: "Trailer identification number",
    },
    {
      key: "license_plate",
      label: "License Plate",
      type: "string",
      description: "Vehicle license plate number",
    },
    {
      key: "driver_name",
      label: "Driver Name",
      type: "string",
      description: "Name of the driver",
    },
    {
      key: "gross_weight",
      label: "Gross Weight",
      type: "string",
      description: "Total weight of vehicle and cargo",
    },
    {
      key: "tare_weight",
      label: "Tare Weight",
      type: "string",
      description: "Weight of empty vehicle",
    },
    {
      key: "net_weight",
      label: "Net Weight",
      type: "string",
      description: "Weight of cargo only (gross minus tare)",
    },
    {
      key: "weight_unit",
      label: "Weight Unit",
      type: "string",
      description: "Unit of weight measurement",
    },
    {
      key: "commodity",
      label: "Commodity",
      type: "string",
      description: "Description of goods being weighed",
    },
    {
      key: "bol_reference",
      label: "BOL Reference",
      type: "string",
      description: "Associated Bill of Lading number",
    },
    {
      key: "shipper",
      label: "Shipper",
      type: "string",
      description: "Shipping company or customer",
    },
    {
      key: "consignee",
      label: "Consignee",
      type: "string",
      description: "Receiving company",
    },
    {
      key: "dot_inspection",
      label: "DOT Inspection",
      type: "boolean",
      description: "Whether DOT inspection was performed",
    },
    {
      key: "overweight_flag",
      label: "Overweight",
      type: "boolean",
      description: "Whether vehicle is overweight",
    },
    {
      key: "certified_weight",
      label: "Certified Weight",
      type: "boolean",
      description: "Whether this is a certified/official weight",
    },
    {
      key: "seal_number",
      label: "Seal Number",
      type: "string",
      description: "Security seal number",
    },
    {
      key: "notes",
      label: "Notes",
      type: "string",
      description: "Additional notes or observations",
    },
  ],
  requiredFields: [
    "document_number",
    "document_date",
    "scale_location",
    "truck_number",
    "gross_weight",
    "tare_weight",
  ],
  optionalFields: [
    "weigh_time",
    "scale_operator",
    "trailer_number",
    "license_plate",
    "driver_name",
    "net_weight",
    "weight_unit",
    "commodity",
    "bol_reference",
    "shipper",
    "consignee",
    "dot_inspection",
    "overweight_flag",
    "certified_weight",
    "seal_number",
    "notes",
  ],
  uiMapping: {
    layout: "weight_ticket_layout",
    sections: [
      {
        name: "document_info",
        label: "Document Information",
        fields: ["document_number", "document_date", "weigh_time"],
        order: 1,
      },
      {
        name: "scale_info",
        label: "Scale Information",
        fields: ["scale_location", "scale_operator"],
        order: 2,
      },
      {
        name: "vehicle_info",
        label: "Vehicle Information",
        fields: [
          "truck_number",
          "trailer_number",
          "license_plate",
          "driver_name",
        ],
        order: 3,
      },
      {
        name: "weights",
        label: "Weight Measurements",
        fields: ["gross_weight", "tare_weight", "net_weight", "weight_unit"],
        order: 4,
      },
      {
        name: "shipment",
        label: "Shipment Information",
        fields: ["commodity", "bol_reference", "shipper", "consignee"],
        order: 5,
      },
      {
        name: "compliance",
        label: "Compliance & Certification",
        fields: [
          "dot_inspection",
          "overweight_flag",
          "certified_weight",
          "seal_number",
        ],
        order: 6,
      },
      {
        name: "notes",
        label: "Additional Information",
        fields: ["notes"],
        collapsible: true,
        order: 7,
      },
    ],
  },
};

interface WeightTicketTemplateProps {
  documentData: ProcessedDocumentData;
  className?: string;
  showQuickSummary?: boolean;
}

export function WeightTicketTemplate({
  documentData,
  className = "",
  showQuickSummary = true,
}: WeightTicketTemplateProps) {
  const { extractedData } = documentData;

  // Quick summary component for Weight Ticket highlights
  const QuickSummary = () => {
    const grossWeight = extractedData.gross_weight;
    const tareWeight = extractedData.tare_weight;
    const netWeight = extractedData.net_weight;
    const weightUnit = extractedData.weight_unit || "lbs";
    const truckNumber = extractedData.truck_number;
    const isOverweight = extractedData.overweight_flag === true;
    const isCertified = extractedData.certified_weight === true;
    const dotInspection = extractedData.dot_inspection === true;
    const scaleLocation = extractedData.scale_location;

    // Calculate net weight if not provided
    const calculatedNet =
      grossWeight && tareWeight
        ? parseFloat(String(grossWeight)) - parseFloat(String(tareWeight))
        : null;

    return (
      <Card
        className={`mb-6 ${isOverweight ? "border-red-200 bg-red-50" : "border-purple-200 bg-purple-50"}`}
      >
        <CardHeader className="pb-3">
          <CardTitle
            className={`flex items-center gap-2 text-lg ${isOverweight ? "text-red-800" : "text-purple-800"}`}
          >
            <Scale className="h-5 w-5" />
            Official Weight Documentation
            {isOverweight && (
              <Badge variant="destructive" className="ml-2">
                OVERWEIGHT
              </Badge>
            )}
            {isCertified && (
              <Badge
                variant="outline"
                className="ml-2 border-green-400 text-green-700"
              >
                CERTIFIED
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {/* Gross Weight */}
            <div className="flex items-center gap-3">
              <Scale
                className={`h-4 w-4 ${isOverweight ? "text-red-600" : "text-purple-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Gross Weight</div>
                <div
                  className={`font-mono text-lg ${isOverweight ? "text-red-600" : "text-purple-600"}`}
                >
                  {grossWeight ? `${String(grossWeight)} ${weightUnit}` : "N/A"}
                </div>
              </div>
            </div>

            {/* Tare Weight */}
            <div className="flex items-center gap-3">
              <Truck
                className={`h-4 w-4 ${isOverweight ? "text-red-600" : "text-purple-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Tare Weight</div>
                <div
                  className={`font-mono text-lg ${isOverweight ? "text-red-600" : "text-purple-600"}`}
                >
                  {tareWeight ? `${String(tareWeight)} ${weightUnit}` : "N/A"}
                </div>
              </div>
            </div>

            {/* Net Weight */}
            <div className="flex items-center gap-3">
              <div
                className={`h-4 w-4 rounded ${isOverweight ? "bg-red-600" : "bg-purple-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Net Weight</div>
                <div
                  className={`font-mono text-lg ${isOverweight ? "text-red-600" : "text-purple-600"}`}
                >
                  {netWeight
                    ? `${String(netWeight)} ${weightUnit}`
                    : calculatedNet
                      ? `${calculatedNet.toFixed(0)} ${weightUnit}`
                      : "N/A"}
                </div>
              </div>
            </div>

            {/* Vehicle ID */}
            <div className="flex items-center gap-3">
              <Truck
                className={`h-4 w-4 ${isOverweight ? "text-red-600" : "text-purple-600"}`}
              />
              <div>
                <div className="text-sm font-medium">Vehicle</div>
                <div className="text-muted-foreground text-sm">
                  {String(truckNumber) || "N/A"}
                </div>
              </div>
            </div>
          </div>

          {/* Scale and Date Information */}
          <div className="mt-4 grid gap-4 border-t border-purple-200 pt-4 md:grid-cols-2">
            {scaleLocation && (
              <div>
                <div
                  className={`text-xs tracking-wide uppercase ${isOverweight ? "text-red-600" : "text-purple-600"}`}
                >
                  Scale Location
                </div>
                <div
                  className={`text-lg font-bold ${isOverweight ? "text-red-800" : "text-purple-800"}`}
                >
                  {String(scaleLocation)}
                </div>
              </div>
            )}

            {extractedData.document_date && (
              <div>
                <div
                  className={`text-xs tracking-wide uppercase ${isOverweight ? "text-red-600" : "text-purple-600"}`}
                >
                  Weigh Date
                </div>
                <div
                  className={`text-lg font-bold ${isOverweight ? "text-red-800" : "text-purple-800"}`}
                >
                  {new Date(
                    String(extractedData.document_date),
                  ).toLocaleDateString()}
                  {extractedData.weigh_time && (
                    <span className="text-muted-foreground ml-2 text-sm">
                      at {String(extractedData.weigh_time)}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Compliance Status */}
          <div className="mt-4 border-t border-purple-200 pt-4">
            <div className="grid gap-4 md:grid-cols-3">
              {isCertified && (
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Status:</span>
                  <Badge
                    variant="outline"
                    className="border-green-400 text-green-700"
                  >
                    Certified Weight
                  </Badge>
                </div>
              )}

              {dotInspection && (
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">DOT:</span>
                  <Badge
                    variant="outline"
                    className="border-blue-400 text-blue-700"
                  >
                    Inspected
                  </Badge>
                </div>
              )}

              {isOverweight && (
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium">Warning:</span>
                  <Badge variant="destructive">Overweight Vehicle</Badge>
                </div>
              )}
            </div>
          </div>

          {/* Ticket Number Highlight */}
          {extractedData.document_number && (
            <div className="mt-4 border-t border-purple-200 pt-4">
              <div
                className={`text-xs tracking-wide uppercase ${isOverweight ? "text-red-600" : "text-purple-600"}`}
              >
                Weight Ticket Number
              </div>
              <div
                className={`font-mono text-2xl font-bold ${isOverweight ? "text-red-800" : "text-purple-800"}`}
              >
                {String(extractedData.document_number)}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={className}>
      {showQuickSummary && <QuickSummary />}

      <DocumentTemplate
        documentData={documentData}
        schema={WEIGHT_TICKET_SCHEMA}
      />
    </div>
  );
}

// Export for use in document routing
export const weightTicketSchema = WEIGHT_TICKET_SCHEMA;
