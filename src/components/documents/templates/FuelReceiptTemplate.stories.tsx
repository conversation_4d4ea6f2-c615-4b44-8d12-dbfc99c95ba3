import React from "react";

import type { ProcessedDocumentData } from ".";

import { FuelReceiptTemplate } from ".";

const mockFuelReceipt: ProcessedDocumentData = {
  documentClass: "fuel_receipt",
  processingType: "schema-driven",
  schemaVersion: "1.0.0",
  extractedData: {
    receipt_number: "RCPT-20240622-01",
    purchase_date: "2024-06-22",
    purchase_time: "14:35",
    station_name: "Speedy Gas",
    station_address: "101 Fuel Rd, Gasville, TX",
    fuel_type: "Diesel",
    gallons: 50.5,
    price_per_gallon: 3.799,
    fuel_cost: 191.45,
    tax_amount: 15.0,
    total_amount: 206.45,
    payment_method: "Credit Card",
    card_last_four: "1234",
    pump_number: "7",
    odometer_reading: 120345,
  },
};

export default {
  title: "Documents/Templates/FuelReceiptTemplate",
  component: FuelReceiptTemplate,
};

export const Default = () => (
  <FuelReceiptTemplate documentData={mockFuelReceipt} />
);
