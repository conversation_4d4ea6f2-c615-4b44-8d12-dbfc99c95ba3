import React from "react";

import type { ProcessedDocumentData } from ".";

import { ProofOfDeliveryTemplate } from ".";

const mockProofOfDelivery: ProcessedDocumentData = {
  documentClass: "proof_of_delivery",
  processingType: "schema-driven",
  schemaVersion: "1.0.0",
  extractedData: {
    document_number: "POD-20240622-01",
    document_date: "2024-06-22",
    delivery_date: "2024-06-22",
    delivery_time: "16:00",
    tracking_number: "TRK-5555",
    bol_number: "BOL-123456",
    pod_number: "POD-20240622-01",
    consignee: "Widgets Unlimited",
    receiver_name: "<PERSON>",
    receiver_title: "Receiving Manager",
    delivery_address: "456 Elm St, Shelbyville, IL",
    pieces_delivered: 10,
    weight_delivered: 2000,
    cargo_description: "Industrial widgets",
    delivery_condition: "Good",
    exceptions: "None",
    signature: "<PERSON>",
    driver_name: "<PERSON>",
    carrier_name: "FastFreight LLC",
    special_instructions: "Leave at dock.",
  },
};

export default {
  title: "Documents/Templates/ProofOfDeliveryTemplate",
  component: ProofOfDeliveryTemplate,
};

export const Default = () => (
  <ProofOfDeliveryTemplate documentData={mockProofOfDelivery} />
);
