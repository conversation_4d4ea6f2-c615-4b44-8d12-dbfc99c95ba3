import React from "react";
import {
  Calendar,
  Cpu,
  DollarSign,
  FileText,
  Hash,
  MapPin,
  Package,
  RefreshCw,
  Truck,
  Users,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

// Types for document data (matching our schema structure)
export interface ProcessedDocumentData {
  documentClass: string;
  subtype?: string;
  processingType: string;
  schemaVersion?: string;
  itemCategory?: string; // "physical_item" | "document"
  itemType?: string; // For physical items
  complianceType?: string; // For regulatory items (DOT, MC, etc.)
  processingNotes?: string; // Additional processing information
  extractedData: Record<string, unknown>;
  confidence?: Record<string, number>;
  compliance?: {
    requiredFields: string[];
    missingFields: string[];
    fieldValidationWarnings?: string[];
    overallConfidence?: number;
    requiresManualReview?: boolean;
  };
  enhancement?: string;
  metadata?: {
    processingTime?: string;
    specializationLevel?: string;
    confidenceThreshold?: number;
  };
}

// Schema field definition (simplified for frontend use)
export interface FieldDefinition {
  key: string;
  label: string;
  type: string;
  description?: string;
  uiComponent?: string;
  sensitive?: boolean;
}

export interface UISection {
  name: string;
  label: string;
  fields: string[];
  collapsible?: boolean;
  order?: number;
}

export interface DocumentSchema {
  documentType: string;
  category: string;
  subtype?: string;
  version: string;
  description?: string;
  fields: FieldDefinition[];
  requiredFields: string[];
  optionalFields: string[];
  uiMapping: {
    layout: string;
    sections: UISection[];
    readOnlyFields?: string[];
    hiddenFields?: string[];
  };
}

// Type for raw metadata input (what comes from the database)
interface RawDocumentMetadata {
  processedData?: ProcessedDocumentData;
  filePackage?: {
    originalName: string;
    sanitizedName: string;
    contentType: string;
    fileSize: number;
    fileExtension: string;
    fileId: string;
    filePath: string;
  };
  identification?: {
    documentType: string;
    confidence: number;
    extractedFields: Record<string, unknown>;
    pageCount?: number;
    warnings: string[];
  };
  analysis?: Record<string, unknown> | string;
  [key: string]: unknown;
}

// Type for legacy analysis format (backwards compatibility)
interface LegacyAnalysis {
  documentType?: string;
  extractedFields?: Record<string, unknown>;
  warnings?: string[];
  confidence?: number;
  identification?: {
    documentType: string;
    confidence: number;
    extractedFields: Record<string, unknown>;
    pageCount?: number;
    warnings: string[];
  };
  [key: string]: unknown;
}

interface DocumentTemplateProps {
  // New: Raw metadata approach (preferred)
  rawMetadata?: RawDocumentMetadata | null;

  // Legacy: Processed document data (for backwards compatibility)
  documentData?: ProcessedDocumentData;

  // Legacy analysis data (for backwards compatibility)
  legacyAnalysis?: LegacyAnalysis | null;

  // Optional props
  schema?: DocumentSchema;
  className?: string;
  isPhysicalItem?: boolean; // Indicates if this is a physical item vs document
  templateHint?: string; // Hint for specialized generic rendering

  // Refresh functionality
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

export default function DocumentTemplate({
  rawMetadata,
  documentData: legacyDocumentData,
  legacyAnalysis,
  schema,
  className = "",
  isPhysicalItem,
  templateHint,
  onRefresh,
  isRefreshing = false,
}: DocumentTemplateProps) {
  // Transform raw metadata into ProcessedDocumentData
  const transformMetadataToProcessedData = (
    metadata: RawDocumentMetadata | null,
  ): ProcessedDocumentData | null => {
    if (!metadata) return null;

    // Use stored ProcessedDocumentData directly if available (new format)
    if (metadata.processedData) {
      console.log("✅ Using stored processedData:", metadata.processedData);
      return metadata.processedData;
    }

    // Try to extract from metadata.analysis (reanalysis format)
    if (metadata.analysis && typeof metadata.analysis === "object") {
      const analysis = metadata.analysis as Record<string, unknown>;
      console.log("🔍 Building from analysis object:", {
        hasDocumentType: !!analysis.documentType,
        documentType: analysis.documentType,
        hasExtractedFields: !!analysis.extractedFields,
        extractedFieldsKeys: analysis.extractedFields
          ? Object.keys(analysis.extractedFields as Record<string, unknown>)
          : [],
        hasIdentification: !!analysis.identification,
      });

      // Check if we have the direct fields from reanalysis
      if (analysis.documentType && analysis.extractedFields) {
        return {
          documentClass: analysis.documentType as string,
          subtype: undefined,
          processingType: "reanalysis_converted",
          schemaVersion: undefined,
          extractedData: analysis.extractedFields as Record<string, unknown>,
          confidence: undefined,
          compliance: {
            requiredFields: [],
            missingFields: [],
            fieldValidationWarnings: (analysis.warnings as string[]) || [],
            overallConfidence: analysis.confidence as number,
            requiresManualReview: false,
          },
          enhancement: undefined,
          metadata: {
            processingTime: new Date().toISOString(),
            specializationLevel: "reanalysis",
            confidenceThreshold: 0.7,
          },
        };
      }

      // Fallback: Try nested identification structure
      if (
        analysis.identification &&
        typeof analysis.identification === "object"
      ) {
        const identification = analysis.identification as Record<
          string,
          unknown
        >;
        return {
          documentClass: (identification.documentType as string) || "unknown",
          subtype: undefined,
          processingType: "identification_converted",
          schemaVersion: undefined,
          extractedData:
            (identification.extractedFields as Record<string, unknown>) || {},
          confidence: undefined,
          compliance: {
            requiredFields: [],
            missingFields: [],
            fieldValidationWarnings:
              (identification.warnings as string[]) || [],
            overallConfidence: identification.confidence as number,
            requiresManualReview: false,
          },
          enhancement: undefined,
          metadata: {
            processingTime: new Date().toISOString(),
            specializationLevel: "identification",
            confidenceThreshold: 0.7,
          },
        };
      }
    }

    // Fallback: Try to construct from identification data (original structure)
    if (metadata.identification) {
      console.log(
        "Building ProcessedDocumentData from identification:",
        metadata.identification,
      );
      return {
        documentClass: metadata.identification.documentType || "unknown",
        subtype: undefined,
        processingType: "legacy_converted",
        schemaVersion: undefined,
        extractedData: metadata.identification.extractedFields || {},
        confidence: undefined,
        compliance: {
          requiredFields: [],
          missingFields: [],
          fieldValidationWarnings: metadata.identification.warnings || [],
          overallConfidence: metadata.identification.confidence,
          requiresManualReview: false,
        },
        enhancement: undefined,
        metadata: {
          processingTime: new Date().toISOString(),
          specializationLevel: "basic",
          confidenceThreshold: 0.7,
        },
      };
    }

    return null;
  };

  // Transform legacy analysis to ProcessedDocumentData
  const transformLegacyAnalysisToProcessedData = (
    analysis: LegacyAnalysis | null,
  ): ProcessedDocumentData | null => {
    if (!analysis) return null;

    // Check if we have the direct fields from legacy analysis
    if (analysis.documentType && analysis.extractedFields) {
      return {
        documentClass: analysis.documentType,
        subtype: undefined,
        processingType: "legacy_analysis_converted",
        schemaVersion: undefined,
        extractedData: analysis.extractedFields,
        confidence: undefined,
        compliance: {
          requiredFields: [],
          missingFields: [],
          fieldValidationWarnings: analysis.warnings || [],
          overallConfidence: analysis.confidence,
          requiresManualReview: false,
        },
        enhancement: undefined,
        metadata: {
          processingTime: new Date().toISOString(),
          specializationLevel: "legacy",
          confidenceThreshold: 0.7,
        },
      };
    }

    // Try nested identification structure
    if (analysis.identification) {
      return {
        documentClass: analysis.identification.documentType || "unknown",
        subtype: undefined,
        processingType: "legacy_identification_converted",
        schemaVersion: undefined,
        extractedData: analysis.identification.extractedFields || {},
        confidence: undefined,
        compliance: {
          requiredFields: [],
          missingFields: [],
          fieldValidationWarnings: analysis.identification.warnings || [],
          overallConfidence: analysis.identification.confidence,
          requiresManualReview: false,
        },
        enhancement: undefined,
        metadata: {
          processingTime: new Date().toISOString(),
          specializationLevel: "legacy_identification",
          confidenceThreshold: 0.7,
        },
      };
    }

    return null;
  };

  // Determine the final ProcessedDocumentData to use
  const documentData =
    legacyDocumentData ||
    transformMetadataToProcessedData(rawMetadata) ||
    transformLegacyAnalysisToProcessedData(legacyAnalysis);

  // If we still don't have processable data, create a minimal structure
  const finalDocumentData: ProcessedDocumentData = documentData || {
    documentClass: "unknown",
    subtype: undefined,
    processingType: "no_data",
    schemaVersion: undefined,
    extractedData: {},
    confidence: undefined,
    compliance: {
      requiredFields: [],
      missingFields: [],
      fieldValidationWarnings: [],
      overallConfidence: 0,
      requiresManualReview: true,
    },
    enhancement: undefined,
    metadata: {
      processingTime: new Date().toISOString(),
      specializationLevel: "none",
      confidenceThreshold: 0.7,
    },
  };

  const {
    extractedData,
    compliance,
    metadata,
    itemCategory,
    itemType,
    complianceType,
    processingNotes,
  } = finalDocumentData;

  // Determine if this is actually a physical item
  const isActuallyPhysicalItem =
    isPhysicalItem || itemCategory === "physical_item";

  // Get field definition from schema
  const getFieldDefinition = (fieldKey: string): FieldDefinition | null => {
    return schema?.fields.find((field) => field.key === fieldKey) || null;
  };

  // Enhanced field rendering for physical items and specialized templates
  const renderFieldValue = (
    fieldKey: string,
    value: unknown,
  ): React.ReactNode => {
    const fieldDef = getFieldDefinition(fieldKey);

    // Handle empty/null values
    if (value === null || value === undefined || value === "") {
      const isMissing = compliance?.missingFields.includes(fieldKey);
      return (
        <span className="text-muted-foreground italic">
          {isMissing ? "Missing (Required)" : "Not provided"}
        </span>
      );
    }

    // Handle sensitive fields
    if (fieldDef?.sensitive) {
      return (
        <span className="bg-muted rounded px-2 py-1 font-mono">••••••••</span>
      );
    }

    // Physical item specific rendering
    if (isActuallyPhysicalItem) {
      const lowerKey = fieldKey.toLowerCase();

      // Highlight important identification numbers
      if (
        lowerKey.includes("number") ||
        lowerKey.includes("id") ||
        fieldKey === "primary_number"
      ) {
        return (
          <span className="rounded border-2 border-blue-200 bg-blue-50 px-2 py-1 font-mono text-lg font-bold">
            {String(value)}
          </span>
        );
      }

      // Show compliance status prominently
      if (lowerKey.includes("compliance") || lowerKey.includes("status")) {
        const isCompliant =
          String(value).toLowerCase().includes("pass") ||
          String(value).toLowerCase().includes("compliant") ||
          String(value).toLowerCase().includes("good");
        return (
          <Badge
            variant={isCompliant ? "default" : "destructive"}
            className="text-sm"
          >
            {String(value)}
          </Badge>
        );
      }

      // Show condition/integrity status
      if (lowerKey.includes("condition") || lowerKey.includes("integrity")) {
        const isGood =
          String(value).toLowerCase().includes("good") ||
          String(value).toLowerCase().includes("intact") ||
          String(value).toLowerCase().includes("secure");
        return (
          <Badge variant={isGood ? "default" : "destructive"}>
            {String(value)}
          </Badge>
        );
      }
    }

    // Type-specific rendering
    switch (fieldDef?.type) {
      case "currency":
        return (
          <span className="font-mono">
            {typeof value === "number" ? `$${value.toFixed(2)}` : `$${value}`}
          </span>
        );

      case "weight":
        return (
          <span className="font-mono">
            {typeof value === "number" ? `${value} lbs` : `${value} lbs`}
          </span>
        );

      case "date":
        return (
          <span>
            {typeof value === "string"
              ? new Date(value).toLocaleDateString()
              : String(value)}
          </span>
        );

      case "boolean":
        return (
          <Badge variant={value ? "default" : "secondary"}>
            {value ? "Yes" : "No"}
          </Badge>
        );

      case "address":
        return (
          <div className="text-sm whitespace-pre-line">{String(value)}</div>
        );

      default:
        return <span>{String(value)}</span>;
    }
  };

  // Render confidence indicator
  const renderConfidence = (fieldKey: string) => {
    const confidence = finalDocumentData.confidence?.[fieldKey];
    if (!confidence) return null;

    const confidencePercent = Math.round(confidence * 100);
    const colorClass =
      confidencePercent >= 90
        ? "text-green-600"
        : confidencePercent >= 70
          ? "text-yellow-600"
          : "text-red-600";

    return (
      <span className={`ml-2 text-xs ${colorClass}`}>
        ({confidencePercent}%)
      </span>
    );
  };

  // Render a single field
  const renderField = (fieldKey: string) => {
    const fieldDef = getFieldDefinition(fieldKey);
    const value = extractedData[fieldKey];
    const isRequired = schema?.requiredFields.includes(fieldKey);
    const isMissing = compliance?.missingFields.includes(fieldKey);

    return (
      <div key={fieldKey} className="space-y-1">
        <div className="flex items-center gap-2">
          <label
            className={`text-sm font-medium ${isMissing ? "text-red-600" : ""}`}
          >
            {fieldDef?.label || fieldKey}
            {isRequired && <span className="ml-1 text-red-500">*</span>}
          </label>
          {renderConfidence(fieldKey)}
        </div>
        <div className="text-sm">{renderFieldValue(fieldKey, value)}</div>
        {fieldDef?.description && (
          <div className="text-muted-foreground text-xs">
            {fieldDef.description}
          </div>
        )}
      </div>
    );
  };

  // Render sections if schema is available
  const renderSections = () => {
    if (!schema?.uiMapping.sections) {
      // Fallback: render all fields without sections
      return (
        <div className="grid gap-4 md:grid-cols-2">
          {Object.keys(extractedData || {}).map(renderField)}
        </div>
      );
    }

    return schema.uiMapping.sections
      .sort((a, b) => (a.order || 0) - (b.order || 0))
      .map((section) => (
        <Card key={section.name} className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">{section.label}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              {section.fields.map(renderField)}
            </div>
          </CardContent>
        </Card>
      ));
  };

  // Enhanced generic template with intelligent field organization

  // Enhanced generic template with intelligent field organization
  const renderGenericTemplate = () => {
    if (!extractedData || Object.keys(extractedData).length === 0) {
      return (
        <div className="py-8 text-center">
          <FileText className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
          <h3 className="mb-2 text-lg font-medium">No Extracted Data</h3>
          <p className="text-muted-foreground">
            {isActuallyPhysicalItem
              ? "This physical item hasn't been processed for data extraction yet."
              : "This document hasn't been processed for data extraction yet."}
          </p>
        </div>
      );
    }

    // Physical item specific rendering
    if (isActuallyPhysicalItem) {
      return renderPhysicalItemTemplate();
    }

    // Template hints are now handled by specialized template components
    // This generic template focuses on intelligent categorization

    // Standard intelligent categorization for documents
    return renderCategorizedDocumentTemplate();
  };

  // Physical item template
  const renderPhysicalItemTemplate = () => {
    const itemTitle = itemType
      ? itemType.replace(/_/g, " ")
      : documentData.documentClass?.replace(/_/g, " ") || "Physical Item";

    return (
      <div className="space-y-6">
        {/* Header with item type and compliance status */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3">
              <Package className="h-6 w-6 text-blue-600" />
              <h3 className="text-xl font-semibold capitalize">{itemTitle}</h3>
              {complianceType && (
                <Badge variant="outline" className="font-mono">
                  {complianceType}
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground mt-1">
              Physical item scanned for identification and compliance
            </p>
            {processingNotes && (
              <p className="mt-1 text-sm text-blue-600">{processingNotes}</p>
            )}
          </div>
        </div>

        {/* Primary identification section */}
        <Card className="border-2 border-blue-200 bg-blue-50/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-blue-800">
              Primary Identification
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {renderPhysicalItemField("primary_number", "Primary Number")}
              {renderPhysicalItemField("item_number", "Item Number")}
              {renderPhysicalItemField("seal_number", "Seal Number")}
              {renderPhysicalItemField("dot_number", "DOT Number")}
              {renderPhysicalItemField("mc_number", "MC Number")}
              {renderPhysicalItemField("unit_number", "Unit Number")}
              {renderPhysicalItemField("container_number", "Container Number")}
              {renderPhysicalItemField("license_plate", "License Plate")}
              {renderPhysicalItemField("vin_number", "VIN Number")}
            </div>
          </CardContent>
        </Card>

        {/* Status and condition */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Status & Condition</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              {renderPhysicalItemField("condition", "Physical Condition")}
              {renderPhysicalItemField("integrity_status", "Integrity Status")}
              {renderPhysicalItemField(
                "compliance_status",
                "Compliance Status",
              )}
              {renderPhysicalItemField("tamper_evident", "Tamper Evidence")}
              {renderPhysicalItemField("secure", "Security Status")}
            </div>
          </CardContent>
        </Card>

        {/* Additional information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Additional Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              {Object.entries(extractedData)
                .filter(([key]) => !isPhysicalItemPrimaryField(key))
                .map(([key, value]) => (
                  <div key={key}>{renderField(key)}</div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Helper function to render physical item fields
  const renderPhysicalItemField = (fieldKey: string, label: string) => {
    const value = extractedData[fieldKey];
    if (!value) return null;

    return (
      <div className="space-y-1">
        <Label className="text-sm font-medium">{label}</Label>
        <div className="text-sm">{renderFieldValue(fieldKey, value)}</div>
      </div>
    );
  };

  // Helper to check if a field is a primary identification field
  const isPhysicalItemPrimaryField = (fieldKey: string): boolean => {
    const primaryFields = [
      "primary_number",
      "item_number",
      "seal_number",
      "dot_number",
      "mc_number",
      "unit_number",
      "container_number",
      "license_plate",
      "vin_number",
      "condition",
      "integrity_status",
      "compliance_status",
      "tamper_evident",
      "secure",
    ];
    return primaryFields.includes(fieldKey);
  };

  // Standard categorized document template
  const renderCategorizedDocumentTemplate = () => {
    // Intelligently categorize fields based on common patterns
    const categorizeFields = () => {
      const categories: Record<
        string,
        { label: string; fields: Array<{ key: string; value: unknown }> }
      > = {
        identification: { label: "Document Information", fields: [] },
        parties: { label: "Parties & Contacts", fields: [] },
        financial: { label: "Financial Information", fields: [] },
        logistics: { label: "Logistics & Shipping", fields: [] },
        cargo: { label: "Cargo & Items", fields: [] },
        dates: { label: "Important Dates", fields: [] },
        location: { label: "Locations", fields: [] },
        other: { label: "Additional Information", fields: [] },
      };

      Object.entries(extractedData).forEach(([key, value]) => {
        const lowerKey = key.toLowerCase();

        // Identification fields
        if (
          lowerKey.includes("number") ||
          lowerKey.includes("id") ||
          lowerKey.includes("reference") ||
          lowerKey.includes("order") ||
          lowerKey.includes("invoice") ||
          lowerKey.includes("po")
        ) {
          categories.identification.fields.push({ key, value });
        }
        // Party/Contact fields
        else if (
          lowerKey.includes("shipper") ||
          lowerKey.includes("consignee") ||
          lowerKey.includes("buyer") ||
          lowerKey.includes("seller") ||
          lowerKey.includes("customer") ||
          lowerKey.includes("vendor") ||
          lowerKey.includes("contact") ||
          lowerKey.includes("company") ||
          lowerKey.includes("name")
        ) {
          categories.parties.fields.push({ key, value });
        }
        // Financial fields
        else if (
          lowerKey.includes("cost") ||
          lowerKey.includes("price") ||
          lowerKey.includes("amount") ||
          lowerKey.includes("total") ||
          lowerKey.includes("fee") ||
          lowerKey.includes("charge") ||
          lowerKey.includes("rate") ||
          lowerKey.includes("currency")
        ) {
          categories.financial.fields.push({ key, value });
        }
        // Logistics fields
        else if (
          lowerKey.includes("vessel") ||
          lowerKey.includes("container") ||
          lowerKey.includes("voyage") ||
          lowerKey.includes("service") ||
          lowerKey.includes("carrier") ||
          lowerKey.includes("freight") ||
          lowerKey.includes("transport") ||
          lowerKey.includes("mode") ||
          lowerKey.includes("equipment")
        ) {
          categories.logistics.fields.push({ key, value });
        }
        // Cargo fields
        else if (
          lowerKey.includes("cargo") ||
          lowerKey.includes("goods") ||
          lowerKey.includes("commodity") ||
          lowerKey.includes("description") ||
          lowerKey.includes("weight") ||
          lowerKey.includes("volume") ||
          lowerKey.includes("quantity") ||
          lowerKey.includes("dimension") ||
          lowerKey.includes("package")
        ) {
          categories.cargo.fields.push({ key, value });
        }
        // Date fields
        else if (
          lowerKey.includes("date") ||
          lowerKey.includes("time") ||
          lowerKey.includes("schedule") ||
          lowerKey.includes("eta") ||
          lowerKey.includes("etd") ||
          lowerKey.includes("pickup") ||
          lowerKey.includes("delivery") ||
          lowerKey.includes("departure") ||
          lowerKey.includes("arrival")
        ) {
          categories.dates.fields.push({ key, value });
        }
        // Location fields
        else if (
          lowerKey.includes("port") ||
          lowerKey.includes("terminal") ||
          lowerKey.includes("address") ||
          lowerKey.includes("city") ||
          lowerKey.includes("country") ||
          lowerKey.includes("location") ||
          lowerKey.includes("origin") ||
          lowerKey.includes("destination") ||
          lowerKey.includes("place")
        ) {
          categories.location.fields.push({ key, value });
        }
        // Everything else
        else {
          categories.other.fields.push({ key, value });
        }
      });

      // Only return categories that have fields
      return Object.entries(categories)
        .filter(([_, category]) => category.fields.length > 0)
        .map(([key, category]) => ({ key, ...category }));
    };

    // Render individual field with smart formatting
    const renderSmartField = (key: string, value: unknown) => {
      const formatValue = (val: unknown): string => {
        if (val === null || val === undefined) return "—";
        if (typeof val === "object") {
          if (Array.isArray(val)) {
            return val
              .map((item) =>
                typeof item === "object" ? JSON.stringify(item) : String(item),
              )
              .join(", ");
          }
          return JSON.stringify(val, null, 2);
        }
        return String(val);
      };

      const formatKey = (key: string): string => {
        return key
          .replace(/([A-Z])/g, " $1")
          .replace(/[_-]/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase())
          .trim();
      };

      const isImportantField = (key: string): boolean => {
        const lowerKey = key.toLowerCase();
        return (
          lowerKey.includes("number") ||
          lowerKey.includes("total") ||
          lowerKey.includes("date") ||
          lowerKey.includes("name") ||
          lowerKey.includes("id") ||
          lowerKey.includes("reference")
        );
      };

      const formattedValue = formatValue(value);
      const isLongValue = formattedValue.length > 50;
      const isJsonValue =
        formattedValue.startsWith("{") || formattedValue.startsWith("[");

      return (
        <div
          key={key}
          className={cn(
            "group relative",
            isImportantField(key) &&
              "ring-primary/20 bg-primary/5 rounded-lg p-3 ring-1",
          )}
        >
          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between">
              <Label
                className={cn(
                  "text-sm font-medium",
                  isImportantField(key) && "text-primary",
                )}
              >
                {formatKey(key)}
              </Label>
              {isImportantField(key) && (
                <Badge variant="secondary" className="text-xs">
                  Key
                </Badge>
              )}
            </div>
            <div
              className={cn(
                "text-sm",
                isLongValue || isJsonValue ? "font-mono text-xs" : "",
                isJsonValue &&
                  "bg-muted max-h-32 overflow-y-auto rounded border p-2",
              )}
            >
              {isJsonValue ? (
                <pre className="whitespace-pre-wrap">{formattedValue}</pre>
              ) : (
                <span className={isImportantField(key) ? "font-medium" : ""}>
                  {formattedValue}
                </span>
              )}
            </div>
          </div>
        </div>
      );
    };

    const categories = categorizeFields();

    return (
      <div className="space-y-6">
        {/* Header with document class */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold capitalize">
              {(finalDocumentData.documentClass || "Document").replace(
                /_/g,
                " ",
              )}{" "}
              Analysis
            </h3>
            <p className="text-muted-foreground">
              Extracted {Object.keys(extractedData).length} fields from this
              document
            </p>
          </div>
          {finalDocumentData.confidence && (
            <Badge variant="outline" className="font-mono">
              {Object.entries(finalDocumentData.confidence)
                .map(([type, conf]) => `${type}: ${conf}%`)
                .join(", ")}
            </Badge>
          )}
        </div>

        {/* Categorized sections */}
        {categories.map(({ key, label, fields }) => (
          <Card key={key} className="relative">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                {key === "identification" && <Hash className="h-5 w-5" />}
                {key === "parties" && <Users className="h-5 w-5" />}
                {key === "financial" && <DollarSign className="h-5 w-5" />}
                {key === "logistics" && <Truck className="h-5 w-5" />}
                {key === "cargo" && <Package className="h-5 w-5" />}
                {key === "dates" && <Calendar className="h-5 w-5" />}
                {key === "location" && <MapPin className="h-5 w-5" />}
                {key === "other" && <FileText className="h-5 w-5" />}
                {label}
                <Badge variant="secondary" className="ml-auto">
                  {fields.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {fields.map(({ key, value }) => renderSmartField(key, value))}
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Processing metadata */}
        {finalDocumentData.metadata && (
          <Card className="border-dashed">
            <CardHeader className="pb-3">
              <CardTitle className="text-muted-foreground flex items-center gap-2 text-sm">
                <Cpu className="h-4 w-4" />
                Processing Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-muted-foreground grid gap-2 text-xs">
                {Object.entries(finalDocumentData.metadata).map(
                  ([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="capitalize">
                        {key.replace(/([A-Z])/g, " $1").toLowerCase()}:
                      </span>
                      <span className="font-mono">{String(value)}</span>
                    </div>
                  ),
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Document Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl capitalize">
                {(finalDocumentData.documentClass || "unknown").replace(
                  /_/g,
                  " ",
                )}
                {finalDocumentData.subtype && (
                  <span className="text-muted-foreground ml-2">
                    ({finalDocumentData.subtype})
                  </span>
                )}
              </CardTitle>
              {schema?.description && (
                <p className="text-muted-foreground mt-1 text-sm">
                  {schema.description}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              {onRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  disabled={isRefreshing}
                >
                  <RefreshCw
                    className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                  />
                  {isRefreshing ? "Analyzing..." : "Refresh Analysis"}
                </Button>
              )}
              <Badge variant="outline">
                {finalDocumentData.processingType}
              </Badge>
              {compliance?.overallConfidence && (
                <Badge
                  variant={
                    compliance.overallConfidence >= 0.9
                      ? "default"
                      : "secondary"
                  }
                >
                  {Math.round(compliance.overallConfidence * 100)}% Confidence
                </Badge>
              )}
              {compliance?.requiresManualReview && (
                <Badge variant="destructive">Requires Review</Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Compliance Warnings */}
      {(compliance?.fieldValidationWarnings?.length ||
        compliance?.missingFields?.length) && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-yellow-800">
              Data Quality Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            {compliance.missingFields.length > 0 && (
              <div className="mb-3">
                <h4 className="mb-2 text-sm font-medium text-yellow-800">
                  Missing Required Fields:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {compliance.missingFields.map((field) => (
                    <Badge
                      key={field}
                      variant="outline"
                      className="border-yellow-400"
                    >
                      {getFieldDefinition(field)?.label || field}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {compliance.fieldValidationWarnings &&
              compliance.fieldValidationWarnings.length > 0 && (
                <div>
                  <h4 className="mb-2 text-sm font-medium text-yellow-800">
                    Validation Warnings:
                  </h4>
                  <ul className="list-inside list-disc space-y-1 text-sm text-yellow-700">
                    {compliance.fieldValidationWarnings.map(
                      (warning, index) => (
                        <li key={index}>{warning}</li>
                      ),
                    )}
                  </ul>
                </div>
              )}
          </CardContent>
        </Card>
      )}

      {/* Document Fields */}
      {renderGenericTemplate()}

      {/* Processing Metadata */}
      {metadata && (
        <Card className="border-slate-200 bg-slate-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Processing Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 text-sm md:grid-cols-3">
              {metadata.processingTime && (
                <div>
                  <label className="font-medium">Processed At:</label>
                  <div className="text-muted-foreground">
                    {new Date(metadata.processingTime).toLocaleString()}
                  </div>
                </div>
              )}
              {metadata.specializationLevel && (
                <div>
                  <label className="font-medium">Specialization:</label>
                  <div className="text-muted-foreground capitalize">
                    {metadata.specializationLevel}
                  </div>
                </div>
              )}
              {finalDocumentData.schemaVersion && (
                <div>
                  <label className="font-medium">Schema Version:</label>
                  <div className="text-muted-foreground">
                    {finalDocumentData.schemaVersion}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Named export for backwards compatibility
export { DocumentTemplate };

// Export types for external use
export type { RawDocumentMetadata, LegacyAnalysis };
