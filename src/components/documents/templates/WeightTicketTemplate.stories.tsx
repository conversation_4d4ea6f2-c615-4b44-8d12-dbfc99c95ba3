import React from "react";

import type { ProcessedDocumentData } from ".";

import { WeightTicketTemplate } from ".";

const mockWeightTicket: ProcessedDocumentData = {
  documentClass: "weight_ticket",
  processingType: "schema-driven",
  schemaVersion: "1.0.0",
  extractedData: {
    document_number: "***********-01",
    document_date: "2024-06-22",
    weigh_time: "09:15",
    scale_location: "Big Rig Scales, Hwy 1",
    scale_operator: "Sam Scale",
    truck_number: "TRK-789",
    trailer_number: "TRL-456",
    license_plate: "ABC-1234",
    driver_name: "<PERSON> Driver",
    gross_weight: "32000",
    tare_weight: "18000",
    net_weight: "14000",
    weight_unit: "lbs",
    commodity: "Industrial widgets",
    bol_reference: "BOL-123456",
    shipper: "Acme Shippers Inc.",
    consignee: "Widgets Unlimited",
    dot_inspection: true,
    overweight_flag: false,
    certified_weight: true,
    seal_number: "SEAL-9999",
    notes: "All weights certified.",
  },
};

export default {
  title: "Documents/Templates/WeightTicketTemplate",
  component: WeightTicketTemplate,
};

export const Default = () => (
  <WeightTicketTemplate documentData={mockWeightTicket} />
);
