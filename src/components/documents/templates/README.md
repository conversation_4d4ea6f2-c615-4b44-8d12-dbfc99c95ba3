# Document Template System

This directory contains the front-end template system for rendering processed documents. The system automatically selects the appropriate template based on the document type and schema data returned from the backend processing pipeline.

## Architecture

### Base Components

- **`DocumentTemplate.tsx`** - Generic base template that can render any document using schema definitions
- **`DocumentTemplateRouter.tsx`** - Smart router that automatically selects specialized templates
- **`[DocumentType]Template.tsx`** - Specialized templates for specific document types

### Current Specialized Templates

- **`BillOfLadingTemplate.tsx`** - Freight shipping documents with logistics-specific styling
- **`FuelReceiptTemplate.tsx`** - Fuel purchase receipts for expense tracking

## Usage

### Simple Usage (Automatic Template Selection)

```tsx
import { DocumentTemplateRouter } from '@/components/documents/templates';

function DocumentViewer({ processedDocument }: { processedDocument: ProcessedDocumentData }) {
  return (
    <DocumentTemplateRouter
      documentData={processedDocument}
      fallbackToGeneric={true}
    />
  );
}
```

### Using Specific Templates

```tsx
import { BillOfLadingTemplate } from '@/components/documents/templates';

function BOLViewer({ processedDocument }: { processedDocument: ProcessedDocumentData }) {
  return (
    <BillOfLadingTemplate
      documentData={processedDocument}
      showQuickSummary={true}
    />
  );
}
```

### Using Generic Template with Custom Schema

```tsx
import { DocumentTemplate } from '@/components/documents/templates';

function CustomDocumentViewer({ processedDocument, schema }: {
  processedDocument: ProcessedDocumentData;
  schema: DocumentSchema;
}) {
  return (
    <DocumentTemplate
      documentData={processedDocument}
      schema={schema}
    />
  );
}
```

## Document Data Structure

The templates expect a `ProcessedDocumentData` object from the backend:

```typescript
interface ProcessedDocumentData {
  documentClass: string;        // e.g., "bill_of_lading"
  subtype?: string;            // e.g., "standard"
  processingType: string;      // "schema-driven" or "legacy"
  schemaVersion?: string;      // Schema version used
  extractedData: Record<string, unknown>;  // Actual field data
  confidence?: Record<string, number>;     // AI confidence per field
  compliance?: {
    requiredFields: string[];
    missingFields: string[];
    fieldValidationWarnings?: string[];
    overallConfidence?: number;
    requiresManualReview?: boolean;
  };
  metadata?: {
    processingTime?: string;
    specializationLevel?: string;
    confidenceThreshold?: number;
  };
}
```

## Features

### Automatic Template Routing

The `DocumentTemplateRouter` automatically selects the best template:

1. **Specialized Template** - If a custom template exists for the document type
2. **Generic Template** - Falls back to schema-driven generic rendering
3. **Error Display** - Shows helpful error if no template can be found

### Safe Value Rendering

All templates handle missing or null values gracefully:

- Required missing fields show as "Missing (Required)" in red
- Optional missing fields show as "Not provided" in muted text
- Sensitive fields (like card numbers) are automatically masked
- Type-specific formatting (currency, dates, weights, etc.)

### Confidence Indicators

When available, confidence scores are displayed next to field values:

- 90%+ confidence: Green
- 70-89% confidence: Yellow
- <70% confidence: Red

### Data Quality Warnings

Templates automatically display:

- Missing required fields
- Field validation warnings
- Overall confidence scores
- Manual review flags

## Creating New Templates

### 1. Create the Schema

First, create a schema in the backend following the convention in `supabase/functions/process-document/schemas/`.

### 2. Create the Template Component

Create a new template file (e.g., `InvoiceTemplate.tsx`):

```tsx
import React from 'react';
import { DocumentTemplate, type ProcessedDocumentData, type DocumentSchema } from './DocumentTemplate';

const INVOICE_SCHEMA: DocumentSchema = {
  // Define your schema here - should match backend schema
};

export function InvoiceTemplate({ documentData, className = "" }: {
  documentData: ProcessedDocumentData;
  className?: string;
}) {
  return (
    <div className={className}>
      {/* Add any custom summary/highlights here */}

      <DocumentTemplate
        documentData={documentData}
        schema={INVOICE_SCHEMA}
      />
    </div>
  );
}

export const invoiceSchema = INVOICE_SCHEMA;
```

### 3. Register in Router

Add your template to `DocumentTemplateRouter.tsx`:

```tsx
// In the switch statement
case 'invoice':
case 'invoice.standard':
  return (
    <InvoiceTemplate
      documentData={documentData}
      className={className}
    />
  );
```

### 4. Export from Index

Add exports to `index.ts`:

```tsx
export { InvoiceTemplate, invoiceSchema } from './InvoiceTemplate';
```

## Field Type Rendering

The system automatically formats fields based on their type:

- **`currency`** - Formats as `$XX.XX`
- **`weight`** - Formats as `XX lbs`
- **`date`** - Formats using `toLocaleDateString()`
- **`boolean`** - Shows as Yes/No badges
- **`address`** - Multi-line display with proper formatting
- **`string`** - Plain text display
- **`number`** - Numeric display

## Best Practices

1. **Always provide fallbacks** for missing data
2. **Use type-safe casting** when working with `unknown` types from `extractedData`
3. **Create specialized summaries** for document types that benefit from them
4. **Follow the color conventions** (blue for BOL, green for receipts, etc.)
5. **Keep schemas in sync** between frontend and backend
6. **Test with missing/partial data** to ensure graceful degradation

## Integration with Existing Code

To integrate with your existing document viewing code:

```tsx
// Replace existing document display logic with:
import { DocumentTemplateRouter } from '@/components/documents/templates';

// In your component
<DocumentTemplateRouter
  documentData={processedDocumentFromBackend}
  fallbackToGeneric={true}
/>
```

The system is designed to be a drop-in replacement that enhances your existing document processing pipeline.
