import React from "react";
import { CreditCard, Fuel, MapPin, Receipt } from "lucide-react";

import type { DocumentSchema, ProcessedDocumentData } from "./DocumentTemplate";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DocumentTemplate } from "./DocumentTemplate";

// Fuel Receipt specific schema (matches our backend schema)
const FUEL_RECEIPT_SCHEMA: DocumentSchema = {
  documentType: "fuel_receipt",
  category: "miscellaneous",
  subtype: "standard",
  version: "1.0.0",
  description: "Standard fuel receipt for expense tracking and tax reporting",
  fields: [
    {
      key: "receipt_number",
      label: "Receipt Number",
      type: "string",
      description: "Unique receipt identification number",
    },
    {
      key: "purchase_date",
      label: "Purchase Date",
      type: "date",
      description: "Date of fuel purchase",
    },
    {
      key: "purchase_time",
      label: "Purchase Time",
      type: "string",
      description: "Time of fuel purchase",
    },
    {
      key: "station_name",
      label: "Station Name",
      type: "string",
      description: "Name of the fuel station",
    },
    {
      key: "station_address",
      label: "Station Address",
      type: "address",
      description: "Complete address of fuel station",
    },
    {
      key: "fuel_type",
      label: "Fuel Type",
      type: "string",
      description: "Type of fuel purchased (diesel, gasoline, etc.)",
    },
    {
      key: "gallons",
      label: "Gallons",
      type: "number",
      description: "Amount of fuel purchased in gallons",
    },
    {
      key: "price_per_gallon",
      label: "Price per Gallon",
      type: "currency",
      description: "Cost per gallon of fuel",
    },
    {
      key: "fuel_cost",
      label: "Fuel Cost",
      type: "currency",
      description: "Total cost of fuel purchased",
    },
    {
      key: "tax_amount",
      label: "Tax Amount",
      type: "currency",
      description: "Total tax amount on purchase",
    },
    {
      key: "total_amount",
      label: "Total Amount",
      type: "currency",
      description: "Total amount paid including taxes",
    },
    {
      key: "payment_method",
      label: "Payment Method",
      type: "string",
      description: "Method of payment (credit, debit, cash, fleet card)",
    },
    {
      key: "card_last_four",
      label: "Card Last Four",
      type: "string",
      description: "Last four digits of payment card",
      sensitive: true,
    },
    {
      key: "pump_number",
      label: "Pump Number",
      type: "string",
      description: "Pump number where fuel was dispensed",
    },
    {
      key: "odometer_reading",
      label: "Odometer Reading",
      type: "number",
      description: "Vehicle odometer reading at time of purchase",
    },
  ],
  requiredFields: [
    "receipt_number",
    "purchase_date",
    "station_name",
    "fuel_type",
    "gallons",
    "price_per_gallon",
    "fuel_cost",
    "total_amount",
    "payment_method",
  ],
  optionalFields: [
    "purchase_time",
    "station_address",
    "tax_amount",
    "card_last_four",
    "pump_number",
    "odometer_reading",
  ],
  uiMapping: {
    layout: "fuel_receipt_layout",
    sections: [
      {
        name: "receipt_info",
        label: "Receipt Information",
        fields: ["receipt_number", "purchase_date", "purchase_time"],
        order: 1,
      },
      {
        name: "station_info",
        label: "Station Information",
        fields: ["station_name", "station_address", "pump_number"],
        order: 2,
      },
      {
        name: "fuel_details",
        label: "Fuel Details",
        fields: ["fuel_type", "gallons", "price_per_gallon", "fuel_cost"],
        order: 3,
      },
      {
        name: "payment_info",
        label: "Payment Information",
        fields: [
          "payment_method",
          "card_last_four",
          "tax_amount",
          "total_amount",
        ],
        order: 4,
      },
      {
        name: "vehicle_info",
        label: "Vehicle Information",
        fields: ["odometer_reading"],
        collapsible: true,
        order: 5,
      },
    ],
  },
};

interface FuelReceiptTemplateProps {
  documentData: ProcessedDocumentData;
  className?: string;
  showQuickSummary?: boolean;
}

export function FuelReceiptTemplate({
  documentData,
  className = "",
  showQuickSummary = true,
}: FuelReceiptTemplateProps) {
  const { extractedData } = documentData;

  // Quick summary component for fuel receipt highlights
  const QuickSummary = () => {
    const totalAmount = extractedData.total_amount as number;
    const gallons = extractedData.gallons as number;
    const pricePerGallon = extractedData.price_per_gallon as number;
    const fuelType = extractedData.fuel_type;

    return (
      <Card className="mb-6 border-green-200 bg-green-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg text-green-800">
            <Fuel className="h-5 w-5" />
            Fuel Purchase Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            {/* Station */}
            <div className="flex items-center gap-3">
              <MapPin className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">Station</div>
                <div className="text-muted-foreground text-sm">
                  {String(extractedData.station_name) || "N/A"}
                </div>
              </div>
            </div>

            {/* Fuel Details */}
            <div className="flex items-center gap-3">
              <Fuel className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">Fuel</div>
                <div className="text-muted-foreground text-sm">
                  {gallons ? `${gallons} gal` : "N/A"}
                  {fuelType && ` ${String(fuelType)}`}
                </div>
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center gap-3">
              <Receipt className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">Price/Gallon</div>
                <div className="text-muted-foreground text-sm">
                  {pricePerGallon ? `$${pricePerGallon.toFixed(3)}` : "N/A"}
                </div>
              </div>
            </div>

            {/* Total */}
            <div>
              <div className="text-sm font-medium">Total Cost</div>
              <div className="font-mono text-lg text-green-600">
                {totalAmount ? `$${totalAmount.toFixed(2)}` : "N/A"}
              </div>
            </div>
          </div>

          {/* Receipt Number & Date Highlight */}
          <div className="mt-4 grid gap-4 border-t border-green-200 pt-4 md:grid-cols-2">
            {extractedData.receipt_number && (
              <div>
                <div className="text-xs tracking-wide text-green-600 uppercase">
                  Receipt Number
                </div>
                <div className="font-mono text-lg font-bold text-green-800">
                  {String(extractedData.receipt_number)}
                </div>
              </div>
            )}

            {extractedData.purchase_date && (
              <div>
                <div className="text-xs tracking-wide text-green-600 uppercase">
                  Purchase Date
                </div>
                <div className="text-lg font-bold text-green-800">
                  {new Date(
                    String(extractedData.purchase_date),
                  ).toLocaleDateString()}
                  {extractedData.purchase_time && (
                    <span className="text-muted-foreground ml-2 text-sm">
                      at {String(extractedData.purchase_time)}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Payment Method */}
          {extractedData.payment_method && (
            <div className="mt-4 border-t border-green-200 pt-4">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Payment:</span>
                <Badge variant="outline" className="border-green-400">
                  {String(extractedData.payment_method)}
                  {extractedData.card_last_four &&
                    ` ••••${String(extractedData.card_last_four)}`}
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={className}>
      {showQuickSummary && <QuickSummary />}

      <DocumentTemplate
        documentData={documentData}
        schema={FUEL_RECEIPT_SCHEMA}
      />
    </div>
  );
}

// Export for use in document routing
export const fuelReceiptSchema = FUEL_RECEIPT_SCHEMA;
