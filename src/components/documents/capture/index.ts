// Components
export { FileCapture } from "./file-capture";
export { AudioCapture } from "./audio-capture";
export { VideoCapture } from "./video-capture";
export { ImageCapture } from "./image-capture";
export { Capture } from "./capture";
export { FileList } from "./file-list";
export { UnifiedDropzone } from "./unified-dropzone";

// Hooks
export {
  useFileCapture,
  useAudioCapture,
  useVideoCapture,
  useImageCapture,
} from "./hooks";

// Types and utilities
export type {
  CapturedFile,
  MediaMetadata,
  CaptureComponentProps,
} from "./types";
export * from "./utils";
