import { useCallback, useState } from "react";

import { CapturedFile, CaptureHookProps } from "../types";
import { extractMetadata, generateId } from "../utils";

export function useFileCapture({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
}: CaptureHookProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFiles = useCallback(
    async (files: FileList | File[]) => {
      const fileArray = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...fileArray]);

      const capturedFiles: CapturedFile[] = await Promise.all(
        fileArray.map(async (file) => ({
          id: generateId(),
          file,
          type: "file" as const,
          source: "upload" as const,
          timestamp: new Date(),
          metadata: await extractMetadata(file),
        })),
      );

      onAdd(capturedFiles);
    },
    [onAdd],
  );

  const removeFile = useCallback((index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const clearFiles = useCallback(() => {
    setSelectedFiles([]);
    onClear();
  }, [onClear]);

  return {
    selectedFiles,
    isDragOver,
    setIsDragOver,
    handleFiles,
    removeFile,
    clearFiles,
  };
}
