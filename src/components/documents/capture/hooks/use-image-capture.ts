import { useCallback, useEffect, useRef, useState } from "react";

import { CapturedFile, CaptureHookProps } from "../types";
import { createFileFromBlob, extractMetadata, generateId } from "../utils";

export function useImageCapture({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
}: CaptureHookProps) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPreview, setCurrentPreview] = useState<{
    blob: Blob;
    url: string;
  } | null>(null);
  const [capturedImages, setCapturedImages] = useState<
    { blob: Blob; url: string }[]
  >([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const handleCapturedImage = useCallback(
    async (file: File, source: "upload" | "camera") => {
      const capturedFile: CapturedFile = {
        id: generateId(),
        file,
        type: "image",
        source,
        timestamp: new Date(),
        metadata: await extractMetadata(file),
      };

      onAdd([capturedFile]);
    },
    [onAdd],
  );

  const stopCamera = useCallback(() => {
    console.log("Stopping image capture camera...");

    // Stop all tracks
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => {
        console.log("Stopping track:", track.kind);
        track.stop();
      });
      streamRef.current = null;
    }

    // Clear video element
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    // Reset states
    setIsStreaming(false);
    setError(null);

    console.log("Image capture camera stopped and cleaned up");
  }, []);

  const startCamera = useCallback(async () => {
    try {
      setError(null);
      console.log("Requesting camera access for image capture...");

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      });

      console.log("Camera access granted, stream received:", stream);
      streamRef.current = stream;

      if (videoRef.current) {
        console.log("Assigning stream to video element");
        videoRef.current.srcObject = stream;

        // Set streaming immediately since we have the stream
        setIsStreaming(true);

        console.log("Image capture camera started successfully");
      } else {
        console.error("Video ref is null");
        setError("Video element not found");
        stopCamera();
        return;
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      setError(
        `Failed to access camera: ${err instanceof Error ? err.message : "Unknown error"}`,
      );
      stopCamera();
    }
  }, [stopCamera]);

  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) {
      console.error("Video ref or canvas ref is null");
      setError("Camera or canvas not available");
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext("2d");

    if (!context) {
      console.error("Cannot get canvas context");
      setError("Canvas context not available");
      return;
    }

    if (video.readyState !== 4) {
      console.error(
        "Video not ready for capture, readyState:",
        video.readyState,
      );
      setError("Video not ready for capture");
      return;
    }

    console.log("Capturing photo...", {
      videoWidth: video.videoWidth,
      videoHeight: video.videoHeight,
      readyState: video.readyState,
    });

    try {
      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the current video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob
      canvas.toBlob(
        async (blob) => {
          if (!blob) {
            console.error("Failed to create blob from canvas");
            setError("Failed to capture photo");
            return;
          }

          console.log("Photo captured successfully");

          // Clear any existing preview and create new one
          if (currentPreview) {
            URL.revokeObjectURL(currentPreview.url);
          }

          // Create image URL for preview
          const imageUrl = URL.createObjectURL(blob);

          // Set as current preview - don't add to captured images yet
          setCurrentPreview({ blob, url: imageUrl });
        },
        "image/png",
        0.9,
      );
    } catch (err) {
      console.error("Error capturing photo:", err);
      setError("Failed to capture photo");
    }
  }, [currentPreview]);

  const handleFiles = useCallback(
    async (files: FileList | File[]) => {
      const fileArray = Array.from(files).filter((file) =>
        file.type.startsWith("image/"),
      );
      setSelectedFiles((prev) => [...prev, ...fileArray]);

      // Auto-capture uploaded files immediately
      fileArray.forEach((file) => {
        handleCapturedImage(file, "upload");
      });
    },
    [handleCapturedImage],
  );

  // Add current preview to captured images
  const addCurrentPreview = useCallback(async () => {
    if (!currentPreview) return;

    const file = createFileFromBlob(
      currentPreview.blob,
      `photo-${Date.now()}.png`,
      "image/png",
    );

    await handleCapturedImage(file, "camera");

    // Clear the preview
    URL.revokeObjectURL(currentPreview.url);
    setCurrentPreview(null);
  }, [currentPreview, handleCapturedImage]);

  // Discard current preview
  const discardCurrentPreview = useCallback(() => {
    if (currentPreview) {
      URL.revokeObjectURL(currentPreview.url);
      setCurrentPreview(null);
    }
  }, [currentPreview]);

  // Function to capture image from captured images list (for previously saved images)
  const captureImage = useCallback(
    async (capturedImage: { blob: Blob; url: string }, index: number) => {
      const file = createFileFromBlob(
        capturedImage.blob,
        `photo-${Date.now()}.png`,
        "image/png",
      );

      await handleCapturedImage(file, "camera");

      // Remove from captured images after capturing
      setCapturedImages((prev) => {
        const removed = prev[index];
        URL.revokeObjectURL(removed.url);
        return prev.filter((_, i) => i !== index);
      });
    },
    [handleCapturedImage],
  );

  const removeImage = useCallback((index: number) => {
    setCapturedImages((prev) => {
      const removed = prev[index];
      URL.revokeObjectURL(removed.url);
      return prev.filter((_, i) => i !== index);
    });
  }, []);

  const removeFile = useCallback((index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
      if (currentPreview) {
        URL.revokeObjectURL(currentPreview.url);
      }
      capturedImages.forEach(({ url }) => URL.revokeObjectURL(url));
    };
  }, [stopCamera, currentPreview, capturedImages]);

  return {
    // State
    isStreaming,
    currentPreview,
    capturedImages,
    selectedFiles,
    error,
    isDragOver,
    setIsDragOver,

    // Refs
    videoRef,
    canvasRef,

    // Actions
    startCamera,
    stopCamera,
    capturePhoto,
    handleFiles,
    addCurrentPreview,
    discardCurrentPreview,
    removeImage,
    removeFile,
    captureImage, // For previously saved images
  };
}
