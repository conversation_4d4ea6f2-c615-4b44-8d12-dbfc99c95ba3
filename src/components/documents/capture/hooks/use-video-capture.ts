import { useCallback, useEffect, useRef, useState } from "react";

import { CapturedFile, CaptureHookProps } from "../types";
import { createFileFromBlob, extractMetadata, generateId } from "../utils";

export function useVideoCapture({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
}: CaptureHookProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPreview, setCurrentPreview] = useState<Blob | null>(null);
  const [recordedBlobs, setRecordedBlobs] = useState<Blob[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const playbackVideoRef = useRef<HTMLVideoElement | null>(null);

  const handleCapturedVideo = useCallback(
    async (file: File, source: "upload" | "camera") => {
      const capturedFile: CapturedFile = {
        id: generateId(),
        file,
        type: "video",
        source,
        timestamp: new Date(),
        metadata: await extractMetadata(file),
      };

      onAdd([capturedFile]);
    },
    [onAdd],
  );

  const stopCamera = useCallback(() => {
    console.log("Stopping camera...");

    // Stop recording if active
    if (isRecording && mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }

    // Clear timer
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Stop all tracks
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => {
        console.log("Stopping track:", track.kind);
        track.stop();
      });
      streamRef.current = null;
    }

    // Clear video element
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    // Reset states
    setIsStreaming(false);
    setRecordingTime(0);
    setError(null);

    console.log("Camera stopped and cleaned up");
  }, [isRecording]);

  const startCamera = useCallback(async () => {
    try {
      setError(null);
      console.log("Requesting camera access...");

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 },
        },
        audio: true,
      });

      console.log("Camera access granted, stream received:", stream);
      streamRef.current = stream;

      if (videoRef.current) {
        console.log("Assigning stream to video element");
        videoRef.current.srcObject = stream;

        // Set streaming immediately since we have the stream
        setIsStreaming(true);

        console.log("Camera started successfully");
      } else {
        console.error("Video ref is null");
        setError("Video element not found");
        stopCamera();
        return;
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      setError(
        `Failed to access camera: ${err instanceof Error ? err.message : "Unknown error"}`,
      );
      stopCamera();
    }
  }, [stopCamera]);

  const startRecording = useCallback(() => {
    if (!streamRef.current) {
      setError("No camera stream available for recording");
      return;
    }

    try {
      const mediaRecorder = new MediaRecorder(streamRef.current, {
        mimeType: "video/webm;codecs=vp9,opus",
      });

      mediaRecorderRef.current = mediaRecorder;

      const chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: "video/webm" });

        // Set as current preview instead of adding to recorded blobs
        setCurrentPreview(blob);
      };

      mediaRecorder.onerror = (event) => {
        console.error("MediaRecorder error:", event);
        setError("Recording failed");
        setIsRecording(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };

      mediaRecorder.start(1000);
      setIsRecording(true);
      setRecordingTime(0);

      // Fixed timer implementation
      intervalRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (err) {
      setError("Failed to start recording.");
      console.error("Error starting recording:", err);
    }
  }, []);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      setRecordingTime(0);
    }
  }, [isRecording]);

  const handleFiles = useCallback(
    async (files: FileList | File[]) => {
      const fileArray = Array.from(files).filter((file) =>
        file.type.startsWith("video/"),
      );
      setSelectedFiles((prev) => [...prev, ...fileArray]);

      // Auto-capture uploaded files immediately
      fileArray.forEach((file) => {
        handleCapturedVideo(file, "upload");
      });
    },
    [handleCapturedVideo],
  );

  // Add current preview to recorded videos
  const addCurrentPreview = useCallback(async () => {
    if (!currentPreview) return;

    const file = createFileFromBlob(
      currentPreview,
      `recording-${Date.now()}.webm`,
      "video/webm",
    );

    await handleCapturedVideo(file, "camera");

    // Clear the preview
    setCurrentPreview(null);
  }, [currentPreview, handleCapturedVideo]);

  // Discard current preview
  const discardCurrentPreview = useCallback(() => {
    setCurrentPreview(null);
  }, []);

  // Function to capture video from recorded blobs list (for previously saved videos)
  const captureRecording = useCallback(
    async (blob: Blob, index: number) => {
      const file = createFileFromBlob(
        blob,
        `recording-${Date.now()}.webm`,
        "video/webm",
      );

      await handleCapturedVideo(file, "camera");

      // Remove from recorded blobs after capturing
      setRecordedBlobs((prev) => prev.filter((_, i) => i !== index));
    },
    [handleCapturedVideo],
  );

  const playRecording = useCallback((blob: Blob) => {
    if (playbackVideoRef.current) {
      playbackVideoRef.current.src = URL.createObjectURL(blob);
      playbackVideoRef.current.play();
    }
  }, []);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  }, []);

  const removeFile = useCallback((index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const removeRecording = useCallback((index: number) => {
    setRecordedBlobs((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  return {
    // State
    isRecording,
    isStreaming,
    currentPreview,
    recordedBlobs,
    selectedFiles,
    error,
    recordingTime,
    isDragOver,
    setIsDragOver,

    // Refs
    videoRef,
    playbackVideoRef,

    // Actions
    startCamera,
    stopCamera,
    startRecording,
    stopRecording,
    handleFiles,
    addCurrentPreview,
    discardCurrentPreview,
    playRecording,
    removeFile,
    removeRecording,
    captureRecording, // For previously saved videos
    formatTime,
  };
}
