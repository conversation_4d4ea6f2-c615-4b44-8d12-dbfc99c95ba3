import { useCallback, useEffect, useRef, useState } from "react";

import { CapturedFile, CaptureHookProps } from "../types";
import { createFileFromBlob, extractMetadata, generateId } from "../utils";

export function useAudioCapture({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
}: CaptureHookProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [recordedBlobs, setRecordedBlobs] = useState<Blob[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const handleCapturedAudio = useCallback(
    async (file: File, source: "upload" | "microphone") => {
      const capturedFile: CapturedFile = {
        id: generateId(),
        file,
        type: "audio",
        source,
        timestamp: new Date(),
        metadata: await extractMetadata(file),
      };

      onAdd([capturedFile]);
    },
    [onAdd],
  );

  const stopRecording = useCallback(() => {
    console.log("Stopping audio recording...");

    // Stop recording if active
    if (isRecording && mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }

    // Clear timer
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Stop all tracks
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => {
        console.log("Stopping audio track:", track.kind);
        track.stop();
      });
      streamRef.current = null;
    }

    // Reset timer but keep recordings
    setRecordingTime(0);
    setError(null);

    console.log("Audio recording stopped and cleaned up");
  }, [isRecording]);

  const startRecording = useCallback(async () => {
    try {
      setError(null);
      console.log("Requesting microphone access...");

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        },
      });

      console.log("Microphone access granted, stream received:", stream);
      streamRef.current = stream;

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm;codecs=opus",
      });

      mediaRecorderRef.current = mediaRecorder;

      const chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: "audio/webm" });

        // Only add to recorded blobs for review - don't auto-capture
        setRecordedBlobs((prev) => [...prev, blob]);

        // Clean up stream after recording is complete
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error("MediaRecorder error:", event);
        setError("Recording failed");
        stopRecording(); // Cleanup on error
      };

      // Handle stream ending (e.g., user revokes permission)
      stream.getAudioTracks()[0].onended = () => {
        console.log("Audio track ended");
        stopRecording();
      };

      mediaRecorder.start(1000);
      setIsRecording(true);
      setRecordingTime(0);

      // Fixed timer implementation
      intervalRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);

      console.log("Audio recording started successfully");
    } catch (err) {
      console.error("Error accessing microphone:", err);
      setError(
        `Failed to access microphone: ${err instanceof Error ? err.message : "Unknown error"}`,
      );
      stopRecording(); // Cleanup on error
    }
  }, [stopRecording]);

  const handleFiles = useCallback(
    async (files: FileList | File[]) => {
      const fileArray = Array.from(files).filter((file) =>
        file.type.startsWith("audio/"),
      );
      setSelectedFiles((prev) => [...prev, ...fileArray]);

      // Auto-capture uploaded files immediately
      fileArray.forEach((file) => {
        handleCapturedAudio(file, "upload");
      });
    },
    [handleCapturedAudio],
  );

  // New function to capture recorded audio after review
  const captureRecording = useCallback(
    async (blob: Blob, index: number) => {
      const file = createFileFromBlob(
        blob,
        `recording-${Date.now()}.webm`,
        "audio/webm",
      );

      await handleCapturedAudio(file, "microphone");

      // Remove from recorded blobs after capturing
      setRecordedBlobs((prev) => prev.filter((_, i) => i !== index));
    },
    [handleCapturedAudio],
  );

  const playRecording = useCallback((blob: Blob) => {
    // Stop any currently playing audio
    if (audioRef.current) {
      audioRef.current.pause();
    }

    const audio = new Audio(URL.createObjectURL(blob));
    audioRef.current = audio;

    audio.onplay = () => setIsPlaying(true);
    audio.onpause = () => setIsPlaying(false);
    audio.onended = () => setIsPlaying(false);

    audio.play().catch((err) => {
      console.error("Error playing audio:", err);
      setIsPlaying(false);
    });
  }, []);

  const pausePlayback = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
  }, []);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  }, []);

  const removeFile = useCallback((index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const removeRecording = useCallback((index: number) => {
    setRecordedBlobs((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopRecording();
    };
  }, [stopRecording]);

  return {
    // State
    isRecording,
    recordedBlobs,
    selectedFiles,
    error,
    recordingTime,
    isPlaying,
    isDragOver,
    setIsDragOver,

    // Refs
    audioRef,

    // Actions
    startRecording,
    stopRecording,
    handleFiles,
    playRecording,
    pausePlayback,
    removeFile,
    removeRecording,
    captureRecording, // New function
    formatTime,
  };
}
