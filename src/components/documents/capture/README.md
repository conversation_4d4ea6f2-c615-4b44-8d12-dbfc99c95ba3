# Document Capture Components

This directory contains the core capture functionality for different media types. These components handle the initial capture phase and are designed to be modular and reusable with a modern hook-based architecture.

## Architecture

### Core Components

- **`FileCapture`** - Drag-and-drop file upload with multiple file support
- **`AudioCapture`** - Microphone recording + audio file upload with playback controls
- **`VideoCapture`** - Camera recording with audio + video file upload
- **`ImageCapture`** - Live camera preview with photo capture + image file upload
- **`Capture`** - Main component with responsive tabbed interface coordinating all capture types

### Shared Components

- **`UnifiedDropzone`** - Context-aware drop zone that accepts different file types based on active tab
- **`FileList`** - Reusable file list component with remove/clear functionality

### Custom Hooks

- **`useFileCapture`** - File upload logic and state management
- **`useAudioCapture`** - Microphone recording, playback, and audio file handling
- **`useVideoCapture`** - Camera streaming, video recording, and file handling
- **`useImageCapture`** - Camera streaming, photo capture, and image file handling

### Types

- **`CapturedFile`** - Represents a captured file with metadata and source information
- **`MediaMetadata`** - File metadata including dimensions, duration, MIME type, and size
- **`CaptureComponentProps`** - Common props interface for all capture components

### Utilities

- **`generateId`** - Generates unique IDs for captured files
- **`extractMetadata`** - Extracts metadata from files (dimensions, duration, etc.)
- **`formatFileSize`** - Formats file sizes in human-readable format
- **`createFileFromBlob`** - Creates File objects from Blob data

## Features

### Responsive Design

- Mobile-first approach with `sm:` breakpoints
- Adaptive layouts that work on all screen sizes
- Touch-friendly controls for mobile devices

### Dark Mode Support

- Full dark mode compatibility with proper contrast ratios
- Uses semantic color tokens for consistent theming
- Smooth transitions between light and dark modes

### Integrated Controls

- Video and image capture components have integrated capture/upload controls in the same block
- No visual separation between device capture and file upload options
- Clear visual dividers with "OR" labels

### Context-Aware Drop Zones

- Unified drop zone component that accepts files based on active tab context
- Files tab: accepts any file type
- Audio tab: accepts only audio files (audio/\*)
- Video tab: accepts only video files (video/\*)
- Image tab: accepts only image files (image/\*)

### Advanced Features

- Real-time camera preview for video and image capture
- Audio recording with time tracking and playback controls
- Automatic metadata extraction for all file types
- File validation and filtering by type
- Progressive enhancement with graceful fallbacks

## Usage

```tsx
import { Capture, CapturedFile } from '@/views/documents/capture';

function MyComponent() {
  const handleCapture = (files: CapturedFile[]) => {
    console.log('Captured files:', files);
    // Handle the captured files
  };

  return (
    <Capture onCapture={handleCapture} />
  );
}
```

### Using Individual Components

```tsx
import { VideoCapture, useVideoCapture } from '@/views/documents/capture';

function CustomVideoCapture() {
  const {
    isRecording,
    startCamera,
    startRecording,
    stopRecording,
    // ... other hooks
  } = useVideoCapture(handleCapture);

  // Custom implementation with direct hook access
}
```

### Using Shared Components

```tsx
import { UnifiedDropzone, FileList } from '@/views/documents/capture';

function CustomUploader() {
  const [files, setFiles] = useState<File[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  return (
    <div>
      <UnifiedDropzone
        onFiles={(fileList) => setFiles(Array.from(fileList))}
        accept="image/*"
        isDragOver={isDragOver}
        setIsDragOver={setIsDragOver}
        title="Drop images here"
        description="JPG, PNG, GIF up to 10MB"
        buttonText="Browse Images"
      />

      <FileList
        files={files}
        onRemove={(index) => setFiles(prev => prev.filter((_, i) => i !== index))}
        title="Selected Images"
      />
    </div>
  );
}
```

## Technical Details

### Browser Compatibility

- Uses modern Web APIs (MediaDevices, MediaRecorder, Canvas)
- Graceful degradation for unsupported features
- Comprehensive error handling for permissions and API failures

### Performance

- Efficient file handling with streaming APIs
- Cleanup of object URLs and media streams
- Optimized re-renders with proper dependency arrays

### Accessibility

- Keyboard navigation support
- Screen reader friendly labels and descriptions
- High contrast mode compatibility
- Focus management for interactive elements
