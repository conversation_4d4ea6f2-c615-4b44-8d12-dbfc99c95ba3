import React from "react";

import { cn } from "@/lib/utils";
import { FileList } from "./file-list";
import { useFileCapture } from "./hooks/use-file-capture";
import { CaptureComponentProps } from "./types";
import { UnifiedDropzone } from "./unified-dropzone";

export const FileCapture: React.FC<CaptureComponentProps> = ({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
  disabled = false,
  className,
}) => {
  const {
    selectedFiles,
    isDragOver,
    setIsDragOver,
    handleFiles,
    removeFile,
    clearFiles,
  } = useFileCapture({ onAdd, onRemove, onUpdate, onClear, capturedFiles });

  return (
    <div className={cn("space-y-4 sm:space-y-6", className)}>
      <UnifiedDropzone
        onFiles={handleFiles}
        isDragOver={isDragOver}
        setIsDragOver={setIsDragOver}
        disabled={disabled}
        uploadButtonText="Browse Files"
        inputId="file-input"
        dropText="Drop any files here or click to browse"
        dragText="Drop files here"
      />

      <FileList
        files={selectedFiles}
        onRemove={removeFile}
        onClear={clearFiles}
        title="Selected Files"
      />
    </div>
  );
};
