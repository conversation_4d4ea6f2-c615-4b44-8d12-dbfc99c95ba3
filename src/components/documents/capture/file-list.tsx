import React from "react";
import { File, X } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { formatFileSize } from "./utils";

interface FileListProps {
  files: File[];
  onRemove: (index: number) => void;
  onClear?: () => void;
  title: string;
  className?: string;
}

export function FileList({
  files,
  onRemove,
  onClear,
  title,
  className,
}: FileListProps) {
  if (files.length === 0) return null;

  return (
    <Card className={className}>
      <CardContent className="p-3 sm:p-4">
        <div className="mb-3 flex items-center justify-between">
          <p className="dark:text-foreground text-sm font-medium sm:text-base">
            {title} ({files.length})
          </p>
          {onClear && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClear}
              className="dark:border-muted-foreground/20 text-xs sm:text-sm"
            >
              Clear All
            </Button>
          )}
        </div>
        <div className="space-y-2">
          {files.map((file, index) => (
            <div
              key={index}
              className="bg-muted/50 dark:bg-muted/20 flex min-w-0 items-center justify-between rounded-md p-2 sm:p-3"
            >
              <div className="flex min-w-0 flex-1 items-center gap-2 overflow-hidden">
                <File className="text-muted-foreground h-4 w-4 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2">
                    <span
                      className="dark:text-foreground max-w-[200px] truncate text-sm font-medium sm:max-w-[300px]"
                      title={file.name}
                    >
                      {file.name}
                    </span>
                    <span className="text-muted-foreground hidden flex-shrink-0 text-xs sm:inline">
                      ({formatFileSize(file.size)})
                    </span>
                  </div>
                  <span className="text-muted-foreground text-xs sm:hidden">
                    {formatFileSize(file.size)}
                  </span>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemove(index)}
                className="hover:bg-destructive/10 dark:hover:bg-destructive/20 ml-2 h-8 w-8 flex-shrink-0 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
