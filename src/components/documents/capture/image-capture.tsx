import React from "react";
import { Camera, Check, X } from "lucide-react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { FileList } from "./file-list";
import { useImageCapture } from "./hooks/use-image-capture";
import { CaptureComponentProps } from "./types";
import { UnifiedDropzone } from "./unified-dropzone";

export const ImageCapture: React.FC<CaptureComponentProps> = ({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
  disabled = false,
  className,
}) => {
  const {
    isStreaming,
    currentPreview,
    capturedImages,
    selectedFiles,
    error,
    isDragOver,
    setIsDragOver,
    videoRef,
    canvasRef,
    startCamera,
    stopCamera,
    capturePhoto,
    handleFiles,
    addCurrentPreview,
    discardCurrentPreview,
    removeImage,
    removeFile,
    captureImage,
  } = useImageCapture({ onAdd, onRemove, onUpdate, onClear, capturedFiles });

  return (
    <div className={cn("space-y-4 sm:space-y-6", className)}>
      {error && (
        <Alert variant="destructive">
          <AlertDescription className="dark:text-destructive-foreground">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Drop Zone - shown when not streaming and no preview */}
      <div className={cn((isStreaming || currentPreview) && "hidden")}>
        <UnifiedDropzone
          onFiles={handleFiles}
          accept="image/*"
          isDragOver={isDragOver}
          setIsDragOver={setIsDragOver}
          disabled={disabled}
          uploadButtonText="Browse Images"
          inputId="image-file-input"
          dropText="Drop images here or click to browse"
          dragText="Drop images here"
        >
          <div className="flex justify-center">
            <Button
              onClick={startCamera}
              disabled={disabled}
              className="min-w-[140px]"
            >
              <Camera className="mr-2 h-4 w-4" />
              Take Photo
            </Button>
          </div>
        </UnifiedDropzone>
      </div>

      {/* Camera Viewer / Preview - shown when streaming or has preview */}
      <div className={cn(!isStreaming && !currentPreview && "hidden")}>
        <Card className="dark:bg-card/50">
          <CardContent className="p-4 sm:p-6">
            {/* Camera Preview - shown when streaming, no preview */}
            <div
              className={cn(
                "relative mx-auto mb-4 aspect-video w-full max-w-2xl overflow-hidden rounded-lg bg-black",
                !isStreaming && "hidden",
              )}
            >
              <video
                ref={videoRef}
                className="h-full w-full object-cover"
                autoPlay
                muted
                playsInline
                onLoadedMetadata={() =>
                  console.log("Image capture video element metadata loaded")
                }
                onPlay={() =>
                  console.log("Image capture video element started playing")
                }
                onError={(e) =>
                  console.error("Image capture video element error:", e)
                }
              />

              {/* Debug indicator */}
              <div className="absolute bottom-2 left-2 rounded bg-black/50 px-2 py-1 text-xs text-white">
                Camera: Active
              </div>
            </div>

            {/* Photo Preview - shown when has preview */}
            <div
              className={cn(
                "relative mx-auto mb-4 w-full max-w-2xl overflow-hidden rounded-lg",
                !currentPreview && "hidden",
              )}
            >
              {currentPreview && (
                <img
                  src={currentPreview.url}
                  alt="Photo preview"
                  className="h-auto w-full rounded-lg object-contain"
                  style={{ maxHeight: "400px" }}
                />
              )}
            </div>

            {/* Camera Controls - shown when streaming */}
            <div
              className={cn(
                "flex flex-wrap justify-center gap-2 sm:gap-3",
                !isStreaming && "hidden",
              )}
            >
              <Button
                onClick={capturePhoto}
                disabled={disabled}
                className="min-w-[120px]"
              >
                <Camera className="mr-2 h-4 w-4" />
                Capture Photo
              </Button>

              <Button
                onClick={stopCamera}
                variant="outline"
                className="min-w-[120px]"
              >
                Stop Camera
              </Button>
            </div>

            {/* Preview Controls - shown when has preview */}
            <div
              className={cn(
                "flex flex-wrap justify-center gap-3",
                !currentPreview && "hidden",
              )}
            >
              <Button
                onClick={addCurrentPreview}
                className="min-w-[100px] bg-green-600 hover:bg-green-700"
                disabled={disabled}
              >
                <Check className="mr-2 h-4 w-4" />
                Add Photo
              </Button>
              <Button
                onClick={discardCurrentPreview}
                variant="outline"
                className="min-w-[100px]"
              >
                <X className="mr-2 h-4 w-4" />
                Discard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Hidden canvas for capturing */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Captured Images - only show when no current preview */}
      {capturedImages.length > 0 && !currentPreview && (
        <Card className="dark:bg-card/50">
          <CardContent className="p-3 sm:p-4">
            <p className="dark:text-foreground mb-3 text-sm font-medium sm:text-base">
              Previous Photos ({capturedImages.length})
            </p>
            <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 sm:gap-4 lg:grid-cols-4">
              {capturedImages.map((capturedImage, index) => (
                <div key={index} className="group relative">
                  <img
                    src={capturedImage.url}
                    alt={`Captured ${index + 1}`}
                    className="dark:border-muted-foreground/20 h-24 w-full rounded border object-cover sm:h-32"
                  />
                  <div className="absolute inset-0 flex items-center justify-center rounded bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
                    <div className="flex gap-1">
                      <Button
                        variant="secondary"
                        size="sm"
                        className="h-6 w-6 border-0 bg-green-500 p-0 hover:bg-green-600"
                        onClick={() => captureImage(capturedImage, index)}
                        title="Use this photo"
                      >
                        <Check className="h-3 w-3 text-white" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => removeImage(index)}
                        title="Remove photo"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selected Files */}
      <FileList
        files={selectedFiles}
        onRemove={removeFile}
        title="Selected Files"
      />
    </div>
  );
};
