import React from "react";
import { Check, Mic, Pause, Play, Square, X } from "lucide-react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { FileList } from "./file-list";
import { useAudioCapture } from "./hooks/use-audio-capture";
import { CaptureComponentProps } from "./types";
import { UnifiedDropzone } from "./unified-dropzone";

export const AudioCapture: React.FC<CaptureComponentProps> = ({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
  disabled = false,
  className,
}) => {
  const {
    isRecording,
    recordedBlobs,
    selectedFiles,
    error,
    recordingTime,
    isPlaying,
    isDragOver,
    setIsDragOver,
    startRecording,
    stopRecording,
    handleFiles,
    playRecording,
    pausePlayback,
    removeFile,
    removeRecording,
    captureRecording,
    formatTime,
  } = useAudioCapture({ onAdd, onRemove, onUpdate, onClear, capturedFiles });

  // For audio, we consider "active" when recording
  const isActive = isRecording;

  return (
    <div className={cn("space-y-4 sm:space-y-6", className)}>
      {error && (
        <Alert variant="destructive">
          <AlertDescription className="dark:text-destructive-foreground">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Conditional Rendering: Drop Zone OR Recording Interface */}
      {!isActive ? (
        /* Drop Zone with Start Recording Button */
        <UnifiedDropzone
          onFiles={handleFiles}
          accept="audio/*"
          isDragOver={isDragOver}
          setIsDragOver={setIsDragOver}
          disabled={disabled}
          uploadButtonText="Browse Audio Files"
          inputId="audio-file-input"
          dropText="Drop audio files here or click to browse"
          dragText="Drop audio files here"
        >
          <div className="flex justify-center">
            <Button
              onClick={startRecording}
              disabled={disabled}
              className="min-w-[140px]"
            >
              <Mic className="mr-2 h-4 w-4" />
              Start Recording
            </Button>
          </div>
        </UnifiedDropzone>
      ) : (
        /* Dedicated Recording Interface */
        <Card className="dark:bg-card/50">
          <CardContent className="p-6 sm:p-8">
            <div className="flex flex-col items-center space-y-6">
              {/* Recording Indicator */}
              <div className="flex items-center gap-4">
                <div className="flex h-16 w-16 animate-pulse items-center justify-center rounded-full bg-red-500 sm:h-20 sm:w-20">
                  <Mic className="h-8 w-8 text-white sm:h-10 sm:w-10" />
                </div>
              </div>

              {/* Recording Time */}
              <div className="text-center">
                <div className="text-foreground dark:text-foreground mb-2 font-mono text-3xl sm:text-4xl">
                  {formatTime(recordingTime)}
                </div>
                <div className="text-muted-foreground text-sm">
                  Recording...
                </div>
              </div>

              {/* Stop Recording Button */}
              <Button
                onClick={stopRecording}
                variant="outline"
                className="dark:border-muted-foreground/20 min-w-[140px]"
              >
                <Square className="mr-2 h-4 w-4" />
                Stop Recording
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recorded Audio */}
      {recordedBlobs.length > 0 && (
        <Card className="dark:bg-card/50">
          <CardContent className="p-3 sm:p-4">
            <p className="dark:text-foreground mb-3 text-sm font-medium sm:text-base">
              Recordings ({recordedBlobs.length})
            </p>
            <div className="space-y-2">
              {recordedBlobs.map((blob, index) => (
                <div
                  key={index}
                  className="bg-muted/50 dark:bg-muted/20 flex items-center justify-between rounded-md p-2 sm:p-3"
                >
                  <div className="flex min-w-0 flex-1 items-center gap-2">
                    <Mic className="text-muted-foreground h-4 w-4 flex-shrink-0" />
                    <span className="dark:text-foreground truncate text-sm font-medium">
                      Recording {index + 1}
                    </span>
                  </div>
                  <div className="flex flex-shrink-0 items-center gap-1 sm:gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        isPlaying ? pausePlayback() : playRecording(blob)
                      }
                      className="hover:bg-muted dark:hover:bg-muted/40 h-8 w-8 p-0"
                    >
                      {isPlaying ? (
                        <Pause className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => captureRecording(blob, index)}
                      className="h-8 w-8 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
                      title="Use this recording"
                    >
                      <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRecording(index)}
                      className="hover:bg-destructive/10 dark:hover:bg-destructive/20 h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selected Files */}
      <FileList
        files={selectedFiles}
        onRemove={removeFile}
        title="Selected Files"
      />
    </div>
  );
};
