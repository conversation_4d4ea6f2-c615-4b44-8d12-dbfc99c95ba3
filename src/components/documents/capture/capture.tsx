import React, { useCallback, useState } from "react";
import { Camera, Mic, Upload, Video } from "lucide-react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { AudioCapture } from "./audio-capture";
import { FileCapture } from "./file-capture";
import { ImageCapture } from "./image-capture";
import { CapturedFile } from "./types";
import { VideoCapture } from "./video-capture";

interface CaptureProps {
  onChange: (files: CapturedFile[]) => void;
  disabled?: boolean;
  className?: string;
}

export const Capture: React.FC<CaptureProps> = ({
  onChange,
  disabled = false,
  className,
}) => {
  const [activeTab, setActiveTab] = useState("files");
  const [capturedFiles, setCapturedFiles] = useState<CapturedFile[]>([]);

  // Add new files to the collection
  const handleAdd = useCallback(
    (newFiles: CapturedFile[]) => {
      setCapturedFiles((prev) => {
        const updated = [...prev, ...newFiles];
        onChange(updated);
        return updated;
      });
    },
    [onChange],
  );

  // Remove a file by ID
  const handleRemove = useCallback(
    (fileId: string) => {
      setCapturedFiles((prev) => {
        const updated = prev.filter((file) => file.id !== fileId);
        onChange(updated);
        return updated;
      });
    },
    [onChange],
  );

  // Update a file by ID
  const handleUpdate = useCallback(
    (fileId: string, updates: Partial<CapturedFile>) => {
      setCapturedFiles((prev) => {
        const updated = prev.map((file) =>
          file.id === fileId ? { ...file, ...updates } : file,
        );
        onChange(updated);
        return updated;
      });
    },
    [onChange],
  );

  // Clear all files
  const handleClear = useCallback(() => {
    setCapturedFiles([]);
    onChange([]);
  }, [onChange]);

  return (
    <div className={cn("w-full", className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        {/* Tab Navigation */}
        <TabsList className="dark:bg-muted/50 mb-4 grid w-full grid-cols-4 sm:mb-6">
          <TabsTrigger
            value="files"
            className="dark:data-[state=active]:bg-background flex items-center gap-1 text-xs sm:gap-2 sm:text-sm"
          >
            <Upload className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Files</span>
          </TabsTrigger>
          <TabsTrigger
            value="audio"
            className="dark:data-[state=active]:bg-background flex items-center gap-1 text-xs sm:gap-2 sm:text-sm"
          >
            <Mic className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Audio</span>
          </TabsTrigger>
          <TabsTrigger
            value="video"
            className="dark:data-[state=active]:bg-background flex items-center gap-1 text-xs sm:gap-2 sm:text-sm"
          >
            <Video className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Video</span>
          </TabsTrigger>
          <TabsTrigger
            value="image"
            className="dark:data-[state=active]:bg-background flex items-center gap-1 text-xs sm:gap-2 sm:text-sm"
          >
            <Camera className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Images</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab Content */}
        <TabsContent value="files" className="mt-0">
          <FileCapture
            onAdd={handleAdd}
            onRemove={handleRemove}
            onUpdate={handleUpdate}
            onClear={handleClear}
            capturedFiles={capturedFiles}
            disabled={disabled}
          />
        </TabsContent>

        <TabsContent value="audio" className="mt-0">
          <AudioCapture
            onAdd={handleAdd}
            onRemove={handleRemove}
            onUpdate={handleUpdate}
            onClear={handleClear}
            capturedFiles={capturedFiles}
            disabled={disabled}
          />
        </TabsContent>

        <TabsContent value="video" className="mt-0">
          <VideoCapture
            onAdd={handleAdd}
            onRemove={handleRemove}
            onUpdate={handleUpdate}
            onClear={handleClear}
            capturedFiles={capturedFiles}
            disabled={disabled}
          />
        </TabsContent>

        <TabsContent value="image" className="mt-0">
          <ImageCapture
            onAdd={handleAdd}
            onRemove={handleRemove}
            onUpdate={handleUpdate}
            onClear={handleClear}
            capturedFiles={capturedFiles}
            disabled={disabled}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
