import { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { fn } from "storybook/test";

import { Capture } from "./capture";

const meta: Meta<typeof Capture> = {
  title: "Views/Documents/Capture",
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: "Capture component for documents",
      },
    },
  },
  component: Capture,
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onChange: fn(),
  },
};
