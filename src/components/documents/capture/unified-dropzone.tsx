import React, { useCallback } from "react";
import { Upload } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface UnifiedDropzoneProps {
  onFiles: (files: FileList) => void;
  accept?: string;
  multiple?: boolean;
  disabled?: boolean;
  isDragOver: boolean;
  setIsDragOver: (isDragOver: boolean) => void;
  className?: string;
  children?: React.ReactNode;
  showUploadButton?: boolean;
  uploadButtonText?: string;
  inputId?: string;
  dropText?: string;
  dragText?: string;
}

export function UnifiedDropzone({
  onFiles,
  accept,
  multiple = true,
  disabled = false,
  isDragOver,
  setIsDragOver,
  className,
  children,
  showUploadButton = true,
  uploadButtonText = "Browse Files",
  inputId = "unified-file-input",
  dropText = "Drop files here or click to browse",
  dragText = "Drop files here",
}: UnifiedDropzoneProps) {
  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      if (disabled) return;

      const files = e.dataTransfer.files;
      if (files.length > 0) {
        onFiles(files);
      }
    },
    [onFiles, disabled, setIsDragOver],
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      if (!disabled) {
        setIsDragOver(true);
      }
    },
    [disabled, setIsDragOver],
  );

  const handleDragLeave = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX;
      const y = e.clientY;

      if (
        x < rect.left ||
        x >= rect.right ||
        y < rect.top ||
        y >= rect.bottom
      ) {
        setIsDragOver(false);
      }
    },
    [setIsDragOver],
  );

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        onFiles(e.target.files);
      }
    },
    [onFiles],
  );

  return (
    <Card
      className={cn(
        "border-2 border-dashed transition-all duration-200",
        "hover:border-primary/50 hover:bg-muted/10",
        "dark:hover:bg-muted/5",
        isDragOver
          ? "border-primary bg-primary/10 dark:bg-primary/5"
          : "border-muted-foreground/25 dark:border-muted-foreground/20",
        disabled &&
          "hover:border-muted-foreground/25 cursor-not-allowed opacity-50 hover:bg-transparent",
        className,
      )}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <CardContent className="p-4 sm:p-6">
        {isDragOver && (
          <div className="bg-primary/20 dark:bg-primary/10 border-primary absolute inset-0 z-10 flex items-center justify-center rounded-lg border-2">
            <div className="text-center">
              <Upload className="text-primary mx-auto mb-2 h-8 w-8 sm:h-12 sm:w-12" />
              <p className="text-primary text-sm font-medium sm:text-base">
                {dragText}
              </p>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {children}

          {showUploadButton && (
            <div className="flex flex-col items-center gap-3">
              <div className="flex items-center gap-3">
                <div className="bg-border dark:bg-border/50 h-px flex-1" />
                <span className="text-muted-foreground bg-background dark:bg-background/50 rounded-full px-2 py-1 text-xs">
                  OR
                </span>
                <div className="bg-border dark:bg-border/50 h-px flex-1" />
              </div>

              <div className="flex flex-col items-center gap-2">
                <Upload className="text-muted-foreground h-5 w-5" />
                <p className="text-muted-foreground text-center text-sm">
                  {dropText}
                </p>
              </div>

              <input
                type="file"
                multiple={multiple}
                accept={accept}
                onChange={handleFileInput}
                className="hidden"
                id={inputId}
                disabled={disabled}
              />
              <label htmlFor={inputId}>
                <Button
                  variant="outline"
                  asChild
                  disabled={disabled}
                  className="dark:border-muted-foreground/20 dark:hover:bg-muted/20"
                >
                  <span>{uploadButtonText}</span>
                </Button>
              </label>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
