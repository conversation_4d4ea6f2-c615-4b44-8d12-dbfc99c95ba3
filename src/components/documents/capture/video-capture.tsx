import React from "react";
import { Check, Play, Square, Video, X } from "lucide-react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { FileList } from "./file-list";
import { useVideoCapture } from "./hooks/use-video-capture";
import { CaptureComponentProps } from "./types";
import { UnifiedDropzone } from "./unified-dropzone";

export const VideoCapture: React.FC<CaptureComponentProps> = ({
  onAdd,
  onRemove,
  onUpdate,
  onClear,
  capturedFiles,
  disabled = false,
  className,
}) => {
  const {
    isRecording,
    isStreaming,
    currentPreview,
    recordedBlobs,
    selectedFiles,
    error,
    recordingTime,
    isDragOver,
    setIsDragOver,
    videoRef,
    playbackVideoRef,
    startCamera,
    stopCamera,
    startRecording,
    stopRecording,
    handleFiles,
    addCurrentPreview,
    discardCurrentPreview,
    playRecording,
    removeFile,
    removeRecording,
    captureRecording,
    formatTime,
  } = useVideoCapture({ onAdd, onRemove, onUpdate, onClear, capturedFiles });

  return (
    <div className={cn("space-y-4 sm:space-y-6", className)}>
      {error && (
        <Alert variant="destructive">
          <AlertDescription className="dark:text-destructive-foreground">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Drop Zone - shown when not streaming and no preview */}
      <div className={cn((isStreaming || currentPreview) && "hidden")}>
        <UnifiedDropzone
          onFiles={handleFiles}
          accept="video/*"
          isDragOver={isDragOver}
          setIsDragOver={setIsDragOver}
          disabled={disabled}
          uploadButtonText="Browse Video Files"
          inputId="video-file-input"
          dropText="Drop video files here or click to browse"
          dragText="Drop video files here"
        >
          <div className="flex justify-center">
            <Button
              onClick={startCamera}
              disabled={disabled}
              className="min-w-[140px]"
            >
              <Video className="mr-2 h-4 w-4" />
              Record Video
            </Button>
          </div>
        </UnifiedDropzone>
      </div>

      {/* Camera Viewer / Preview - shown when streaming or has preview */}
      <div className={cn(!isStreaming && !currentPreview && "hidden")}>
        <Card className="dark:bg-card/50">
          <CardContent className="p-4 sm:p-6">
            {/* Camera Preview - shown when streaming */}
            <div
              className={cn(
                "relative mx-auto mb-4 aspect-video w-full max-w-2xl overflow-hidden rounded-lg bg-black",
                !isStreaming && "hidden",
              )}
            >
              <video
                ref={videoRef}
                className="h-full w-full object-cover"
                autoPlay
                muted
                playsInline
                onLoadedMetadata={() =>
                  console.log("Video element metadata loaded")
                }
                onPlay={() => console.log("Video element started playing")}
                onError={(e) => console.error("Video element error:", e)}
              />

              {/* Debug indicator */}
              <div className="absolute bottom-2 left-2 rounded bg-black/50 px-2 py-1 text-xs text-white">
                Camera: Active
              </div>

              {isRecording && (
                <div className="absolute top-2 left-2 animate-pulse rounded-full bg-red-500 px-2 py-1 font-mono text-xs text-white sm:top-4 sm:left-4 sm:px-3 sm:text-sm">
                  REC {formatTime(recordingTime)}
                </div>
              )}
            </div>

            {/* Video Preview - shown when has preview */}
            <div
              className={cn(
                "relative mx-auto mb-4 w-full max-w-2xl overflow-hidden rounded-lg",
                !currentPreview && "hidden",
              )}
            >
              {currentPreview && (
                <video
                  controls
                  className="h-auto w-full rounded-lg"
                  style={{ maxHeight: "400px" }}
                  src={URL.createObjectURL(currentPreview)}
                />
              )}
            </div>

            {/* Camera Controls - shown when streaming */}
            <div
              className={cn(
                "flex flex-wrap justify-center gap-2 sm:gap-3",
                !isStreaming && "hidden",
              )}
            >
              {!isRecording ? (
                <Button
                  onClick={startRecording}
                  disabled={disabled}
                  className="min-w-[120px]"
                >
                  <Video className="mr-2 h-4 w-4" />
                  Start Recording
                </Button>
              ) : (
                <Button
                  onClick={stopRecording}
                  variant="destructive"
                  className="min-w-[120px]"
                >
                  <Square className="mr-2 h-4 w-4" />
                  Stop Recording
                </Button>
              )}

              <Button
                onClick={stopCamera}
                variant="outline"
                className="min-w-[120px]"
                disabled={isRecording}
              >
                Stop Camera
              </Button>
            </div>

            {/* Preview Controls - shown when has preview */}
            <div
              className={cn(
                "flex flex-wrap justify-center gap-3",
                !currentPreview && "hidden",
              )}
            >
              <Button
                onClick={addCurrentPreview}
                className="min-w-[100px] bg-green-600 hover:bg-green-700"
                disabled={disabled}
              >
                <Check className="mr-2 h-4 w-4" />
                Add Video
              </Button>
              <Button
                onClick={discardCurrentPreview}
                variant="outline"
                className="min-w-[100px]"
              >
                <X className="mr-2 h-4 w-4" />
                Discard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recorded Videos - only show when no current preview */}
      {recordedBlobs.length > 0 && !currentPreview && (
        <Card className="dark:bg-card/50">
          <CardContent className="p-3 sm:p-4">
            <p className="dark:text-foreground mb-3 text-sm font-medium sm:text-base">
              Previous Recordings ({recordedBlobs.length})
            </p>
            <div className="space-y-2">
              {recordedBlobs.map((blob, index) => (
                <div
                  key={index}
                  className="bg-muted/50 dark:bg-muted/20 flex items-center justify-between rounded-md p-2 sm:p-3"
                >
                  <div className="flex min-w-0 flex-1 items-center gap-2">
                    <Video className="text-muted-foreground h-4 w-4 flex-shrink-0" />
                    <span className="dark:text-foreground truncate text-sm font-medium">
                      Recording {index + 1}
                    </span>
                  </div>
                  <div className="flex flex-shrink-0 items-center gap-1 sm:gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => playRecording(blob)}
                      className="hover:bg-muted dark:hover:bg-muted/40 h-8 w-8 p-0"
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => captureRecording(blob, index)}
                      className="h-8 w-8 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
                      title="Use this recording"
                    >
                      <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRecording(index)}
                      className="hover:bg-destructive/10 dark:hover:bg-destructive/20 h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Playback Video (hidden) */}
      <video ref={playbackVideoRef} className="hidden" controls />

      {/* Selected Files */}
      <FileList
        files={selectedFiles}
        onRemove={removeFile}
        title="Selected Files"
      />
    </div>
  );
};
