export interface CapturedFile {
  id: string;
  file: File;
  type: "file" | "audio" | "video" | "image";
  source: "upload" | "camera" | "microphone";
  timestamp: Date;
  metadata?: MediaMetadata;
}

export interface MediaMetadata {
  fileName: string;
  mimeType: string;
  size: number;
  dimensions?: {
    width: number;
    height: number;
  };
  duration?: number; // in seconds for audio/video
}

export interface CaptureHookProps {
  onAdd: (newFiles: CapturedFile[]) => void;
  onRemove: (fileId: string) => void;
  onUpdate: (fileId: string, updates: Partial<CapturedFile>) => void;
  onClear: () => void;
  capturedFiles: CapturedFile[];
}

export interface CaptureComponentProps {
  onAdd: (newFiles: CapturedFile[]) => void;
  onRemove: (fileId: string) => void;
  onUpdate: (fileId: string, updates: Partial<CapturedFile>) => void;
  onClear: () => void;
  capturedFiles: CapturedFile[];
  disabled?: boolean;
  className?: string;
}
