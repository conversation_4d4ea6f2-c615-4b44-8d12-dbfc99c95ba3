import { MediaMetadata } from "./types";

export const generateId = () => Math.random().toString(36).substr(2, 9);

export const getMimeType = (file: File): string => {
  return file.type || "application/octet-stream";
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export const getImageDimensions = (
  file: File,
): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

export const getVideoDimensions = (
  file: File,
): Promise<{ width: number; height: number; duration: number }> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    video.onloadedmetadata = () => {
      resolve({
        width: video.videoWidth,
        height: video.videoHeight,
        duration: video.duration,
      });
    };
    video.onerror = reject;
    video.src = URL.createObjectURL(file);
  });
};

export const getAudioDuration = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    audio.onloadedmetadata = () => {
      resolve(audio.duration);
    };
    audio.onerror = reject;
    audio.src = URL.createObjectURL(file);
  });
};

export const extractMetadata = async (file: File): Promise<MediaMetadata> => {
  const metadata: MediaMetadata = {
    fileName: file.name,
    mimeType: getMimeType(file),
    size: file.size,
  };

  try {
    if (file.type.startsWith("image/")) {
      metadata.dimensions = await getImageDimensions(file);
    } else if (file.type.startsWith("video/")) {
      const videoData = await getVideoDimensions(file);
      metadata.dimensions = {
        width: videoData.width,
        height: videoData.height,
      };
      metadata.duration = videoData.duration;
    } else if (file.type.startsWith("audio/")) {
      metadata.duration = await getAudioDuration(file);
    }
  } catch (error) {
    console.warn("Failed to extract metadata for file:", file.name, error);
  }

  return metadata;
};

export const createFileFromBlob = (
  blob: Blob,
  filename: string,
  type: string,
): File => {
  return new File([blob], filename, { type });
};
