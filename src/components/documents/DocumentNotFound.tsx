import { ArrowLeft, FileText } from "lucide-react";
import { useNavigate } from "react-router";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export default function DocumentNotFound() {
  const navigate = useNavigate();

  return (
    <div className="container py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Document Not Found</h1>
          <p className="text-muted-foreground">
            The requested document could not be found
          </p>
        </div>
      </div>
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <FileText className="text-muted-foreground mb-4 h-12 w-12" />
          <h2 className="mb-2 text-xl font-semibold">Document Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The document you're looking for might have been removed or you don't
            have permission to view it.
          </p>
          <Button onClick={() => navigate("/app/drivers/documents")}>
            Return to Documents
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
