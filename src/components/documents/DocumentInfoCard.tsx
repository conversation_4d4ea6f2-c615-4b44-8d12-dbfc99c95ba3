import React from "react";
import { CalendarIcon } from "lucide-react";
import { Link } from "react-router";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formatDate } from "@/lib/formatters";

interface DocumentInfoCardProps {
  document: {
    name: string;
    content_type: string;
    size: number;
    created_at: string;
    updated_at: string;
    type: string;
    metadata?: {
      fileId?: string;
      cachePath?: string;
      scan_method?: string;
    };
  };
  analysis: any;
}

export default function DocumentInfoCard({
  document,
  analysis,
}: DocumentInfoCardProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Document Details</CardTitle>
        <CardDescription>Information about this document</CardDescription>
      </CardHeader>
      <CardContent>
        <dl className="space-y-4">
          <div>
            <dt className="text-muted-foreground text-sm font-medium">
              File Name
            </dt>
            <dd className="text-sm">{document.name}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground text-sm font-medium">
              File Type
            </dt>
            <dd className="text-sm">{document.content_type}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground text-sm font-medium">
              File Size
            </dt>
            <dd className="text-sm">{formatFileSize(document.size)}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground text-sm font-medium">
              Upload Date
            </dt>
            <dd className="flex items-center gap-2 text-sm">
              <CalendarIcon className="text-muted-foreground h-4 w-4" />
              {formatDate(document.created_at)}
            </dd>
          </div>
          {document.metadata?.scan_method && (
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Scan Method
              </dt>
              <dd className="text-sm capitalize">
                {document.metadata.scan_method}
              </dd>
            </div>
          )}
          {document.type && (
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Document Type
              </dt>
              <dd className="text-sm capitalize">{document.type}</dd>
            </div>
          )}
          {document.metadata?.fileId && (
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                File ID
              </dt>
              <dd className="font-mono text-sm">{document.metadata.fileId}</dd>
            </div>
          )}
          {document.metadata?.cachePath && (
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Cache Path
              </dt>
              <dd className="font-mono text-sm break-all">
                {document.metadata.cachePath}
              </dd>
            </div>
          )}
          {analysis?.documentType && (
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Identified As
              </dt>
              <dd className="text-sm">{analysis.documentType}</dd>
            </div>
          )}
          {analysis?.confidence && (
            <div>
              <dt className="text-muted-foreground text-sm font-medium">
                Confidence
              </dt>
              <dd className="text-sm">{analysis.confidence}%</dd>
            </div>
          )}
        </dl>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full" asChild>
          <Link to="/documents">Back to Documents</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
