import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, FileText, LucideIcon } from "lucide-react";

import DialogForm from "@/components/shared/DialogForm";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON>alog<PERSON>eader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useIsMobile } from "@/hooks/use-mobile";
import { BillOfLadingForm } from "./templates";

interface DocumentFormDialogProps {
  onSubmit: (values: any) => void;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  title?: string;
  description?: string;
  children?: React.ReactNode;
  documentType?: string;
}

type DocumentTypeOption = {
  id: string;
  name: string;
  description: string;
  icon: LucideIcon;
};

// Document type options
const documentTypes: DocumentTypeOption[] = [
  {
    id: "bill_of_lading",
    name: "Bill of Lading",
    description: "Create a shipping bill of lading document",
    icon: FileText,
  },
  // Add more document types as needed
];

export default function DocumentFormDialog({
  onSubmit,
  isOpen,
  onOpenChange,
  title = "Create Document",
  description = "Generate a new document from a template",
  children,
  documentType,
}: DocumentFormDialogProps) {
  const isMobile = useIsMobile();
  const [selectedDocType, setSelectedDocType] = useState<string | null>(
    documentType || null,
  );

  // Initialize template data state
  const [templateData, setTemplateData] = useState({
    shipper: {
      name: "",
      address: "",
      contact: "",
    },
    consignee: {
      name: "",
      address: "",
      contact: "",
    },
    carrier: {
      name: "",
      scac: "",
      contact: "",
    },
    shipment: {
      number: "",
      date: new Date().toISOString().split("T")[0],
      reference: "",
      specialInstructions: "",
    },
    items: [
      {
        description: "",
        weight: "",
        pieces: 1,
        packageType: "Box",
        hazmat: false,
      },
    ],
  });

  // Reset selected document type when dialog is opened/closed
  useEffect(() => {
    if (!isOpen) {
      // Reset to initial state when dialog is closed
      setSelectedDocType(documentType || null);
    }
  }, [isOpen, documentType]);

  // Handle type selection
  const handleTypeSelect = (type: string) => {
    setSelectedDocType(type);
  };

  // Handle going back to type selection
  const handleBack = () => {
    setSelectedDocType(null);
  };

  // Based on document type, we'll render the appropriate form
  const renderForm = () => {
    if (!selectedDocType) {
      return (
        <div className="py-6">
          <div className="grid grid-cols-1 gap-4">
            {documentTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => handleTypeSelect(type.id)}
                className="hover:border-primary hover:bg-primary/5 flex flex-col items-center rounded-lg border p-6 text-center transition-colors"
              >
                <type.icon className="text-primary mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-medium">{type.name}</h3>
                <p className="text-muted-foreground text-sm">
                  {type.description}
                </p>
              </button>
            ))}
          </div>
        </div>
      );
    }

    switch (selectedDocType) {
      case "bill_of_lading":
        return (
          <div>
            <div className="mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="text-muted-foreground hover:text-foreground flex items-center"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to document types
              </Button>
            </div>
            <DialogForm
              Component={BillOfLadingForm}
              onSubmit={(values) => {
                // Wrap the form data with the template data
                onSubmit({ templateData: values });
              }}
              title={title}
              description={description}
              useTrigger={false}
              open={isOpen}
              onOpenChange={onOpenChange}
              data={templateData}
              onChange={setTemplateData}
            />
          </div>
        );
      // Add more cases for other document types here
      default:
        return (
          <div className="p-4 text-center">
            <p className="text-muted-foreground">
              No form available for this document type
            </p>
          </div>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent
        className={`${isMobile ? "h-[95vh] max-w-full rounded-t-lg" : "max-h-[90vh] sm:max-w-4xl"} overflow-y-auto`}
      >
        <DialogHeader>
          <DialogTitle>
            {selectedDocType
              ? documentTypes.find((t) => t.id === selectedDocType)?.name ||
                title
              : title}
          </DialogTitle>
          <DialogDescription>
            {selectedDocType
              ? "Fill out the form to generate your document"
              : description}
          </DialogDescription>
        </DialogHeader>
        {renderForm()}
      </DialogContent>
    </Dialog>
  );
}
