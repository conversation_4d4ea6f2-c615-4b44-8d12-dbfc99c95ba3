import { AlertCircle } from "lucide-react";

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface DocumentWarningsProps {
  warnings: string[];
}

export default function DocumentWarnings({ warnings }: DocumentWarningsProps) {
  if (!warnings || warnings.length === 0) {
    return null;
  }

  return (
    <Card className="mt-6 border-amber-200">
      <CardHeader className="rounded-t-lg bg-amber-50 text-amber-900">
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          Warnings
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <ul className="space-y-2">
          {warnings.map((warning, index) => (
            <li key={index} className="flex items-start gap-2 text-sm">
              <AlertCircle className="mt-0.5 h-4 w-4 shrink-0 text-amber-600" />
              <span>{warning}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
