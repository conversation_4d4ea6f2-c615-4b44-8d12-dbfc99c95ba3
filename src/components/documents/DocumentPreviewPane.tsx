import { useRef } from "react";
import { AlertCircle, Loader2 } from "lucide-react";

import { useDocumentImage } from "@/hooks/use-document-image";

interface DocumentPreviewPaneProps {
  document: {
    name: string;
    url: string;
    content_type?: string | null;
  };
}

export default function DocumentPreviewPane({
  document,
}: DocumentPreviewPaneProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const {
    loading: imageLoading,
    error: imageError,
    imageUrl,
  } = useDocumentImage(document.url, document.content_type);

  const handleLoad = () => {
    console.log("Document loaded successfully");
  };

  const handleError = () => {
    console.error("Document preview error:", document.url);
  };

  // Helper function to determine document type
  const isPdf = document.content_type?.includes("pdf");
  const isImage = document.content_type?.includes("image");

  // Determine loading state
  const loading = isImage ? imageLoading : false;
  const error = isImage ? imageError : null;

  if (!document.url) {
    return (
      <div className="bg-muted/30 flex h-full w-full items-center justify-center p-4">
        <p className="text-muted-foreground">No document URL provided</p>
      </div>
    );
  }

  return (
    <div className="border-muted relative h-full min-h-[300px] w-full overflow-hidden rounded-md border">
      {loading && (
        <div className="bg-muted/30 absolute inset-0 z-10 flex items-center justify-center">
          <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
        </div>
      )}

      {error && (
        <div className="bg-muted/30 flex h-full w-full items-center justify-center p-4">
          <div className="flex flex-col items-center gap-2 text-center">
            <AlertCircle className="text-destructive h-8 w-8" />
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      )}

      {isPdf && !error ? (
        <iframe
          ref={iframeRef}
          src={`${document.url}#toolbar=0`}
          className="h-full w-full"
          onLoad={handleLoad}
          onError={handleError}
          title={document.name}
        />
      ) : isImage && !error ? (
        imageUrl ? (
          <img
            src={imageUrl}
            alt={document.name}
            className="h-full w-full object-contain"
            onLoad={handleLoad}
            onError={handleError}
          />
        ) : (
          <div className="h-full w-full" /> // Placeholder while loading cached image
        )
      ) : !error ? (
        <div className="bg-muted/30 flex h-full w-full items-center justify-center p-4">
          <p className="text-muted-foreground">
            Preview not available for this document type
            {document.content_type ? ` (${document.content_type})` : ""}
          </p>
        </div>
      ) : null}
    </div>
  );
}
