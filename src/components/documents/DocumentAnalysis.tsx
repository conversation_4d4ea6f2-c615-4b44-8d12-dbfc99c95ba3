import React from "react";
import { Info, RefreshCw } from "lucide-react";

import { Button } from "@/components/ui/button";

// Type for legacy analysis format (backwards compatibility)
interface LegacyAnalysis {
  documentType?: string;
  extractedFields?: Record<string, unknown>;
  warnings?: string[];
  confidence?: number;
  [key: string]: unknown;
}

interface DocumentAnalysisProps {
  analysis: LegacyAnalysis | null;
  isAnalyzing: boolean;
  onRetryAnalysis: () => void;
}

export default function DocumentAnalysis({
  analysis,
  isAnalyzing,
  onRetryAnalysis,
}: DocumentAnalysisProps) {
  return (
    <>
      <div className="mb-4 flex justify-end">
        <Button
          variant="outline"
          onClick={onRetryAnalysis}
          disabled={isAnalyzing}
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          {isAnalyzing ? "Analyzing..." : "Retry Analysis"}
        </Button>
      </div>

      {analysis ? (
        <div className="rounded-lg border p-4">
          <pre className="max-h-[600px] overflow-auto text-sm whitespace-pre-wrap">
            {JSON.stringify(analysis, null, 2)}
          </pre>
        </div>
      ) : (
        <div className="bg-muted rounded-lg border p-8 text-center">
          <Info className="text-muted-foreground mx-auto h-12 w-12" />
          <p className="text-muted-foreground mt-2">
            No analysis data available for this document
          </p>
          <Button
            className="mt-4"
            variant="outline"
            onClick={onRetryAnalysis}
            disabled={isAnalyzing}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            {isAnalyzing ? "Analyzing..." : "Analyze Document"}
          </Button>
        </div>
      )}
    </>
  );
}
