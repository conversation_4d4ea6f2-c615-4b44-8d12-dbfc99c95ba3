"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import { Link } from "react-router";

import type { ActionContext, TableConfig } from "@/components/tables/table";

import { DocumentMenu } from "@/components/actions/DocumentMenu";
import DocumentViewer from "@/components/common/DocumentViewer";
import PreviewOrganization from "@/components/common/PreviewOrganization";
import { SearchText } from "@/components/search";
import { createTypedExportCSVAction } from "@/components/tables/actions";
import { DataTableColumnHeader } from "@/components/tables/helpers";
import Table from "@/components/tables/table";
import { Badge } from "@/components/ui/badge";

const i18n = {
  en: {
    noData: "There are no documents yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search documents...",
    },
    headers: {
      name: "Name",
      documentType: "Document Type",
      processingStatus: "Processing",
      confidence: "Confidence",
      organization: "Organization",
    },
  },
  links: {
    documents: "/demo/documents/[id]",
  },
};

export const groupName = "document";

export type DocumentType = {
  id: string;
  name: string;
  type: string;
  organization?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  url?: string;
  storage_path?: string;
  metadata?: {
    documentClass?: string;
    subtype?: string;
    processingType?: string;
    confidence?: Record<string, number>;
    compliance?: {
      overallConfidence?: number;
      requiresManualReview?: boolean;
      missingFields?: string[];
    };
    [key: string]: unknown;
  };
};

export type DocumentData = {
  documents: DocumentType[];
  total: number;
};

interface ListDocumentsProps extends PropsWithChildren {
  loading?: boolean;
  documents?: DocumentData;
  group?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onDelete?: (id: string) => Promise<boolean>;
}

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

export default function ListDocuments({
  group = groupName,
  loading = false,
  documents,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  onDelete,
  children,
}: ListDocumentsProps) {
  // Transform data to PaginatedResponse format
  const data = useMemo(() => {
    if (!documents) return undefined;
    return {
      items: documents.documents,
      total: documents.total,
    };
  }, [documents]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      header={
        <>
          <SearchText
            group={group}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          {/* <div>
            <SearchOrganizations
              group={group}
              loading={loading}
              size="sm"
              variant="outline"
              className="w-fit min-w-48"
              clearable
            />
          </div> */}
        </>
      }
      actions={useMemo(
        () => [
          // Selection actions (for bulk operations)
          createTypedExportCSVAction<DocumentType>(
            ["id", "name", "type", "organization"],
            {
              filename: "documents_export.csv",
              label: "Export Selected Documents",
              resolvers: {
                organization: (org: unknown) => {
                  if (org && typeof org === "object" && "name" in org) {
                    return (org.name as string) || "";
                  }
                  return "";
                },
              },
            },
          ),
          // Row actions (for individual rows)
          {
            type: "row",
            label: "Document Actions",
            render: (context: ActionContext<DocumentType>) => {
              if (context.type === "row") {
                return (
                  <DocumentMenu
                    document={{
                      ...context.row,
                      metadata:
                        (context.row.metadata as Record<string, unknown>) || {},
                    }}
                    variant="ghost"
                    onDelete={onDelete}
                  />
                );
              }
              return null;
            },
          },
        ],
        [onDelete],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "preview",
              accessorKey: "preview",
              header: () => null,
              cell: ({ row }) => <DocumentViewer document={row.original} />,
            },
            {
              id: "name",
              accessorKey: "name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }) => (
                <Link
                  to={i18n.links.documents.replace("[id]", row.original.id)}
                  className="h-fit"
                >
                  <p className="font-semibold">{row.getValue("name")}</p>
                </Link>
              ),
              enableHiding: false,
            },
            {
              id: "documentType",
              accessorKey: "metadata.documentClass",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.documentType}
                />
              ),
              cell: ({ row }) => {
                const documentClass = row.original.metadata?.documentClass;
                const subtype = row.original.metadata?.subtype;

                if (documentClass) {
                  const formatted = documentClass
                    .replace(/_/g, " ")
                    .replace(/\b\w/g, (l) => l.toUpperCase());
                  const displayText = subtype
                    ? `${formatted} (${subtype})`
                    : formatted;
                  return <Badge variant="outline">{displayText}</Badge>;
                }
                return <Badge variant="secondary">General</Badge>;
              },
            },
            {
              id: "processingStatus",
              accessorKey: "metadata.processingType",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.processingStatus}
                />
              ),
              cell: ({ row }) => {
                const processingType = row.original.metadata?.processingType;
                const requiresReview =
                  row.original.metadata?.compliance?.requiresManualReview;

                if (requiresReview) {
                  return <Badge variant="destructive">Needs Review</Badge>;
                }

                if (processingType === "schema-driven") {
                  return <Badge variant="default">Processed</Badge>;
                } else if (processingType === "legacy") {
                  return <Badge variant="secondary">Legacy</Badge>;
                }

                return <Badge variant="outline">Unknown</Badge>;
              },
            },
            {
              id: "confidence",
              accessorKey: "metadata.compliance.overallConfidence",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.confidence}
                />
              ),
              cell: ({ row }) => {
                const confidence =
                  row.original.metadata?.compliance?.overallConfidence;

                if (typeof confidence === "number") {
                  const percentage = Math.round(confidence * 100);
                  const variant =
                    percentage >= 90
                      ? "default"
                      : percentage >= 70
                        ? "secondary"
                        : "destructive";
                  return <Badge variant={variant}>{percentage}%</Badge>;
                }

                return <span className="text-muted-foreground">N/A</span>;
              },
            },
            {
              id: "organization",
              accessorKey: "organization.name",
              header: ({ column }) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }) => (
                <PreviewOrganization
                  size="sm"
                  organization={row.original.organization}
                />
              ),
              filterFn: (row, id, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<DocumentType, DocumentType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
