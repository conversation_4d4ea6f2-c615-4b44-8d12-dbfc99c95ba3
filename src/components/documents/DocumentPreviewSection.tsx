import DocumentPreviewPane from "@/components/documents/DocumentPreviewPane";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface DocumentPreviewSectionProps {
  document: {
    name: string;
    content_type?: string | null;
    url: string;
  };
  description?: string;
}

export default function DocumentPreviewSection({
  document,
  description,
}: DocumentPreviewSectionProps) {
  return (
    <div className="space-y-6 md:col-span-2">
      <Card>
        <CardHeader>
          <CardTitle>Document Preview</CardTitle>
          <CardDescription>View your document content</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/30 min-h-[400px] rounded-lg border">
            {document.content_type && (
              <DocumentPreviewPane
                document={{
                  name: document.name,
                  url: document.url,
                  content_type: document.content_type,
                }}
              />
            )}
          </div>
        </CardContent>
      </Card>

      {description && (
        <Card>
          <CardHeader>
            <CardTitle>Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-wrap">{description}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
