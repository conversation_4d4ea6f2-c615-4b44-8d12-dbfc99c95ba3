import { FileText, Plus } from "lucide-react";

import { Button, ButtonProps } from "@/components/ui/button";
import CreateDocumentDialog from "./CreateDocumentDialog";

interface AddDocumentButtonProps extends ButtonProps {
  shipmentId?: string;
  driverId?: string;
  organizationId?: string;
  onDocumentCreated?: (document: any) => void;
  label?: string;
}

export default function AddDocumentButton({
  shipmentId,
  driverId,
  organizationId,
  onDocumentCreated,
  label = "Add Document",
  ...buttonProps
}: AddDocumentButtonProps) {
  return (
    <CreateDocumentDialog
      shipmentId={shipmentId}
      driverId={driverId}
      organizationId={organizationId}
      onDocumentCreated={onDocumentCreated}
    >
      <Button {...buttonProps}>
        <Plus className="mr-2 h-4 w-4" />
        {label}
      </Button>
    </CreateDocumentDialog>
  );
}
