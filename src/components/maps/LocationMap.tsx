import { useEffect, useRef } from "react";
import mapboxgl from "mapbox-gl";

import "mapbox-gl/dist/mapbox-gl.css";

import { supabase } from "@/supabase/client";

interface LocationMapProps {
  latitude?: number;
  longitude?: number;
}

const LocationMap = ({
  latitude = 40.7128,
  longitude = -74.006,
}: LocationMapProps) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const marker = useRef<mapboxgl.Marker | null>(null);

  useEffect(() => {
    const initializeMap = async () => {
      if (!mapContainer.current) return;

      try {
        // Get Mapbox token from Supabase Edge Function
        const {
          data: { MAPBOX_ACCESS_TOKEN },
          error: tokenError,
        } = await supabase.functions.invoke("get-secret", {
          body: { name: "MAPBOX_ACCESS_TOKEN" },
        });

        if (tokenError) {
          console.error("Failed to get Mapbox token:", tokenError);
          return;
        }

        mapboxgl.accessToken = MAPBOX_ACCESS_TOKEN;

        // Initialize map
        map.current = new mapboxgl.Map({
          container: mapContainer.current,
          style: "mapbox://styles/mapbox/streets-v12",
          center: [longitude, latitude],
          zoom: 15,
        });

        // Add navigation controls
        map.current.addControl(new mapboxgl.NavigationControl(), "top-right");

        // Add marker
        marker.current = new mapboxgl.Marker()
          .setLngLat([longitude, latitude])
          .addTo(map.current);
      } catch (error) {
        console.error("Error initializing map:", error);
      }
    };

    initializeMap();

    return () => {
      if (marker.current) {
        marker.current.remove();
      }
      if (map.current) {
        map.current.remove();
      }
    };
  }, [latitude, longitude]);

  return (
    <div
      ref={mapContainer}
      className="h-full min-h-[300px] w-full rounded-lg"
    />
  );
};

export default LocationMap;
