# Map Components

This directory contains our mapping components built on top of Mapbox. These components handle everything from simple location displays to complex route visualizations and interactive maps.

> **Quick Start**
>
> - Need to show locations or routes? Start here
> - Built on Mapbox GL JS
> - Support static and interactive modes
> - Handle markers, routes, and polygons
> - Include common map controls

> [!NOTE] Map components provide consistent mapping functionality across the application. They handle common mapping needs like location markers, route visualization, and geofencing, while maintaining consistent styling and interaction patterns.

## Component Types

1. Base Maps:
   - `StaticMap` - Non-interactive location display
   - `InteractiveMap` - Full interactive map
   - `MiniMap` - Compact map preview
   - `RoutingMap` - Route planning interface

2. Map Features:
   - `LocationMarker` - Location point display
   - `RouteLayer` - Route path visualization
   - `GeofenceLayer` - Geofence boundary display
   - `ClusterLayer` - Grouped location markers

3. Map Controls:
   - `ZoomControls` - Zoom in/out buttons
   - `LayerToggle` - Layer visibility control
   - `StyleSelector` - Map style switcher
   - `SearchControl` - Location search

## File Structure

```
maps/
├── base/           # Base map components
├── features/       # Map feature layers
├── controls/       # Map control components
├── hooks/          # Map-related hooks
├── utils/          # Map utilities
└── README.md       # This file
```

## Component Pattern

Maps typically follow this pattern:

```typescript
interface MapProps {
  // Map configuration
  center?: [number, number];
  zoom?: number;
  style?: 'streets' | 'satellite' | 'light' | 'dark';
  interactive?: boolean;
  // Features
  markers?: MapMarker[];
  routes?: MapRoute[];
  geofences?: MapGeofence[];
  // Events
  onMarkerClick?: (marker: MapMarker) => void;
  onBoundsChange?: (bounds: MapBounds) => void;
  // Customization
  controls?: MapControls[];
  className?: string;
}

export function Map({
  center = [-122.4194, 37.7749],
  zoom = 12,
  style = 'streets',
  interactive = true,
  markers = [],
  routes = [],
  geofences = [],
  onMarkerClick,
  onBoundsChange,
  controls = ['zoom', 'style'],
  className
}: MapProps) {
  const mapRef = useRef<MapboxMap>(null);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) return;

    const map = new MapboxGL.Map({
      container: mapRef.current,
      style: `mapbox://styles/mapbox/${style}-v11`,
      center,
      zoom,
      interactive
    });

    return () => map.remove();
  }, []);

  return (
    <div ref={mapRef} className={cn('map-container', className)}>
      {controls.includes('zoom') && <ZoomControls />}
      {controls.includes('style') && <StyleSelector />}
    </div>
  );
}
```

## Best Practices

1. **Map Configuration**
   - Use consistent map styles
   - Handle responsive sizing
   - Support different zoom levels
   - Enable/disable interactions

2. **Feature Management**
   - Efficient marker rendering
   - Smooth route updates
   - Clear boundary displays
   - Handle feature updates

3. **Performance**
   - Optimize marker clustering
   - Manage viewport updates
   - Cache map resources
   - Handle large datasets

4. **Interaction**
   - Clear click handling
   - Smooth pan/zoom
   - Responsive controls
   - Mobile support

5. **Customization**
   - Consistent styling
   - Flexible controls
   - Custom markers
   - Feature toggles

## Examples

### Simple Map

```typescript
export function LocationMap({
  location,
  zoom = 15,
  className
}: LocationMapProps) {
  return (
    <StaticMap
      center={[location.longitude, location.latitude]}
      zoom={zoom}
      markers={[{ id: location.id, ...location }]}
      className={className}
    />
  );
}
```

### Complex Map

```typescript
export function RouteMap({
  origin,
  destination,
  waypoints,
  onRouteUpdate
}: RouteMapProps) {
  const [bounds, setBounds] = useState<MapBounds>();

  return (
    <InteractiveMap
      markers={[origin, ...waypoints, destination]}
      routes={[{ origin, destination, waypoints }]}
      onBoundsChange={setBounds}
      controls={['zoom', 'style', 'traffic']}
      className="h-[600px]"
    >
      <RouteDetails />
      <TripMetrics />
    </InteractiveMap>
  );
}
```

## When to Use Map Components

Use these components when you need to:

1. Display location data
2. Visualize routes or boundaries
3. Create interactive map interfaces
4. Show geographic relationships

## Common Use Cases

1. Location Selection
   - Address picking
   - Area selection
   - Point of interest marking

2. Route Visualization
   - Trip planning
   - Delivery routes
   - Service areas

3. Geographic Analysis
   - Coverage areas
   - Density mapping
   - Distance calculations
