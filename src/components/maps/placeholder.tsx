import { MapPin } from "lucide-react";

export default function Placeholder() {
  return (
    <div className="relative size-full">
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <MapPin className="text-muted-foreground mx-auto mb-4 h-16 w-16" />
          <p className="text-foreground text-lg font-medium">Interactive Map</p>
          <p className="text-muted-foreground text-sm">
            Route visualization with tracking points
          </p>
        </div>
      </div>

      {/* Decorative route line */}
      <div className="bg-primary/30 absolute top-20 left-10 h-1 w-3/4 rotate-12 transform rounded-full"></div>
      <div className="bg-primary/40 absolute top-32 left-20 h-1 w-2/3 -rotate-6 transform rounded-full"></div>

      {/* Tracking Points */}
      <div className="border-card bg-primary absolute top-16 left-8 h-4 w-4 rounded-full border-2 shadow-lg"></div>
      <div className="border-card bg-secondary absolute top-1/2 left-1/2 h-4 w-4 rounded-full border-2 shadow-lg"></div>
      <div className="border-card bg-primary absolute right-12 bottom-20 h-4 w-4 rounded-full border-2 shadow-lg"></div>
    </div>
  );
}
