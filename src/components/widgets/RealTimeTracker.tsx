import { useCallback, useState } from "react";
import { MapPin } from "lucide-react";

import { Button } from "@/components/ui/button";

// TODO: add callbacks for tracking and stop tracking
// TODO: wire up to get/watch user location
// TODO: use wake lock api to screen on (only if charging)
// TODO: add permissions check, and clear asking for permissions
// TODO: inform user to keep device awake and charged
// TODO: find way to dim screen when kept on
// TODO: add a notification to the user when tracking starts and stops

// TODO: use this component to update user position in the database

export default function RealTimeTracker() {
  const [tracking, setTracking] = useState(false);

  const handleTracking = useCallback(() => {
    setTracking(!tracking);
  }, [tracking]);

  return (
    <div className="flex flex-col gap-2">
      <Button variant="outline" onClick={handleTracking}>
        <MapPin className="h-4 w-4" />
        {tracking ? "Stop Tracking" : "Start Tracking"}
      </Button>
    </div>
  );
}
