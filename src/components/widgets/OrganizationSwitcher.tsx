import { useEffect, useState } from "react";
import { Building, Check, ChevronsUpDown, PlusCircle } from "lucide-react";
import { useNavigate } from "react-router";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useOrganization } from "@/contexts/Organization";
import { useUser } from "@/contexts/User";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

export function OrganizationSwitcher() {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentOrganization, switchOrganization, isLoading } =
    useOrganization();
  const { memberships } = useUser();

  // Log for debugging
  useEffect(() => {
    console.log("Current memberships:", memberships);
    console.log("Current organization:", currentOrganization);
  }, [memberships, currentOrganization]);

  // Extract organizations from memberships
  const organizations = memberships.map((membership) => ({
    id: membership.organization.id,
    name: membership.organization.name,
    industry: membership.organization.industry,
    size: membership.organization.size,
  }));

  if (isLoading) {
    return (
      <Button variant="outline" className="w-full justify-start">
        <div className="bg-muted mr-2 h-5 w-5 animate-pulse rounded-full" />
        <span className="bg-muted h-4 w-24 animate-pulse rounded"></span>
      </Button>
    );
  }

  // If there are no organizations, show a button to create one
  if (organizations.length === 0) {
    return (
      <Button
        variant="outline"
        className="w-full justify-between"
        onClick={() => navigate("/app/console/organization/create")}
      >
        <div className="flex items-center gap-2">
          <PlusCircle className="h-5 w-5" />
          <span>Create Organization</span>
        </div>
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select organization"
          className="w-full justify-between"
        >
          <div className="flex items-center gap-2 truncate">
            <Building className="h-5 w-5" />
            <span className="truncate">
              {currentOrganization?.name || "Select Organization"}
            </span>
          </div>
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search organization..." />
          <CommandList>
            <CommandEmpty>No organization found.</CommandEmpty>
            <CommandGroup heading="Organizations">
              {organizations.map((org) => (
                <CommandItem
                  key={org.id}
                  onSelect={() => {
                    switchOrganization(org.id);
                    setOpen(false);
                    toast({
                      title: "Organization Switched",
                      description: `You are now viewing ${org.name}`,
                    });
                  }}
                  className="text-sm"
                >
                  <div className="flex items-center gap-2 truncate">
                    <Building className="h-4 w-4" />
                    <span className="truncate">{org.name}</span>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      currentOrganization?.id === org.id
                        ? "opacity-100"
                        : "opacity-0",
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                onSelect={() => {
                  setOpen(false);
                  navigate("/app/console/organization/create");
                }}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Organization
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
