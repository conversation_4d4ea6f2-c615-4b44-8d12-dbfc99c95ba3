import { useState } from "react";
import { Camera, Upload } from "lucide-react";
import { useNavigate } from "react-router";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";

type DocumentScannerProps = {
  onScanComplete?: (documentId: string) => void;
  variant?:
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive"
    | "primary"
    | "muted"
    | "accent";
  size?: "md" | "sm" | "lg" | "icon";
  buttonText?: string;
  showIcon?: boolean;
};

export default function DocumentScanner({
  onScanComplete,
  variant = "outline",
  size = "md",
  buttonText = "Scan Document",
  showIcon = true,
}: DocumentScannerProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);

  const handleScanInApp = () => {
    setIsOpen(false);
    navigate("/documents/scan");
  };

  const handleUploadInApp = () => {
    setIsOpen(false);
    navigate("/documents/create");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size}>
          {showIcon && <Camera className="mr-2 h-4 w-4" />}
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Document Options</DialogTitle>
          <DialogDescription>
            Choose how you want to handle your document
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4 py-4">
          <Button
            variant="outline"
            className="flex h-28 flex-col items-center justify-center gap-2 p-4"
            onClick={handleScanInApp}
          >
            <Camera className="h-8 w-8" />
            <span>Scan Document</span>
          </Button>
          <Button
            variant="outline"
            className="flex h-28 flex-col items-center justify-center gap-2 p-4"
            onClick={handleUploadInApp}
          >
            <Upload className="h-8 w-8" />
            <span>Upload Document</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
