import { useCallback } from "react";
import { Building2, LogOut, Plus, Settings, User } from "lucide-react";
import { Link, useNavigate } from "react-router";
import { toast } from "sonner";

import { useLogout } from "@/api";
import ThemeToggle from "@/components/layouts/ThemeToggle";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import NotificationsPopover from "@/components/widgets/NotificationsPopover";
import { useOrganization } from "@/contexts/Organization";
import { useUser } from "@/contexts/User";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    account: "Account",
    driver: "Driver Account",
    organization: "Organization Account",
    organizations: "Organizations",
    createDriver: "Create Driver Account",
    createOrganization: "Create Organization",
    settings: "Settings",
    logout: "Logout",
    toggleTheme: "Toggle theme",
    switchTo: "Switch to",
    currentOrganization: "Current Organization",
    messages: {
      logoutSuccess: "Logged out successfully",
      logoutError: "Error logging out",
      organizationSwitched: "Organization switched successfully",
    },
    tiers: {
      free: "Free",
      pro: "Pro",
    },
  },
  links: {
    auth: "/auth",
    account: "/app/account",
    console: "/app/console",
    createDriver: "/app/onboarding/driver",
    createOrganization: "/app/onboarding/organization",
  },
};

export default function UserManager({
  className,
  loading,
}: {
  className?: string;
  loading?: boolean;
}) {
  const { user, driver, memberships } = useUser();
  const { currentOrganization, switchOrganization } = useOrganization();
  const signOut = useLogout();
  const navigate = useNavigate();

  const handleLogout = useCallback(async () => {
    try {
      await signOut.mutateAsync(undefined);
      navigate(i18n.links.auth);
      toast.success(i18n.en.messages.logoutSuccess);
    } catch (error) {
      toast.error(i18n.en.messages.logoutError);
    }
  }, [navigate, signOut]);

  const handleOrganizationSwitch = useCallback(
    (organizationId: string) => {
      switchOrganization(organizationId);
      navigate(i18n.links.console);
      toast.success(i18n.en.messages.organizationSwitched);
    },
    [switchOrganization, navigate],
  );

  return (
    <div className={cn("flex items-center gap-4", className)}>
      <ThemeToggle />
      <NotificationsPopover />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            disabled={loading}
            variant="ghost"
            className="relative h-10 w-10 rounded-full"
          >
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-primary text-primary-foreground">
                {user?.email?.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64" align="end">
          <DropdownMenuLabel>My Accounts</DropdownMenuLabel>

          {driver && (
            <DropdownMenuItem asChild>
              <Link
                to="/app/drivers"
                className="flex items-center justify-between"
              >
                <div className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  {i18n.en.driver}
                </div>
                {driver.tier && (
                  <Badge
                    variant="secondary"
                    className="bg-secondary/30 ml-2 px-3 py-1 font-semibold shadow-xs"
                  >
                    {driver.tier === "pro"
                      ? i18n.en.tiers.pro
                      : i18n.en.tiers.free}
                  </Badge>
                )}
              </Link>
            </DropdownMenuItem>
          )}

          {!driver && (
            <DropdownMenuItem asChild>
              <Link to={i18n.links.createDriver}>
                <Plus className="mr-2 h-4 w-4" />
                {i18n.en.createDriver}
              </Link>
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuLabel>{i18n.en.organizations}</DropdownMenuLabel>

          {/* Current Organization */}
          {currentOrganization && (
            <DropdownMenuItem asChild>
              <Link
                to={i18n.links.console}
                className="flex items-center justify-between"
              >
                <div className="flex items-center">
                  <Building2 className="mr-2 h-4 w-4" />
                  <div>
                    <div className="font-medium">
                      {currentOrganization.name}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {i18n.en.currentOrganization}
                    </div>
                  </div>
                </div>
                <Badge variant="default" className="ml-2 px-2 py-1 text-xs">
                  Current
                </Badge>
              </Link>
            </DropdownMenuItem>
          )}

          {/* Other Organizations */}
          {memberships
            .filter(
              (membership) =>
                membership.organization.id !== currentOrganization?.id,
            )
            .map((membership) => (
              <DropdownMenuItem
                key={membership.id}
                onClick={() =>
                  handleOrganizationSwitch(membership.organization.id)
                }
                className="flex cursor-pointer items-center justify-between"
              >
                <div className="flex items-center">
                  <Building2 className="mr-2 h-4 w-4" />
                  <div>
                    <div className="font-medium">
                      {membership.organization.name}
                    </div>
                    <div className="text-muted-foreground text-xs capitalize">
                      {membership.role} • {membership.organization.industry}
                    </div>
                  </div>
                </div>
              </DropdownMenuItem>
            ))}

          {/* Create Organization Option */}
          {memberships.length === 0 && (
            <DropdownMenuItem asChild>
              <Link to={i18n.links.createOrganization}>
                <Plus className="mr-2 h-4 w-4" />
                {i18n.en.createOrganization}
              </Link>
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuItem asChild>
            <Link to={i18n.links.account}>
              <User className="mr-2 h-4 w-4" />
              {i18n.en.account}
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            {i18n.en.logout}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
