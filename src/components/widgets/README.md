# Widget Components

This directory contains hybrid components that combine data handling with their own presentation layer. Unlike pure UI components that are strictly presentational, or pure data hooks that only handle logic, widgets are self-contained features that handle both their data needs and how they're displayed.

> **Quick Start**
>
> - Need a self-contained feature? Create a widget
> - Widgets handle their own data AND presentation
> - Use hooks from `@/api` for data operations
> - Can compose using `@/components/ui`, but can also contain direct UI code
> - Place domain-specific widgets in appropriate subdirectories

> [!NOTE] Widgets are complete, self-contained features. While they can use shared UI components, they're also free to implement their own presentation logic when needed. This hybrid approach makes widgets ideal for complex features where the display logic is tightly coupled with the data handling.

## Key Characteristics

1. Hybrid Nature:
   - Self-contained features
   - Handle both data and presentation
   - Can implement custom UI when needed
   - Can compose shared UI components

2. Data Handling:
   - Connect to API endpoints via hooks
   - Handle loading and error states
   - Manage data mutations
   - Cache management via React Query

3. Presentation:
   - Can contain direct JSX/TSX
   - Can use shared UI components
   - Handle their own layouts
   - Manage their own styling

## File Structure

```
widgets/
├── [domain]/         # Domain-specific widgets
│   ├── lists/       # List/table widgets
│   ├── cards/       # Card/detail widgets
│   ├── forms/       # Form wrapper widgets
│   └── ...
└── README.md        # This file
```

## Widget Pattern

Widgets typically follow this pattern:

```typescript
export function MyWidget() {
  // Data fetching
  const { data, isLoading, error } = useMyData();

  // State management
  const [state, setState] = useState();

  // Event handlers
  const handleAction = () => {
    // Business logic
  };

  // Loading/error states
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  // Render using presentational components
  return (
    <Card>
      <MyPresentationalComponent
        data={data}
        onAction={handleAction}
      />
    </Card>
  );
}
```

## Best Practices

1. **Data Handling**
   - Use React Query hooks for data operations
   - Handle all loading/error states
   - Transform data before passing to presentational components
   - Keep data fetching logic centralized

2. **Component Composition**
   - Use presentational components for UI
   - Keep widgets focused on data and logic
   - Minimize direct DOM manipulation
   - Handle responsive behavior

3. **State Management**
   - Keep complex state in widgets
   - Use appropriate state management tools
   - Handle side effects properly
   - Maintain clear data flow

4. **Error Handling**
   - Provide meaningful error messages
   - Handle edge cases gracefully
   - Include error boundaries where appropriate
   - Log errors for debugging

5. **Performance**
   - Implement proper caching strategies
   - Optimize re-renders
   - Use pagination/infinite scroll where needed
   - Monitor and optimize data fetching

## Examples

- Lists: `LoadsList`, `ShipmentsList`
- Cards: `LoadCard`, `ShipmentCard`
- Forms: `LoadFormWidget`, `ShipmentFormWidget`
- Dashboards: `AnalyticsDashboard`, `MetricsWidget`
