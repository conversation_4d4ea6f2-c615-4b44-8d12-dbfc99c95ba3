import { format } from "date-fns";
import { Bell } from "lucide-react";
import { <PERSON> } from "react-router";

// import { useListUnreadNotifications } from "@/api/user/use-list-unread-notifications";
import { NotificationTypeBadge } from "@/components/common/types/NotificationType";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";

export default function NotificationsPopover() {
  // const { data: notifications, isLoading } = useListUnreadNotifications();
  const notifications = [];
  const isLoading = false;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {notifications && notifications.length > 0 && (
            <span className="bg-destructive absolute top-1 right-1 h-2 w-2 rounded-full" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Notifications</h4>
            {notifications && notifications.length > 0 && (
              <Badge variant="secondary" className="ml-auto">
                {notifications.length} new
              </Badge>
            )}
          </div>
          <div className="space-y-3">
            {isLoading ? (
              Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex flex-col gap-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              ))
            ) : notifications && notifications.length > 0 ? (
              <>
                {notifications.map((notification) => (
                  <div key={notification.id} className="flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{notification.title}</span>
                      <NotificationTypeBadge type={notification.type} />
                    </div>
                    <p className="text-muted-foreground text-sm">
                      {notification.message}
                    </p>
                    <span className="text-muted-foreground text-xs">
                      {format(
                        new Date(notification.created_at),
                        "MMM d, h:mm a",
                      )}
                    </span>
                  </div>
                ))}
                <Button variant="outline" className="w-full" asChild>
                  <Link to="/app/notifications">View all notifications</Link>
                </Button>
              </>
            ) : (
              <p className="text-muted-foreground py-4 text-center text-sm">
                No new notifications
              </p>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
