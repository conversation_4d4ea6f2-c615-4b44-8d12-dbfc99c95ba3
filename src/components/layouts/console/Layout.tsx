import { Fragment } from "react/jsx-runtime";
import { Loader2 } from "lucide-react";
import { useLocation } from "react-router";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";
import UserManager from "@/components/widgets/UserManager";
import { cn } from "@/lib/utils";
import { Copyright } from "../Copyright";
import { AppSidebar, AppSidebarProps } from "./Sidebar";

// Helper function to generate breadcrumbs from pathname
function getBreadcrumbs(pathname: string) {
  // Remove leading slash and split into segments
  const segments = pathname.replace(/^\//, "").split("/");

  // Skip 'app' from the breadcrumbs since it's the root
  if (segments[0] === "app") {
    segments.shift();
  }

  // Handle empty path (dashboard)
  if (segments.length === 0 || (segments.length === 1 && segments[0] === "")) {
    return [{ label: "Dashboard", path: "/app" }];
  }

  // Build breadcrumb items
  return segments.map((segment, index) => {
    // Format the label (capitalize, remove ID params, etc)
    let label = segment;
    if (segment.match(/^[0-9a-f-]+$/)) {
      label = "Details"; // For ID segments
    } else if (segment.includes("-")) {
      label = segment
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    } else {
      label = segment.charAt(0).toUpperCase() + segment.slice(1);
    }

    // Build the path
    const path = `/app/${segments.slice(0, index + 1).join("/")}`;

    return { label, path };
  });
}

function LoadingBreadcrumbs() {
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="h-4 w-24" />
      <BreadcrumbSeparator />
      <Skeleton className="h-4 w-32" />
    </div>
  );
}

function LoadingContent() {
  return (
    <div className="flex flex-1 flex-col gap-8 px-4 py-10">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-8 w-24" />
      </div>
      <div className="grid gap-6">
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-24 w-full" />
      </div>
    </div>
  );
}

export function AppBody({
  children,
  loading = false,
}: {
  children: React.ReactNode;
  loading?: boolean;
}) {
  const sidebar = useSidebar();
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  return (
    <SidebarInset>
      <header className="sticky top-0 z-10 flex shrink-0 px-4 pt-2">
        <div
          className={cn(
            "glass-effect bg-sidebar/50 mx-auto flex h-16 w-full shrink-0 items-center justify-between gap-2 rounded-lg border px-4",
            {
              "max-w-screen-xl": !sidebar.open,
            },
          )}
        >
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              {loading ? (
                <LoadingBreadcrumbs />
              ) : (
                breadcrumbs.map((crumb, index) => (
                  <Fragment key={crumb.path}>
                    {index > 0 && <BreadcrumbSeparator />}
                    <BreadcrumbItem>
                      <BreadcrumbPage className="line-clamp-1">
                        {crumb.label}
                      </BreadcrumbPage>
                    </BreadcrumbItem>
                  </Fragment>
                ))
              )}
            </BreadcrumbList>
          </Breadcrumb>

          <div className="ml-auto px-3">
            <UserManager loading={loading} />
          </div>
        </div>
      </header>

      <div className="mx-auto flex w-full max-w-[100vw] flex-1 flex-col gap-4 px-4 py-8 md:max-w-[calc(100vw-19rem)]">
        <div className="mx-auto w-full max-w-screen-xl">
          {loading ? (
            <LoadingContent />
          ) : children ? (
            <div className="flex flex-1 flex-col gap-4 px-4 py-10">
              {children}
            </div>
          ) : (
            <div className="flex min-h-[50vh] items-center justify-center">
              <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
            </div>
          )}
        </div>
      </div>

      <footer className="border-border flex items-center justify-center border-t px-8 py-6">
        <Copyright />
      </footer>
    </SidebarInset>
  );
}

export default function Layout({
  children,
  loading = false,
  links = [],
}: {
  children: React.ReactNode;
  loading?: boolean;
  links?: AppSidebarProps["links"];
}) {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "19rem",
        } as React.CSSProperties
      }
    >
      <AppSidebar loading={loading} links={links} />
      <AppBody loading={loading}>{children}</AppBody>
    </SidebarProvider>
  );
}
