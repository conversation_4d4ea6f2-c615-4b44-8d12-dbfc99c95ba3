import * as React from "react";
import { NavLink, useLocation } from "react-router";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { OrganizationSwitcher } from "@/components/widgets/OrganizationSwitcher";
import { cn } from "@/lib/utils";

export interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  loading?: boolean;
  links: {
    title: string;
    url: string;
    icon: React.ElementType;
    items?: {
      title: string;
      url: string;
      icon: React.ElementType;
    }[];
  }[];
}

export function AppSidebar({
  loading = false,
  links = [],
  ...props
}: AppSidebarProps) {
  const location = useLocation();

  return (
    <Sidebar variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <OrganizationSwitcher />
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu className="gap-2">
            {React.useMemo(() => {
              return links.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.url}
                      className={cn("font-medium", {
                        "bg-primary/25":
                          item.url === "/app/console"
                            ? item.url === location.pathname
                            : location.pathname.startsWith(item.url),
                      })}
                    >
                      {item.icon && <item.icon className="mr-2 size-5" />}
                      {item.title}
                    </NavLink>
                  </SidebarMenuButton>
                  {item.items?.length ? (
                    <SidebarMenuSub className="ml-0 border-l-0 px-1.5">
                      {item.items.map((item) => (
                        <SidebarMenuSubItem key={item.title}>
                          <SidebarMenuSubButton
                            asChild
                            // isActive={location.pathname === item.url}
                          >
                            <NavLink
                              to={item.url}
                              className={cn({
                                "bg-primary/40": location.pathname.startsWith(
                                  item.url,
                                ),
                              })}
                            >
                              {item.icon && (
                                <item.icon className="mr-2 size-5" />
                              )}
                              {item.title}
                            </NavLink>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  ) : null}
                </SidebarMenuItem>
              ));
            }, [location.pathname, links])}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter></SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
