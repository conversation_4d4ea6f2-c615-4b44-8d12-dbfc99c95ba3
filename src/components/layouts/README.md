# Layout Components

This directory contains components that define the structural organization of pages and views. Layouts handle the arrangement of content, navigation, and common UI elements that appear across multiple pages.

> **Quick Start**
>
> - Need to structure a page or view? Start here
> - Layouts define content areas and spacing
> - Handle responsive behavior and breakpoints
> - Manage navigation and common UI elements
> - Consider nesting layouts for complex pages

> [!NOTE] Layout components provide consistent structure across the application. They handle everything from the high-level app shell to specific content arrangements. Layouts ensure consistent spacing, responsive behavior, and proper organization of UI elements.

## Layout Types

1. Application Layouts:
   - `AppLayout` - Main application shell with navigation
   - `AuthLayout` - Authentication pages structure
   - `PublicLayout` - Public pages and marketing
   - `ConsoleLayout` - Admin/dashboard structure

2. Page Layouts:
   - `DashboardLayout` - Dashboard grid system
   - `SplitLayout` - Two-column page structure
   - `DetailLayout` - Entity detail pages
   - `ListLayout` - List/table pages

3. Content Layouts:
   - `CardGrid` - Grid of card components
   - `SidebarLayout` - Content with sidebar
   - `TabLayout` - Tabbed content areas
   - `StackLayout` - Vertical content stack

## File Structure

```
layouts/
├── app/            # Application-level layouts
├── pages/          # Page-specific layouts
├── content/        # Content arrangement layouts
├── navigation/     # Navigation-related layouts
├── shared/         # Reusable layout components
└── README.md       # This file
```

## Layout Pattern

Layouts typically follow this pattern:

```typescript
interface DetailLayoutProps {
  // Structure props
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  // Content
  children: React.ReactNode;
  // Layout options
  width?: 'full' | 'contained';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export function DetailLayout({
  sidebar,
  header,
  footer,
  children,
  width = 'contained',
  padding = 'md'
}: DetailLayoutProps) {
  return (
    <div className={cn("detail-layout", width)}>
      {header && <header className="layout-header">{header}</header>}
      <div className="layout-content">
        {sidebar && <aside className="layout-sidebar">{sidebar}</aside>}
        <main className={cn("layout-main", padding)}>{children}</main>
      </div>
      {footer && <footer className="layout-footer">{footer}</footer>}
    </div>
  );
}
```

## Best Practices

1. **Structure**
   - Use semantic HTML elements
   - Maintain consistent spacing
   - Handle responsive breakpoints
   - Support flexible content areas

2. **Composition**
   - Accept multiple content slots
   - Allow layout customization
   - Support nested layouts
   - Handle optional sections

3. **Responsiveness**
   - Mobile-first approach
   - Flexible grid systems
   - Breakpoint-based adjustments
   - Content reflow strategies

4. **Navigation**
   - Clear navigation patterns
   - Consistent header/footer
   - Proper sidebar behavior
   - Mobile navigation support

5. **Performance**
   - Minimize layout shifts
   - Optimize for paint/reflow
   - Handle dynamic content
   - Support progressive loading

## Examples

### Simple Layout

```typescript
export function SplitLayout({
  left,
  right,
  ratio = "1/1"
}: SplitLayoutProps) {
  return (
    <div className={cn("split-layout", `ratio-${ratio}`)}>
      <div className="left-content">{left}</div>
      <div className="right-content">{right}</div>
    </div>
  );
}
```

### Complex Layout

```typescript
export function ConsoleLayout({
  navigation,
  toolbar,
  sidebar,
  children,
  footer
}: ConsoleLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="console-layout">
      <nav className="top-nav">{navigation}</nav>
      <div className="toolbar">{toolbar}</div>
      <div className="content-area">
        {sidebar && (
          <aside className={cn("sidebar", { open: sidebarOpen })}>
            {sidebar}
          </aside>
        )}
        <main className="main-content">{children}</main>
      </div>
      {footer && <footer className="footer">{footer}</footer>}
    </div>
  );
}
```

## When to Create a Layout

Create a layout component when you need to:

1. Define a repeatable page structure
2. Manage complex content arrangements
3. Handle responsive layout patterns
4. Create consistent spacing systems
5. Organize navigation and UI elements
