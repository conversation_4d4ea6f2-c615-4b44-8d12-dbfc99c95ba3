import { Link } from "react-router";

import Brand from "@/components/layouts/Brand";
import UserManager from "@/components/widgets/UserManager";
import { cn } from "@/lib/utils";

const i18n = {
  en: {},
  links: {
    home: "/",
  },
};

export default function Header({
  className,
  loading = false,
}: {
  className?: string;
  loading?: boolean;
}) {
  return (
    <header
      className={cn(
        "bg-background/95 supports-backdrop-filter:bg-background/60 sticky top-0 z-50 w-full backdrop-blur-sm",
        className,
      )}
    >
      <div className="flex h-20 items-center justify-between px-8">
        <Link to={i18n.links.home} className="flex items-center space-x-2">
          <Brand />
        </Link>

        <UserManager loading={loading} />
      </div>
    </header>
  );
}
