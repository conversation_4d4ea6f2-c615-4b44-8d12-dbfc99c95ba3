import Footer from "@/components/layouts/app/Footer";
import Header from "@/components/layouts/app/Header";

export default function AppLayout({
  children,
  loading,
}: {
  children: React.ReactNode;
  loading: boolean;
}) {
  return (
    <div className="bg-background grid min-h-dvh w-full grid-rows-[auto_1fr_auto]">
      <Header loading={loading} />
      <main className="size-full">{children}</main>
      <Footer />
    </div>
  );
}
