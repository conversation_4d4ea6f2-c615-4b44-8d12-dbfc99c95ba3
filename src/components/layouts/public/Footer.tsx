import { <PERSON>ed<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router";

import Logo from "@/components/brand/Logo";
import ThemeToggle from "@/components/layouts/ThemeToggle";
import { Button } from "@/components/ui/button";
import { company, companyLinks } from "@/lib/constants/company";

export default function Footer() {
  return (
    <footer className="from-primary/5 via-background to-secondary/5 relative overflow-hidden bg-gradient-to-br py-12">
      {/* Background Pattern */}
      <div className="from-primary/3 absolute inset-0 bg-gradient-to-r to-transparent opacity-50"></div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid gap-8 md:grid-cols-3">
          <div className="flex flex-col gap-4">
            <h3 className="text-2xl font-bold">
              <span className="from-primary to-secondary bg-gradient-to-r bg-clip-text text-transparent">
                <Logo />
              </span>
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              {company.description}
            </p>
            <div className="flex gap-4">
              <Button
                variant="outline"
                size="icon"
                aria-label="LinkedIn"
                className="border-primary/20 text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200"
                asChild
              >
                <Link
                  to={companyLinks.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Linkedin className="h-5 w-5" />
                </Link>
              </Button>
            </div>
            <div className="flex gap-4">
              <ThemeToggle />
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-primary mb-4 text-lg font-semibold">
              Platform
            </h3>
            <ul className="text-muted-foreground space-y-3">
              <li>
                <Link
                  to="/drivers"
                  className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                >
                  For Drivers
                </Link>
              </li>
              <li>
                <Link
                  to="/shippers"
                  className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                >
                  For Shippers
                </Link>
              </li>
              <li>
                <Link
                  to="/developers"
                  className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                >
                  Developers
                </Link>
              </li>
              <li>
                <Link
                  to="/roadmap"
                  className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                >
                  Roadmap
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-secondary mb-4 text-lg font-semibold">Legal</h3>
            <ul className="text-muted-foreground space-y-3">
              <li>
                <Link
                  to="/legal"
                  className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                >
                  Legal Center
                </Link>
              </li>
              <li>
                <Link
                  to="/legal/terms-of-service"
                  className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                >
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link
                  to="/legal/privacy-policy"
                  className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  to="/legal/compliance"
                  className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                >
                  Compliance
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-primary/10 mt-12 border-t pt-8 text-center">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()}{" "}
            <span className="text-primary font-medium">QuikSkope</span>. All
            rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
