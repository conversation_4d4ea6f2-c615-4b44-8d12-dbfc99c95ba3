import { Menu } from "lucide-react";
import { Link, NavLink } from "react-router";

import Logo from "@/components/brand/Logo";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useUser } from "@/contexts/User";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    solutions: "Solutions",
    pricing: "Pricing",
    drivers: "Drivers",
    documents: "Documents",
    auth: "Get Started",
    app: "Dashboard",
  },
  links: {
    solutions: "/solutions",
    pricing: "/pricing",
    drivers: "/drivers",
    documents: "/documents",
    auth: "/auth",
    app: "/app",
    home: "/",
  },
};

const links = [
  // {
  //   label: i18n.en.drivers,
  //   href: i18n.links.drivers,
  // },
  // {
  //   label: i18n.en.documents,
  //   href: i18n.links.documents,
  // },
  // {
  //   label: i18n.en.solutions,
  //   href: i18n.links.solutions,
  // },
  // {
  //   label: i18n.en.pricing,
  //   href: i18n.links.pricing,
  // },
];

export default function Header({ className }: { className?: string }) {
  const { session, isLoading } = useUser();

  return (
    <header
      className={cn(
        "bg-background/95 supports-backdrop-filter:bg-background/60 sticky top-0 z-50 w-full backdrop-blur-sm",
        className,
      )}
    >
      <div className="mx-auto flex h-20 max-w-7xl items-center justify-between px-8">
        <Link to={i18n.links.home} className="flex items-center space-x-2">
          <Logo />
        </Link>

        <div className="hidden flex-1 items-center justify-end gap-6 md:flex">
          <nav className="flex items-center gap-6">
            {links.map((link) => (
              <NavLink
                key={link.href}
                to={link.href}
                className="text-muted-foreground hover:text-primary text-sm font-medium transition-colors"
              >
                {link.label}
              </NavLink>
            ))}
          </nav>

          <Button asChild variant="secondary" size="lg" className="font-bold">
            {session ? (
              <Link to={i18n.links.app}>{i18n.en.app}</Link>
            ) : (
              <Link to={i18n.links.auth}>{i18n.en.auth}</Link>
            )}
          </Button>
        </div>

        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="md:hidden">
              <Menu className="h-6 w-6" />
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="w-[300px] sm:w-[400px]">
            <nav className="flex flex-col gap-4">
              {links.map((link) => (
                <NavLink
                  key={link.href}
                  to={link.href}
                  className="hover:text-primary text-sm font-medium transition-colors"
                >
                  {link.label}
                </NavLink>
              ))}
            </nav>
            <Separator className="my-4" />
            <Button asChild size="lg" variant="secondary">
              {session ? (
                <Link to={i18n.links.app}>{i18n.en.app}</Link>
              ) : (
                <Link to={i18n.links.auth}>{i18n.en.auth}</Link>
              )}
            </Button>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
}
