import type { PopoverContentProps } from "@radix-ui/react-popover";
import type { PropsWithChildren } from "react";

import { useCallback, useEffect, useMemo, useState } from "react";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { CheckIcon, X } from "lucide-react";

import type { ButtonProps } from "@/components/ui/button";
import type { CommandProps } from "@/components/ui/command";

import { SelectorContainer } from "@/components/selectors/selector-container";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    addItem: "Add",
    inputPlaceholder: "Search...",
    noResults: "No results found.",
  },
};

export interface GenericNode {
  id: string;
  [key: string]: unknown;
}

// Default render functions
function defaultRenderId<T extends GenericNode>(item: T): string {
  return item.id;
}

function defaultRenderValue<T extends GenericNode>(item: T): string {
  return item.id;
}

function defaultRenderItem<T extends GenericNode>(item: T): React.ReactNode {
  return <span>{item.id}</span>;
}

function defaultRenderEmpty(text: string = i18n.en.noResults): React.ReactNode {
  return <span>{text}</span>;
}

function defaultRenderLoading(): React.ReactNode {
  // TODO: add size, quantity, and spacing as props
  return (
    <div className="grid gap-1 px-2 py-1">
      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-16 w-full" />
    </div>
  );
}

function defaultRenderCheck(isSelected: boolean): React.ReactNode {
  return (
    <div
      className={cn(
        "border-primary ml-auto flex size-4 items-center justify-center rounded-sm border",
        isSelected
          ? "bg-primary text-primary-foreground"
          : "opacity-50 [&_svg]:invisible",
      )}
    >
      <CheckIcon className={cn("size-4")} />
    </div>
  );
}

function defaultRenderSelection<T extends GenericNode>(
  item: T,
  renderItem?: (value: T) => React.ReactNode,
  renderId?: (value: T) => string,
): React.ReactNode {
  return renderItem ? renderItem(item) : renderId ? renderId(item) : item.id;
}

function defaultRenderMultipleSelection<T extends GenericNode>(
  items: T[],
  renderItem?: (value: T) => React.ReactNode,
  renderId?: (value: T) => string,
  onRemove?: (item: T) => void | Promise<void>,
): React.ReactNode {
  if (items.length === 0) return null;

  // Always render as badges in multi-select mode, even for single items

  return (
    <div className="flex flex-wrap gap-1">
      {items.slice(0, 3).map((item) => (
        <Badge
          key={renderId ? renderId(item) : item.id}
          variant="secondary"
          className="text-xs"
        >
          {renderItem ? renderItem(item) : renderId ? renderId(item) : item.id}
          {onRemove && (
            <span
              className="hover:bg-background/20 ml-1 cursor-pointer rounded-full p-0.5"
              onClick={(e) => {
                e.stopPropagation();
                void onRemove(item);
              }}
            >
              <X className="size-3" />
            </span>
          )}
        </Badge>
      ))}
      {items.length > 3 && (
        <Badge variant="outline" className="text-xs">
          +{items.length - 3} more
        </Badge>
      )}
    </div>
  );
}

export interface SelectorProps<DataType extends GenericNode>
  extends PropsWithChildren<
    Pick<CommandProps, "shouldFilter" | "filter" | "loop">
  > {
  loading?: boolean;
  pending?: boolean;
  disabled?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | null;
  variant?: ButtonProps["variant"];
  align?: PopoverContentProps["align"];
  className?: string;
  label?: React.ReactNode;
  placeholder?: string;
  heading?: string;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  knob?: React.ReactNode;
  data?: DataType[];

  // Single select props
  open?: boolean;
  /** Controls the search/filter input text, NOT the selected item */
  value?: DataType["id"];
  /** The currently selected item object - use null to explicitly indicate no selection */
  selection?: DataType | null;
  defaultValue?: DataType["id"];
  /** Called when the search/filter input changes */
  onValueChange?: (value: string) => void | Promise<void>;
  /** Called when the selection changes */
  onSelectionChange?: (selection?: DataType | null) => void | Promise<void>;
  /** Called when an item is selected */
  onSelect?: (value: DataType) => void | Promise<void>;

  // Multi-select props
  multiple?: boolean;
  /** Array of selected items - use null to explicitly indicate no selections */
  selections?: DataType[] | null;
  defaultSelections?: DataType[];
  onSelectionsChange?: (selections: DataType[]) => void | Promise<void>;
  maxSelections?: number;

  // Common props
  onOpenChange?: (open: boolean) => void;
  renderId?: (value: DataType) => string;
  renderValue?: (value: DataType) => string;
  renderSelection?: (value?: DataType) => React.ReactNode;
  renderMultipleSelection?: (
    values: DataType[],
    onRemove?: (item: DataType) => void | Promise<void>,
  ) => React.ReactNode;
  renderItem?: (value: DataType) => React.ReactNode;
  renderEmpty?: () => React.ReactNode;
  renderLoading?: () => React.ReactNode;
  renderCheck?: (isSelected: boolean) => React.ReactNode;
  useDialog?: boolean;
  useTrigger?: boolean;
  usePopoverPortal?: boolean;
  showCheck?: boolean;
  closeOnSelect?: boolean;
  clearOnSelect?: boolean;
  clearable?: boolean;
  onClear?: () => void | Promise<void>;
}

export function Selector<DataType extends GenericNode>({
  children,
  loading = false,
  pending = false,
  disabled,
  size = "md",
  variant = "ghost",
  align = "start",
  className = "",
  placeholder = i18n.en.inputPlaceholder,
  label = i18n.en.addItem,
  heading,
  footer,
  header,
  knob = (
    <CaretSortIcon
      className={cn("text-muted-foreground ms-auto", {
        "size-4": size === "sm",
        "size-5": size === "md",
        "size-6": size === "lg",
        "size-8": size === "xl",
      })}
    />
  ),
  data: controlledData = [],
  // Single select props
  open: controlledOpen,
  value: controlledValue,
  selection: controlledSelection,
  defaultValue,
  onValueChange,
  onSelectionChange,
  onSelect,

  // Multi-select props
  multiple = false,
  selections: controlledSelections,
  defaultSelections,
  onSelectionsChange,
  maxSelections,

  // Common props
  onOpenChange,
  renderId = defaultRenderId,
  renderValue = defaultRenderValue,
  renderSelection,
  renderMultipleSelection,
  renderItem = defaultRenderItem,
  renderEmpty = defaultRenderEmpty,
  renderLoading = defaultRenderLoading,
  renderCheck = defaultRenderCheck,
  useDialog = true,
  useTrigger = true,
  usePopoverPortal = true,
  showCheck = multiple,
  closeOnSelect = multiple ? false : showCheck ? false : true,
  clearOnSelect = true,
  clearable = false,
  onClear,
  shouldFilter = true,
  filter,
  loop = true,
}: SelectorProps<DataType>) {
  const [selecting, setSelecting] = useState(false);
  const [open, setOpen] = useState(false);

  // Single select state
  const [value, setValue] = useState(defaultValue ?? "");
  const [selection, setSelection] = useState<DataType | null | undefined>(
    undefined,
  );

  // Multi-select state
  const [selections, setSelections] = useState<DataType[]>(
    defaultSelections ?? [],
  );

  const data = useMemo(() => {
    const internalData = [...controlledData];

    if (selection) {
      const index = internalData.findIndex(
        (item) => renderId(item) === renderId(selection),
      );

      if (index === -1) {
        internalData.unshift(selection);
      } else if (index >= 0) {
        internalData.splice(index, 1);
        internalData.unshift(selection);
      }
    }

    return internalData;
  }, [controlledData, selection, renderId]);

  // Internal input state for cmdk compatibility when filtering is enabled
  const [internalInputValue, setInternalInputValue] = useState(
    defaultValue ?? "",
  );

  const currentOpen = controlledOpen ?? open;

  // Single select computed values - use internal state when shouldFilter is true to avoid cmdk conflicts
  const currentValue =
    shouldFilter && controlledValue !== undefined
      ? internalInputValue
      : (controlledValue ?? value);

  // Use controlled selection if explicitly provided (including null), otherwise use internal state
  const currentSelection =
    controlledSelection !== undefined ? controlledSelection : selection;

  // Multi-select computed values - use controlled selections if explicitly provided (including null)
  const currentSelections =
    controlledSelections !== undefined
      ? (controlledSelections ?? [])
      : selections;

  // Sync internal value state when controlled value changes
  useEffect(() => {
    if (controlledValue !== undefined) {
      setValue(controlledValue);
      // Also sync internal input value for cmdk compatibility
      if (shouldFilter) {
        setInternalInputValue(controlledValue);
      }
    }
  }, [controlledValue, shouldFilter]);

  // Sync internal selection state when controlled selection changes
  useEffect(() => {
    if (controlledSelection !== undefined) {
      setSelection(controlledSelection);
    }
  }, [controlledSelection]);

  // Sync internal selections state when controlled selections changes
  useEffect(() => {
    if (controlledSelections !== undefined) {
      setSelections(controlledSelections ?? []);
    }
  }, [controlledSelections]);

  const handleOpenChange = useCallback(
    async (newOpen: boolean) => {
      setOpen(newOpen);

      // Only reset filter for uncontrolled components without selections
      if (
        newOpen &&
        onValueChange &&
        controlledValue === undefined &&
        !currentSelection
      ) {
        await onValueChange("");
        setInternalInputValue("");
      }

      if (onOpenChange) {
        onOpenChange(newOpen);
      }
    },
    [onOpenChange, onValueChange, controlledValue, currentSelection],
  );

  const handleInputChange = useCallback(
    async (text: string) => {
      // Always update internal state for immediate UI response
      setValue(text);

      // Update internal input value for cmdk compatibility when filtering
      if (shouldFilter) {
        setInternalInputValue(text);
      }

      // Always call the controlled callback if provided
      if (onValueChange) {
        await onValueChange(text);
      }
    },
    [onValueChange, shouldFilter],
  );

  const handleRemoveSelection = useCallback(
    async (itemToRemove: DataType) => {
      if (!multiple) return;

      const itemId = renderId(itemToRemove);
      const newSelections = currentSelections.filter(
        (item) => renderId(item) !== itemId,
      );

      // Always update internal state for immediate UI response
      setSelections(newSelections);

      if (onSelectionsChange) {
        await onSelectionsChange(newSelections);
      }
    },
    [multiple, currentSelections, renderId, onSelectionsChange],
  );

  const handleSelection = useCallback(
    async (item: DataType) => {
      setSelecting(true);
      try {
        const itemId = renderId(item);

        if (multiple) {
          const isSelected = currentSelections.some(
            (sel) => renderId(sel) === itemId,
          );
          let newSelections: DataType[];

          if (isSelected) {
            // Remove from selection
            newSelections = currentSelections.filter(
              (sel) => renderId(sel) !== itemId,
            );
          } else {
            // Add to selection (if under max limit)
            if (maxSelections && currentSelections.length >= maxSelections) {
              return; // Don't allow selection if at max
            }
            newSelections = [...currentSelections, item];
          }

          // Always update internal state for immediate UI response
          setSelections(newSelections);

          if (onSelectionsChange) {
            await onSelectionsChange(newSelections);
          }
        } else {
          // Single select logic - always update internal state for immediate UI response
          setSelection(item);

          // Clear the input value after selection based on clearOnSelect prop and controlled state
          if (clearOnSelect && controlledValue === undefined) {
            setValue("");
            if (shouldFilter) {
              setInternalInputValue("");
            }
          } else if (
            clearOnSelect &&
            controlledValue !== undefined &&
            onValueChange
          ) {
            // For controlled components, call the callback to clear
            await onValueChange("");
            setInternalInputValue("");
          }

          if (onSelect) {
            await onSelect(item);
          }
          if (onSelectionChange) {
            await onSelectionChange(item);
          }
        }

        if (closeOnSelect) {
          setOpen(false);
        }
      } catch (error) {
        console.error("Selection failed:", error);
      } finally {
        setSelecting(false);
      }
    },
    [
      multiple,
      currentSelections,
      maxSelections,
      renderId,
      onSelect,
      onSelectionChange,
      onSelectionsChange,
      closeOnSelect,
      controlledValue,
      shouldFilter,
      clearOnSelect,
      onValueChange,
    ],
  );

  const handleClear = useCallback(async () => {
    if (onClear) {
      await onClear();
    }
    if (multiple) {
      setSelections([]);
    } else {
      setSelection(undefined);
    }
    setValue("");
    if (shouldFilter) {
      setInternalInputValue("");
    }
  }, [onClear, multiple, shouldFilter]);

  // Determine if we have a meaningful selection for clear button visibility
  const hasValue = useMemo(() => {
    if (multiple) {
      return selections.length > 0;
    } else {
      return !!selection;
    }
  }, [multiple, selections, selection]);

  // closed rendered item
  const child = useMemo(() => {
    if (loading) return null;

    if (multiple) {
      const displaySelections = currentSelections;
      return (
        <div className="flex flex-1">
          {displaySelections.length > 0
            ? renderMultipleSelection
              ? renderMultipleSelection(
                  displaySelections,
                  handleRemoveSelection,
                )
              : defaultRenderMultipleSelection(
                  displaySelections,
                  renderItem,
                  renderId,
                  handleRemoveSelection,
                )
            : label}
        </div>
      );
    } else {
      return (
        <div className="flex flex-1">
          {currentSelection
            ? renderSelection
              ? renderSelection(currentSelection)
              : defaultRenderSelection(currentSelection, renderItem, renderId)
            : label}
        </div>
      );
    }
  }, [
    loading,
    multiple,
    currentSelections,
    currentSelection,
    renderMultipleSelection,
    renderSelection,
    renderItem,
    renderId,
    label,
    handleRemoveSelection,
  ]);
  const input = (
    <CommandInput
      placeholder={placeholder}
      disabled={disabled}
      value={currentValue}
      onValueChange={handleInputChange}
    />
  );
  const list = useMemo(
    () => (
      <CommandList className={cn("w-full p-0", "px-0")}>
        {loading ? (
          renderLoading()
        ) : (
          <CommandEmpty>{renderEmpty()}</CommandEmpty>
        )}
        {header ? (
          <CommandGroup className="border-border bg-card sticky top-0 z-10 items-center justify-center border-b">
            {header}
          </CommandGroup>
        ) : null}
        {loading ? null : (
          <CommandGroup heading={heading} className={cn("p-0", "px-0")}>
            {data.map((item) => {
              const itemId = renderId(item);
              const itemValue = renderValue(item);
              const isSelected = multiple
                ? currentSelections.some((sel) => renderId(sel) === itemId)
                : Boolean(
                    currentSelection && renderId(currentSelection) === itemId,
                  );
              const isAtMaxSelections =
                multiple &&
                maxSelections !== undefined &&
                maxSelections > 0 &&
                currentSelections.length >= maxSelections &&
                !isSelected;

              return (
                <CommandItem
                  disabled={selecting || pending || isAtMaxSelections}
                  key={itemId}
                  value={itemValue}
                  onSelect={() => handleSelection(item)}
                >
                  {renderItem(item)}
                  <div className="ml-auto pl-2">
                    {showCheck && renderCheck(isSelected)}
                  </div>
                </CommandItem>
              );
            })}
          </CommandGroup>
        )}

        {footer ? (
          <CommandGroup className="border-border bg-card sticky bottom-0 items-center justify-center border-t">
            {footer}
          </CommandGroup>
        ) : null}
      </CommandList>
    ),
    [
      selecting,
      multiple,
      currentSelection,
      currentSelections,
      maxSelections,
      showCheck,
      loading,
      pending,
      data,
      heading,
      footer,
      header,
      renderId,
      renderValue,
      renderItem,
      handleSelection,
      renderEmpty,
      renderLoading,
      renderCheck,
    ],
  );
  const trigger = useMemo(
    () => (
      <Button
        type="button"
        onClick={() => handleOpenChange(true)}
        disabled={disabled ?? loading}
        className={cn(
          // completely reset all button styles to prevent double border
          "size-full flex-1 border-0 bg-transparent p-0 shadow-none",
          // remove all focus styling - handled by container
          "focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0",
          // add back only the padding we need
          {
            "px-1": size === "sm",
            "px-2": size === "md",
            "px-2.5": size === "lg",
            "px-3": size === "xl",
          },
        )}
        variant="ghost"
        size={size === "xl" ? "lg" : size}
      >
        {children ?? (
          <div className="flex w-full items-center justify-between gap-2">
            {loading ? (
              <>
                <Skeleton
                  className={cn("size-full min-w-16 flex-1", {
                    "h-4": size === "sm",
                    "h-5": size === "md",
                    "h-6": size === "lg",
                    "h-8": size === "xl",
                  })}
                />
                <Skeleton
                  className={cn("aspect-square rounded-md", {
                    "size-5": size === "sm",
                    "size-6": size === "md",
                    "size-7": size === "lg",
                    "size-9": size === "xl",
                  })}
                />
              </>
            ) : (
              <>
                {child}
                {knob}
              </>
            )}
          </div>
        )}
      </Button>
    ),
    [child, knob, loading, disabled, size, children, handleOpenChange],
  );

  if (useDialog) {
    return (
      <>
        {useTrigger ? (
          <SelectorContainer
            variant={variant}
            className={cn("", className)}
            hasValue={clearable && hasValue}
            loading={loading}
            onClear={clearable ? handleClear : undefined}
            size={size}
          >
            {trigger}
          </SelectorContainer>
        ) : null}
        <CommandDialog
          className={cn("overflow-auto border p-0")}
          open={currentOpen}
          onOpenChange={handleOpenChange}
          shouldFilter={shouldFilter}
          filter={filter}
          loop={loop}
        >
          {input}
          <div className="overflow-auto rounded-lg p-1">{list}</div>
        </CommandDialog>
      </>
    );
  }

  return (
    <Popover open={currentOpen} onOpenChange={handleOpenChange}>
      {useTrigger ? (
        <SelectorContainer
          variant={variant}
          className={cn("", className)}
          hasValue={clearable && hasValue}
          loading={loading}
          onClear={clearable ? handleClear : undefined}
          size={size}
        >
          <PopoverTrigger asChild>{trigger}</PopoverTrigger>
        </SelectorContainer>
      ) : null}
      <PopoverContent
        usePortal={usePopoverPortal}
        className="w-[var(--radix-popover-trigger-width)] p-0"
        align={align}
      >
        <Command
          className="w-full p-0"
          shouldFilter={shouldFilter}
          filter={filter}
          loop={loop}
        >
          {input}
          <div className="p-1">{list}</div>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
