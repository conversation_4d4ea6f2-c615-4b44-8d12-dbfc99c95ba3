# Selector Components

This directory contains presentational components that provide selection interfaces for entity data. These components are designed to be flexible in how they receive and handle data, making them versatile for forms, filters, and any scenario where users need to select from available options.

> **Quick Start**
>
> - Need a selection interface for entity data? Use a selector
> - Components handle the selection UI and search interactions
> - Flexible data input (search params, direct state, etc.)
> - Support single and multiple selection modes
> - Can be used in forms, filters, or standalone

> [!NOTE] Selector components are presentational building blocks that can be wired up to data in various ways. For example, a `LoadSelector` can receive data directly as props, drive querying by search parameters, or connect to form state - while maintaining the same consistent selection experience across all use cases.

## Selector Types

1. Entity Selectors:
   - `LoadSelector` - Selection interface for loads
   - `ShipmentSelector` - Shipment selection UI
   - `LocationSelector` - Location picking interface
   - `OrganizationSelector` - Organization selection

2. Relationship Selectors:
   - `DriverSelector` - Driver selection interface
   - `CustomerSelector` - Customer picking UI
   - `TeamMemberSelector` - Team member selection
   - `VehicleSelector` - Vehicle picking interface

3. Attribute Selectors:
   - `StatusSelector` - Status selection UI
   - `TypeSelector` - Type selection interface
   - `TagSelector` - Tag selection
   - `CategorySelector` - Category picking

## File Structure

```
selectors/
├── entities/        # Core entity selection UIs
├── relationships/   # Relationship selection UIs
├── attributes/      # Attribute selection UIs
├── shared/         # Shared selector components
└── README.md       # This file
```

## Selector Pattern

Selectors typically follow this pattern:

```typescript
interface LoadSelectorProps {
  // Data input
  items: Load[];
  // Search configuration
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  // Selection configuration
  mode?: 'single' | 'multiple';
  value?: string | string[];
  onChange?: (value: string | string[]) => void;
  // UI customization
  placeholder?: string;
  size?: 'sm' | 'md' | 'lg';
  // Additional features
  withCreate?: boolean;
  withDetails?: boolean;
}

export function LoadSelector({
  items,
  searchValue = '',
  onSearchChange,
  mode = 'single',
  value,
  onChange,
  placeholder = 'Select a load...',
  size = 'md',
  withCreate,
  withDetails
}: LoadSelectorProps) {
  // Local search state if not controlled
  const [localSearch, setLocalSearch] = useState(searchValue);
  const search = onSearchChange ? searchValue : localSearch;
  const handleSearch = onSearchChange ?? setLocalSearch;

  // Filter items based on search
  const filtered = useSearch(items, search);

  return (
    <Combobox
      value={value}
      onChange={onChange}
      multiple={mode === 'multiple'}
    >
      <div className={cn("selector", size)}>
        <SearchInput
          value={search}
          onChange={handleSearch}
          placeholder={placeholder}
        />
        <Options>
          {filtered.map(load => (
            <Option key={load.id} value={load.id}>
              <LoadBadge load={load} />
              {withDetails && <LoadDetails load={load} />}
            </Option>
          ))}
          {withCreate && <CreateOption />}
        </Options>
      </div>
    </Combobox>
  );
}
```

## Best Practices

1. **Data Flexibility**
   - Accept data as props
   - Support controlled/uncontrolled search
   - Allow custom filtering logic
   - Enable external data management

2. **Selection UX**
   - Clear selection indicators
   - Intuitive search behavior
   - Keyboard navigation support
   - Mobile-friendly interaction

3. **Flexibility**
   - Support different selection modes
   - Allow custom rendering
   - Enable create/edit flows
   - Customizable display options

4. **Performance**
   - Efficient item rendering
   - Optimize search logic
   - Virtual scrolling for large lists
   - Minimize re-renders

5. **Integration**
   - Form library compatibility
   - URL parameter support
   - Clear value formatting
   - Error state handling

## Examples

### Simple Selector

```typescript
export function StatusSelector({
  statuses,
  value,
  onChange
}: StatusSelectorProps) {
  return (
    <Select value={value} onChange={onChange}>
      {statuses.map(status => (
        <Option key={status.value} value={status.value}>
          <StatusBadge status={status} />
        </Option>
      ))}
    </Select>
  );
}
```

### Complex Selector

```typescript
export function LocationSelector({
  locations,
  value,
  onChange,
  mode = 'single',
  withMap,
  onBoundsChange
}: LocationSelectorProps) {
  return (
    <div className="location-selector">
      {withMap && (
        <MapView
          locations={locations}
          onBoundsChange={onBoundsChange}
          onLocationSelect={onChange}
        />
      )}
      <Combobox value={value} onChange={onChange} multiple={mode === 'multiple'}>
        <SearchInput />
        <LocationList locations={locations} />
      </Combobox>
    </div>
  );
}
```

## When to Create a Selector

Create a selector component when you need to:

1. Provide a consistent selection interface
2. Support different data input methods
3. Enable flexible search interactions
4. Create reusable selection patterns
5. Handle complex selection scenarios
