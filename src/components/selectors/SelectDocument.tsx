import { useCallback } from "react";
import { FileIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type {
  GenericNode,
  SelectorProps,
} from "@/components/selectors/Selector";

import PreviewDocument from "@/components/common/PreviewDocument";
import { useSearchValue } from "@/components/search/value";
import { Selector } from "@/components/selectors/Selector";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    label: "Document",
    description: "The document",
    placeholder: "Select a document",
  },
};

export interface PartialDocument extends GenericNode {
  type: string;
  name: string;
  url: string;
  size?: number;
}
export interface SelectDocumentProps<DataType extends PartialDocument>
  extends SelectorProps<DataType> {
  placeholder?: string;
}

export function SelectDocument<DataType extends PartialDocument>({
  loading = false,
  variant = "outline",
  align = "center",
  className,
  disabled,
  useDialog,
  placeholder = i18n.en.placeholder,
  data,
  value,
  onSelect,
  size = "md",
  ...props
}: SelectDocumentProps<DataType>) {
  return (
    <Selector<DataType>
      renderValue={useCallback((value: PartialDocument) => value.name, [])}
      renderLoading={useCallback(
        () => (
          <div className="flex w-full flex-col gap-1">
            {Array.from({ length: 4 }).map((_, index) => (
              <PreviewDocument loading size={size} key={index} />
            ))}
          </div>
        ),
        [size],
      )}
      renderItem={useCallback(
        (value: PartialDocument) => (
          <PreviewDocument document={value} size={size} />
        ),
        [size],
      )}
      label={
        <span className="text-muted-foreground hover:text-accent-foreground flex items-center gap-1 text-sm">
          <FileIcon className="m-2 me-0 size-4" />
          <span>{placeholder}</span>
        </span>
      }
      {...props}
      size={size}
      loading={loading}
      disabled={disabled}
      variant={variant}
      align={align}
      className={className}
      useDialog={useDialog ?? false}
      data={data}
      value={value}
      onSelect={onSelect}
    />
  );
}

export default SelectDocument;

export interface SelectDocumentFieldProps<DataType extends PartialDocument>
  extends SelectDocumentProps<DataType> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectDocumentField<DataType extends PartialDocument>({
  loading = false,
  name = "documentId",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  size = "md",
  showLabel = true,
  showDescription = true,
  ...props
}: SelectDocumentFieldProps<DataType>) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectDocument
              {...props}
              loading={loading}
              placeholder={placeholder}
              data={data}
              selection={data?.find((document) => document.id === field.value)}
              onSelect={async (document: DataType): Promise<void> => {
                if (onSelect) {
                  await onSelect(document);
                }

                field.onChange(document.id);
              }}
              size={size}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchDocumentProps<DataType extends PartialDocument>
  extends SelectorProps<DataType> {
  placeholder?: string;
  name?: string;
  group?: string;
  defaultValue?: string;
}

export function SearchDocument<DataType extends PartialDocument>({
  loading = false,
  placeholder = i18n.en.placeholder,
  name = "document",
  group,
  data,
  defaultValue,
  ...props
}: SearchDocumentProps<DataType>) {
  const { selection, onClear, onSelectionChange } = useSearchValue<DataType>({
    name,
    group,
    defaultValue,
    data,
  });

  return (
    <SelectDocument
      {...props}
      loading={loading}
      placeholder={placeholder}
      data={data}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
