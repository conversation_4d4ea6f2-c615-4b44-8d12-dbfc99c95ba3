"use client";

import { useCallback } from "react";
import { TagIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type {
  GenericNode,
  SelectorProps,
} from "@/components/selectors/Selector";

import { useSearchValue } from "@/components/search/value";
import { Selector } from "@/components/selectors/Selector";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    label: "Value",
    description: "The value",
    placeholder: "Select a value",
  },
};

export interface PartialValue extends GenericNode {
  type?:
    | "ORDER_CATEGORY"
    | "ORDER_TYPE"
    | "CONTACT"
    | "EXPENSE"
    | "MEDICAL_ROLE"
    | "ACTION"
    | "TAG";
  value: string;
}

export interface SelectValueProps<DataType extends PartialValue>
  extends SelectorProps<DataType> {
  placeholder?: string;
}

export function SelectValue<DataType extends PartialValue>({
  size = "md",
  loading = false,
  variant = "outline",
  align = "center",
  className,
  disabled,
  useDialog,
  placeholder = i18n.en.placeholder,
  data,
  value,
  onSelect,
  ...props
}: SelectValueProps<DataType>) {
  return (
    <Selector<DataType>
      renderValue={useCallback((value: DataType) => value.value, [])}
      renderLoading={useCallback(
        () => (
          <div className="w-[200px]">
            <span className="text-muted-foreground text-sm">Loading...</span>
          </div>
        ),
        [],
      )}
      renderItem={useCallback(
        (value: DataType) => (
          <span className="text-sm">{value.value}</span>
        ),
        [],
      )}
      label={
        <span className="text-muted-foreground hover:text-accent-foreground flex items-center gap-1 text-sm">
          <TagIcon className="m-2 me-0 size-4" />
          <span>{placeholder}</span>
        </span>
      }
      {...props}
      size={size}
      className={className}
      loading={loading}
      disabled={disabled}
      variant={variant}
      align={align}
      useDialog={useDialog ?? false}
      data={data}
      value={value}
      onSelect={onSelect}
      renderId={useCallback((value: DataType) => value.value, [])}
    />
  );
}

export default SelectValue;

export interface SelectValueFieldProps<DataType extends PartialValue>
  extends SelectValueProps<DataType> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  showLabel?: boolean;
  showDescription?: boolean;
}

export function SelectValueField<DataType extends PartialValue>({
  loading = false,
  name = "type",
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  data,
  onSelect,
  showDescription = false,
  showLabel = true,
  ...props
}: SelectValueFieldProps<DataType>) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel
            className={cn({
              "sr-only": !showLabel,
            })}
          >
            {label}
          </FormLabel>
          <FormDescription
            className={cn({
              "sr-only": !showDescription,
            })}
          >
            {description}
          </FormDescription>
          <FormControl>
            <SelectValue
              {...props}
              loading={loading}
              placeholder={placeholder}
              data={data}
              selection={data?.find((value) => value.value === field.value)}
              onSelect={async (value: DataType): Promise<void> => {
                if (onSelect) {
                  await onSelect(value);
                }

                field.onChange(value.value);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export interface SearchValueProps<DataType extends PartialValue>
  extends SelectorProps<DataType> {
  placeholder?: string;
  name?: string;
  group?: string;
  defaultValue?: string;
}

export function SearchValue<DataType extends PartialValue>({
  loading = false,
  placeholder = i18n.en.placeholder,
  name = "value",
  group,
  data,
  defaultValue,
  ...props
}: SearchValueProps<DataType>) {
  const { selection, onClear, onSelectionChange } = useSearchValue<DataType>({
    name,
    group,
    defaultValue,
    data,
  });

  return (
    <SelectValue
      {...props}
      loading={loading}
      placeholder={placeholder}
      data={data}
      onClear={onClear}
      selection={selection}
      onSelectionChange={onSelectionChange}
    />
  );
}
