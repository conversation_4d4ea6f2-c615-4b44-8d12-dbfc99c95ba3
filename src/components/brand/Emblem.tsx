import { memo } from "react";
import { Crosshair2Icon } from "@radix-ui/react-icons";

import { cn } from "@/lib/utils";

export function Emblem({
  size = "md",
  className,
  shadow = true,
  link,
}: {
  size?: "sm" | "md" | "lg";
  className?: string;
  shadow?: boolean;
  link?: boolean;
}) {
  return (
    <div
      className={cn(
        // Base container styling
        "relative overflow-visible rounded-lg",
        // Gradient background
        "from-primary via-primary/90 to-primary/80 bg-gradient-to-br",
        // Border and shadow effects
        "border-primary/20 shadow-primary/25 border shadow-lg",
        // Hover effects for interactive state
        {
          "hover:shadow-primary/40 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl":
            link,
          "shadow-primary/30 shadow-xl": shadow,
        },
        // Size variants
        {
          "size-6 p-1": size === "sm",
          "size-8 p-1.5": size === "md",
          "size-12 p-2": size === "lg",
        },
        className,
      )}
    >
      {/* Inner glow effect */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-white/20 to-transparent opacity-40" />

      {/* Icon container with enhanced styling */}
      <div className="relative z-10 flex h-full w-full items-center justify-center">
        <Crosshair2Icon
          className={cn(
            // Icon styling - thicker and more prominent
            "text-white drop-shadow-sm",
            // Size-based icon scaling
            {
              "h-4 w-4": size === "sm",
              "h-5 w-5": size === "md",
              "h-7 w-7": size === "lg",
            },
          )}
          style={{
            // Make the icon thicker/bolder - even thicker for large size
            strokeWidth: size === "lg" ? 3.5 : size === "md" ? 2.5 : 2,
            filter: "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))",
          }}
        />
      </div>

      {/* Subtle animated pulse for extra visual appeal */}
      <div
        className="bg-primary/20 absolute inset-0 animate-pulse rounded-lg opacity-0"
        style={{ animationDuration: "3s" }}
      />
    </div>
  );
}

export default memo(Emblem);
