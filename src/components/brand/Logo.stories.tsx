import type { <PERSON>a, StoryObj } from "@storybook/react-vite";

import Logo from "./Logo";

const meta: Meta<typeof Logo> = {
  title: "Components/Brand/Logo",
  component: Logo,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A beautiful logo component with gradient backgrounds, subtle color blending, and premium visual effects. Features layered gradients for depth and a subtle animated pulse.",
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const OnLightBackground: Story = {
  args: {},
  decorators: [
    (Story) => (
      <div className="rounded-lg bg-white p-8">
        <Story />
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Logo displayed on a light background to showcase contrast and gradient effects.",
      },
    },
  },
};

export const OnDarkBackground: Story = {
  args: {},
  decorators: [
    (Story) => (
      <div className="rounded-lg bg-gray-900 p-8">
        <Story />
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Logo displayed on a dark background demonstrating versatility and visual appeal.",
      },
    },
  },
};

export const OnColoredBackground: Story = {
  args: {},
  decorators: [
    (Story) => (
      <div className="rounded-lg bg-gradient-to-br from-blue-100 to-purple-100 p-8">
        <Story />
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Logo on a colored gradient background showing how the subtle colors blend harmoniously.",
      },
    },
  },
};

export const InNavigation: Story = {
  render: () => (
    <nav className="border-b border-gray-200 bg-white p-4 shadow-sm">
      <div className="mx-auto flex max-w-6xl items-center justify-between">
        <Logo />
        <div className="flex items-center gap-6">
          <span className="text-gray-600">Dashboard</span>
          <span className="text-gray-600">Settings</span>
          <span className="text-gray-600">Profile</span>
        </div>
      </div>
    </nav>
  ),
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        story:
          "Logo used in a typical navigation bar context showing real-world usage.",
      },
    },
  },
};

export const Responsive: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="mb-4 text-lg font-medium">Desktop</h3>
        <div className="flex justify-center">
          <Logo />
        </div>
      </div>
      <div className="text-center">
        <h3 className="mb-4 text-lg font-medium">Mobile (same component)</h3>
        <div className="flex justify-center">
          <Logo />
        </div>
        <p className="text-muted-foreground mt-2 text-sm">
          Logo maintains its visual appeal across all screen sizes
        </p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Logo responsiveness demonstration showing consistent appearance across different contexts.",
      },
    },
  },
};

export const BrandShowcase: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-8 p-8 md:grid-cols-3">
      <div className="rounded-lg border bg-white p-6 text-center shadow-sm">
        <Logo />
        <p className="mt-4 text-xs text-gray-500">Light Background</p>
      </div>
      <div className="rounded-lg bg-gray-900 p-6 text-center">
        <Logo />
        <p className="mt-4 text-xs text-gray-400">Dark Background</p>
      </div>
      <div className="rounded-lg bg-gradient-to-br from-blue-50 to-indigo-100 p-6 text-center">
        <Logo />
        <p className="mt-4 text-xs text-gray-600">Gradient Background</p>
      </div>
      <div className="rounded-lg bg-green-50 p-6 text-center">
        <Logo />
        <p className="mt-4 text-xs text-green-700">Success Context</p>
      </div>
      <div className="rounded-lg bg-red-50 p-6 text-center">
        <Logo />
        <p className="mt-4 text-xs text-red-700">Error Context</p>
      </div>
      <div className="rounded-lg bg-yellow-50 p-6 text-center">
        <Logo />
        <p className="mt-4 text-xs text-yellow-700">Warning Context</p>
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        story:
          "Comprehensive brand showcase demonstrating logo performance across various backgrounds and contexts.",
      },
    },
  },
};
