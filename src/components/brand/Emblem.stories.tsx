import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";

import { Emblem } from "./Emblem";

const meta: Meta<typeof Emblem> = {
  title: "Components/Brand/Emblem",
  component: Emblem,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A stunning emblem component with gradient backgrounds, shadows, and enhanced visual effects. Serves as a beautiful placeholder that can be enhanced with custom SVGs.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "radio" },
      options: ["sm", "md", "lg"],
      description: "Size variant of the emblem",
    },
    shadow: {
      control: { type: "boolean" },
      description: "Enable enhanced shadow effects",
    },
    link: {
      control: { type: "boolean" },
      description: "Enable interactive hover effects",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    size: "md",
    shadow: true,
    link: false,
  },
};

export const Small: Story = {
  args: {
    size: "sm",
    shadow: true,
    link: false,
  },
};

export const Large: Story = {
  args: {
    size: "lg",
    shadow: true,
    link: false,
  },
};

export const Interactive: Story = {
  args: {
    size: "md",
    shadow: true,
    link: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive emblem with hover effects - hover to see the scaling and enhanced shadow.",
      },
    },
  },
};

export const NoShadow: Story = {
  args: {
    size: "md",
    shadow: false,
    link: false,
  },
  parameters: {
    docs: {
      description: {
        story: "Emblem without enhanced shadow effects for minimal styling.",
      },
    },
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="text-center">
        <Emblem size="sm" />
        <p className="text-muted-foreground mt-2 text-sm">Small</p>
      </div>
      <div className="text-center">
        <Emblem size="md" />
        <p className="text-muted-foreground mt-2 text-sm">Medium</p>
      </div>
      <div className="text-center">
        <Emblem size="lg" />
        <p className="text-muted-foreground mt-2 text-sm">Large</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Comparison of all available size variants.",
      },
    },
  },
};

export const InteractiveComparison: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="text-center">
        <Emblem size="md" link={false} />
        <p className="text-muted-foreground mt-2 text-sm">Static</p>
      </div>
      <div className="text-center">
        <Emblem size="md" link={true} />
        <p className="text-muted-foreground mt-2 text-sm">Interactive</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Comparison between static and interactive states. Hover over the interactive emblem to see the effects.",
      },
    },
  },
};

export const OnDarkBackground: Story = {
  args: {
    size: "lg",
    shadow: true,
    link: true,
  },
  decorators: [
    (Story) => (
      <div className="rounded-lg bg-gray-900 p-8">
        <Story />
      </div>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story:
          "Emblem displayed on a dark background to showcase the gradient and shadow effects.",
      },
    },
  },
};
