import { cn } from "@/lib/utils";
import Emblem from "./Emblem";

export default function Logo({
  className,
  useImage = true,
}: {
  className?: string;
  useImage?: boolean;
}) {
  return useImage ? (
    <img
      src="/brand/logo_color.png"
      alt="QuikSkope"
      width={160}
      className={cn(className)}
    />
  ) : (
    <div
      className={cn(
        "relative flex w-fit items-center gap-3 overflow-hidden rounded-full p-3",
        className,
      )}
    >
      {/* Gradient background with subtle color blending */}
      <div className="from-primary/80 via-primary/70 to-primary/60 absolute inset-0 bg-gradient-to-r" />

      {/* Secondary gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-transparent to-purple-500/15" />

      {/* Subtle inner glow */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/10 to-transparent" />

      {/* Content layer */}
      <div className="relative z-10 flex items-center gap-3">
        <Emblem size="md" className="ring-2 ring-white/20" />
        <span className="pr-2 text-xl font-bold whitespace-pre-wrap text-white drop-shadow-sm">
          QuikSkope
        </span>
      </div>

      {/* Subtle animated pulse for premium feel */}
      <div className="absolute inset-0 animate-pulse rounded-full bg-white/5 opacity-50" />
    </div>
  );
}
