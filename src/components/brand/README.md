# Brand Components

This directory contains brand-related components for the application, including logos, emblems, and brand identity elements.

## Components

### Logo

A beautiful logo component featuring the QuikSkope brand with sophisticated gradient backgrounds and subtle color blending. The logo combines the enhanced Emblem component with elegant typography in a premium design package.

#### Features

- **Layered Gradients**: Multiple gradient layers for depth and visual richness
  - Primary gradient: Horizontal blend from `primary/80` to `primary/60`
  - Secondary gradient: Diagonal blue-to-purple accent overlay
  - Inner glow: Subtle white highlight for premium feel
- **Enhanced Emblem**: Integrated emblem with white ring accent
- **Typography**: Bold "QuikSkope" text with drop shadow
- **Animated Pulse**: Gentle pulse animation for subtle movement
- **Responsive Design**: Maintains visual appeal across all screen sizes

#### Usage

```tsx
import Logo from "@/components/brand/Logo";

// Basic usage
<Logo />

// In navigation
<nav className="p-4">
  <Logo />
</nav>
```

#### Visual Design

The logo employs a sophisticated layering system:

1. **Base Container**: Rounded pill shape with overflow hidden
2. **Primary Gradient**: Horizontal primary color blend for brand consistency
3. **Accent Overlay**: Subtle blue-purple diagonal gradient for depth
4. **Inner Glow**: White gradient overlay for luminosity
5. **Content Layer**: Emblem and text with proper z-index stacking
6. **Pulse Animation**: Gentle white overlay pulse for premium feel

#### Color Scheme

- **Primary Colors**: CSS variable `--primary` in multiple opacities
- **Accent Colors**: Subtle blue (`blue-500/20`) and purple (`purple-500/15`)
- **Text**: Pure white with drop shadow for contrast
- **Ring Accent**: Semi-transparent white ring around emblem

#### Use Cases

- Navigation headers
- Hero sections
- Email signatures
- Brand collateral
- Loading screens
- About pages

### Emblem

A stunning emblem component that serves as a beautiful placeholder for the brand icon. Features enhanced visual effects including gradients, shadows, and interactive animations.

#### Features

- **Gradient Background**: Beautiful gradient from primary color variations
- **Enhanced Shadows**: Customizable shadow effects with primary color tinting
- **Interactive States**: Hover effects with scaling and enhanced shadows
- **Multiple Sizes**: Small (24px), Medium (32px), Large (48px)
- **Progressive Stroke Width**: Thicker strokes for larger sizes (sm: 2, md: 2.5, lg: 3.5)
- **Inner Glow**: Subtle white overlay for depth
- **Animated Pulse**: Gentle 3-second pulse animation for visual appeal

#### Usage

```tsx
import { Emblem } from "@/components/brand/Emblem";

// Basic usage
<Emblem />

// With different sizes
<Emblem size="sm" />
<Emblem size="md" />
<Emblem size="lg" />

// Interactive emblem
<Emblem link={true} />

// Without shadow effects
<Emblem shadow={false} />
```

#### Props

| Prop        | Type                   | Default     | Description                      |
| ----------- | ---------------------- | ----------- | -------------------------------- |
| `size`      | `"sm" \| "md" \| "lg"` | `"md"`      | Size variant of the emblem       |
| `className` | `string`               | `undefined` | Additional CSS classes           |
| `shadow`    | `boolean`              | `true`      | Enable enhanced shadow effects   |
| `link`      | `boolean`              | `false`     | Enable interactive hover effects |

#### Visual Design

The emblem uses the following design system:

- **Primary Colors**: Uses CSS variables `--primary` for consistent theming
- **Shadow Effects**: Primary-tinted shadows for brand consistency
- **Border Radius**: `rounded-lg` for modern appearance
- **Transitions**: Smooth 300ms transitions for interactive states
- **Z-Index Layering**: Proper layering for glow and pulse effects

#### Accessibility

- The component maintains focus indicators when used as a link
- Color contrast is maintained through the white icon on primary background
- Hover states provide clear visual feedback

#### Future Enhancements

This component is designed as a placeholder that can be enhanced with:

- Custom SVG icons
- Brand-specific graphics
- Animation variations
- Theme-specific styling

#### Examples in Storybook

The component includes comprehensive Storybook stories showcasing:

- All size variants
- Interactive vs static states
- Shadow effect variations
- Dark background compatibility
- Comparative displays

## Logo

Additional brand components like the main logo can be added to this directory following the same design principles established by the Emblem component.

## Theming

All brand components use semantic CSS variables defined in the global styles:

- `--primary`: Main brand color with opacity variations
- `--primary-foreground`: Text on primary background
- `--shadow`: Shadow effects

This ensures consistent theming across light and dark modes while maintaining the sophisticated gradient effects that define the brand's premium aesthetic.
