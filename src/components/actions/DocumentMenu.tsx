import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Download, Edit, MoreVertical, Trash2 } from "lucide-react";
import { Link } from "react-router";
import { toast } from "sonner";

import DialogConfirmation from "@/components/shared/DialogConfirmation";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { supabase } from "@/supabase/client";

interface DocumentMenuProps {
  document: {
    id: string;
    name: string;
    url?: string;
    storage_path?: string;
    metadata?: Record<string, unknown>;
  };
  variant?: "ghost" | "outline";
  size?: "sm" | "md" | "icon";
  onDelete?: (id: string) => Promise<boolean>;
  onSuccess?: () => void;
}

export function DocumentMenu({
  document,
  variant = "ghost",
  size = "icon",
  onDelete,
  onSuccess,
}: DocumentMenuProps) {
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const queryClient = useQueryClient();

  async function handleDelete() {
    if (isDeleting) return;

    try {
      setIsDeleting(true);

      // Use external delete function if provided
      if (onDelete) {
        const success = await onDelete(document.id);
        if (success) {
          if (onSuccess) {
            onSuccess();
          }
          setIsDeleteOpen(false);
        }
        return;
      }

      // Fallback to inline delete implementation
      console.log(`Deleting document: ${document.id}`);

      // Delete from storage if storage_path exists
      if (document.storage_path) {
        console.log(`Deleting from storage: ${document.storage_path}`);
        const { error: storageError } = await supabase.storage
          .from("demo")
          .remove([document.storage_path]);

        if (storageError) {
          console.warn("Storage deletion error:", storageError);
          // Continue with database deletion even if storage fails
        }
      }

      // Delete the document record from database
      console.log(`Deleting document record: ${document.id}`);
      const { error: dbError } = await supabase
        .from("documents")
        .delete()
        .eq("id", document.id);

      if (dbError) {
        throw new Error(`Failed to delete document: ${dbError.message}`);
      }

      toast.success("Document deleted successfully");

      // Invalidate queries to refresh data
      if (queryClient) {
        queryClient.invalidateQueries({ queryKey: ["documents"] });
        queryClient.invalidateQueries({ queryKey: ["document", document.id] });
      }

      if (onSuccess) {
        onSuccess();
      }

      setIsDeleteOpen(false);
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete document",
      );
    } finally {
      setIsDeleting(false);
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className="h-8 w-8 p-0"
            disabled={isDeleting}
          >
            <span className="sr-only">Open menu</span>
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {document.url && (
            <DropdownMenuItem asChild>
              <a
                href={document.url}
                download
                target="_blank"
                rel="noreferrer"
                className="flex cursor-pointer items-center"
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </a>
            </DropdownMenuItem>
          )}

          <DropdownMenuItem asChild>
            <Link
              to={`/app/drivers/documents/${document.id}/edit`}
              className="flex cursor-pointer items-center"
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            className="text-destructive focus:text-destructive flex cursor-pointer items-center"
            onClick={() => setIsDeleteOpen(true)}
            disabled={isDeleting}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {isDeleting ? "Deleting..." : "Delete"}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DialogConfirmation
        open={isDeleteOpen}
        onOpenChange={setIsDeleteOpen}
        title={`Delete Document: ${document.name}`}
        description="Are you sure you want to delete this document? This action cannot be undone and will remove both the file and database record permanently."
        onClick={handleDelete}
        Icon={Trash2}
        variant="destructive"
        action={isDeleting ? "Deleting..." : "Delete"}
        cancel="Cancel"
        useTrigger={false}
        disabled={isDeleting}
      />
    </>
  );
}
