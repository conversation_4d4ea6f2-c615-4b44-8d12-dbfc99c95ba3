import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type MembersRole = Enums<"members_role">;

const i18n = {
  en: {
    owner: "Owner",
    admin: "Admin",
    billing: "Billing",
    member: "Member",
    viewer: "Viewer",
  },
} as const;

const getBadgeVariant = (role: MembersRole): BadgeProps["variant"] => {
  switch (role) {
    case "owner":
      return "destructive";
    case "admin":
      return "destructive";
    case "billing":
      return "accent";
    case "member":
      return "default";
    case "viewer":
      return "secondary";
    default:
      return "default";
  }
};

export interface MembersRoleBadgeProps extends BadgeProps {
  loading?: boolean;
  role: MembersRole;
}

export function MembersRoleBadge({
  loading = false,
  role,
  ...props
}: MembersRoleBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(role)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[role]}
    </Badge>
  );
}
