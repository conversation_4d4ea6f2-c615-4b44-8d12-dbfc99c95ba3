import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type ShipmentMode = Enums<"shipment_mode">;

const i18n = {
  en: {
    open: "Open",
    closed: "Closed",
  },
} as const;

const getBadgeVariant = (mode: ShipmentMode): BadgeProps["variant"] => {
  switch (mode) {
    case "open":
      return "accent";
    case "closed":
      return "secondary";
    default:
      return "default";
  }
};

export interface ShipmentModeBadgeProps extends BadgeProps {
  loading?: boolean;
  mode: ShipmentMode;
}

export function ShipmentModeBadge({
  loading = false,
  mode,
  ...props
}: ShipmentModeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(mode)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[mode]}
    </Badge>
  );
}
