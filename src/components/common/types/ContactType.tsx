import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type ContactType = Enums<"contact_type">;

const i18n = {
  en: {
    billing: "Billing",
    manager: "Manager",
    dispatcher: "Dispatcher",
    safety_officer: "Safety Officer",
    maintenance: "Maintenance",
    warehouse: "Warehouse",
    customs: "Customs",
    logistics: "Logistics",
    sales: "Sales",
    support: "Support",
    other: "Other",
  },
} as const;

const getBadgeVariant = (type: ContactType): BadgeProps["variant"] => {
  switch (type) {
    case "billing":
      return "accent";
    case "manager":
      return "default";
    case "dispatcher":
      return "default";
    case "safety_officer":
      return "destructive";
    case "maintenance":
      return "secondary";
    case "warehouse":
      return "secondary";
    case "customs":
      return "outline";
    case "logistics":
      return "default";
    case "sales":
      return "accent";
    case "support":
      return "secondary";
    case "other":
      return "outline";
    default:
      return "default";
  }
};

export interface ContactTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: ContactType;
}

export function ContactTypeBadge({
  loading = false,
  type,
  ...props
}: ContactTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
