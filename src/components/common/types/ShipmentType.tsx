import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type ShipmentType = Enums<"shipment_type">;

const i18n = {
  en: {
    air: "Air",
    ocean: "Ocean",
    ground: "Ground",
    other: "Other",
  },
} as const;

const getBadgeVariant = (type: ShipmentType): BadgeProps["variant"] => {
  switch (type) {
    case "air":
      return "accent";
    case "ocean":
      return "secondary";
    case "ground":
      return "default";
    case "other":
      return "outline";
    default:
      return "default";
  }
};

export interface ShipmentTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: ShipmentType;
}

export function ShipmentTypeBadge({
  loading = false,
  type,
  ...props
}: ShipmentTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
