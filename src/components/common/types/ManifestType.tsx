import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type ManifestType = Enums<"manifest_type">;

const i18n = {
  en: {
    cargo: "Cargo",
    container: "Container",
    out_of_gauge: "Out of Gauge",
    customs: "Customs",
  },
} as const;

const getBadgeVariant = (type: ManifestType): BadgeProps["variant"] => {
  switch (type) {
    case "cargo":
      return "default";
    case "container":
      return "accent";
    case "out_of_gauge":
      return "destructive";
    case "customs":
      return "outline";
    default:
      return "default";
  }
};

export interface ManifestTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: ManifestType;
}

export function ManifestTypeBadge({
  loading = false,
  type,
  ...props
}: ManifestTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
