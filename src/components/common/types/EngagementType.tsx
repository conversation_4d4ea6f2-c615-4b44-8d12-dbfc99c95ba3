import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type EngagementType = Enums<"engagement_type">;

const i18n = {
  en: {
    driver_request: "Driver Request",
    organization_offer: "Organization Offer",
  },
} as const;

const getBadgeVariant = (type: EngagementType): BadgeProps["variant"] => {
  switch (type) {
    case "driver_request":
      return "accent";
    case "organization_offer":
      return "default";
    default:
      return "default";
  }
};

export interface EngagementTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: EngagementType;
}

export function EngagementTypeBadge({
  loading = false,
  type,
  ...props
}: EngagementTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
