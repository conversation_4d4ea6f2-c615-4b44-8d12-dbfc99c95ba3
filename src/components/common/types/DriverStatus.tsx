import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type DriverStatus = Enums<"driver_status">;

const i18n = {
  en: {
    active: "Active",
    inactive: "Inactive",
    suspended: "Suspended",
  },
} as const;

const getBadgeVariant = (status: DriverStatus): BadgeProps["variant"] => {
  switch (status) {
    case "active":
      return "default";
    case "inactive":
      return "secondary";
    case "suspended":
      return "destructive";
    default:
      return "default";
  }
};

export interface DriverStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: DriverStatus;
}

export function DriverStatusBadge({
  loading = false,
  status,
  ...props
}: DriverStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
