import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type MemberStatus = Enums<"member_status">;

const i18n = {
  en: {
    pending: "Pending",
    active: "Active",
    inactive: "Inactive",
    declined: "Declined",
  },
} as const;

const getBadgeVariant = (status: MemberStatus): BadgeProps["variant"] => {
  switch (status) {
    case "pending":
      return "secondary";
    case "active":
      return "default";
    case "inactive":
      return "outline";
    case "declined":
      return "destructive";
    default:
      return "default";
  }
};

export interface MemberStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: MemberStatus;
}

export function MemberStatusBadge({
  loading = false,
  status,
  ...props
}: MemberStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
