import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type NotificationType = Enums<"notification_type">;

const i18n = {
  en: {
    shipment_update: "Shipment Update",
    incident_report: "Incident Report",
    system_alert: "System Alert",
    payment_update: "Payment Update",
  },
} as const;

const getBadgeVariant = (type: NotificationType): BadgeProps["variant"] => {
  switch (type) {
    case "shipment_update":
      return "default";
    case "incident_report":
      return "destructive";
    case "system_alert":
      return "secondary";
    case "payment_update":
      return "accent";
    default:
      return "default";
  }
};

export interface NotificationTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: NotificationType;
}

export function NotificationTypeBadge({
  loading = false,
  type,
  ...props
}: NotificationTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
