import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type LoadStatus = Enums<"load_status">;

const i18n = {
  en: {
    pending: "Pending",
    packaged: "Packaged",
    loaded: "Loaded",
    in_transit: "In Transit",
    delivered: "Delivered",
    missing: "Missing",
    damaged: "Damaged",
    rejected: "Rejected",
    returned: "Returned",
    customs_hold: "Customs Hold",
  },
} as const;

const getBadgeVariant = (status: LoadStatus): BadgeProps["variant"] => {
  switch (status) {
    case "pending":
      return "secondary";
    case "packaged":
      return "accent";
    case "loaded":
      return "accent";
    case "in_transit":
      return "default";
    case "delivered":
      return "default";
    case "missing":
      return "destructive";
    case "damaged":
      return "destructive";
    case "rejected":
      return "destructive";
    case "returned":
      return "secondary";
    case "customs_hold":
      return "outline";
    default:
      return "default";
  }
};

export interface LoadStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: LoadStatus;
}

export function LoadStatusBadge({
  loading = false,
  status,
  ...props
}: LoadStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
