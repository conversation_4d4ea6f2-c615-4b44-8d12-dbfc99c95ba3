import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type IncidentType = Enums<"incident_type">;

const i18n = {
  en: {
    accident: "Accident",
    delay: "Delay",
    damage: "Damage",
    theft: "Theft",
    weather: "Weather",
    mechanical: "Mechanical",
    other: "Other",
  },
} as const;

const getBadgeVariant = (type: IncidentType): BadgeProps["variant"] => {
  switch (type) {
    case "accident":
      return "destructive";
    case "delay":
      return "secondary";
    case "damage":
      return "destructive";
    case "theft":
      return "destructive";
    case "weather":
      return "secondary";
    case "mechanical":
      return "accent";
    case "other":
      return "default";
    default:
      return "default";
  }
};

export interface IncidentTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: IncidentType;
}

export function IncidentTypeBadge({
  loading = false,
  type,
  ...props
}: IncidentTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
