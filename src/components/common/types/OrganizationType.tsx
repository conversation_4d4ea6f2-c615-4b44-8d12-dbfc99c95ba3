import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type OrganizationType = Enums<"organization_type">;

const i18n = {
  en: {
    individual: "Individual",
    private: "Private",
    non_profit: "Non-Profit",
    government: "Government",
  },
} as const;

const getBadgeVariant = (type: OrganizationType): BadgeProps["variant"] => {
  switch (type) {
    case "individual":
      return "default";
    case "private":
      return "accent";
    case "non_profit":
      return "secondary";
    case "government":
      return "outline";
    default:
      return "default";
  }
};

export interface OrganizationTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: OrganizationType;
}

export function OrganizationTypeBadge({
  loading = false,
  type,
  ...props
}: OrganizationTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
