import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type ShipmentStatus = Enums<"shipment_status">;

const i18n = {
  en: {
    pending: "Pending",
    scheduled: "Scheduled",
    assigned: "Assigned",
    confirmed: "Confirmed",
    in_progress: "In Progress",
    completed: "Completed",
    cancelled: "Cancelled",
  },
} as const;

const getBadgeVariant = (status: ShipmentStatus): BadgeProps["variant"] => {
  switch (status) {
    case "pending":
      return "secondary";
    case "scheduled":
      return "accent";
    case "assigned":
      return "accent";
    case "confirmed":
      return "default";
    case "in_progress":
      return "default";
    case "completed":
      return "default";
    case "cancelled":
      return "destructive";
    default:
      return "default";
  }
};

export interface ShipmentStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: ShipmentStatus;
}

export function ShipmentStatusBadge({
  loading = false,
  status,
  ...props
}: ShipmentStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
