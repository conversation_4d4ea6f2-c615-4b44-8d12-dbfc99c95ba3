import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type EngagementStatus = Enums<"engagement_status">;

const i18n = {
  en: {
    pending: "Pending",
    accepted: "Accepted",
    declined: "Declined",
    expired: "Expired",
    cancelled: "Cancelled",
    completed: "Completed",
  },
} as const;

const getBadgeVariant = (status: EngagementStatus): BadgeProps["variant"] => {
  switch (status) {
    case "pending":
      return "secondary";
    case "accepted":
      return "default";
    case "declined":
      return "destructive";
    case "expired":
      return "outline";
    case "cancelled":
      return "destructive";
    case "completed":
      return "accent";
    default:
      return "default";
  }
};

export interface EngagementStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: EngagementStatus;
}

export function EngagementStatusBadge({
  loading = false,
  status,
  ...props
}: EngagementStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
