import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type OrganizationStatus = Enums<"organization_status">;

const i18n = {
  en: {
    pending: "Pending",
    active: "Active",
    suspended: "Suspended",
    inactive: "Inactive",
  },
} as const;

const getBadgeVariant = (status: OrganizationStatus): BadgeProps["variant"] => {
  switch (status) {
    case "pending":
      return "secondary";
    case "active":
      return "default";
    case "suspended":
      return "destructive";
    case "inactive":
      return "outline";
    default:
      return "default";
  }
};

export interface OrganizationStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: OrganizationStatus;
}

export function OrganizationStatusBadge({
  loading = false,
  status,
  ...props
}: OrganizationStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
