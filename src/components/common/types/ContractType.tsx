import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type ContractType = Enums<"contract_type">;

const i18n = {
  en: {
    bill_of_lading: "Bill of Lading",
    agreement: "Agreement",
    service_agreement: "Service Agreement",
    lease_agreement: "Lease Agreement",
    insurance_certificate: "Insurance Certificate",
    power_of_attorney: "Power of Attorney",
    other: "Other",
  },
} as const;

const getBadgeVariant = (type: ContractType): BadgeProps["variant"] => {
  switch (type) {
    case "bill_of_lading":
      return "default";
    case "agreement":
      return "secondary";
    case "service_agreement":
      return "accent";
    case "lease_agreement":
      return "accent";
    case "insurance_certificate":
      return "destructive";
    case "power_of_attorney":
      return "outline";
    case "other":
      return "secondary";
    default:
      return "default";
  }
};

export interface ContractTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: ContractType;
}

export function ContractTypeBadge({
  loading = false,
  type,
  ...props
}: ContractTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
