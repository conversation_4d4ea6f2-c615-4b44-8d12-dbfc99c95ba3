import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type InvitationStatus = Enums<"invitation_status">;

const i18n = {
  en: {
    pending: "Pending",
    accepted: "Accepted",
    revoked: "Revoked",
    rejected: "Rejected",
  },
} as const;

const getBadgeVariant = (status: InvitationStatus): BadgeProps["variant"] => {
  switch (status) {
    case "pending":
      return "secondary";
    case "accepted":
      return "default";
    case "revoked":
      return "destructive";
    case "rejected":
      return "destructive";
    default:
      return "default";
  }
};

export interface InvitationStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: InvitationStatus;
}

export function InvitationStatusBadge({
  loading = false,
  status,
  ...props
}: InvitationStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
