import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type QualificationStatus = Enums<"qualification_status">;

const i18n = {
  en: {
    pending: "Pending",
    verified: "Verified",
    expired: "Expired",
    revoked: "Revoked",
  },
} as const;

const getBadgeVariant = (
  status: QualificationStatus,
): BadgeProps["variant"] => {
  switch (status) {
    case "pending":
      return "secondary";
    case "verified":
      return "default";
    case "expired":
      return "outline";
    case "revoked":
      return "destructive";
    default:
      return "default";
  }
};

export interface QualificationStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: QualificationStatus;
}

export function QualificationStatusBadge({
  loading = false,
  status,
  ...props
}: QualificationStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
