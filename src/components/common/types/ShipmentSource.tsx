import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type ShipmentSource = Enums<"shipment_source">;

const i18n = {
  en: {
    driver: "Driver",
    organization: "Organization",
    system: "System",
  },
} as const;

const getBadgeVariant = (source: ShipmentSource): BadgeProps["variant"] => {
  switch (source) {
    case "driver":
      return "accent";
    case "organization":
      return "default";
    case "system":
      return "secondary";
    default:
      return "default";
  }
};

export interface ShipmentSourceBadgeProps extends BadgeProps {
  loading?: boolean;
  source: ShipmentSource;
}

export function ShipmentSourceBadge({
  loading = false,
  source,
  ...props
}: ShipmentSourceBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(source)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[source]}
    </Badge>
  );
}
