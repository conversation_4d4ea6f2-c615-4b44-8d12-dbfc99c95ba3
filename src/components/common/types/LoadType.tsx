import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type LoadType = Enums<"load_type">;

const i18n = {
  en: {
    general: "General",
    bulk_dry: "Bulk Dry",
    bulk_liquid: "Bulk Liquid",
    container_20ft: "20ft Container",
    container_40ft: "40ft Container",
    container_reefer: "Reefer Container",
    container_flat_rack: "Flat Rack Container",
    container_open_top: "Open Top Container",
    container_tank: "Tank Container",
    breakbulk: "Breakbulk",
    ro_ro: "Roll-on/Roll-off",
    heavy_lift: "Heavy Lift",
    project_cargo: "Project Cargo",
    dangerous_goods: "Dangerous Goods",
    temperature_controlled: "Temperature Controlled",
    livestock: "Livestock",
    vehicles: "Vehicles",
    machinery: "Machinery",
    perishables: "Perishables",
    valuables: "Valuables",
    other: "Other",
  },
} as const;

const getBadgeVariant = (type: LoadType): BadgeProps["variant"] => {
  switch (type) {
    case "general":
      return "default";
    case "bulk_dry":
    case "bulk_liquid":
      return "secondary";
    case "container_20ft":
    case "container_40ft":
    case "container_reefer":
    case "container_flat_rack":
    case "container_open_top":
    case "container_tank":
      return "accent";
    case "breakbulk":
    case "ro_ro":
    case "heavy_lift":
    case "project_cargo":
      return "outline";
    case "dangerous_goods":
      return "destructive";
    case "temperature_controlled":
    case "livestock":
    case "perishables":
      return "destructive";
    case "vehicles":
    case "machinery":
    case "valuables":
      return "accent";
    case "other":
      return "secondary";
    default:
      return "default";
  }
};

export interface LoadTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: LoadType;
}

export function LoadTypeBadge({
  loading = false,
  type,
  ...props
}: LoadTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
