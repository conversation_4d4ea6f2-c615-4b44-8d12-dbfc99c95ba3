import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type IncidentStatus = Enums<"incident_status">;

const i18n = {
  en: {
    reported: "Reported",
    investigating: "Investigating",
    resolved: "Resolved",
    closed: "Closed",
  },
} as const;

const getBadgeVariant = (status: IncidentStatus): BadgeProps["variant"] => {
  switch (status) {
    case "reported":
      return "destructive";
    case "investigating":
      return "secondary";
    case "resolved":
      return "accent";
    case "closed":
      return "default";
    default:
      return "default";
  }
};

export interface IncidentStatusBadgeProps extends BadgeProps {
  loading?: boolean;
  status: IncidentStatus;
}

export function IncidentStatusBadge({
  loading = false,
  status,
  ...props
}: IncidentStatusBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(status)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[status]}
    </Badge>
  );
}
