import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type LocationType = Enums<"location_type">;

const i18n = {
  en: {
    billing: "Billing",
    commercial: "Commercial",
    industrial: "Industrial",
    government: "Government",
    public: "Public",
    residential: "Residential",
    warehouse: "Warehouse",
    distribution_center: "Distribution Center",
    retail: "Retail",
    other: "Other",
  },
} as const;

const getBadgeVariant = (type: LocationType): BadgeProps["variant"] => {
  switch (type) {
    case "billing":
      return "accent";
    case "commercial":
      return "default";
    case "industrial":
      return "destructive";
    case "government":
      return "outline";
    case "public":
      return "secondary";
    case "residential":
      return "default";
    case "warehouse":
      return "accent";
    case "distribution_center":
      return "accent";
    case "retail":
      return "default";
    case "other":
      return "secondary";
    default:
      return "default";
  }
};

export interface LocationTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: LocationType;
}

export function LocationTypeBadge({
  loading = false,
  type,
  ...props
}: LocationTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
