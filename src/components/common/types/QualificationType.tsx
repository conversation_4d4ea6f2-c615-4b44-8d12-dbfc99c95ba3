import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type QualificationType = Enums<"qualification_type">;

const i18n = {
  en: {
    commercial_drivers_license: "Commercial Driver's License",
    hazmat_endorsement: "HAZMAT Endorsement",
    medical_certificate: "Medical Certificate",
    defensive_driving_certificate: "Defensive Driving Certificate",
    tanker_endorsement: "Tanker Endorsement",
    doubles_triples_endorsement: "Doubles/Triples Endorsement",
    other: "Other",
  },
} as const;

const getBadgeVariant = (type: QualificationType): BadgeProps["variant"] => {
  switch (type) {
    case "commercial_drivers_license":
      return "default";
    case "hazmat_endorsement":
      return "destructive";
    case "medical_certificate":
      return "accent";
    case "defensive_driving_certificate":
      return "secondary";
    case "tanker_endorsement":
      return "destructive";
    case "doubles_triples_endorsement":
      return "destructive";
    case "other":
      return "outline";
    default:
      return "default";
  }
};

export interface QualificationTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: QualificationType;
}

export function QualificationTypeBadge({
  loading = false,
  type,
  ...props
}: QualificationTypeBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[type]}
    </Badge>
  );
}
