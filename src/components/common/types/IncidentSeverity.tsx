import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums } from "@/supabase/types";

export type IncidentSeverity = Enums<"incident_severity">;

const i18n = {
  en: {
    low: "Low",
    medium: "Medium",
    high: "High",
    critical: "Critical",
  },
} as const;

const getBadgeVariant = (severity: IncidentSeverity): BadgeProps["variant"] => {
  switch (severity) {
    case "low":
      return "secondary";
    case "medium":
      return "accent";
    case "high":
      return "destructive";
    case "critical":
      return "destructive";
    default:
      return "default";
  }
};

export interface IncidentSeverityBadgeProps extends BadgeProps {
  loading?: boolean;
  severity: IncidentSeverity;
}

export function IncidentSeverityBadge({
  loading = false,
  severity,
  ...props
}: IncidentSeverityBadgeProps) {
  return (
    <Badge {...props} variant={getBadgeVariant(severity)}>
      {loading ? <Skeleton className="h-4 w-12" /> : i18n.en[severity]}
    </Badge>
  );
}
