import type { BadgeProps } from "@/components/ui/badge";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Enums, Json, Tables } from "@/supabase/types";

export type DocumentType = Enums<"document_type">;

// Helper function to extract AI document type from metadata
export function getAIDocumentType(metadata: Json | null): string | null {
  if (!metadata || typeof metadata !== "object") return null;
  const meta = metadata as Record<string, unknown>;
  return (meta.ai_document_type as string) || null;
}

// Helper function to get the best display document type from a document
export function getDisplayDocumentType(document: Tables<"documents">): {
  type: string;
  isAIType: boolean;
  displayName: string;
} {
  const aiType = getAIDocumentType(document.metadata);

  if (aiType) {
    return {
      type: aiType,
      isAIType: true,
      displayName: getAIDocumentDisplayName(aiType),
    };
  }

  return {
    type: document.type,
    isAIType: false,
    displayName: i18n.en[document.type],
  };
}

// Helper function to get display name for AI document types
export function getAIDocumentDisplayName(aiType: string): string {
  const aiTypeMap: Record<string, string> = {
    bill_of_lading: "Bill of Lading",
    delivery_order: "Delivery Order",
    proof_of_delivery: "Proof of Delivery",
    fuel_receipt: "Fuel Receipt",
    temperature_log: "Temperature Log",
    weight_ticket: "Weight Ticket",
    freight_bill: "Freight Bill",
    commercial_invoice: "Commercial Invoice",
    manifest: "Manifest",
    inspection_report: "Inspection Report",
    container_seal: "Container Seal",
    dot_number_placard: "DOT Number Placard",
    mc_number_placard: "MC Number Placard",
    vehicle_id_plate: "Vehicle ID Plate",
    license_plate: "License Plate",
    container_number_plate: "Container Number Plate",
  };

  return (
    aiTypeMap[aiType] ||
    aiType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
  );
}

const i18n = {
  en: {
    manifest: "Manifest",
    contract: "Contract",
    general: "General",
    other: "Other",
    verification: "Verification",
  },
} as const;

const getBadgeVariant = (type: DocumentType): BadgeProps["variant"] => {
  switch (type) {
    case "manifest":
      return "default";
    case "contract":
      return "accent";
    case "general":
      return "secondary";
    case "other":
      return "outline";
    case "verification":
      return "destructive";
    default:
      return "default";
  }
};

// Get badge variant for AI document types
const getAIBadgeVariant = (aiType: string): BadgeProps["variant"] => {
  switch (aiType) {
    case "bill_of_lading":
    case "delivery_order":
    case "manifest":
      return "default";
    case "proof_of_delivery":
      return "accent";
    case "fuel_receipt":
    case "commercial_invoice":
    case "freight_bill":
      return "accent";
    case "temperature_log":
    case "weight_ticket":
      return "destructive";
    case "container_seal":
    case "dot_number_placard":
    case "mc_number_placard":
      return "outline";
    default:
      return "secondary";
  }
};

export interface DocumentTypeBadgeProps extends BadgeProps {
  loading?: boolean;
  type: DocumentType;
  metadata?: Json | null; // Optional metadata to show AI type
  preferAIType?: boolean; // Whether to prefer AI type over database type
}

export function DocumentTypeBadge({
  loading = false,
  type,
  metadata,
  preferAIType = true,
  ...props
}: DocumentTypeBadgeProps) {
  if (loading) {
    return (
      <Badge {...props} variant="secondary">
        <Skeleton className="h-4 w-12" />
      </Badge>
    );
  }

  // Try to use AI document type if available and preferred
  const aiType = preferAIType ? getAIDocumentType(metadata) : null;

  if (aiType) {
    return (
      <Badge {...props} variant={getAIBadgeVariant(aiType)}>
        {getAIDocumentDisplayName(aiType)}
      </Badge>
    );
  }

  // Fallback to database enum type
  return (
    <Badge {...props} variant={getBadgeVariant(type)}>
      {i18n.en[type]}
    </Badge>
  );
}

// Convenience component that always shows AI type if available
export function AIDocumentTypeBadge(
  props: Omit<DocumentTypeBadgeProps, "preferAIType">,
) {
  return <DocumentTypeBadge {...props} preferAIType={true} />;
}
