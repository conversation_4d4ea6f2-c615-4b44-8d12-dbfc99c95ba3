import CopyButton from "@/components/shared/CopyButton";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { transformPhoneNumber } from "@/lib/masks/phone";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    phone: "Phone",
    notAvailable: "N/A",
    actions: {
      copyPhone: "Copy Phone",
    },
  },
};

export default function ContactPhone({
  loading,
  phone,
  className,
  showCopy = true,
  hideEmpty = false,
}: {
  loading?: boolean;
  phone?: string | null;
  className?: string;
  showCopy?: boolean;
  hideEmpty?: boolean;
}) {
  if (loading) {
    return <Skeleton className="h-6 w-full" />;
  }

  if (hideEmpty && !phone) {
    return null;
  }

  return (
    <div className="group flex items-center gap-2">
      <dt className="sr-only">{i18n.en.phone}</dt>
      <dd className={cn("text-muted-foreground text-sm", className)}>
        {phone ? (
          <Button asChild variant="link" className="h-6 p-0 text-inherit">
            <a href={`tel:${phone}`}>
              <span className="w-full truncate">
                {transformPhoneNumber(phone)}
              </span>
            </a>
          </Button>
        ) : (
          <span className="h-6 w-full truncate px-0.5 text-inherit">
            {i18n.en.notAvailable}
          </span>
        )}
      </dd>
      {showCopy && (
        <CopyButton text={phone ?? ""} label={i18n.en.actions.copyPhone} />
      )}
    </div>
  );
}
