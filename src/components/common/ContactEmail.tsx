import CopyButton from "@/components/shared/CopyButton";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    email: "Email",
    notAvailable: "N/A",
    actions: {
      copyEmail: "Copy Email",
    },
  },
};

export default function ContactEmail({
  loading,
  email,
  className,
  hideEmpty = false,
  showCopy = true,
}: {
  loading?: boolean;
  email?: string | null;
  className?: string;
  showCopy?: boolean;
  hideEmpty?: boolean;
}) {
  if (loading) {
    return <Skeleton className="h-6 w-full" />;
  }

  if (hideEmpty && !email) {
    return null;
  }

  return (
    <div className="group flex items-center gap-2">
      <dt className="sr-only">{i18n.en.email}</dt>
      <dd className={cn("text-muted-foreground text-sm", className)}>
        {email ? (
          <Button
            asChild
            variant="link"
            className="h-6 w-full px-0.5 text-inherit"
          >
            <a href={`mailto:${email}`}>
              <span className="w-full truncate">{email}</span>
            </a>
          </Button>
        ) : (
          <span className="h-6 w-full truncate px-0.5 text-inherit">
            {i18n.en.notAvailable}
          </span>
        )}
      </dd>
      {showCopy && (
        <CopyButton text={email ?? ""} label={i18n.en.actions.copyEmail} />
      )}
    </div>
  );
}
