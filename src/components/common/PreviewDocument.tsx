import type { VariantProps } from "class-variance-authority";
import type { PropsWithChildren } from "react";

import { cva } from "class-variance-authority";
import { Link } from "react-router";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import DocumentViewer from "./DocumentViewer";

const i18n = {
  en: {
    generics: {
      document: "Document",
    },
  },
  links: {
    document: "/app/documents/[documentId]",
  },
};

const previewDocumentVariants = cva(
  "flex w-full items-center justify-start gap-2 rounded-lg",
  {
    variants: {
      size: {
        sm: "h-6 rounded-md text-xs",
        md: "h-8 rounded-md",
        lg: "h-10",
        xl: "h-16 text-lg",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const previewDocumentDescriptionVariants = cva(
  "text-muted-foreground overflow-hidden text-ellipsis",
  {
    variants: {
      size: {
        sm: "text-xs font-light",
        md: "text-sm font-light",
        lg: "text-base font-normal",
        xl: "text-lg font-normal",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface DocumentPartial {
  document?: {
    id: string;
    name: string;
    description?: string | null;
    type: string;
    url: string;
  } | null;
}

export interface PreviewDocumentProps
  extends VariantProps<typeof previewDocumentVariants>,
    DocumentPartial,
    PropsWithChildren {
  loading?: boolean;
  link?: boolean;
  description?: string;
  className?: string;
}

export default function PreviewDocument({
  loading = false,
  link = false,
  size = "md",
  className,
  document,
  description,
  children,
}: PreviewDocumentProps) {
  return (
    <div className={cn(previewDocumentVariants({ size, className }))}>
      <DocumentViewer
        loading={loading}
        size={size}
        document={
          document
            ? {
                id: document.id,
                name: document.name,
                type: document.type,
                url: document.url,
              }
            : undefined
        }
      />

      {loading ? (
        <div className="flex w-full min-w-32 grow flex-col gap-0.5">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-3 w-[70%]" />
        </div>
      ) : link ? (
        <Link
          className="flex min-w-0 flex-1 flex-col items-start justify-start"
          to={i18n.links.document.replace("[documentId]", document?.id ?? "")}
        >
          <p className="truncate font-semibold">{document?.name}</p>
          {(document?.description ?? description) && (
            <p
              className={cn(
                previewDocumentDescriptionVariants({ size }),
                "mt-1 truncate",
              )}
            >
              {document?.description ?? description}
            </p>
          )}
          {children && (
            <div
              className={cn(
                previewDocumentDescriptionVariants({ size }),
                "mt-1",
              )}
            >
              {children}
            </div>
          )}
        </Link>
      ) : (
        <div className="flex min-w-0 flex-1 flex-col items-start justify-start text-nowrap">
          <p className="truncate font-semibold">{document?.name}</p>
          {(document?.description ?? description) && (
            <p
              className={cn(
                previewDocumentDescriptionVariants({ size }),
                "mt-1 truncate",
              )}
            >
              {document?.description ?? description}
            </p>
          )}
          {children && (
            <div
              className={cn(
                previewDocumentDescriptionVariants({ size }),
                "mt-1",
              )}
            >
              {children}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
