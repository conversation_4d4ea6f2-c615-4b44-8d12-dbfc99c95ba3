import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

const driverScoreVariants = cva("font-bold", {
  variants: {
    size: {
      sm: "text-xl",
      md: "text-3xl",
      lg: "text-4xl",
      xl: "text-5xl",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

export interface DriverScoreProps
  extends VariantProps<typeof driverScoreVariants> {
  score: number;
  showLabel?: boolean;
  className?: string;
  isLoading?: boolean;
}

const getScoreColor = (score: number) => {
  // Base cases for specific score ranges
  if (score < 50) return "text-red-500"; // Red for under 50
  if (score < 99) return "text-yellow-500"; // Yellow for below 99
  if (score === 100) return "text-gray-500"; // Gray for 100 (starting base)

  // Create a color ramp for higher scores
  if (score >= 450) return "text-emerald-500"; // High scores get emerald
  if (score >= 350) return "text-green-500";
  if (score >= 250) return "text-teal-500";
  if (score >= 150) return "text-blue-500";

  // Default fallback
  return "text-indigo-500";
};

const getScoreLabel = (score: number) => {
  if (score < 50) return "Poor";
  if (score < 99) return "Needs Improvement";
  if (score === 100) return "Fair"; // Starting base
  if (score >= 450) return "Excellent";
  if (score >= 350) return "Great";
  if (score >= 250) return "Good";
  if (score >= 150) return "Above Average";
  return "Average";
};

export function DriverScore({
  score,
  showLabel = true,
  size,
  className = "",
  isLoading = false,
}: DriverScoreProps) {
  if (isLoading) {
    return <Skeleton className={cn(`h-8 w-16`, className)} />;
  }

  return (
    <div className={cn("flex items-center", className)}>
      <span className={cn(driverScoreVariants({ size }), getScoreColor(score))}>
        {score}
      </span>
      {showLabel && (
        <Badge variant="secondary" className="ml-2">
          {getScoreLabel(score)}
        </Badge>
      )}
    </div>
  );
}

export { getScoreColor, getScoreLabel };
