import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import DocumentType from "./DocumentType";

// CVA definition for consistent sizing and floating avatar styling
const documentViewerVariants = cva(
  "relative rounded-sm transition-all duration-200 hover:shadow-lg",
  {
    variants: {
      size: {
        sm: "size-6 text-sm [&_svg]:size-4",
        md: "size-8 text-base [&_svg]:size-6",
        lg: "size-10 text-lg [&_svg]:size-7",
        xl: "size-16 text-lg [&_svg]:size-8",
      },
      shadow: {
        true: "shadow-md",
        false: "",
      },
    },
    defaultVariants: {
      size: "md",
      shadow: true,
    },
  },
);

export interface DocumentViewerProps
  extends VariantProps<typeof documentViewerVariants> {
  className?: string;
  document?: {
    id: string;
    name: string;
    type: string;
    url?: string;
    storage_path?: string;
    metadata?: unknown; // Changed from { cachePath?: string } to any to support various metadata formats
  };
  loading?: boolean;
  shadow?: boolean;
}

export default function DocumentViewer({
  loading = false,
  document,
  size = "md",
  shadow = true,
  className,
}: DocumentViewerProps) {
  return loading ? (
    <Skeleton
      className={cn(
        "relative shrink-0 rounded-sm",
        {
          "size-6": size === "sm",
          "size-8": size === "md",
          "size-10": size === "lg",
          "size-16": size === "xl",
          "shadow-md": shadow,
        },
        className,
      )}
    />
  ) : (
    <Button
      variant="outline"
      size="icon"
      asChild
      className={cn(
        documentViewerVariants({ size, shadow }),
        "border-border/50 bg-background hover:bg-muted/50 border",
        className,
      )}
    >
      <a href={document?.url} target="_blank" rel="noreferrer">
        <DocumentType type={document?.type} />
      </a>
    </Button>
  );
}
