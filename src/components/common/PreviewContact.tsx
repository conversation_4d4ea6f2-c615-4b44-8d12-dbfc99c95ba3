import type { PropsWithChildren } from "react";

import { useMemo } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import ContactData from "./ContactData";

const i18n = {
  en: {
    avatarFor: "avatar image for ",
  },
};

export default function PreviewContact({
  loading = false,
  hideEmpty = false,
  link,
  firstName,
  lastName,
  email,
  phone,
  avatar,
  className,
  children,
}: PropsWithChildren<{
  loading?: boolean;
  hideEmpty?: boolean;
  link?: string;
  firstName?: string;
  lastName?: string;
  email?: string | null;
  phone?: string | null;
  avatar?: string | null;
  className?: string;
}>) {
  const { name, initials } = useMemo(
    () => ({
      name: `${firstName ?? ""} ${lastName ?? ""}`.trim(),
      initials:
        [(firstName ?? " ")[0], (lastName ?? " ")[0]].join("").trim() || "AA",
    }),
    [firstName, lastName],
  );
  return (
    <div
      className={cn(
        "flex w-full flex-row items-start justify-start gap-2",
        className,
      )}
    >
      {loading ? (
        <Skeleton className="size-9" />
      ) : (
        <div
          className={cn("grid", {
            "grid-rows-2 gap-2": !!children,
          })}
        >
          <Avatar className="size-9 rounded-lg">
            <AvatarImage asChild src={avatar ?? ""}>
              <img
                src={avatar ?? ""}
                alt={i18n.en.avatarFor + name}
                width={32}
                height={32}
              />
            </AvatarImage>
            <AvatarFallback className="rounded-lg">{initials}</AvatarFallback>
          </Avatar>
          {children}
        </div>
      )}
      <ContactData
        loading={loading}
        hideEmpty={hideEmpty}
        link={link}
        name={name}
        email={email}
        phone={phone}
      />
    </div>
  );
}
