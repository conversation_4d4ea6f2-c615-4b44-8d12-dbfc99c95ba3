import { Link } from "react-router";

import CopyButton from "@/components/shared/CopyButton";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    name: "Name",
    notAvailable: "N/A",
    actions: {
      copyName: "Copy Name",
    },
  },
};

export default function ContactName({
  name,
  link,
  size = "md",
  className,
  showCopyButton = true,
}: {
  name: string;
  link?: string;
  size?: "md" | "lg";
  className?: string;
  showCopyButton?: boolean;
}) {
  return (
    <div className="group flex items-center gap-2">
      <dt className="sr-only">{i18n.en.name}</dt>
      <dd
        className={cn("leading-none font-medium", { "text-lg": size === "lg" })}
      >
        {link ? (
          <Button
            asChild
            variant="link"
            className={cn(
              "text-foreground hover:text-primary focus:text-primary h-6 w-full truncate px-0.5 text-sm transition-colors hover:no-underline",
              className,
            )}
          >
            <Link to={link}>{name}</Link>
          </Button>
        ) : (
          <span className="h-6 w-full truncate px-0.5 text-sm">{name}</span>
        )}
      </dd>
      {showCopyButton && (
        <CopyButton text={name} label={i18n.en.actions.copyName} />
      )}
    </div>
  );
}
