# Common Components

This directory contains presentational components that provide consistent visual representations of domain entities and concepts across the application. These components focus on how things look rather than how they behave.

> **Quick Start**
>
> - Need to display a domain entity? Check here first
> - Components are presentation-focused (Load, Shipment, Location displays)
> - Use for consistent representation of business objects
> - Can be used in lists, cards, or detail views
> - Should match the domain model's structure

> [!NOTE] Common components ensure that domain entities are displayed consistently throughout the application. For example, a `LoadBadge` will look the same whether it appears in a list, a dashboard, or a detail page. These components are the visual building blocks for our domain objects.

## Component Categories

1. Entity Displays:
   - `LoadBadge` - Compact load representation
   - `ShipmentCard` - Shipment summary display
   - `LocationPin` - Location visualization
   - `OrganizationLogo` - Organization branding

2. Status Indicators:
   - `LoadStatus` - Load state indicator
   - `ShipmentProgress` - Shipment progress
   - `PaymentStatus` - Payment state
   - `VerificationBadge` - Verification state

3. Entity Metadata:
   - `LoadDimensions` - Load size/weight
   - `ShipmentRoute` - Route visualization
   - `PriceDisplay` - Standardized pricing
   - `DateTimeDisplay` - Formatted timestamps

## File Structure

```
common/
├── loads/          # Load-related displays
├── shipments/      # Shipment visualizations
├── locations/      # Location components
├── organizations/  # Organization displays
├── shared/         # Cross-entity components
└── README.md       # This file
```

## Component Pattern

Common components typically follow this pattern:

```typescript
interface LoadBadgeProps {
  // Entity-specific props
  type: LoadType;
  status: LoadStatus;
  weight?: number;
  // Display options
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'compact';
}

export function LoadBadge({
  type,
  status,
  weight,
  size = 'md',
  variant = 'default'
}: LoadBadgeProps) {
  return (
    <div className={cn("load-badge", size, variant)}>
      <LoadTypeIcon type={type} />
      <LoadStatusDot status={status} />
      {variant === 'default' && weight && (
        <span className="weight">{formatWeight(weight)}</span>
      )}
    </div>
  );
}
```

## Best Practices

1. **Visual Consistency**
   - Use consistent colors and icons
   - Maintain uniform spacing
   - Follow typography guidelines
   - Respect size variants

2. **Information Hierarchy**
   - Show most important details first
   - Support different information densities
   - Use appropriate emphasis
   - Consider context of use

3. **Flexibility**
   - Support different sizes
   - Offer layout variants
   - Allow content truncation
   - Handle missing data gracefully

4. **Accessibility**
   - Provide meaningful labels
   - Use semantic HTML
   - Ensure color contrast
   - Support screen readers

5. **Maintainability**
   - Keep components focused
   - Share common styles
   - Document variants
   - Use TypeScript for props

## Examples

### Simple Display

```typescript
export function OrganizationBadge({
  name,
  type,
  verified
}: OrganizationBadgeProps) {
  return (
    <div className="org-badge">
      <OrganizationIcon type={type} />
      <span className="name">{name}</span>
      {verified && <VerifiedBadge />}
    </div>
  );
}
```

### Complex Display

```typescript
export function ShipmentTimeline({
  origin,
  destination,
  stops,
  status
}: ShipmentTimelineProps) {
  return (
    <div className="shipment-timeline">
      <TimelinePoint location={origin} type="start" />
      {stops.map(stop => (
        <TimelinePoint key={stop.id} location={stop} type="stop" />
      ))}
      <TimelinePoint location={destination} type="end" />
      <TimelineProgress status={status} />
    </div>
  );
}
```

## When to Create a Common Component

Create a common component when you need to:

1. Display a domain entity consistently
2. Show entity status or metadata
3. Represent business concepts visually
4. Create reusable visual patterns for entities
