import CopyButton from "@/components/shared/CopyButton";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    address: "Address",
    notAvailable: "N/A",
    actions: {
      copyAddress: "Copy Address",
    },
  },
};

export default function LocationAddress({
  loading = false,
  link = true,
  truncate = true,
  showCopyButton = true,
  size = "md",
  address,
  className,
}: {
  loading?: boolean;
  link?: boolean;
  truncate?: boolean;
  showCopyButton?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | null;
  address?: string | null;
  className?: string;
}) {
  const addressClassName = cn("min-h-6 p-0 text-sm text-gray-500", className, {
    "w-full max-w-[600px] truncate overflow-hidden": truncate,
    "text-xs": size === "sm",
    "text-sm": size === "md" || size === "lg",
    "text-lg": size === "xl",
  });

  return loading ? (
    <Skeleton className="min-h-6 w-full max-w-[600px]" />
  ) : (
    <dl className="group grid h-fit max-w-full grid-cols-[1fr_auto] items-center gap-2 overflow-hidden">
      {/* TODO: add full address as data entries */}
      <dt className="sr-only">{i18n.en.address}</dt>
      <dd className="truncate overflow-hidden">
        {address ? (
          link ? (
            <Button asChild variant="link" className={addressClassName}>
              <a
                href={`https://maps.google.com/?q=${address}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <span className={addressClassName}>{address}</span>
              </a>
            </Button>
          ) : (
            <span className={addressClassName}>{address}</span>
          )
        ) : (
          <span className={addressClassName}>{i18n.en.notAvailable}</span>
        )}
      </dd>

      {showCopyButton && (
        <CopyButton
          size={size}
          hidden={!address && !link}
          text={address ?? ""}
          label={i18n.en.actions.copyAddress}
        />
      )}
    </dl>
  );
}
