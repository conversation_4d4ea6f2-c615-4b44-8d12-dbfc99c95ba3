import ContactEmail from "@/components/common/ContactEmail";
import Contact<PERSON>ame from "@/components/common/ContactName";
import ContactPhone from "@/components/common/ContactPhone";
import { Skeleton } from "@/components/ui/skeleton";

export default function ContactData({
  loading = false,
  hideEmpty = false,
  link,
  name,
  email,
  phone,
}: {
  loading?: boolean;
  hideEmpty?: boolean;
  link?: string;
  name: string;
  email?: string | null;
  phone?: string | null;
}) {
  return loading ? (
    <div className="grid flex-1 gap-2">
      <Skeleton className="h-5 w-[180px]" />
      <Skeleton className="h-5 w-[140px]" />
      <Skeleton className="h-5 w-[120px]" />
    </div>
  ) : (
    <dl className="grid flex-1 gap-1">
      <ContactName name={name} link={link} className="text-foreground" />
      <ContactEmail email={email} hideEmpty={hideEmpty} />
      <ContactPhone phone={phone} hideEmpty={hideEmpty} />
    </dl>
  );
}
