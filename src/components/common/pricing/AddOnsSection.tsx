import { BadgeDollarSign, Check } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

type AddOn = {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
};

interface AddOnsSectionProps {
  title: string;
  addOns: AddOn[];
}

const AddOnsSection = ({ title, addOns }: AddOnsSectionProps) => (
  <div className="mb-20">
    <h2 className="mb-8 text-center text-3xl font-bold">{title}</h2>
    <div className="mx-auto grid max-w-4xl gap-8 md:grid-cols-1">
      {addOns.map((addon) => (
        <Card
          key={addon.name}
          className="glass-effect hover-lift border border-white/10 p-8"
        >
          <div className="mb-6 flex items-start justify-between">
            <div className="flex-1">
              <div className="mb-2 flex items-center gap-2">
                <BadgeDollarSign className="text-accent h-5 w-5" />
                <h3 className="text-2xl font-bold">{addon.name}</h3>
              </div>
              <p className="text-muted-foreground">{addon.description}</p>
            </div>
            <div className="text-right">
              <span className="text-4xl font-bold">{addon.price}</span>
              <div className="text-muted-foreground">{addon.period}</div>
            </div>
          </div>
          <Button variant="primary" className="mb-8 w-full">
            Add to Plan
          </Button>
          <ul className="grid gap-4 md:grid-cols-2">
            {addon.features.map((feature) => (
              <li
                key={feature}
                className="text-muted-foreground flex items-center"
              >
                <Check className="text-accent mr-2 h-5 w-5 shrink-0" />
                {feature}
              </li>
            ))}
          </ul>
        </Card>
      ))}
    </div>
  </div>
);

export default AddOnsSection;
