import { Check } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

type Plan = {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  highlighted?: boolean;
};

interface PricingSectionProps {
  title: string;
  plans: Plan[];
}

const PricingSection = ({ title, plans }: PricingSectionProps) => (
  <div className="mb-20">
    <h2 className="mb-8 text-center text-3xl font-bold">{title}</h2>
    <div
      className={`grid ${plans.length === 2 ? "max-w-4xl md:grid-cols-2" : "md:grid-cols-3"} mx-auto gap-8`}
    >
      {plans.map((plan) => (
        <Card
          key={plan.name}
          className={`glass-effect hover-lift relative p-8 ${
            plan.highlighted
              ? "border-accent border-2"
              : "border border-white/10"
          }`}
        >
          {plan.highlighted && (
            <Badge variant="accent" className="absolute -top-3 right-4">
              Most Value
            </Badge>
          )}
          <h3 className="mb-2 text-2xl font-bold">{plan.name}</h3>
          <div className="mb-4">
            <span className="text-4xl font-bold">{plan.price}</span>
            <span className="text-muted-foreground ml-2">{plan.period}</span>
          </div>
          <p className="text-muted-foreground mb-6">{plan.description}</p>
          <Button
            className="mb-8 w-full"
            variant={plan.highlighted ? "accent" : "default"}
          >
            Get Started
          </Button>
          <ul className="space-y-4">
            {plan.features.map((feature) => (
              <li
                key={feature}
                className="text-muted-foreground flex items-center"
              >
                <Check className="text-accent mr-2 h-5 w-5" />
                {feature}
              </li>
            ))}
          </ul>
        </Card>
      ))}
    </div>
  </div>
);

export default PricingSection;
