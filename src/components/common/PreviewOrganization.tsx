import type { VariantProps } from "class-variance-authority";
import type { PropsWithChildren } from "react";

import { cva } from "class-variance-authority";
import { Link } from "react-router";

import { Emblem } from "@/components/brand/Emblem";
import { FloatingAvatar } from "@/components/shared/FloatingAvatar";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    generics: {
      avatar: "Organization Avatar",
    },
  },
  links: {
    organization: "/app/organizations/[organizationId]",
  },
};

const previewOrganizationVariants = cva(
  "flex w-full items-center gap-2 rounded-lg",
  {
    variants: {
      size: {
        sm: "h-6 rounded-md text-xs",
        md: "h-8 rounded-md",
        lg: "h-10",
        xl: "h-16 text-lg",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const previewOrganizationDescriptionVariants = cva(
  "text-muted-foreground overflow-hidden text-ellipsis",
  {
    variants: {
      size: {
        sm: "text-xs font-light",
        md: "text-sm font-light",
        lg: "text-base font-normal",
        xl: "text-lg font-normal",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface OrganizationPartial {
  organization?: {
    id: string;
    name: string;
    avatar?: string | null;
    description?: string | null;
  } | null;
}

export interface PreviewOrganizationProps
  extends VariantProps<typeof previewOrganizationVariants>,
    OrganizationPartial,
    PropsWithChildren {
  loading?: boolean;
  link?: boolean;
  shadow?: boolean;
  description?: string;
  className?: string;
}

export function OrganizationAvatar({
  loading = false,
  link = false,
  size = "md",
  shadow = true,
  className,
  organization,
}: PreviewOrganizationProps) {
  if (
    !loading &&
    !organization?.avatar &&
    organization?.name === "AXA Professionals"
  ) {
    return (
      <Emblem
        shadow={shadow}
        className={cn("size-8 overflow-visible", className, {
          "cursor-pointer": link,
          "size-6": size === "sm",
          "size-10": size === "lg",
        })}
      />
    );
  }

  return (
    <FloatingAvatar
      loading={loading}
      shadow={shadow}
      size={size}
      link={link}
      avatar={organization?.avatar}
      label={organization?.name[0]}
    />
  );
}

export default function PreviewOrganization({
  loading = false,
  link = false,
  size = "md",
  shadow = true,
  className,
  organization,
  description,
  children,
}: PreviewOrganizationProps) {
  return (
    <div className={cn(previewOrganizationVariants({ size, className }))}>
      <OrganizationAvatar
        loading={loading}
        shadow={shadow}
        size={size}
        link={link}
        organization={organization}
      />

      {loading ? (
        <div className="flex w-full min-w-32 grow flex-col gap-0.5">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-3 w-[70%]" />
        </div>
      ) : link ? (
        <Link
          className="flex grow flex-col items-start justify-start"
          to={i18n.links.organization.replace(
            "[organizationId]",
            organization?.id ?? "",
          )}
        >
          <p className="overflow-hidden font-semibold text-ellipsis">
            {organization?.name}
          </p>
          {(organization?.description ?? description) && (
            <p
              className={cn(
                previewOrganizationDescriptionVariants({ size }),
                "mt-1",
              )}
            >
              {organization?.description ?? description}
            </p>
          )}
        </Link>
      ) : (
        <div className="flex grow flex-col items-start justify-start">
          <p className="overflow-hidden font-semibold text-ellipsis">
            {organization?.name}
          </p>
          {(organization?.description ?? description) && (
            <p
              className={cn(
                previewOrganizationDescriptionVariants({ size }),
                "mt-1",
              )}
            >
              {organization?.description ?? description}
            </p>
          )}
        </div>
      )}

      {children ? <div>{children}</div> : null}
    </div>
  );
}
