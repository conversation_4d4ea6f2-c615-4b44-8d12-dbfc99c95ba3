# Components

> [!KNOWLEDGE]
>
> **Data Handling Rules**
> Components in this directory are strictly presentational - they should never directly fetch or mutate data. Instead:
>
> - Use hooks from `src/hooks/` for data operations
> - Pass data and callbacks as props
> - Keep data fetching in pages or widgets
> - Widgets are the only exception as hybrid components
>
> **Hook Integration**
> Components typically integrate with hooks from `src/hooks/`:
>
> - `useToast` - For feedback notifications
> - `useSession` - For auth state
> - `useOrganization` - For org context
> - `usePermissions` - For access control
> - `usePreferences` - For user settings
> - Form components use `react-hook-form` with `zod` validation
>
> **Usage in Pages**
> Components are composed in `src/pages/` following these patterns:
>
> - Pages handle data fetching and mutations
> - Components receive data via props
> - Widgets can be used directly for self-contained features
> - Layout components wrap page content
> - Example structure:
>
> ```tsx
> // src/pages/app/console/loads/index.tsx
> function LoadsPage() {
>   const { data, isLoading } = useLoads();
>   return (
>     <ConsoleLayout>
>       <LoadsList data={data} isLoading={isLoading} />
>     </ConsoleLayout>
>   );
> }
> ```
>
> **UI Components**
> Need a basic UI component? These foundational building blocks are built on Shadcn UI with custom styling and functionality. Components include Button, Input, Select, Card, Badge, Dialog, Sheet, Tabs, and more, all with consistent theming and behavior. Find these in `src/components/ui/`.
>
> **Form Components**
> Need a form component? These reusable form components follow consistent patterns for handling state, validation, and submission. Common fields include Currency, PhoneNumber, DatePicker, Description, Summary, Weight, and domain-specific types like LoadType, ShipmentMode, and LocationType. Find these in `src/components/forms/`.
>
> **Widget Components**
> Need a self-contained feature? These hybrid components combine data handling with their own presentation layer. Examples include UserManager, RealTimeTracker, TeamSwitcher, and DocumentScanner. Find these in `src/components/widgets/`.
>
> **Shared Components**
> Need a reusable component with both behavior and UI? These range from simple utilities to complex systems. Examples include CopyButton, DataTable, DialogForm, DialogConfirmation, ErrorAlert, FilterSystem, and DragHandle. Find these in `src/components/shared/`.
>
> **Common Components**
> Need to display a domain entity? These components provide consistent visual representations of business objects. Examples include LoadStatusBadge, ShipmentCard, ContactData (ContactEmail, ContactPhone, etc), Preview components (PreviewOrganization, PreviewLocation, etc), and various status indicators and enum representations. Find these in `src/components/common/`.
>
> **Layout Components**
> Need to structure a page or view? These components define structural organization. Examples include AppLayout, PublicLayout, ConsoleLayout, Header, Footer, and various content arrangements. Find these in `src/components/layouts/`.
>
> **Authentication Components**
> Need auth UI? These components handle user authentication through Supabase. Components include SignInForm, SignUpForm, MagicLinkForm, OAuth providers, and protection components like RequireAuth and RequireRole. Find these in `src/components/authentication/`.
>
> **Selector Components**
> Need a selection interface? These components provide searchable selection UIs. Examples include the core Selector component, LoadSelector, ShipmentSelector, LocationSelector, DriverSelector, and various attribute selectors. Find these in `src/components/selectors/`.
>
> **Map Components**
> Need to show locations or routes? These Mapbox-based components handle geographic visualization. Components include StaticMap, InteractiveMap, LocationMarker, RouteLayer, and various map controls. Find these in `src/components/maps/`.

---

This directory contains all the React components used in the application. Components are organized into specialized directories, each serving a specific purpose in the application architecture.

> **Quick Start Guide**
>
> - UI primitives → `ui/` (Shadcn/Radix components)
> - Forms → `forms/` (Form components and fields)
> - Data + UI → `widgets/` (Self-contained features)
> - Reusable behavior → `shared/` (Complex reusable components)
> - Domain displays → `common/` (Entity visualization)
> - Page structure → `layouts/` (Page organization)
> - Auth flows → `authentication/` (Supabase auth)
> - Selection UIs → `selectors/` (Search/select interfaces)
> - Map features → `maps/` (Mapbox integration)

## Directory Structure

```
components/
├── authentication/  # Supabase auth components and guards
├── common/         # Domain entity display components
├── forms/          # Form components and field types
├── layouts/        # Page and content layout components
├── maps/           # Mapbox-based mapping components
├── selectors/      # Entity selection interfaces
├── shared/         # Reusable behavioral components
├── ui/            # Shadcn UI base components
└── widgets/        # Self-contained feature components
```

## Component Categories

### Authentication (`authentication/`)

- Auth flows with Supabase integration
- Sign in/up forms and OAuth providers
- Route protection and access control
- Session management components

### Common (`common/`)

- Domain entity displays
- Status indicators and badges
- Entity metadata visualization
- Consistent business object representation

### Forms (`forms/`)

- Form components using react-hook-form
- Reusable form fields
- Validation with zod
- Consistent submission patterns

### Layouts (`layouts/`)

- Page structure components
- Content organization
- Navigation layouts
- Responsive containers

### Maps (`maps/`)

- Mapbox GL integration
- Location visualization
- Route display
- Interactive map features

### Selectors (`selectors/`)

- Entity selection interfaces
- Search and filter UIs
- Multi-select components
- Relationship pickers

### Shared (`shared/`)

- Complex reusable components
- Utility components
- Interactive elements
- Behavioral patterns

### UI (`ui/`)

- Shadcn/Radix UI components
- Custom themed elements
- Basic input components
- Display primitives

### Widgets (`widgets/`)

- Self-contained features
- Data + UI combinations
- Complex interactions
- Domain-specific functionality

## Type Components

The `common/types/` directory contains badge components for database enums:

### Status Components

- `DriverStatus`, `LoadStatus`, `ShipmentStatus`
- `EngagementStatus`, `InvitationStatus`
- `MemberStatus`, `OrganizationStatus`
- `QualificationStatus`, `IncidentStatus`

### Type Components

- `LoadType`, `ShipmentType`, `LocationType`
- `ContactType`, `ContractType`, `DocumentType`
- `EngagementType`, `ManifestType`
- `OrganizationType`, `QualificationType`

### Other Types

- `IncidentSeverity`, `MembersRole`
- `ShipmentMode`, `ShipmentSource`

## Best Practices

1. **Component Organization**
   - Place components in appropriate directories
   - Follow directory-specific patterns
   - Maintain consistent file structure
   - Use index files for exports

2. **Code Quality**
   - Write TypeScript interfaces
   - Include prop documentation
   - Handle loading/error states
   - Follow accessibility guidelines

3. **Styling**
   - Use Tailwind utilities
   - Follow design system
   - Support dark mode
   - Maintain responsive layouts

4. **State Management**
   - Keep state close to usage
   - Use appropriate hooks
   - Handle side effects
   - Manage loading states

5. **Performance**
   - Optimize re-renders
   - Lazy load when needed
   - Handle large datasets
   - Cache appropriately

## Usage Examples

### Type Badge

```tsx
import { ShipmentStatusBadge } from "@/components/common/types/ShipmentStatus";

function ShipmentCard({ status }) {
  return (
    <div className="flex items-center gap-2">
      <span>Status:</span>
      <ShipmentStatusBadge status={status} />
    </div>
  );
}
```

### Form Component

```tsx
import { LoadForm } from "@/components/forms/LoadForm";

function CreateLoad() {
  const handleSubmit = (values: LoadFormValues) => {
    // Handle submission
  };

  return <LoadForm onSubmit={handleSubmit} />;
}
```

### Widget Usage

```tsx
import { LoadsList } from "@/components/widgets/loads/LoadsList";

function LoadsPage() {
  return (
    <div className="space-y-6">
      <h1>Loads</h1>
      <LoadsList />
    </div>
  );
}
```

## When to Use Each Directory

- `authentication/` - For auth flows and protection
- `common/` - For domain entity display
- `forms/` - For data collection interfaces
- `layouts/` - For page structure
- `maps/` - For geographic visualization
- `selectors/` - For entity selection
- `shared/` - For reusable behavior
- `ui/` - For basic UI elements
- `widgets/` - For complete features
