# Development Tools

⚠️ **WARNING: CONVENTION-BREAKING CODE** ⚠️

This folder contains development tools and utilities that **intentionally break** the established project conventions. These components are designed for:

- Testing data flows
- Making direct changes to the database
- Providing shortcuts for development workflows
- Populating test data

## Important Notes

1. **Do not use these components in production code**
2. **Do not use these components as examples for regular development**
3. **These tools are hidden behind environment flags**

These tools are only meant for development and testing purposes. They bypass normal application flows and security considerations that would be important in production.

## Available Tools

- `CreateOrgButton`: Creates an organization and all required resources with a single click
