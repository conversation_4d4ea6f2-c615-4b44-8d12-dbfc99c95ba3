import { useState } from "react";
import { RefreshCw } from "lucide-react";
import { useNavigate } from "react-router";

import { useCreateOrganization } from "@/api/organizations/use-create-organization";
import { ErrorAlert } from "@/components/shared/ErrorAlert";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/supabase/client";

/**
 * Development-only component that creates an organization with a single click
 * This component is intentionally breaking project conventions for dev purposes
 */
export function CreateOrgButton() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const createOrganization = useCreateOrganization();

  // Only show in development
  if (process.env.NODE_ENV === "production") {
    return null;
  }

  const handleCreateOrg = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get current user
      const { data: authData } = await supabase.auth.getUser();
      const userId = authData.user?.id;
      const userEmail = authData.user?.email;

      if (!userId || !userEmail) {
        throw new Error("User not authenticated");
      }

      // Create location first (bypassing RLS by using service role if needed in dev)
      const { data: location, error: locationError } = await supabase
        .from("locations")
        .insert({
          type: "commercial",
          formatted: "123 Dev Street, San Francisco, CA 94107",
          street: "123 Dev Street",
          city: "San Francisco",
          state: "CA",
          postal: "94107",
          country: "United States",
          latitude: 37.7749,
          longitude: -122.4194,
        })
        .select()
        .single();

      if (locationError) throw locationError;

      if (!location?.id) {
        throw new Error("Failed to create location");
      }

      // Create organization with location_id
      const org = await createOrganization.mutateAsync({
        name: `Test Org ${new Date().toISOString().slice(0, 10)}`,
        industry: "Transportation",
        size: "11-50",
        type: "private",
        status: "active",
        location_id: location.id,
        user_id: userId, // Ensure the user_id is set to satisfy RLS
      });

      if (!org?.id) {
        throw new Error("Failed to create organization");
      }

      // Update location with organization_id
      const { error: updateLocationError } = await supabase
        .from("locations")
        .update({ organization_id: org.id })
        .eq("id", location.id);

      if (updateLocationError) throw updateLocationError;

      // Create member record
      const { error: memberError } = await supabase.from("members").insert({
        organization_id: org.id,
        user_id: userId,
        email: userEmail,
        role: "owner",
        status: "active",
      });

      if (memberError) throw memberError;

      toast({
        title: "Development Organization Created",
        description: `Organization ID: ${org.id}`,
      });

      // Navigate to console
      navigate("/app/console");
    } catch (error) {
      console.error("Error creating dev organization:", error);
      setError(error instanceof Error ? error : new Error(String(error)));
      toast({
        variant: "destructive",
        title: "Creation Failed",
        description:
          error instanceof Error
            ? `${error.message} - ${JSON.stringify(error)}`
            : "Unknown error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {error && <ErrorAlert error={error} />}
      <Button
        variant="outline"
        size="sm"
        onClick={handleCreateOrg}
        disabled={isLoading}
        className="fixed right-4 bottom-4 z-50 border-yellow-300 bg-yellow-100 text-yellow-900 hover:bg-yellow-200"
      >
        {isLoading ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : null}
        Dev: Create Org + Resources
      </Button>
    </>
  );
}
