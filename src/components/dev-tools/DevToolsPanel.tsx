import { useState } from "react";
import { Bug, ChevronUp, Database, X } from "lucide-react";

import { Button } from "@/components/ui/button";

/**
 * Development panel with various tools for testing and database manipulation
 * Intentionally breaks project conventions for development purposes
 */
export function DevToolsPanel() {
  const [isOpen, setIsOpen] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV === "production") {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50">
      {isOpen ? (
        <div className="w-[350px] rounded-md border-2 border-yellow-300 bg-yellow-50 p-4 shadow-lg">
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center">
              <Database className="mr-2 h-5 w-5 text-yellow-600" />
              <h3 className="font-semibold text-yellow-900">
                Development Tools
              </h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-6 w-6 p-0 text-yellow-700 hover:bg-yellow-100 hover:text-yellow-900"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="space-y-2">
            <p className="mb-2 text-xs text-yellow-700">
              These tools are for development only and bypass normal application
              flows.
            </p>

            {/* Individual tool buttons can go here */}
            <div className="grid grid-cols-1 gap-2">
              {/* Add more tools here in the future */}
              <Button
                variant="outline"
                size="sm"
                className="border-yellow-300 bg-white text-yellow-900 hover:bg-yellow-100"
                onClick={() => console.log("Tool action here")}
              >
                Create Member
              </Button>
            </div>
          </div>

          <div className="mt-4 border-t border-yellow-200 pt-2">
            <p className="text-xs text-yellow-600">
              Environment: {process.env.NODE_ENV || "development"}
            </p>
          </div>
        </div>
      ) : (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(true)}
          className="flex items-center gap-2 border-yellow-300 bg-yellow-100 text-yellow-900 hover:bg-yellow-200"
        >
          <Bug className="h-4 w-4" />
          <span>Dev Tools</span>
          <ChevronUp className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}
