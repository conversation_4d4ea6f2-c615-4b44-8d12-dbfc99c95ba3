import type React from "react";

import { Linkedin, Truck } from "lucide-react";

import Brand from "@/components/layouts/Brand";
import ThemeToggle from "@/components/layouts/ThemeToggle";
import { Button } from "@/components/ui/button";
import { company, companyLinks } from "@/lib/constants/company";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <div className="bg-background min-h-screen">
      {/* Navigation */}
      <nav className="border-border bg-background/95 sticky top-0 z-50 border-b backdrop-blur-md">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="bg-primary flex h-8 w-8 items-center justify-center rounded-lg shadow-lg">
                <Truck className="text-primary-foreground h-5 w-5" />
              </div>
              <span className="from-primary to-secondary bg-gradient-to-r bg-clip-text text-xl font-bold text-transparent">
                QuikSkope
              </span>
            </div>
            <div className="hidden items-center space-x-8 md:flex">
              <a
                href="/drivers"
                className="text-muted-foreground hover:text-primary font-medium transition-colors"
              >
                For Drivers
              </a>
              <a
                href="/shippers"
                className="text-muted-foreground hover:text-primary font-medium transition-colors"
              >
                For Shippers
              </a>
              <a
                href="/developers"
                className="text-muted-foreground hover:text-primary font-medium transition-colors"
              >
                Developers
              </a>
              <a
                href="/roadmap"
                className="text-muted-foreground hover:text-primary font-medium transition-colors"
              >
                Roadmap
              </a>
              <Button
                variant="ghost"
                className="text-foreground hover:text-primary"
              >
                Sign In
              </Button>
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg">
                Get Started
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>{children}</main>

      {/* Footer */}
      <footer className="from-primary/5 via-background to-secondary/5 relative overflow-hidden bg-gradient-to-br py-12">
        {/* Background Pattern */}
        <div className="from-primary/3 absolute inset-0 bg-gradient-to-r to-transparent opacity-50"></div>

        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid gap-8 md:grid-cols-3">
            <div className="flex flex-col gap-4">
              <h3 className="text-2xl font-bold">
                <span className="from-primary to-secondary bg-gradient-to-r bg-clip-text text-transparent">
                  <Brand />
                </span>
              </h3>
              <p className="text-muted-foreground leading-relaxed">
                {company.description}
              </p>
              <div className="flex gap-4">
                <Button
                  variant="outline"
                  size="icon"
                  aria-label="LinkedIn"
                  className="border-primary/20 text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200"
                  asChild
                >
                  <a
                    href={companyLinks.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Linkedin className="h-5 w-5" />
                  </a>
                </Button>
              </div>
              <div className="flex gap-4">
                <ThemeToggle />
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-primary mb-4 text-lg font-semibold">
                Platform
              </h3>
              <ul className="text-muted-foreground space-y-3">
                <li>
                  <a
                    href="/drivers"
                    className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                  >
                    For Drivers
                  </a>
                </li>
                <li>
                  <a
                    href="/shippers"
                    className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                  >
                    For Shippers
                  </a>
                </li>
                <li>
                  <a
                    href="/developers"
                    className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                  >
                    Developers
                  </a>
                </li>
                <li>
                  <a
                    href="/roadmap"
                    className="hover:text-primary underline-offset-4 transition-colors hover:underline"
                  >
                    Roadmap
                  </a>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="text-secondary mb-4 text-lg font-semibold">
                Legal
              </h3>
              <ul className="text-muted-foreground space-y-3">
                <li>
                  <a
                    href="/legal"
                    className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                  >
                    Legal Center
                  </a>
                </li>
                <li>
                  <a
                    href="/legal/terms-of-service"
                    className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                  >
                    Terms of Service
                  </a>
                </li>
                <li>
                  <a
                    href="/legal/privacy-policy"
                    className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                  >
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a
                    href="/legal/compliance"
                    className="hover:text-secondary underline-offset-4 transition-colors hover:underline"
                  >
                    Compliance
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-primary/10 mt-12 border-t pt-8 text-center">
            <p className="text-muted-foreground">
              &copy; {new Date().getFullYear()}{" "}
              <span className="text-primary font-medium">QuikSkope</span>. All
              rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
