"use client";

import type { PropsWithChildren } from "react";

import { createContext, use, useCallback, useMemo } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router";

export interface SearchParamsContextValue {
  searchParams: Record<string, string | string[]>;
  setSearchParams: (name: string | [string, string][], value?: string) => void;
}

export const SearchParamsContext = createContext<SearchParamsContextValue>({
  searchParams: {},
  setSearchParams: () => void 0,
});

export function useSearchParamsValues() {
  const context = use(SearchParamsContext);
  return context.searchParams;
}

export function useSearchParamsContext() {
  return use(SearchParamsContext);
}

export function useSearchParam(key: string, defaultValue?: string) {
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const { value, setValue } = useMemo(
    () => ({
      value: searchParams[key] ?? defaultValue,
      setValue: (value?: string) => setSearchParams(key, value),
    }),
    [searchParams, setSearchParams, key, defaultValue],
  );

  return [value, setValue] as const;
}

export default function SearchParams({ children }: PropsWithChildren) {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  const createQueryString = useCallback(
    (param: string | [string, string][], value?: string) => {
      const params = new URLSearchParams(searchParams.toString());

      const paramsToSet = Array.isArray(param) ? param : [[param, value]];

      for (const [key, value] of paramsToSet) {
        if (key) {
          if (value) {
            params.set(key, value);
          } else {
            params.delete(key);
          }
        }
      }
      return params.toString();
    },
    [searchParams],
  );

  return (
    <SearchParamsContext.Provider
      value={useMemo(() => {
        const params: Record<string, string | string[]> = {};

        for (const [key, value] of searchParams.entries()) {
          params[key] = value;
        }

        return {
          searchParams: params,
          setSearchParams: (
            name: string | [string, string][],
            value?: string,
          ) => {
            navigate(location.pathname + "?" + createQueryString(name, value), {
              replace: true,
            });
          },
        };
      }, [searchParams, location.pathname, navigate, createQueryString])}
    >
      {children}
    </SearchParamsContext.Provider>
  );
}
