"use client";

import React, { useMemo } from "react";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useSearchPagination } from "./pagination";

export interface SearchPaginationProps {
  /**
   * Total number of items to paginate
   */
  totalItems: number;

  /**
   * Namespace for the search params
   */
  namespace?: string;

  /**
   * Default page size
   * @default 10
   */
  defaultPageSize?: number;

  /**
   * Default page index (0-based)
   * @default 0
   */
  defaultPageIndex?: number;

  /**
   * Number of sibling pages to show on each side of the current page
   * @default 1
   */
  siblingCount?: number;

  /**
   * Custom class name for the pagination component
   */
  className?: string;

  /**
   * Whether to show page numbers
   * @default true
   */
  showPageNumbers?: boolean;

  /**
   * Custom labels for the pagination buttons
   */
  labels?: {
    previous?: string;
    previousShort?: string;
    next?: string;
    nextShort?: string;
    page?: string;
  };
}

/**
 * SearchPagination component that uses URL search parameters for state management
 *
 * @example
 * ```tsx
 * // Basic usage
 * <SearchPagination
 *   totalItems={100}
 *   namespace="users"
 * />
 *
 * // With custom labels and without page numbers
 * <SearchPagination
 *   totalItems={100}
 *   namespace="users"
 *   showPageNumbers={false}
 *   labels={{
 *     previous: "Previous Page",
 *     previousShort: "Prev",
 *     next: "Next Page",
 *     nextShort: "Next",
 *     page: "Page {current} of {total}"
 *   }}
 * />
 * ```
 */
export function SearchPagination({
  totalItems,
  namespace,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  siblingCount = 1,
  className,
  showPageNumbers = true,
  labels = {
    previous: "Previous",
    previousShort: "Prev",
    next: "Next",
    nextShort: "Next",
    page: "Page {current} of {total}",
  },
}: SearchPaginationProps) {
  const { pagination, setPagination } = useSearchPagination({
    group: namespace,
    defaultPageSize,
    defaultPageIndex,
  });

  const { pageIndex, pageSize } = pagination;
  const totalPages = Math.ceil(totalItems / pageSize);

  // Generate page numbers to display
  const pageNumbers = useMemo(() => {
    if (!showPageNumbers) return [];

    const totalPageNumbers = siblingCount * 2 + 3; // siblings + current + first + last

    // Case 1: If the number of pages is less than the page numbers we want to show
    if (totalPageNumbers >= totalPages) {
      return Array.from({ length: totalPages }, (_, i) => i);
    }

    // Calculate left and right sibling index
    const leftSiblingIndex = Math.max(pageIndex - siblingCount, 0);
    const rightSiblingIndex = Math.min(
      pageIndex + siblingCount,
      totalPages - 1,
    );

    // Do not show dots when there is just one page number to be inserted
    const shouldShowLeftDots = leftSiblingIndex > 1;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 2;

    // Case 2: No left dots to show, but right dots to be shown
    if (!shouldShowLeftDots && shouldShowRightDots) {
      const leftItemCount = 1 + 2 * siblingCount;
      const leftRange = Array.from({ length: leftItemCount }, (_, i) => i);

      return [...leftRange, -1, totalPages - 1];
    }

    // Case 3: No right dots to show, but left dots to be shown
    if (shouldShowLeftDots && !shouldShowRightDots) {
      const rightItemCount = 1 + 2 * siblingCount;
      const rightRange = Array.from(
        { length: rightItemCount },
        (_, i) => totalPages - rightItemCount + i,
      );

      return [0, -1, ...rightRange];
    }

    // Case 4: Both left and right dots to be shown
    if (shouldShowLeftDots && shouldShowRightDots) {
      const middleRange = Array.from(
        { length: rightSiblingIndex - leftSiblingIndex + 1 },
        (_, i) => leftSiblingIndex + i,
      );

      return [0, -1, ...middleRange, -2, totalPages - 1];
    }

    return [];
  }, [pageIndex, totalPages, siblingCount, showPageNumbers]);

  const handlePageChange = (newPageIndex: number) => {
    if (newPageIndex >= 0 && newPageIndex < totalPages) {
      setPagination({ pageIndex: newPageIndex, pageSize });
    }
  };

  if (totalPages <= 1) {
    return null;
  }

  return (
    <Pagination className={className}>
      <PaginationContent className="flex-wrap justify-center gap-1">
        <PaginationItem>
          <PaginationPrevious
            size="md"
            onClick={() => handlePageChange(pageIndex - 1)}
            aria-disabled={pageIndex === 0}
            tabIndex={pageIndex === 0 ? -1 : 0}
            className={pageIndex === 0 ? "pointer-events-none opacity-50" : ""}
          >
            <span className="hidden sm:inline">{labels.previous}</span>
            <span className="sm:hidden">{labels.previousShort}</span>
          </PaginationPrevious>
        </PaginationItem>

        {!showPageNumbers && (
          <PaginationItem>
            <span className="flex h-9 items-center justify-center px-2 text-xs sm:px-4 sm:text-sm">
              {(labels.page || "Page {current} of {total}")
                .replace("{current}", (pageIndex + 1).toString())
                .replace("{total}", totalPages.toString())}
            </span>
          </PaginationItem>
        )}

        {showPageNumbers &&
          pageNumbers.map((pageNumber, i) => {
            if (pageNumber === -1 || pageNumber === -2) {
              return (
                <PaginationItem key={`ellipsis-${i}`}>
                  <PaginationEllipsis />
                </PaginationItem>
              );
            }

            return (
              <PaginationItem key={pageNumber}>
                <PaginationLink
                  size="icon"
                  isActive={pageNumber === pageIndex}
                  onClick={() => handlePageChange(pageNumber)}
                >
                  {pageNumber + 1}
                </PaginationLink>
              </PaginationItem>
            );
          })}

        <PaginationItem>
          <PaginationNext
            size="md"
            onClick={() => handlePageChange(pageIndex + 1)}
            aria-disabled={pageIndex === totalPages - 1}
            tabIndex={pageIndex === totalPages - 1 ? -1 : 0}
            className={
              pageIndex === totalPages - 1
                ? "pointer-events-none opacity-50"
                : ""
            }
          >
            <span className="hidden sm:inline">{labels.next}</span>
            <span className="sm:hidden">{labels.nextShort}</span>
          </PaginationNext>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
