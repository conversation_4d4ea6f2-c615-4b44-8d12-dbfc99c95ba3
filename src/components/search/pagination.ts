import type { OnChangeFn, PaginationState } from "@tanstack/react-table";

import { useCallback, useMemo } from "react";

import { useSearchParamsContext } from "@/components/search/SearchParams";

export const SEARCH_PAGINATION_NAME = "pagination";

// Helper function to parse with fallback
function parseWithFallback(value: string | undefined, fallback: number) {
  if (!value || value.trim() === "") return fallback;
  const parsed = parseInt(value, 10);
  return Number.isNaN(parsed) ? fallback : parsed;
}

function getSearchPaginationValue({
  group,
  name = SEARCH_PAGINATION_NAME,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  searchParams,
}: {
  group?: string;
  name?: string;
  defaultPageIndex?: number;
  defaultPageSize?: number;
  searchParams: Record<string, string | string[] | undefined>;
}) {
  const key = [name, group].filter(Boolean).join("-");
  const pageIndex = searchParams[[key, "index"].join("-")] as
    | string
    | undefined;
  const pageSize = searchParams[[key, "size"].join("-")] as string | undefined;
  return {
    pageIndex: parseWithFallback(pageIndex, defaultPageIndex),
    pageSize: parseWithFallback(pageSize, defaultPageSize),
  };
}

export function useSearchPaginationValue(
  group?: string,
  name = SEARCH_PAGINATION_NAME,
  defaultPageSize = 10,
  defaultPageIndex = 0,
) {
  const { searchParams } = useSearchParamsContext();

  const { pageIndex, pageSize } = getSearchPaginationValue({
    group,
    name,
    defaultPageIndex,
    defaultPageSize,
    searchParams,
  });

  return useMemo(() => {
    return {
      pageIndex,
      pageSize,
    };
  }, [pageIndex, pageSize]);
}

export function useSearchPagination({
  group,
  name = SEARCH_PAGINATION_NAME,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  onChange,
}: {
  group?: string;
  name?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onChange?: OnChangeFn<PaginationState>;
} = {}) {
  const key = [name, group].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const { pageIndex, pageSize } = useMemo(() => {
    return getSearchPaginationValue({
      group,
      name,
      defaultPageIndex,
      defaultPageSize,
      searchParams,
    });
  }, [searchParams, name, group, defaultPageIndex, defaultPageSize]);

  const setPagination = useCallback<OnChangeFn<PaginationState>>(
    (value: PaginationState | ((old: PaginationState) => PaginationState)) => {
      const newValue =
        typeof value === "function"
          ? value({
              pageIndex,
              pageSize,
            })
          : value;
      setSearchParams([
        [[key, "index"].join("-"), newValue.pageIndex.toString()],
        [[key, "size"].join("-"), newValue.pageSize.toString()],
      ]);
      onChange?.(newValue);
    },
    [setSearchParams, onChange, key, pageIndex, pageSize],
  );

  return useMemo(
    () => ({ pagination: { pageIndex, pageSize }, setPagination }),
    [pageIndex, pageSize, setPagination],
  );
}
