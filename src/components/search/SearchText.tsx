import type { VariantProps } from "class-variance-authority";

import * as React from "react";
import { useCallback, useRef } from "react";
import { cva } from "class-variance-authority";
import { SearchIcon, XIcon } from "lucide-react";

import { SEARCH_TEXT_NAME, useSearchText } from "@/components/search/text";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const searchInputVariants = cva("size-full truncate overflow-hidden", {
  variants: {
    size: {
      sm: "min-h-9 pl-8",
      md: "min-h-10 pl-8",
      lg: "min-h-12 pl-10 text-lg",
      xl: "min-h-14 pl-12 text-xl",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

const i18n = {
  en: {
    actions: {
      search: "Search",
    },
  },
};

export interface SearchTextProps
  extends VariantProps<typeof searchInputVariants> {
  loading?: boolean;
  name?: string;
  group?: string;
  className?: string;
  placeholder?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
}

export function SearchText({
  loading,
  size = "md",
  name = SEARCH_TEXT_NAME,
  group,
  className,
  placeholder = i18n.en.actions.search,
  defaultValue,
  onChange,
}: SearchTextProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const {
    value,
    defaultValue: searchParamsDefaultValue,
    handleClear,
    handleValueChange,
  } = useSearchText({
    name,
    group,
    defaultValue,
    onChange,
  });

  const handleClearWithRef = useCallback(() => {
    handleClear();
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  }, [handleClear]);

  return (
    <div className={cn("relative size-fit min-w-0 shrink-0", className)}>
      <SearchIcon
        className={cn("text-muted-foreground absolute", {
          "top-2.5 left-2.5 size-4": size === "sm",
          "top-3 left-3 size-4": size === "md",
          "top-3 left-3 size-6": size === "lg",
          "top-4 left-3 size-6": size === "xl",
        })}
      />
      <Input
        ref={inputRef}
        type="text"
        name={name}
        readOnly={loading}
        placeholder={placeholder}
        className={cn(searchInputVariants({ size }), {
          "pr-8": value && (size === "sm" || size === "md"),
          "pr-12": value && size === "lg",
          "pr-14": value && size === "xl",
        })}
        defaultValue={searchParamsDefaultValue}
        onChange={handleValueChange}
      />
      {value && (
        <Button
          className={cn("absolute top-0 right-0", {
            "top-1.5 right-1 size-6 p-0": size === "sm",
            "top-2 right-1.5 size-6 p-0": size === "md",
            "top-1.5 right-1.5": size === "lg",
            "top-2.5 right-2.5": size === "xl",
          })}
          variant="ghost"
          size="icon"
          aria-label="Clear search"
          onClick={handleClearWithRef}
        >
          <XIcon
            className={cn("text-muted-foreground", {
              "size-3.5": size === "sm",
              "size-4": size === "md",
              "size-5": size === "lg",
              "size-6": size === "xl",
            })}
          />
        </Button>
      )}
    </div>
  );
}
