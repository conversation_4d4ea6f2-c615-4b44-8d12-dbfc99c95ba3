"use client";

import type { ReactNode } from "react";

import { useCallback, useEffect } from "react";

import { useSearchParamsContext } from "@/components/search/SearchParams";

export const SEARCH_FILTER_NAME = "filter";

export function useSearchFilterValue<T extends string | number | null = string>(
  groupId: string,
  name = SEARCH_FILTER_NAME,
) {
  const { searchParams } = useSearchParamsContext();
  const key = [name, groupId].filter(Boolean).join("-");
  return searchParams[key] as T | undefined;
}

export interface FilterGroup {
  id: string;
  label: string | ReactNode;
  options: FilterOption[];
}

export interface FilterOption {
  value: string | number | null;
  label: string | ReactNode;
}

export interface UseSearchFilterOptions {
  name?: string;
  groups: FilterGroup[];
  onChange?: (value: string, group: FilterGroup) => void;
}

export function useSearchFilter({
  name = SEARCH_FILTER_NAME,
  groups,
  onChange,
}: UseSearchFilterOptions) {
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const handleValueChange = useCallback(
    (value: string, group: FilterGroup) => {
      const key = [name, group.id].filter(Boolean).join("-");
      setSearchParams(key, value);
      onChange?.(value, group);
    },
    [setSearchParams, name, onChange],
  );

  const getFilterValue = useCallback(
    (groupId: string) => {
      const key = [name, groupId].filter(Boolean).join("-");
      return searchParams[key] as string | undefined;
    },
    [searchParams, name],
  );

  // Handle default value initialization when labels match search params
  useEffect(() => {
    const changes: [string, string][] = [];
    for (const group of groups) {
      const key = [name, group.id].filter(Boolean).join("-");
      if (group.label === searchParams[key]) {
        if (group.options[0]?.value !== null) {
          changes.push([key, group.options[0]?.value?.toString() ?? ""]);
        }
        break;
      }
    }
    if (changes.length > 0) {
      setSearchParams(changes);
    }
  }, [name, groups, searchParams, setSearchParams]);

  return {
    handleValueChange,
    getFilterValue,
  };
}
