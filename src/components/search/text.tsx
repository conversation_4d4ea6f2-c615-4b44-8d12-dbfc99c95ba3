"use client";

import type { ChangeEvent } from "react";

import { useCallback, useEffect, useMemo } from "react";

import { useSearchParamsContext } from "@/components/search/SearchParams";
import { useDebounceValue } from "@/hooks/use-debounce-value";

export const SEARCH_TEXT_NAME = "search";

export function useSearchTextValue(group?: string, name = SEARCH_TEXT_NAME) {
  const { searchParams } = useSearchParamsContext();
  const key = [name, group].filter(Boolean).join("-");
  return searchParams[key] as string | undefined;
}

export interface UseSearchTextOptions {
  name?: string;
  group?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
}

export function useSearchText({
  name = SEARCH_TEXT_NAME,
  group,
  defaultValue,
  onChange,
}: UseSearchTextOptions = {}) {
  const key = [name, group].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  // Memoize the current value from search params so useDebounceValue can detect changes
  const currentSearchValue = useMemo(() => {
    return (searchParams[key] as string | undefined) ?? defaultValue ?? "";
  }, [searchParams, key, defaultValue]);

  const [value, setValue] = useDebounceValue<string>(currentSearchValue, {
    delay: 500,
  });

  const handleClear = useCallback(() => {
    setValue("");
    onChange?.("");
  }, [onChange, setValue]);

  const handleValueChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      setValue(event.target.value);
      onChange?.(event.target.value);
    },
    [setValue, onChange],
  );

  useEffect(() => {
    if (value !== searchParams[key]) {
      setSearchParams(key, value);
    }
  }, [key, value, searchParams, setSearchParams]);

  return {
    value,
    defaultValue: searchParams[key] as string | undefined,
    handleClear,
    handleValueChange,
  };
}
