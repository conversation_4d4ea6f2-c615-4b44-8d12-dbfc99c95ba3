"use client";

import type { DateRange } from "react-day-picker";

import { useCallback, useEffect, useMemo, useState } from "react";
import { endOfDay, format, startOfDay } from "date-fns";

import { useSearchParamsContext } from "@/components/search/SearchParams";

export const SEARCH_DATE_GROUP = "range";
export const SEARCH_DATE_NAME = "date";
export const SEARCH_START_DATE_NAME = "startDate";
export const SEARCH_END_DATE_NAME = "endDate";

export function useSearchDateRangeValue(
  group = SEARCH_DATE_GROUP,
  name = SEARCH_DATE_NAME,
) {
  const { searchParams } = useSearchParamsContext();
  return useMemo(() => {
    const key = [group, name].filter(Boolean).join("-");
    const startDateString = searchParams[
      [key, SEARCH_START_DATE_NAME].join("-")
    ] as string | undefined;
    const endDateString = searchParams[
      [key, SEARCH_END_DATE_NAME].join("-")
    ] as string | undefined;

    return {
      [SEARCH_START_DATE_NAME]: startDateString
        ? startOfDay(startDateString)
        : undefined,
      [SEARCH_END_DATE_NAME]: endDateString
        ? endOfDay(endDateString)
        : undefined,
    };
  }, [searchParams, group, name]);
}

export interface UseSearchDateRangeOptions {
  name?: string;
  group?: string;
  defaultValue?: DateRange;
  onChange?: (dateRange?: DateRange | null) => void | Promise<void>;
  onSelect?: (dateRange?: DateRange | null) => void | Promise<void>;
}

export function useSearchDateRange({
  name = SEARCH_DATE_NAME,
  group = SEARCH_DATE_GROUP,
  defaultValue,
  onChange,
  onSelect: onSelectCallback,
}: UseSearchDateRangeOptions = {}) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  // Get current values from search params
  const currentStartDate = searchParams[
    [key, SEARCH_START_DATE_NAME].join("-")
  ] as string | undefined;
  const currentEndDate = searchParams[[key, SEARCH_END_DATE_NAME].join("-")] as
    | string
    | undefined;

  // Compute initial selection from URL or default
  const initialSelection = useMemo(() => {
    if (currentStartDate || currentEndDate) {
      return {
        from: currentStartDate ? new Date(currentStartDate) : undefined,
        to: currentEndDate ? new Date(currentEndDate) : undefined,
      };
    }
    return defaultValue;
  }, [currentStartDate, currentEndDate, defaultValue]);

  // Initialize selection state
  const [selection, setSelection] = useState<DateRange | null | undefined>(
    initialSelection,
  );

  const onDateRangeChange = useCallback(
    async (dateRange?: DateRange | null) => {
      await onChange?.(dateRange);
    },
    [onChange],
  );

  const onSelect = useCallback(
    async (dates?: DateRange | null) => {
      const dateRange = dates || undefined;

      setSearchParams([
        [
          [key, SEARCH_START_DATE_NAME].join("-"),
          dateRange?.from?.toISOString() ?? "",
        ],
        [
          [key, SEARCH_END_DATE_NAME].join("-"),
          dateRange?.to?.toISOString() ?? "",
        ],
      ]);

      setSelection(dateRange);
      await onSelectCallback?.(dateRange);
      await onDateRangeChange(dateRange);
    },
    [setSearchParams, key, onSelectCallback, onDateRangeChange],
  );

  const onClear = useCallback(async () => {
    await onSelect(undefined);
    await onChange?.(null);
  }, [onSelect, onChange]);

  // Sync selection with URL changes (handles browser navigation)
  useEffect(() => {
    const urlDateRange = {
      from: currentStartDate ? new Date(currentStartDate) : undefined,
      to: currentEndDate ? new Date(currentEndDate) : undefined,
    };

    // Check if URL has any dates
    const hasUrlDates = !!(currentStartDate || currentEndDate);

    if (hasUrlDates) {
      // Compare current selection with URL dates
      const selectionMatches =
        selection?.from?.toISOString() === urlDateRange.from?.toISOString() &&
        selection?.to?.toISOString() === urlDateRange.to?.toISOString();

      if (!selectionMatches) {
        setSelection(urlDateRange);
      }
    } else if (!hasUrlDates && selection) {
      // Clear selection if no URL dates
      setSelection(null);
    }
  }, [currentStartDate, currentEndDate, selection]);

  const { values, hasValue, text } = useMemo(() => {
    const startDate = currentStartDate;
    const endDate = currentEndDate;

    return {
      text: [startDate, endDate]
        .filter((date): date is string => Boolean(date))
        .map((date: string) => {
          return format(new Date(date), "MM/dd/yyyy");
        })
        .join(" - "),
      hasValue: !!(startDate ?? endDate),
      values: {
        from: startDate ? new Date(startDate) : undefined,
        to: endDate ? new Date(endDate) : undefined,
      },
    };
  }, [currentStartDate, currentEndDate]);

  return {
    selection: selection === undefined ? null : selection,
    onSelect,
    onClear,
    values,
    hasValue,
    text,
  };
}

// Single date hook for individual date selection
export interface UseSearchDateOptions {
  name?: string;
  group?: string;
  defaultValue?: Date;
  onChange?: (date?: Date | null) => void | Promise<void>;
  onSelect?: (date?: Date | null) => void | Promise<void>;
}

export function useSearchDate({
  name = SEARCH_DATE_NAME,
  group = SEARCH_DATE_GROUP,
  defaultValue,
  onChange,
  onSelect: onSelectCallback,
}: UseSearchDateOptions = {}) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  // Get current value from search params
  const currentValue = searchParams[key] as string | undefined;

  // Compute initial selection from URL or default
  const initialSelection = useMemo(() => {
    if (currentValue) {
      return new Date(currentValue);
    }
    return defaultValue;
  }, [currentValue, defaultValue]);

  // Initialize selection state
  const [selection, setSelection] = useState<Date | null | undefined>(
    initialSelection,
  );

  const onDateChange = useCallback(
    async (date?: Date | null) => {
      await onChange?.(date);
    },
    [onChange],
  );

  const onSelect = useCallback(
    async (date?: Date | null) => {
      const dateValue = date || undefined;

      setSearchParams([[key, dateValue?.toISOString() ?? ""]]);

      setSelection(dateValue);
      await onSelectCallback?.(dateValue);
      await onDateChange(dateValue);
    },
    [setSearchParams, key, onSelectCallback, onDateChange],
  );

  const onClear = useCallback(async () => {
    await onSelect(undefined);
    await onChange?.(null);
  }, [onSelect, onChange]);

  // Sync selection with URL changes (handles browser navigation)
  useEffect(() => {
    if (currentValue) {
      const urlDate = new Date(currentValue);

      if (selection?.toISOString() !== urlDate.toISOString()) {
        setSelection(urlDate);
      }
    } else if (!currentValue && selection) {
      // Clear selection if no URL value
      setSelection(null);
    }
  }, [currentValue, selection]);

  const { value, hasValue, text } = useMemo(() => {
    const dateValue = currentValue ? new Date(currentValue) : undefined;

    return {
      value: dateValue,
      hasValue: !!currentValue,
      text: dateValue ? format(dateValue, "MM/dd/yyyy") : "",
    };
  }, [currentValue]);

  return {
    selection: selection === undefined ? null : selection,
    onSelect,
    onClear,
    value,
    hasValue,
    text,
  };
}
