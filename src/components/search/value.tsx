import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import type { GenericNode } from "@/components/selectors/Selector";

import { useSearchParamsContext } from "@/components/search/SearchParams";
import { useDebounceValue } from "@/hooks/use-debounce-value";

export const SEARCH_VALUE_NAME = "value";
export const SEARCH_VALUE_GROUP = "value";

export function useSearchValueResult<T extends string = string>(
  group = SEARCH_VALUE_GROUP,
  name = SEARCH_VALUE_NAME,
) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams } = useSearchParamsContext();
  return searchParams[key] as T | undefined;
}

// For backward compatibility with SearchValue component
export interface SearchValueOption<T extends string = string>
  extends GenericNode {
  value: T;
  label: React.ReactNode;
}

// Main hook interface - designed around GenericNode data types
export interface UseSearchValueOptions<V extends GenericNode = GenericNode> {
  name: string;
  group?: string;
  defaultValue?: V["id"];
  defaultSymbol?: string;
  data?: V[];
  onChange?: (value?: string) => void;
  onSelect?: (value?: V | null) => void;
  debounce?: number;
  // Function to extract the search value from the node - defaults to using id
  getValue?: (item: V) => V["id"];
}

// Main hook - works with any GenericNode type, returns id-based values
export function useSearchValue<V extends GenericNode = GenericNode>({
  name = SEARCH_VALUE_NAME,
  group,
  defaultValue,
  defaultSymbol,
  data = [],
  onChange,
  onSelect,
  debounce = 500,
  getValue,
}: UseSearchValueOptions<V>) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  // Function to get value from item - use provided getValue or default to id
  const getItemValue = useCallback(
    (item: V): V["id"] => {
      if (getValue) {
        return getValue(item);
      }
      // Check if it's a SearchValueOption (for backward compatibility)
      if ("value" in item && typeof item.value === "string") {
        return item.value as V["id"];
      }
      // Default to GenericNode.id (the foundation)
      return item.id;
    },
    [getValue],
  );

  const getNextValue = useCallback(
    (value?: V) => {
      if (value && defaultSymbol) {
        const itemValue = getItemValue(value);
        if (itemValue === defaultSymbol) {
          // Create a new value with empty string for special symbol handling
          const transformedValue = { ...value };
          if ("value" in transformedValue) {
            // Type assertion for SearchValueOption compatibility
            (transformedValue as unknown as { value: V["id"] }).value =
              "" as V["id"];
          }
          return transformedValue;
        }
      }
      return value;
    },
    [getItemValue, defaultSymbol],
  );

  // Compute the initial selection once - used only for initialization
  const initialSelection = useMemo(() => {
    if (!defaultValue || data.length === 0) return undefined;
    return (
      data.find((item) => getItemValue(item) === defaultValue) ?? undefined
    );
  }, [defaultValue, data, getItemValue]);

  // Get the current value from search params as a string (not array)
  const currentValue = Array.isArray(searchParams[key])
    ? searchParams[key][0]
    : searchParams[key];

  const [value, setValue] = useDebounceValue(
    currentValue ?? defaultValue ?? "",
    {
      delay: debounce,
    },
  );

  // Initialize selection state with the computed initial selection
  const [selection, setSelection] = useState<V | null | undefined>(
    initialSelection,
  );

  const onValueChange = useCallback(
    (value?: string) => {
      setValue(value ?? "");
      onChange?.(value);
    },
    [onChange, setValue],
  );

  const onSelectionChange = useCallback(
    (value?: V | null) => {
      const nextValue = value ? getNextValue(value) : value;

      const valueToSet = nextValue ? getItemValue(nextValue) : "";
      setSearchParams([[key, valueToSet]]);
      setSelection(nextValue);
      onSelect?.(nextValue);
      onValueChange(valueToSet);
    },
    [onSelect, setSearchParams, key, getItemValue, onValueChange, getNextValue],
  );

  const onClear = useCallback(() => {
    onSelectionChange(undefined);
    onChange?.("");
  }, [onSelectionChange, onChange]);

  // Sync debounced value with URL changes (handles browser navigation)
  useEffect(() => {
    const urlValue = currentValue ?? defaultValue ?? "";
    if (value !== urlValue) {
      setValue(urlValue);
    }
  }, [currentValue, defaultValue, value, setValue]);

  // Sync selection with current value from URL (handles browser navigation)
  useEffect(() => {
    if (currentValue && data.length > 0) {
      const matchingItem = data.find(
        (item) => getItemValue(item) === currentValue,
      );
      if (matchingItem !== selection) {
        setSelection(matchingItem);
      }
    } else if (!currentValue) {
      // Clear selection if no current value (handles back button to empty state)
      if (selection !== null) {
        setSelection(null);
      }
    }
  }, [currentValue, data, getItemValue, selection]);

  return {
    value: value || "",
    selection: selection === undefined ? null : selection,
    onSelectionChange,
    onValueChange,
    onClear,
  };
}
