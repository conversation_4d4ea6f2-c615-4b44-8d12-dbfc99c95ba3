"use client";

import type { ComponentPropsWithoutRef, ReactNode } from "react";

import { ListFilterIcon } from "lucide-react";

import type { FilterGroup } from "./filter";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SEARCH_FILTER_NAME, useSearchFilter } from "./filter";

type FilterContentProps = ComponentPropsWithoutRef<typeof DropdownMenuContent>;
export interface SearchFilterProps<T extends FilterGroup[] = []> {
  loading?: boolean;
  disabled?: boolean;
  name?: string;
  groups: T;
  placeholder?: string;
  className?: string;
  ariaLabel?: string;
  defaultValue?: string;
  onChange?: (value: string, group: FilterGroup) => void;
  align?: FilterContentProps["align"];
  side?: FilterContentProps["side"];
  sideOffset?: FilterContentProps["sideOffset"];
  children?: ReactNode;
}

export function SearchFilter<T extends FilterGroup[] = []>({
  loading,
  disabled,
  name = SEARCH_FILTER_NAME,
  groups,
  className,
  ariaLabel,
  onChange,
  align = "start",
  side = "bottom",
  sideOffset = 0,
  children,
}: SearchFilterProps<T>) {
  const { handleValueChange, getFilterValue } = useSearchFilter({
    name,
    groups,
    onChange,
  });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        asChild
        className={className}
        disabled={loading ?? disabled}
      >
        {children ?? (
          <Button variant="ghost" size="icon" aria-label={ariaLabel}>
            <ListFilterIcon size="20" color="currentColor" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align={align}
        side={side}
        sideOffset={sideOffset}
        className="size-fit max-h-[480px] overflow-y-auto"
      >
        {groups.map((group, index) => (
          <div key={group.id}>
            <DropdownMenuRadioGroup
              value={getFilterValue(group.id)}
              onValueChange={(value) => handleValueChange(value, group)}
            >
              <DropdownMenuLabel>{group.label}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {group.options.map((option) =>
                option.value !== null ? (
                  <DropdownMenuRadioItem
                    key={option.value}
                    value={option.value.toString()}
                  >
                    {option.label}
                  </DropdownMenuRadioItem>
                ) : (
                  <DropdownMenuRadioItem key={option.value} value={undefined}>
                    {option.label}
                  </DropdownMenuRadioItem>
                ),
              )}
            </DropdownMenuRadioGroup>
            {index < groups.length - 1 && <DropdownMenuSeparator />}
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
