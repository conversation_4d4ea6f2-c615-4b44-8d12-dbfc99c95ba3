"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  OnChangeFn,
  PaginationState,
  SortingState,
  TableState,
  Updater,
  VisibilityState,
} from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo, useState } from "react";
import {
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";

export interface UseDataTableProps<TData, TValue> {
  debug?: boolean;
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  rowCount?: number;
  pagination?: PaginationState;
  setPagination?: OnChangeFn<PaginationState>;
  manualPagination?: boolean;
  onStateChange?: (updater: Updater<TableState>) => void;
}

export function useDataTable<TData, TValue>({
  debug,
  columns,
  data,
  manualPagination,
  rowCount,
  pagination,
  setPagination,
  onStateChange,
}: UseDataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable<TData>({
    debugAll: debug,
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    enableRowSelection: true,
    manualPagination,
    // manualFiltering: true,
    rowCount,
    onStateChange,
    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  return useMemo(
    () => ({
      table,
    }),
    [table],
  );
}

export interface DataTableProps<TData, TValue> {
  loading?: boolean;
  slots?: number;
  table: ReturnType<typeof useDataTable<TData, TValue>>["table"];
  tableClassName?: string;
  headerClassName?: string;
  rowClassName?: string;
  cellClassName?: string;
}

export function DataTable<TData, TValue>({
  loading = false,
  slots = 10,
  children,
  table,
  tableClassName,
  headerClassName,
  rowClassName,
  cellClassName,
}: PropsWithChildren<DataTableProps<TData, TValue>>) {
  const { pagination } = table.getState();
  const headerGroups = table.getHeaderGroups();
  const rowModel = table.getRowModel();

  const pageSize = (pagination.pageSize as number | undefined) ?? slots;
  const filler = (pageSize - rowModel.rows.length) % slots;

  return (
    <Table className={tableClassName}>
      <TableHeader>
        {headerGroups.map((headerGroup) => (
          <TableRow key={headerGroup.id} className="h-[60px]">
            {headerGroup.headers.map((header) => {
              const meta = header.column.columnDef.meta as
                | {
                    className?: string;
                  }
                | undefined;
              return (
                <TableHead
                  key={header.id}
                  colSpan={header.colSpan}
                  className={cn(headerClassName, meta?.className)}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </TableHead>
              );
            })}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {loading ? (
          new Array(pageSize).fill(null).map((_, index) => (
            <TableRow key={index} className={cn("h-10", rowClassName)}>
              <TableCell
                colSpan={headerGroups[0]?.headers.length ?? 1}
                className="size-full text-center"
              >
                <div className={cn("size-full h-10", rowClassName)}>
                  <Skeleton className="size-full" />
                </div>
              </TableCell>
            </TableRow>
          ))
        ) : /* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */
        rowModel.rows?.length ? (
          <>
            {rowModel.rows.map((row) => (
              <TableRow
                key={row.id}
                className={cn("min-h-14", rowClassName)}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className={cn("min-h-14", cellClassName)}
                  >
                    {flexRender(cell.column.columnDef.cell, {
                      ...cell.getContext(),
                      className: cellClassName,
                    })}
                  </TableCell>
                ))}
              </TableRow>
            ))}
            {filler > 0 &&
              new Array(filler).fill(null).map((_, index) => (
                <TableRow key={index} className={cn("h-12", rowClassName)}>
                  <TableCell
                    colSpan={headerGroups[0]?.headers.length ?? 1}
                    className="size-full text-center"
                  >
                    <div
                      className={cn(
                        "size-full h-10 rounded-lg border border-dashed",
                        rowClassName,
                      )}
                    />
                  </TableCell>
                </TableRow>
              ))}
          </>
        ) : (
          <TableRow className={cn("h-10", rowClassName)}>
            <TableCell
              colSpan={headerGroups[0]?.headers.length ?? 1}
              className="min-h-60 text-center"
            >
              {children}
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
