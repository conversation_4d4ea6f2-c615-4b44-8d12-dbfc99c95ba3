import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { DatePickerField } from "@/components/forms/fields/DatePicker";
import { SummaryField } from "@/components/forms/fields/Summary";
import {
  STOP_TYPES,
  StopTypeField,
} from "@/components/forms/fields/types/StopType";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      type: {
        label: "Type",
        description: "Type of stop",
        placeholder: "Select stop type",
      },
      label: {
        label: "Label",
        description: "A descriptive label for the stop",
        placeholder: "Enter a label",
      },
      sequence_number: {
        label: "Sequence Number",
        description: "Order in the sequence of stops",
        placeholder: "Enter sequence number",
      },
      arrived_at: {
        label: "Arrival Time",
        description: "When the stop was reached",
        placeholder: "Select arrival time",
      },
      departed_at: {
        label: "Departure Time",
        description: "When the stop was left",
        placeholder: "Select departure time",
      },
      latitude: {
        label: "Latitude",
        description: "Latitude coordinate",
        placeholder: "Enter latitude",
      },
      longitude: {
        label: "Longitude",
        description: "Longitude coordinate",
        placeholder: "Enter longitude",
      },
      tags: {
        label: "Tags",
        description: "Tags for categorizing the stop",
        placeholder: "Enter tags",
      },
    },
    actions: {
      submit: "Save Stop",
      cancel: "Cancel",
    },
  },
};

const stopFormSchema = z.object({
  type: z.enum(
    STOP_TYPES as [
      (typeof STOP_TYPES)[number],
      ...(typeof STOP_TYPES)[number][],
    ],
    {
      required_error: "Please select a stop type",
    },
  ),
  label: z.string().optional(),
  sequence_number: z.coerce.number().min(0, "Sequence number must be positive"),
  arrived_at: z.date().nullable(),
  departed_at: z.date().nullable(),
  latitude: z.coerce.number().min(-90).max(90).optional(),
  longitude: z.coerce.number().min(-180).max(180).optional(),
  tags: z.array(z.string()).optional(),
  shipment_id: z.string(),
  load_id: z.string().optional(),
  location_id: z.string().optional(),
  next_stop_id: z.string().optional(),
  previous_stop_id: z.string().optional(),
});

export type StopFormValues = z.infer<typeof stopFormSchema>;
export type StopFormProps = PropsWithChildren<
  Parameters<typeof useForm<StopFormValues>>[0] & {
    onSubmit?: (values: StopFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function StopForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: StopFormProps) {
  const form = useForm<StopFormValues>({
    ...props,
    resolver: zodResolver(stopFormSchema),
    defaultValues: {
      type: STOP_TYPES[0],
      label: "",
      sequence_number: 0,
      arrived_at: null,
      departed_at: null,
      latitude: undefined,
      longitude: undefined,
      tags: [],
      shipment_id: "",
      load_id: undefined,
      location_id: undefined,
      next_stop_id: undefined,
      previous_stop_id: undefined,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <StopTypeField
          name="type"
          label={i18n.en.fields.type.label}
          description={i18n.en.fields.type.description}
          placeholder={i18n.en.fields.type.placeholder}
        />

        <SummaryField
          name="label"
          label={i18n.en.fields.label.label}
          description={i18n.en.fields.label.description}
          placeholder={i18n.en.fields.label.placeholder}
        />

        <SummaryField
          name="sequence_number"
          type="number"
          label={i18n.en.fields.sequence_number.label}
          description={i18n.en.fields.sequence_number.description}
          placeholder={i18n.en.fields.sequence_number.placeholder}
        />

        <DatePickerField
          name="arrived_at"
          label={i18n.en.fields.arrived_at.label}
          description={i18n.en.fields.arrived_at.description}
          placeholder={i18n.en.fields.arrived_at.placeholder}
        />

        <DatePickerField
          name="departed_at"
          label={i18n.en.fields.departed_at.label}
          description={i18n.en.fields.departed_at.description}
          placeholder={i18n.en.fields.departed_at.placeholder}
        />

        <SummaryField
          name="latitude"
          type="number"
          step="any"
          label={i18n.en.fields.latitude.label}
          description={i18n.en.fields.latitude.description}
          placeholder={i18n.en.fields.latitude.placeholder}
        />

        <SummaryField
          name="longitude"
          type="number"
          step="any"
          label={i18n.en.fields.longitude.label}
          description={i18n.en.fields.longitude.description}
          placeholder={i18n.en.fields.longitude.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <StopFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function StopFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<StopFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
