import type { PropsWithChildren } from "react";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";
import type { UserContextType } from "@/contexts/User";

import { EmailField } from "@/components/forms/fields/Email";
import { SummaryField } from "@/components/forms/fields/Summary";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      username: {
        label: "Username",
        description: "Your display name",
        placeholder: "Enter username",
      },
      email: {
        label: "Email",
        description: "Your email address",
        placeholder: "Enter email address",
      },
      avatar: {
        label: "Avatar URL",
        description: "URL to your profile picture",
        placeholder: "Enter avatar URL",
      },
    },
    actions: {
      submit: "Save Changes",
      cancel: "Cancel",
    },
  },
};

const profileFormSchema = z.object({
  username: z.string().min(2, "Username must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  avatar: z.string().url("Please enter a valid URL").optional(),
});

export type ProfileFormValues = z.infer<typeof profileFormSchema>;
export type ProfileFormProps = PropsWithChildren<
  Parameters<typeof useForm<ProfileFormValues>>[0] & {
    user: UserContextType["user"];
    onSubmit: (values: ProfileFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function ProfileForm({
  children,
  user,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: ProfileFormProps) {
  const form = useForm<ProfileFormValues>({
    ...props,
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      username: user?.email?.split("@")[0] || "",
      email: user?.email || "",
      avatar: user?.user_metadata?.avatar || "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <SummaryField
          name="username"
          label={i18n.en.fields.username.label}
          description={i18n.en.fields.username.description}
          placeholder={i18n.en.fields.username.placeholder}
        />

        <EmailField
          name="email"
          label={i18n.en.fields.email.label}
          description={i18n.en.fields.email.description}
          placeholder={i18n.en.fields.email.placeholder}
        />

        <SummaryField
          name="avatar"
          type="url"
          label={i18n.en.fields.avatar.label}
          description={i18n.en.fields.avatar.description}
          placeholder={i18n.en.fields.avatar.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <ProfileFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ProfileFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ProfileFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
