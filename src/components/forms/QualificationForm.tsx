import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { DatePickerField } from "@/components/forms/fields/DatePicker";
import { SummaryField } from "@/components/forms/fields/Summary";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { cn } from "@/lib/utils";
import {
  QUALIFICATION_TYPES,
  QualificationTypeField,
} from "./fields/types/QualificationType";

const i18n = {
  en: {
    fields: {
      type: {
        label: "Type",
        description: "Type of qualification",
        placeholder: "Select qualification type",
      },
      issuing_state: {
        label: "Issuing State",
        description: "State that issued the qualification",
        placeholder: "Enter state (e.g. CA)",
      },
      issued_at: {
        label: "Issue Date",
        description: "When the qualification was issued",
        placeholder: "Select issue date",
      },
      expires_at: {
        label: "Expiration Date",
        description: "When the qualification expires",
        placeholder: "Select expiration date",
      },
    },
    actions: {
      submit: "Add Qualification",
      update: "Update Qualification",
      cancel: "Cancel",
    },
  },
};

const qualificationFormSchema = z.object({
  type: z.enum(
    QUALIFICATION_TYPES as [
      (typeof QUALIFICATION_TYPES)[number],
      ...(typeof QUALIFICATION_TYPES)[number][],
    ],
    {
      required_error: "Please select a qualification type",
    },
  ),
  issuing_state: z.string().min(2, "State is required"),
  issued_at: z.date(),
  expires_at: z.date(),
});

export type QualificationFormValues = z.infer<typeof qualificationFormSchema>;
export type QualificationFormProps = PropsWithChildren<
  Parameters<typeof useForm<QualificationFormValues>>[0] & {
    onSubmit?: (values: QualificationFormValues) => void | Promise<void>;
    onCancel?: () => void;
    isEditing?: boolean;
  }
>;

export default function QualificationForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  isEditing = false,
  ...props
}: QualificationFormProps) {
  const form = useForm<QualificationFormValues>({
    ...props,
    resolver: zodResolver(qualificationFormSchema),
    defaultValues: {
      type: QUALIFICATION_TYPES[0],
      issuing_state: "",
      issued_at: new Date(),
      expires_at: new Date(),
      ...props.defaultValues,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <QualificationTypeField
          name="type"
          label={i18n.en.fields.type.label}
          description={i18n.en.fields.type.description}
          placeholder={i18n.en.fields.type.placeholder}
        />

        <SummaryField
          name="issuing_state"
          label={i18n.en.fields.issuing_state.label}
          description={i18n.en.fields.issuing_state.description}
          placeholder={i18n.en.fields.issuing_state.placeholder}
        />

        <DatePickerField
          name="issued_at"
          label={i18n.en.fields.issued_at.label}
          description={i18n.en.fields.issued_at.description}
          placeholder={i18n.en.fields.issued_at.placeholder}
          disabled={undefined} // Allow all dates
        />

        <DatePickerField
          name="expires_at"
          label={i18n.en.fields.expires_at.label}
          description={i18n.en.fields.expires_at.description}
          placeholder={i18n.en.fields.expires_at.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <QualificationFormSubmitButton isEditing={isEditing} />
          </div>
        )}
      </form>
    </Form>
  );
}

export function QualificationFormSubmitButton({
  children,
  isEditing = false,
  ...props
}: ButtonProps & { isEditing?: boolean }) {
  const form = useFormContext<QualificationFormValues>();
  const buttonText =
    children || (isEditing ? i18n.en.actions.update : i18n.en.actions.submit);

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {buttonText}
    </Button>
  );
}
