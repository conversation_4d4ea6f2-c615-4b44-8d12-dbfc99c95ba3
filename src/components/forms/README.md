# Form Conventions

This directory contains reusable form components following a consistent pattern for handling form state, validation, and submission.

> **Quick Start**
>
> - Form fields (inputs, selects, etc.) → `fields/`
> - Domain forms (loads, shipments, etc.) → `[domain]/`
> - Create new form? Copy pattern from existing form in same domain
> - Need a new field type? Check `fields/` first, then create in appropriate location
> - Always include: form component, submit button component, i18n object, proper types

> [!NOTE] Forms in this directory follow a consistent pattern for maximum reusability and type safety. Common form fields are available in `fields/` (Currency, DatePicker, Description, Summary, Weight) and domain-specific types in `fields/types/` (LoadType, ShipmentMode, LocationType). When creating a new form, start by checking existing components - most common field types are already available. Domain forms (e.g., LoadForm, ShipmentForm) showcase how to compose these fields and handle complex validation logic. **Important:** All forms should be built in this directory and imported where needed, regardless of where they're used in the application. This ensures consistency and makes forms easier to find and maintain.

## Core Principles

1. Forms are built using:
   - `react-hook-form` for form state management
   - `zod` for schema validation
   - `@hookform/resolvers/zod` for connecting validation to form state
   - Shadcn UI components for the UI elements

2. Each form is a standalone component that:
   - Manages its own form state
   - Handles its own validation
   - Provides TypeScript types for form values
   - Can be used independently or composed into larger forms

## File Structure

```
forms/
├── fields/           # Reusable form field components
├── [domain]/         # Domain-specific form components
│   ├── types/       # Domain-specific type components
│   └── ...
└── README.md        # This file
```

## Form Component Pattern

Each form component follows this pattern:

```typescript
export function MyForm({
  children,          // For custom button layouts
  onSubmit,          // Form submission handler
  ...props           // Additional form config
}: MyFormProps) {
  const form = useForm<MyFormValues>({
    resolver: zodResolver(myFormSchema),
    defaultValues: { ... },
    ...props
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Form fields */}
        {children ?? (
          <div className="flex justify-end">
            <MyFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}
```

## Submit Button Pattern

Each form has an accompanying submit button component:

```typescript
export function MyFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<MyFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled ||
        form.formState.isSubmitting ||
        !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
```

## Internationalization

Forms use an `i18n` object for all text content:

```typescript
const i18n = {
  en: {
    fields: {
      fieldName: {
        label: "Field Label",
        placeholder: "Placeholder text",
        description: "Help text",
      },
    },
    actions: {
      submit: "Submit",
      // Other action labels
    },
  },
};
```

## Props Pattern

Form props follow this pattern:

```typescript
export type MyFormValues = z.infer<typeof myFormSchema>;

export type MyFormProps = PropsWithChildren<
  Parameters<typeof useForm<MyFormValues>>[0] & {
    onSubmit: (values: MyFormValues) => void;
  }
>;
```

## Usage Examples

### Basic Usage

```tsx
<MyForm onSubmit={handleSubmit} />
```

### Custom Button Layout

```tsx
<MyForm onSubmit={handleSubmit}>
  <div className="flex justify-between">
    <Button variant="outline" onClick={onBack}>
      Back
    </Button>
    <MyFormSubmitButton />
  </div>
</MyForm>
```

### With Default Values

```tsx
<MyForm
  onSubmit={handleSubmit}
  defaultValues={{
    field1: "value1",
    field2: "value2",
  }}
/>
```

## Field Components

Field components should:

1. Use Shadcn UI components
2. Include proper form control wrappers
3. Support validation messages
4. Be properly typed
5. Follow accessibility best practices

Example:

```tsx
<FormField
  control={form.control}
  name="fieldName"
  render={({ field }) => (
    <FormItem>
      <FormLabel>{i18n.en.fields.fieldName.label}</FormLabel>
      <FormControl>
        <Input placeholder={i18n.en.fields.fieldName.placeholder} {...field} />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

## Best Practices

1. **Type Safety**
   - Always define and export form value types
   - Use zod for runtime validation
   - Leverage TypeScript for compile-time checks

2. **Validation**
   - Define validation schema using zod
   - Include helpful error messages
   - Handle all edge cases

3. **State Management**
   - Use form context for shared state
   - Keep form state isolated
   - Handle loading and error states

4. **Accessibility**
   - Include proper ARIA labels
   - Support keyboard navigation
   - Show validation feedback
   - Use semantic HTML

5. **Styling**
   - Use Tailwind utility classes
   - Follow spacing conventions
   - Maintain consistent sizing
   - Support responsive layouts

6. **Button Handling**
   - Allow custom button layouts via children
   - Provide default submit button
   - Handle loading states
   - Show proper feedback

7. **Error Handling**
   - Display field-level errors
   - Support form-level errors
   - Handle submission errors
   - Provide clear feedback
