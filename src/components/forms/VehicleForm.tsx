import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { SummaryField } from "@/components/forms/fields/Summary";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      make: {
        label: "Make",
        description: "Vehicle manufacturer",
        placeholder: "Enter vehicle make",
      },
      model: {
        label: "Model",
        description: "Vehicle model",
        placeholder: "Enter vehicle model",
      },
      year: {
        label: "Year",
        description: "Vehicle manufacturing year",
        placeholder: "Enter vehicle year",
      },
      license_plate: {
        label: "License Plate",
        description: "Vehicle license plate number",
        placeholder: "Enter license plate",
      },
      vin: {
        label: "VIN",
        description: "Vehicle Identification Number",
        placeholder: "Enter VIN",
      },
      mc_number: {
        label: "MC Number",
        description: "Motor Carrier Number",
        placeholder: "Enter MC number",
      },
      us_dot: {
        label: "US DOT",
        description: "US Department of Transportation Number",
        placeholder: "Enter US DOT number",
      },
    },
    actions: {
      submit: "Save Vehicle",
      cancel: "Cancel",
    },
  },
};

const vehicleFormSchema = z.object({
  make: z.string().min(1, "Make is required"),
  model: z.string().min(1, "Model is required"),
  year: z.coerce
    .number()
    .min(1900, "Year must be after 1900")
    .max(new Date().getFullYear() + 1, "Year cannot be in the future"),
  license_plate: z.string().min(1, "License plate is required"),
  vin: z.string().min(17, "VIN must be 17 characters").max(17),
  mc_number: z.string().min(1, "MC number is required"),
  us_dot: z.string().min(1, "US DOT number is required"),
  driver_id: z.string().optional(),
});

export type VehicleFormValues = z.infer<typeof vehicleFormSchema>;
export type VehicleFormProps = PropsWithChildren<
  Parameters<typeof useForm<VehicleFormValues>>[0] & {
    onSubmit?: (values: VehicleFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function VehicleForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: VehicleFormProps) {
  const form = useForm<VehicleFormValues>({
    ...props,
    resolver: zodResolver(vehicleFormSchema),
    defaultValues: {
      make: "",
      model: "",
      year: new Date().getFullYear(),
      license_plate: "",
      vin: "",
      mc_number: "",
      us_dot: "",
      driver_id: undefined,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <SummaryField
          name="make"
          label={i18n.en.fields.make.label}
          description={i18n.en.fields.make.description}
          placeholder={i18n.en.fields.make.placeholder}
        />

        <SummaryField
          name="model"
          label={i18n.en.fields.model.label}
          description={i18n.en.fields.model.description}
          placeholder={i18n.en.fields.model.placeholder}
        />

        <SummaryField
          name="year"
          type="number"
          label={i18n.en.fields.year.label}
          description={i18n.en.fields.year.description}
          placeholder={i18n.en.fields.year.placeholder}
        />

        <SummaryField
          name="license_plate"
          label={i18n.en.fields.license_plate.label}
          description={i18n.en.fields.license_plate.description}
          placeholder={i18n.en.fields.license_plate.placeholder}
        />

        <SummaryField
          name="vin"
          label={i18n.en.fields.vin.label}
          description={i18n.en.fields.vin.description}
          placeholder={i18n.en.fields.vin.placeholder}
        />

        {/* TODO: create a field for mc_number with proper mask */}
        <SummaryField
          name="mc_number"
          label={i18n.en.fields.mc_number.label}
          description={i18n.en.fields.mc_number.description}
          placeholder={i18n.en.fields.mc_number.placeholder}
        />

        <SummaryField
          name="us_dot"
          label={i18n.en.fields.us_dot.label}
          description={i18n.en.fields.us_dot.description}
          placeholder={i18n.en.fields.us_dot.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <VehicleFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function VehicleFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<VehicleFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
