import { useState } from "react";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";

import {
  DriverAddressForm,
  DriverAddressValues,
} from "@/components/forms/onboarding/steps/DriverAddressForm";
import {
  DriverAvatarForm,
  DriverAvatarValues,
} from "@/components/forms/onboarding/steps/DriverAvatarForm";
import {
  DriverBasicInfoForm,
  DriverBasicInfoValues,
} from "@/components/forms/onboarding/steps/DriverBasicInfoForm";
import { DriverConfirmation } from "@/components/forms/onboarding/steps/DriverConfirmation";
import { Button } from "@/components/ui/button";
import { MapboxSuggestion } from "@/lib/mapbox";
import { cn } from "@/lib/utils";

// Animation variants for the steps
const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
};

// Combined form values from all steps
export interface DriverOnboardingValues {
  // Step 1: Basic Info (required)
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;

  // Step 2: Avatar (optional)
  avatar?: File | null;

  // Step 3: Address (optional)
  address?: string;
  addressDetails?: MapboxSuggestion;
}

export interface DriverOnboardingMultiStepProps {
  defaultValues?: Partial<DriverOnboardingValues>;
  onSubmit: (values: DriverOnboardingValues) => Promise<void>;
}

export function DriverOnboardingMultiStep({
  defaultValues = {},
  onSubmit,
}: DriverOnboardingMultiStepProps) {
  const [step, setStep] = useState(0);
  const [direction, setDirection] = useState(0);
  const [formValues, setFormValues] = useState<DriverOnboardingValues>({
    firstName: defaultValues.firstName || "",
    lastName: defaultValues.lastName || "",
    email: defaultValues.email || "",
    phoneNumber: defaultValues.phoneNumber || "",
    avatar: defaultValues.avatar || null,
    address: defaultValues.address || "",
    addressDetails: defaultValues.addressDetails || null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Navigate to the next step
  const nextStep = () => {
    setDirection(1);
    setStep((prev) => Math.min(prev + 1, 3));
  };

  // Navigate to the previous step
  const prevStep = () => {
    setDirection(-1);
    setStep((prev) => Math.max(prev - 1, 0));
  };

  // Handle form submission for each step
  const handleStepSubmit = (stepData: Partial<DriverOnboardingValues>) => {
    setFormValues((prev) => ({ ...prev, ...stepData }));
    nextStep();
  };

  // Skip optional steps
  const handleSkip = () => {
    nextStep();
  };

  // Final submission
  const handleFinalSubmit = async () => {
    try {
      setIsSubmitting(true);
      await onSubmit(formValues);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Step indicators
  const StepIndicator = ({ currentStep }: { currentStep: number }) => (
    <div className="mb-8 flex justify-center">
      {[0, 1, 2, 3].map((idx) => (
        <div
          key={idx}
          className={cn(
            "mx-1 h-3 w-3 rounded-full",
            idx === currentStep
              ? "bg-primary"
              : idx < currentStep
                ? "bg-primary/60"
                : "bg-gray-300",
          )}
        />
      ))}
    </div>
  );

  return (
    <div className="w-full">
      <StepIndicator currentStep={step} />

      <div className="relative overflow-hidden">
        {step === 0 && (
          <motion.div
            key="step1"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <DriverBasicInfoForm
              defaultValues={{
                firstName: formValues.firstName,
                lastName: formValues.lastName,
                email: formValues.email,
                phoneNumber: formValues.phoneNumber,
              }}
              onSubmit={(values: DriverBasicInfoValues) =>
                handleStepSubmit(values)
              }
            />
          </motion.div>
        )}

        {step === 1 && (
          <motion.div
            key="step2"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <DriverAvatarForm
              defaultValues={{
                avatar: formValues.avatar,
              }}
              onSubmit={(values: DriverAvatarValues) =>
                handleStepSubmit(values)
              }
            >
              <div className="mt-6 flex justify-between">
                <Button type="button" variant="outline" onClick={prevStep}>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <div className="space-x-2">
                  <Button type="button" variant="ghost" onClick={handleSkip}>
                    Skip
                  </Button>
                  <Button type="submit">
                    Next
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </DriverAvatarForm>
          </motion.div>
        )}

        {step === 2 && (
          <motion.div
            key="step3"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <DriverAddressForm
              defaultValues={{
                address: formValues.address,
                addressDetails: formValues.addressDetails,
              }}
              onSubmit={(values: DriverAddressValues) =>
                handleStepSubmit(values)
              }
            >
              <div className="mt-6 flex justify-between">
                <Button type="button" variant="outline" onClick={prevStep}>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <div className="space-x-2">
                  <Button type="button" variant="ghost" onClick={handleSkip}>
                    Skip
                  </Button>
                  <Button type="submit">
                    Next
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </DriverAddressForm>
          </motion.div>
        )}

        {step === 3 && (
          <motion.div
            key="step4"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <DriverConfirmation
              formValues={formValues}
              isSubmitting={isSubmitting}
              onSubmit={handleFinalSubmit}
              onBack={prevStep}
            />
          </motion.div>
        )}
      </div>
    </div>
  );
}
