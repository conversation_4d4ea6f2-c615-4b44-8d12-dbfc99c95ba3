# Onboarding Forms

This directory contains form components specifically designed for user onboarding flows in the application.

## Available Forms

### OrganizationOnboardingMultiStep

A multi-step form for organization onboarding with the following steps:

1. Organization Details (required) - Name, industry, type, size, address
2. Team Members (optional) - Invite team members with different roles
3. Confirmation - Review all information before submission

```tsx
import { OrganizationOnboardingMultiStep } from "@/components/forms/onboarding/organization/OrganizationOnboardingMultiStep";

// Basic usage
<OrganizationOnboardingMultiStep
  onSubmit={handleSubmit}
  defaultValues={{
    name: "Acme Corp",
    industry: "logistics",
    size: "1-10",
    type: "private",
    address: "123 Main St",
    members: [{ email: "<EMAIL>", role: "member" }],
  }}
/>;
```

Features:

- Framer Motion animations between steps
- Skip functionality for optional steps
- Step indicators to show progress
- Confirmation screen with data preview
- Proper validation for each step

### DriverOnboardingMultiStep

A multi-step form for driver onboarding with the following steps:

1. Basic Information (required) - First name, last name, email, phone number
2. Profile Picture (optional) - Upload a profile picture
3. Address (optional) - Enter home or work address
4. Confirmation - Review all information before submission

```tsx
import { DriverOnboardingMultiStep } from "@/components/forms/onboarding/DriverOnboardingMultiStep";

// Basic usage
<DriverOnboardingMultiStep
  onSubmit={handleSubmit}
  defaultValues={{
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneNumber: "1234567890",
    // Optional fields
    address: "123 Main St",
    avatar: null,
  }}
/>;
```

Features:

- Framer Motion animations between steps
- Skip functionality for optional steps
- Step indicators to show progress
- Confirmation screen with data preview
- Proper validation for each step

### DriverOnboardingForm (Legacy)

A form component for driver onboarding that collects:

- First name
- Last name
- Email
- Phone number

```tsx
import { DriverOnboardingForm } from "@/components/forms/onboarding/DriverOnboardingForm";

// Basic usage
<DriverOnboardingForm onSubmit={handleSubmit} />

// With default values
<DriverOnboardingForm
  onSubmit={handleSubmit}
  defaultValues={{
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneNumber: "1234567890"
  }}
/>

// With custom submit button
<DriverOnboardingForm onSubmit={handleSubmit}>
  <div className="flex justify-between">
    <Button variant="outline" onClick={onBack}>Back</Button>
    <DriverOnboardingFormSubmitButton />
  </div>
</DriverOnboardingForm>
```

## Form Structure

Each onboarding form follows the standard form pattern defined in the parent forms directory:

1. A schema for validation using Zod
2. An i18n object for text content
3. A main form component that handles form state and rendering
4. A submit button component for easy reuse

## Multi-Step Form Structure

The multi-step forms follow a specific pattern:

1. A main container component that manages state and step transitions
2. Individual step components for each part of the form
3. A confirmation component to review all data
4. Framer Motion animations for transitions between steps

## Adding New Onboarding Forms

When adding new onboarding forms:

1. Follow the naming convention: `[Entity]OnboardingForm.tsx` or `[Entity]OnboardingMultiStep.tsx`
2. Implement both the form component and submit button component
3. Export types for form values and props
4. Update this README with documentation for the new form
