import { ChevronLeft } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { DriverOnboardingValues } from "../DriverOnboardingMultiStep";

// i18n object for text content
const i18n = {
  en: {
    title: "Review Your Information",
    description: "Please review your information before submitting",
    sections: {
      basicInfo: "Basic Information",
      avatar: "Profile Picture",
      address: "Address",
    },
    fields: {
      firstName: "First Name",
      lastName: "Last Name",
      email: "Email",
      phoneNumber: "Phone Number",
      address: "Address",
    },
    actions: {
      back: "Back",
      submit: "Create Driver Profile",
      submitting: "Creating Profile...",
    },
    noAvatar: "No profile picture uploaded",
    noAddress: "No address provided",
  },
};

interface DriverConfirmationProps {
  formValues: DriverOnboardingValues;
  isSubmitting: boolean;
  onSubmit: () => Promise<void>;
  onBack: () => void;
}

export function DriverConfirmation({
  formValues,
  isSubmitting,
  onSubmit,
  onBack,
}: DriverConfirmationProps) {
  const { firstName, lastName, email, phoneNumber, avatar, address } =
    formValues;
  const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <div className="mb-6 flex flex-col items-center justify-center">
        <Avatar className="h-24 w-24">
          {avatar ? (
            <AvatarImage
              src={URL.createObjectURL(avatar)}
              alt={`${firstName} ${lastName}`}
            />
          ) : null}
          <AvatarFallback className="text-xl">{initials}</AvatarFallback>
        </Avatar>
        <h3 className="mt-2 text-xl font-semibold">
          {firstName} {lastName}
        </h3>
        <p className="text-muted-foreground">{email}</p>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div>
              <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                {i18n.en.sections.basicInfo}
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-muted-foreground text-sm">
                    {i18n.en.fields.firstName}
                  </p>
                  <p>{firstName}</p>
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">
                    {i18n.en.fields.lastName}
                  </p>
                  <p>{lastName}</p>
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">
                    {i18n.en.fields.email}
                  </p>
                  <p>{email}</p>
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">
                    {i18n.en.fields.phoneNumber}
                  </p>
                  <p>{phoneNumber}</p>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                {i18n.en.sections.avatar}
              </h4>
              <p>{avatar ? "Profile picture uploaded" : i18n.en.noAvatar}</p>
            </div>

            <Separator />

            <div>
              <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                {i18n.en.sections.address}
              </h4>
              <p>{address || i18n.en.noAddress}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mt-6 flex justify-between">
        <Button type="button" variant="outline" onClick={onBack}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          {i18n.en.actions.back}
        </Button>
        <Button onClick={onSubmit} disabled={isSubmitting}>
          {isSubmitting ? i18n.en.actions.submitting : i18n.en.actions.submit}
        </Button>
      </div>
    </div>
  );
}
