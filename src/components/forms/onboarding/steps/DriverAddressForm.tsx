import { PropsWithChildren } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { AddressAutocompleteField } from "@/components/forms/fields/AddressAutocomplete";
import { Form } from "@/components/ui/form";

// Define the form schema with validation
const driverAddressSchema = z.object({
  address: z.string().optional(),
  addressDetails: z.any().optional(),
});

// i18n object for text content
const i18n = {
  en: {
    title: "Your Address",
    description: "Where are you located? (optional)",
    fields: {
      address: {
        label: "Address",
        description: "Enter your home or work address",
      },
    },
  },
};

// Export the type for use in other components
export type DriverAddressValues = z.infer<typeof driverAddressSchema>;

export type DriverAddressFormProps = PropsWithChildren<
  Parameters<typeof useForm<DriverAddressValues>>[0] & {
    onSubmit: (values: DriverAddressValues) => void;
  }
>;

export function DriverAddressForm({
  children,
  onSubmit,
  ...props
}: DriverAddressFormProps) {
  const form = useForm<DriverAddressValues>({
    resolver: zodResolver(driverAddressSchema),
    defaultValues: {
      address: "",
      addressDetails: null,
    },
    ...props,
  });

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <AddressAutocompleteField
            name="address"
            label={i18n.en.fields.address.label}
            description={i18n.en.fields.address.description}
          />

          {children}
        </form>
      </Form>
    </div>
  );
}
