import { PropsWithChildren } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { ImageUploadField } from "@/components/forms/fields/ImageUpload";
import { Form } from "@/components/ui/form";

// Define the form schema with validation
const driverAvatarSchema = z.object({
  avatar: z.instanceof(File).nullable().optional(),
});

// i18n object for text content
const i18n = {
  en: {
    title: "Profile Picture",
    description: "Upload a profile picture (optional)",
    fields: {
      avatar: {
        label: "Profile Picture",
        description: "Upload a clear photo of yourself",
      },
    },
  },
};

// Export the type for use in other components
export type DriverAvatarValues = z.infer<typeof driverAvatarSchema>;

export type DriverAvatarFormProps = PropsWithChildren<
  Parameters<typeof useForm<DriverAvatarValues>>[0] & {
    onSubmit: (values: DriverAvatarValues) => void;
  }
>;

export function DriverAvatarForm({
  children,
  onSubmit,
  ...props
}: DriverAvatarFormProps) {
  const form = useForm<DriverAvatarValues>({
    resolver: zodResolver(driverAvatarSchema),
    defaultValues: {
      avatar: null,
    },
    ...props,
  });

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="mx-auto max-w-sm">
            <ImageUploadField
              name="avatar"
              label={i18n.en.fields.avatar.label}
              description={i18n.en.fields.avatar.description}
              aspectRatio={1}
              minWidth={200}
              minHeight={200}
            />
          </div>

          {children}
        </form>
      </Form>
    </div>
  );
}
