import { PropsWithChildren } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChevronRight } from "lucide-react";
import { useForm, useFormContext } from "react-hook-form";
import * as z from "zod";

import { EmailField } from "@/components/forms/fields/Email";
import { PhoneNumberField } from "@/components/forms/fields/PhoneNumber";
import { Button, ButtonProps } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

// Define the form schema with validation
const driverBasicInfoSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z
    .string()
    .min(10, "Phone number must be at least 10 characters"),
});

// i18n object for text content
const i18n = {
  en: {
    title: "Basic Information",
    description: "Let's start with your basic information",
    fields: {
      firstName: {
        label: "First Name",
        placeholder: "John",
      },
      lastName: {
        label: "Last Name",
        placeholder: "Doe",
      },
      email: {
        label: "Email",
        placeholder: "<EMAIL>",
      },
      phoneNumber: {
        label: "Phone Number",
        placeholder: "+****************",
      },
    },
    actions: {
      submit: "Continue",
    },
  },
};

// Export the type for use in other components
export type DriverBasicInfoValues = z.infer<typeof driverBasicInfoSchema>;

export type DriverBasicInfoFormProps = PropsWithChildren<
  Parameters<typeof useForm<DriverBasicInfoValues>>[0] & {
    onSubmit: (values: DriverBasicInfoValues) => void;
  }
>;

export function DriverBasicInfoForm({
  children,
  onSubmit,
  ...props
}: DriverBasicInfoFormProps) {
  const form = useForm<DriverBasicInfoValues>({
    resolver: zodResolver(driverBasicInfoSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
    },
    ...props,
  });

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{i18n.en.fields.firstName.label}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={i18n.en.fields.firstName.placeholder}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{i18n.en.fields.lastName.label}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={i18n.en.fields.lastName.placeholder}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <EmailField
            name="email"
            label={i18n.en.fields.email.label}
            placeholder={i18n.en.fields.email.placeholder}
          />

          <PhoneNumberField
            name="phoneNumber"
            label={i18n.en.fields.phoneNumber.label}
            placeholder={i18n.en.fields.phoneNumber.placeholder}
          />

          {children ?? (
            <div className="flex justify-end">
              <DriverBasicInfoFormSubmitButton />
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}

export function DriverBasicInfoFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<DriverBasicInfoValues>();

  return (
    <Button
      {...props}
      className={cn("w-full", props.className)}
      disabled={props.disabled || form.formState.isSubmitting}
      type="submit"
    >
      {children}
      <ChevronRight className="ml-2 h-4 w-4" />
    </Button>
  );
}
