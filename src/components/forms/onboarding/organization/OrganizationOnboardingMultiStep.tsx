import { useState } from "react";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  OrganizationAvatarStep,
  OrganizationAvatarValues,
} from "./steps/OrganizationAvatarStep";
import { OrganizationConfirmationStep } from "./steps/OrganizationConfirmationStep";
import {
  OrganizationDetailsStep,
  OrganizationDetailsValues,
} from "./steps/OrganizationDetailsStep";
import { TeamMembersStep, TeamMembersValues } from "./steps/TeamMembersStep";

// Animation variants for the steps
const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
};

// Combined form values from all steps
export interface OrganizationOnboardingValues
  extends OrganizationDetailsValues,
    TeamMembersValues {
  // Step 2: Organization Avatar (optional)
  avatar?: File;
}

export interface OrganizationOnboardingMultiStepProps {
  defaultValues?: Partial<OrganizationOnboardingValues>;
  onSubmit: (values: OrganizationOnboardingValues) => Promise<void>;
}

export function OrganizationOnboardingMultiStep({
  defaultValues = {},
  onSubmit,
}: OrganizationOnboardingMultiStepProps) {
  const [step, setStep] = useState(0);
  const [direction, setDirection] = useState(0);
  const [formValues, setFormValues] = useState<OrganizationOnboardingValues>({
    name: defaultValues.name || "",
    industry: defaultValues.industry || undefined,
    size: defaultValues.size || undefined,
    type: defaultValues.type || undefined,
    address: defaultValues.address || undefined,
    avatar: defaultValues.avatar,
    members: defaultValues.members || [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Navigate to the next step
  const nextStep = () => {
    setDirection(1);
    setStep((prev) => Math.min(prev + 1, 3));
  };

  // Navigate to the previous step
  const prevStep = () => {
    setDirection(-1);
    setStep((prev) => Math.max(prev - 1, 0));
  };

  // Handle form submission for each step
  const handleStepSubmit = (
    stepData: Partial<OrganizationOnboardingValues>,
  ) => {
    setFormValues((prev) => ({ ...prev, ...stepData }));
    nextStep();
  };

  // Skip optional steps
  const handleSkipAvatar = () => {
    setFormValues((prev) => ({ ...prev, avatar: undefined }));
    nextStep();
  };

  // Skip optional steps
  const handleSkipMembers = () => {
    setFormValues((prev) => ({ ...prev, members: [] }));
    nextStep();
  };

  // Final submission
  const handleFinalSubmit = async () => {
    try {
      setIsSubmitting(true);
      await onSubmit(formValues);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Step indicators
  const StepIndicator = ({ currentStep }: { currentStep: number }) => (
    <div className="mb-8 flex justify-center">
      {[0, 1, 2, 3].map((idx) => (
        <div
          key={idx}
          className={cn(
            "mx-1 h-3 w-3 rounded-full",
            idx === currentStep
              ? "bg-primary"
              : idx < currentStep
                ? "bg-primary/60"
                : "bg-gray-300",
          )}
        />
      ))}
    </div>
  );

  return (
    <div className="w-full">
      <StepIndicator currentStep={step} />

      <div className="relative overflow-hidden">
        {step === 0 && (
          <motion.div
            key="step1"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <OrganizationDetailsStep
              defaultValues={{
                name: formValues.name,
                industry: formValues.industry,
                size: formValues.size,
                type: formValues.type,
                address: formValues.address,
              }}
              onSubmit={(values: OrganizationDetailsValues) =>
                handleStepSubmit(values)
              }
            />
          </motion.div>
        )}

        {step === 1 && (
          <motion.div
            key="step2"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <OrganizationAvatarStep
              defaultValues={{
                avatar: formValues.avatar,
              }}
              onSubmit={(values: OrganizationAvatarValues) =>
                handleStepSubmit(values)
              }
              onBack={prevStep}
              onSkip={handleSkipAvatar}
            />
          </motion.div>
        )}

        {step === 2 && (
          <motion.div
            key="step3"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <TeamMembersStep
              defaultValues={{
                members: formValues.members,
              }}
              onSubmit={(values: TeamMembersValues) => handleStepSubmit(values)}
            >
              <div className="mt-6 flex justify-between">
                <Button type="button" variant="outline" onClick={prevStep}>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <div className="space-x-2">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={handleSkipMembers}
                  >
                    Skip
                  </Button>
                  <Button type="submit">
                    Next
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </TeamMembersStep>
          </motion.div>
        )}

        {step === 3 && (
          <motion.div
            key="step4"
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="w-full"
          >
            <OrganizationConfirmationStep
              formValues={formValues}
              isSubmitting={isSubmitting}
              onSubmit={handleFinalSubmit}
              onBack={prevStep}
            />
          </motion.div>
        )}
      </div>
    </div>
  );
}
