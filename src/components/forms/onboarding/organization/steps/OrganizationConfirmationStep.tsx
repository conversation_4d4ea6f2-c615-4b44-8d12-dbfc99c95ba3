import { ChevronLeft } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { OrganizationOnboardingValues } from "../OrganizationOnboardingMultiStep";

// i18n object for text content
const i18n = {
  en: {
    title: "Confirm Details",
    description: "Review and confirm your organization setup",
    sections: {
      organization: {
        title: "Organization Details",
        fields: {
          name: "Name",
          industry: "Industry",
          type: "Type",
          size: "Size",
          address: "Address",
        },
      },
      members: {
        title: "Team Members",
        fields: {
          email: "Email",
          role: "Role",
        },
      },
    },
    actions: {
      back: "Back",
      confirm: "Create Organization",
      submitting: "Creating Organization...",
    },
    noMembers: "No team members added",
    noAvatar: "No logo uploaded",
  },
};

interface OrganizationConfirmationStepProps {
  formValues: OrganizationOnboardingValues;
  isSubmitting: boolean;
  onSubmit: () => Promise<void>;
  onBack: () => void;
}

export function OrganizationConfirmationStep({
  formValues,
  isSubmitting,
  onSubmit,
  onBack,
}: OrganizationConfirmationStepProps) {
  const { name, industry, type, size, address, avatar, members } = formValues;

  // Get the display values for select fields
  const getIndustryDisplay = () => {
    const options = {
      agriculture: "Agriculture",
      automotive: "Automotive",
      construction: "Construction",
      consulting: "Consulting",
      education: "Education",
      energy: "Energy & Utilities",
      entertainment: "Entertainment & Media",
      finance: "Finance & Banking",
      food: "Food & Beverage",
      government: "Government",
      healthcare: "Healthcare",
      hospitality: "Hospitality & Tourism",
      insurance: "Insurance",
      logistics: "Logistics & Transportation",
      manufacturing: "Manufacturing",
      mining: "Mining & Resources",
      non_profit: "Non-Profit",
      pharmaceuticals: "Pharmaceuticals",
      real_estate: "Real Estate",
      retail: "Retail",
      technology: "Technology & IT",
      telecommunications: "Telecommunications",
      other: "Other",
    };
    return options[industry as keyof typeof options] || industry;
  };

  const getTypeDisplay = () => {
    const options = {
      individual: "Individual",
      private: "Private Company",
      non_profit: "Non-Profit",
      government: "Government",
      public: "Public Company",
      partnership: "Partnership",
      cooperative: "Cooperative",
    };
    return options[type as keyof typeof options] || type;
  };

  const getSizeDisplay = () => {
    const options = {
      "1-10": "1-10 employees",
      "11-50": "11-50 employees",
      "51-200": "51-200 employees",
      "201-500": "201-500 employees",
      "501-1000": "501-1000 employees",
      "1001+": "1001+ employees",
    };
    return options[size as keyof typeof options] || size;
  };

  const getRoleDisplay = (role: string) => {
    const options = {
      owner: "Owner",
      admin: "Admin",
      billing: "Billing",
      member: "Member",
      viewer: "Viewer",
    };
    return options[role as keyof typeof options] || role;
  };

  // Create a URL for the avatar if it exists
  const avatarUrl = avatar ? URL.createObjectURL(avatar) : null;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <div className="flex justify-center">
        <Avatar className="h-24 w-24">
          {avatarUrl ? (
            <AvatarImage src={avatarUrl} alt={name} />
          ) : (
            <AvatarFallback>
              {name.substring(0, 2).toUpperCase()}
            </AvatarFallback>
          )}
        </Avatar>
      </div>

      <Card>
        <CardContent className="p-6">
          <h3 className="mb-4 text-xl font-semibold">
            {i18n.en.sections.organization.title}
          </h3>
          <dl className="space-y-2">
            <div className="grid grid-cols-3 gap-4">
              <dt className="text-muted-foreground font-medium">
                {i18n.en.sections.organization.fields.name}
              </dt>
              <dd className="col-span-2">{name}</dd>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <dt className="text-muted-foreground font-medium">
                {i18n.en.sections.organization.fields.industry}
              </dt>
              <dd className="col-span-2">{getIndustryDisplay()}</dd>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <dt className="text-muted-foreground font-medium">
                {i18n.en.sections.organization.fields.type}
              </dt>
              <dd className="col-span-2">{getTypeDisplay()}</dd>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <dt className="text-muted-foreground font-medium">
                {i18n.en.sections.organization.fields.size}
              </dt>
              <dd className="col-span-2">{getSizeDisplay()}</dd>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <dt className="text-muted-foreground font-medium">
                {i18n.en.sections.organization.fields.address}
              </dt>
              <dd className="col-span-2">{address || "-"}</dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      {members.length > 0 ? (
        <Card>
          <CardContent className="p-6">
            <h3 className="mb-4 text-xl font-semibold">
              {i18n.en.sections.members.title}
            </h3>
            <div className="space-y-4">
              {members.map((member, index) => (
                <div
                  key={index}
                  className="grid grid-cols-2 gap-4 rounded-lg border p-4"
                >
                  <div>
                    <div className="text-muted-foreground text-sm">
                      {i18n.en.sections.members.fields.email}
                    </div>
                    <div>{member.email}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground text-sm">
                      {i18n.en.sections.members.fields.role}
                    </div>
                    <div className="capitalize">
                      {getRoleDisplay(member.role)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6">
            <h3 className="mb-4 text-xl font-semibold">
              {i18n.en.sections.members.title}
            </h3>
            <p className="text-muted-foreground">{i18n.en.noMembers}</p>
          </CardContent>
        </Card>
      )}

      <div className="mt-6 flex justify-between">
        <Button type="button" variant="outline" onClick={onBack}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          {i18n.en.actions.back}
        </Button>
        <Button onClick={onSubmit} disabled={isSubmitting}>
          {isSubmitting ? i18n.en.actions.submitting : i18n.en.actions.confirm}
        </Button>
      </div>
    </div>
  );
}
