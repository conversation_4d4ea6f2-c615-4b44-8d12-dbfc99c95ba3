import { PropsWithChildren } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useForm, useFormContext } from "react-hook-form";
import * as z from "zod";

import { ImageUploadField } from "@/components/forms/fields/ImageUpload";
import { Button, ButtonProps } from "@/components/ui/button";
import { Form } from "@/components/ui/form";

// i18n object for text content
const i18n = {
  en: {
    title: "Organization Logo",
    description: "Upload a logo for your organization (optional)",
    fields: {
      avatar: {
        label: "Organization Logo",
        description: "Upload a logo or image that represents your organization",
        placeholder: "Drag and drop or click to upload",
      },
    },
    actions: {
      skip: "Skip",
      back: "Back",
      submit: "Continue",
    },
  },
};

// Validation schema for the avatar step
const avatarSchema = z.object({
  avatar: z
    .instanceof(File)
    .optional()
    .refine(
      (file) => {
        if (!file) return true;
        return file.size <= 5 * 1024 * 1024; // 5MB
      },
      {
        message: "Image must be less than 5MB",
      },
    )
    .refine(
      (file) => {
        if (!file) return true;
        return [
          "image/jpeg",
          "image/jpg",
          "image/png",
          "image/webp",
          "image/svg+xml",
        ].includes(file.type);
      },
      {
        message: "Unsupported file format. Use JPEG, PNG, WEBP or SVG",
      },
    ),
});

// Export the type for use in other components
export type OrganizationAvatarValues = z.infer<typeof avatarSchema>;

export type OrganizationAvatarStepProps = PropsWithChildren<
  Parameters<typeof useForm<OrganizationAvatarValues>>[0] & {
    onSubmit: (values: OrganizationAvatarValues) => void;
    onBack?: () => void;
    onSkip?: () => void;
  }
>;

export function OrganizationAvatarStep({
  children,
  onSubmit,
  onBack,
  onSkip,
  ...props
}: OrganizationAvatarStepProps) {
  const form = useForm<OrganizationAvatarValues>({
    resolver: zodResolver(avatarSchema),
    defaultValues: {
      avatar: undefined,
    },
    ...props,
  });

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <ImageUploadField
            name="avatar"
            className="mx-auto h-40 w-40 rounded-full"
            label={i18n.en.fields.avatar.label}
            description={i18n.en.fields.avatar.description}
          />

          {children ?? (
            <div className="mt-6 flex justify-between">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  {i18n.en.actions.back}
                </Button>
              )}
              <div className="space-x-2">
                {onSkip && (
                  <Button type="button" variant="ghost" onClick={onSkip}>
                    {i18n.en.actions.skip}
                  </Button>
                )}
                <OrganizationAvatarStepSubmitButton />
              </div>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}

export function OrganizationAvatarStepSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrganizationAvatarValues>();

  return (
    <Button
      {...props}
      disabled={props.disabled || form.formState.isSubmitting}
      type="submit"
    >
      {children}
      <ChevronRight className="ml-2 h-4 w-4" />
    </Button>
  );
}
