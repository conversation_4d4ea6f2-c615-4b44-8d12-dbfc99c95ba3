import { PropsWithChildren } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChevronRight } from "lucide-react";
import { useForm, useFormContext } from "react-hook-form";
import * as z from "zod";

import { AddressAutocompleteField } from "@/components/forms/fields/AddressAutocomplete";
import { CompanyTypeField } from "@/components/forms/fields/company";
import { CompanyIndustryField } from "@/components/forms/fields/company/CompanyIndustry";
import { CompanySizeField } from "@/components/forms/fields/company/CompanySize";
import { Button, ButtonProps } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { organizationSchema } from "@/lib/validation/schemas/organizations";

// i18n object for text content
const i18n = {
  en: {
    title: "Organization Setup",
    description:
      "Set up your company profile to start managing shipments and drivers",
    fields: {
      name: {
        label: "Company Name",
        placeholder: "Acme Corp",
      },
      type: {
        label: "Company Type",
        placeholder: "Select organization type",
        options: {
          individual: "Individual",
          private: "Private Company",
          non_profit: "Non Profit",
          government: "Government",
        },
      },
      address: {
        label: "Company Address",
        placeholder: "123 Main St, City, State",
      },
    },
    actions: {
      submit: "Continue",
    },
  },
};

// Export the type for use in other components
export type OrganizationDetailsValues = z.infer<typeof organizationSchema>;

export type OrganizationDetailsStepProps = PropsWithChildren<
  Parameters<typeof useForm<OrganizationDetailsValues>>[0] & {
    onSubmit: (values: OrganizationDetailsValues) => void;
  }
>;

export function OrganizationDetailsStep({
  children,
  onSubmit,
  ...props
}: OrganizationDetailsStepProps) {
  const form = useForm<OrganizationDetailsValues>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: "",
      industry: undefined,
      size: undefined,
      type: undefined,
      address: "",
    },
    ...props,
  });

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{i18n.en.fields.name.label}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={i18n.en.fields.name.placeholder}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <CompanyIndustryField name="industry" />

          <CompanyTypeField name="type" />

          <CompanySizeField name="size" />

          <AddressAutocompleteField
            name="address"
            label={i18n.en.fields.address.label}
            placeholder={i18n.en.fields.address.placeholder}
          />

          {children ?? (
            <div className="flex justify-end">
              <OrganizationDetailsStepSubmitButton />
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}

export function OrganizationDetailsStepSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrganizationDetailsValues>();

  return (
    <Button
      {...props}
      className={cn("w-full", props.className)}
      disabled={props.disabled || form.formState.isSubmitting}
      type="submit"
    >
      {children}
      <ChevronRight className="ml-2 h-4 w-4" />
    </Button>
  );
}
