import { PropsWithChildren } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserPlus } from "lucide-react";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { membersSchema } from "@/lib/validation/schemas/members";

// i18n object for text content
const i18n = {
  en: {
    title: "Invite Team Members",
    description:
      "Invite your team members to collaborate on QuikSkope (optional)",
    fields: {
      email: {
        label: "Email",
        placeholder: "<EMAIL>",
      },
      role: {
        label: "Role",
        placeholder: "Select role",
        options: {
          owner: "Owner",
          admin: "Admin",
          billing: "Billing",
          member: "Member",
          viewer: "Viewer",
        },
      },
    },
    actions: {
      addMember: "Add Another Team Member",
      removeMember: "Remove",
    },
  },
};

// Export the type for use in other components
export type TeamMembersValues = z.infer<typeof membersSchema>;

export type TeamMembersStepProps = PropsWithChildren<
  Parameters<typeof useForm<TeamMembersValues>>[0] & {
    onSubmit: (values: TeamMembersValues) => void;
  }
>;

export function TeamMembersStep({
  children,
  onSubmit,
  ...props
}: TeamMembersStepProps) {
  const form = useForm<TeamMembersValues>({
    resolver: zodResolver(membersSchema),
    defaultValues: {
      members: [{ email: "", role: "member" }],
    },
    ...props,
  });

  const addMember = () => {
    const members = form.getValues("members");
    form.setValue("members", [...members, { email: "", role: "member" }]);
  };

  const removeMember = (index: number) => {
    const members = form.getValues("members");
    form.setValue(
      "members",
      members.filter((_, i) => i !== index),
    );
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.description}</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {form.watch("members").map((_, index) => (
            <div key={index} className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Team Member {index + 1}</h3>
                {index > 0 && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => removeMember(index)}
                  >
                    {i18n.en.actions.removeMember}
                  </Button>
                )}
              </div>

              <FormField
                control={form.control}
                name={`members.${index}.email`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{i18n.en.fields.email.label}</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder={i18n.en.fields.email.placeholder}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`members.${index}.role`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{i18n.en.fields.role.label}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={i18n.en.fields.role.placeholder}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(i18n.en.fields.role.options).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          ),
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={addMember}
            className="w-full"
          >
            <UserPlus className="mr-2 h-4 w-4" />
            {i18n.en.actions.addMember}
          </Button>

          {children}
        </form>
      </Form>
    </div>
  );
}
