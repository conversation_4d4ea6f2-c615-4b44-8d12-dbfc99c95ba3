import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { WeightUnit } from "@/components/forms/fields/Weight";
import type { ButtonProps } from "@/components/ui/button";
import type { Enums } from "@/supabase/types";

import { CurrencyField } from "@/components/forms/fields/Currency";
import { DatePickerField } from "@/components/forms/fields/DatePicker";
import { DescriptionField } from "@/components/forms/fields/Description";
import { SummaryField } from "@/components/forms/fields/Summary";
import {
  LOAD_TYPES,
  LoadTypeField,
} from "@/components/forms/fields/types/LoadType";
import { WEIGHT_UNITS, WeightField } from "@/components/forms/fields/Weight";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form } from "@/components/ui/form";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      label: {
        label: "Label",
        description: "A descriptive label for the load",
        placeholder: "Enter a label",
      },
      type: {
        label: "Type",
        description: "The type of load",
        placeholder: "Select a type",
      },
      perishable: {
        label: "Perishable",
        description: "Whether the load contains perishable items",
      },
      weight: {
        label: "Weight",
        description: "The weight of the load",
        placeholder: "Enter weight",
      },
      amount: {
        label: "Valuation",
        description: "The monetary value of the load",
        placeholder: "Enter valuation",
      },
      notes: {
        label: "Notes",
        description: "Additional notes about the load",
        placeholder: "Enter any additional notes",
      },
      start_date: {
        label: "Start Date",
        description: "When the load needs to be picked up",
        placeholder: "Select start date",
      },
    },
    actions: {
      submit: "Submit",
      cancel: "Cancel",
    },
  },
};

const loadFormSchema = z.object({
  id: z.string().optional(),
  label: z.string().min(1, "Label is required"),
  type: z.enum(
    LOAD_TYPES as unknown as [Enums<"load_type">, ...Enums<"load_type">[]],
    {
      required_error: "Please select a load type",
    },
  ),
  perishable: z.boolean().default(false),
  weight: z.string().optional(),
  weight_unit: z
    .enum(WEIGHT_UNITS as unknown as [WeightUnit, ...WeightUnit[]])
    .default("kg"),
  amount: z.string().optional(),
  notes: z.string().optional(),
  startDate: z.date().nullable(),
});

export type LoadFormValues = z.infer<typeof loadFormSchema>;
export type LoadFormProps = PropsWithChildren<
  Parameters<typeof useForm<LoadFormValues>>[0] & {
    onSubmit?: (values: LoadFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function LoadForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: LoadFormProps) {
  const form = useForm<LoadFormValues>({
    ...props,
    resolver: zodResolver(loadFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <SummaryField
          name="label"
          label={i18n.en.fields.label.label}
          description={i18n.en.fields.label.description}
          placeholder={i18n.en.fields.label.placeholder}
        />

        <LoadTypeField
          name="type"
          label={i18n.en.fields.type.label}
          description={i18n.en.fields.type.description}
          placeholder={i18n.en.fields.type.placeholder}
        />

        <div className="flex items-center space-x-2">
          <Checkbox
            id="perishable"
            checked={form.watch("perishable")}
            onCheckedChange={(checked) => {
              form.setValue("perishable", checked as boolean);
            }}
            disabled={form.formState.isSubmitting}
          />
          <label
            htmlFor="perishable"
            className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {i18n.en.fields.perishable.label}
          </label>
        </div>

        <WeightField
          name="weight"
          unitName="weight_unit"
          label={i18n.en.fields.weight.label}
          description={i18n.en.fields.weight.description}
          placeholder={i18n.en.fields.weight.placeholder}
        />

        <CurrencyField
          name="amount"
          label={i18n.en.fields.amount.label}
          description={i18n.en.fields.amount.description}
          placeholder={i18n.en.fields.amount.placeholder}
        />

        <DatePickerField
          name="startDate"
          label={i18n.en.fields.start_date.label}
          description={i18n.en.fields.start_date.description}
          placeholder={i18n.en.fields.start_date.placeholder}
        />

        <DescriptionField
          name="notes"
          label={i18n.en.fields.notes.label}
          description={i18n.en.fields.notes.description}
          placeholder={i18n.en.fields.notes.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <LoadFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function LoadFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<LoadFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
