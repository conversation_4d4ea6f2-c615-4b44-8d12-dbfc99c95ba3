import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";
import type { Enums } from "@/supabase/types";

import { CurrencyField } from "@/components/forms/fields/Currency";
import { WeightField } from "@/components/forms/fields/Weight";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      mode: {
        label: "Mode",
        description: "The mode of shipment",
        placeholder: "Select a mode",
        options: {
          closed: "Closed",
          open: "Open",
        },
      },
      source: {
        label: "Source",
        description: "The source of the shipment",
        placeholder: "Select a source",
        options: {
          system: "System",
          driver: "Driver",
          organization: "Organization",
        },
      },
      weight: {
        label: "Weight",
        description: "The weight of the shipment",
        placeholder: "Enter weight",
      },
      amount: {
        label: "Valuation",
        description: "The monetary value of the shipment",
        placeholder: "Enter valuation",
      },
      driver: {
        label: "Driver",
        description: "The assigned driver",
        placeholder: "Select a driver",
      },
      organization: {
        label: "Organization",
        description: "The organization handling the shipment",
        placeholder: "Select an organization",
      },
    },
    actions: {
      submit: "Submit",
      cancel: "Cancel",
    },
  },
};

const SHIPMENT_MODES = ["open", "closed"] as const;

const SHIPMENT_SOURCES = ["driver", "organization", "system"] as const;

const shipmentFormSchema = z.object({
  id: z.string().optional(),
  mode: z.enum(
    SHIPMENT_MODES as unknown as [
      Enums<"shipment_mode">,
      ...Enums<"shipment_mode">[],
    ],
    {
      required_error: "Please select a mode",
    },
  ),
  source: z.enum(
    SHIPMENT_SOURCES as unknown as [
      Enums<"shipment_source">,
      ...Enums<"shipment_source">[],
    ],
    {
      required_error: "Please select a source",
    },
  ),
  weight: z.string().optional(),
  weight_unit: z.enum(["kg", "lb", "g", "oz", "mt"]).default("kg"),
  amount: z.string().optional(),
  driver_id: z.string().optional(),
  organization_id: z.string().optional(),
});

export type ShipmentFormValues = z.infer<typeof shipmentFormSchema>;
export type ShipmentFormProps = PropsWithChildren<
  Parameters<typeof useForm<ShipmentFormValues>>[0] & {
    onSubmit?: (values: ShipmentFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function ShipmentForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: ShipmentFormProps) {
  const form = useForm<ShipmentFormValues>({
    ...props,
    resolver: zodResolver(shipmentFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.mode.label}
          </label>
          <Select
            onValueChange={(value) =>
              form.setValue("mode", value as ShipmentFormValues["mode"])
            }
            defaultValue={form.getValues("mode")}
          >
            <SelectTrigger>
              <SelectValue placeholder={i18n.en.fields.mode.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {SHIPMENT_MODES.map((mode) => (
                <SelectItem key={mode} value={mode}>
                  {i18n.en.fields.mode.options[mode]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.source.label}
          </label>
          <Select
            onValueChange={(value) =>
              form.setValue("source", value as ShipmentFormValues["source"])
            }
            defaultValue={form.getValues("source")}
          >
            <SelectTrigger>
              <SelectValue placeholder={i18n.en.fields.source.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {SHIPMENT_SOURCES.map((source) => (
                <SelectItem key={source} value={source}>
                  {i18n.en.fields.source.options[source]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <WeightField
          name="weight"
          unitName="weight_unit"
          label={i18n.en.fields.weight.label}
          description={i18n.en.fields.weight.description}
          placeholder={i18n.en.fields.weight.placeholder}
        />

        <CurrencyField
          name="amount"
          label={i18n.en.fields.amount.label}
          description={i18n.en.fields.amount.description}
          placeholder={i18n.en.fields.amount.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <ShipmentFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ShipmentFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ShipmentFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
