import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { AddressOption } from "@/components/forms/fields/AddressAutocomplete";
import type { ButtonProps } from "@/components/ui/button";

import { AddressAutocompleteField } from "@/components/forms/fields/AddressAutocomplete";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    address: {
      label: "Address",
      description: "The address for this location",
      placeholder: "Enter or select an address",
    },
    type: {
      label: "Location Type",
      description: "The type of location",
      placeholder: "Select a location type",
      options: {
        billing: "Billing",
        commercial: "Commercial",
        industrial: "Industrial",
        government: "Government",
        public: "Public",
        residential: "Residential",
        warehouse: "Warehouse",
        distribution_center: "Distribution Center",
        retail: "Retail",
        other: "Other",
      },
    },
    actions: {
      submit: "Submit",
      cancel: "Cancel",
    },
  },
};

const LOCATION_TYPES = [
  "billing",
  "commercial",
  "industrial",
  "government",
  "public",
  "residential",
  "warehouse",
  "distribution_center",
  "retail",
  "other",
] as const;

const locationFormSchema = z.object({
  id: z.string().optional(),
  address: z.custom<AddressOption>((val) => val !== undefined, {
    message: "Please select an address",
  }),
  type: z.enum(LOCATION_TYPES, {
    required_error: "Please select a location type",
  }),
});

export type LocationFormValues = z.infer<typeof locationFormSchema>;
export type LocationFormProps = PropsWithChildren<
  Parameters<typeof useForm<LocationFormValues>>[0] & {
    onSubmit?: (values: LocationFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function LocationForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: LocationFormProps) {
  const form = useForm<LocationFormValues>({
    ...props,
    resolver: zodResolver(locationFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <AddressAutocompleteField
          name="address"
          label={i18n.en.address.label}
          description={i18n.en.address.description}
          placeholder={i18n.en.address.placeholder}
        />

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.type.label}
          </label>
          <Select
            onValueChange={(value) =>
              form.setValue("type", value as LocationFormValues["type"])
            }
            defaultValue={form.getValues("type")}
          >
            <SelectTrigger>
              <SelectValue placeholder={i18n.en.type.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {LOCATION_TYPES.map((type) => (
                <SelectItem key={type} value={type}>
                  {i18n.en.type.options[type]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <LocationFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function LocationFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<LocationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
