import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      firstName: {
        label: "First Name",
        description: "Your first name",
        placeholder: "John",
      },
      lastName: {
        label: "Last Name",
        description: "Your last name",
        placeholder: "Doe",
      },
      email: {
        label: "Work Email",
        description: "Your work email address",
        placeholder: "<EMAIL>",
      },
      companyName: {
        label: "Company Name",
        description: "Your company name",
        placeholder: "Acme Corp",
      },
      jobTitle: {
        label: "Job Title",
        description: "Your job title",
        placeholder: "Supply Chain Manager",
      },
      companySize: {
        label: "Company Size",
        description: "Number of employees",
        placeholder: "Select company size",
        options: {
          "1-10": "1-10 employees",
          "11-50": "11-50 employees",
          "51-200": "51-200 employees",
          "201-500": "201-500 employees",
          "501+": "501+ employees",
        },
      },
      message: {
        label: "How can we help?",
        description: "Tell us about your needs",
        placeholder: "Tell us about your supply chain needs...",
      },
    },
    actions: {
      submit: "Request Demo",
    },
  },
};

const COMPANY_SIZES = ["1-10", "11-50", "51-200", "201-500", "501+"] as const;

const demoRequestFormSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  companyName: z.string().min(2, "Company name must be at least 2 characters"),
  jobTitle: z.string().min(2, "Job title must be at least 2 characters"),
  companySize: z.enum(COMPANY_SIZES, {
    required_error: "Please select a company size",
  }),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

export type DemoRequestFormValues = z.infer<typeof demoRequestFormSchema>;
export type DemoRequestFormProps = PropsWithChildren<
  Parameters<typeof useForm<DemoRequestFormValues>>[0] & {
    onSubmit?: (values: DemoRequestFormValues) => void | Promise<void>;
  }
>;

export default function DemoRequestForm({
  children,
  onSubmit = () => void 0,
  ...props
}: DemoRequestFormProps) {
  const form = useForm<DemoRequestFormValues>({
    ...props,
    resolver: zodResolver(demoRequestFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      companyName: "",
      jobTitle: "",
      companySize: undefined,
      message: "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {i18n.en.fields.firstName.label}
            </label>
            <Input
              {...form.register("firstName")}
              placeholder={i18n.en.fields.firstName.placeholder}
            />
            {form.formState.errors.firstName && (
              <p className="text-destructive text-sm">
                {form.formState.errors.firstName.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {i18n.en.fields.lastName.label}
            </label>
            <Input
              {...form.register("lastName")}
              placeholder={i18n.en.fields.lastName.placeholder}
            />
            {form.formState.errors.lastName && (
              <p className="text-destructive text-sm">
                {form.formState.errors.lastName.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.email.label}
          </label>
          <Input
            {...form.register("email")}
            type="email"
            placeholder={i18n.en.fields.email.placeholder}
          />
          {form.formState.errors.email && (
            <p className="text-destructive text-sm">
              {form.formState.errors.email.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.companyName.label}
          </label>
          <Input
            {...form.register("companyName")}
            placeholder={i18n.en.fields.companyName.placeholder}
          />
          {form.formState.errors.companyName && (
            <p className="text-destructive text-sm">
              {form.formState.errors.companyName.message}
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {i18n.en.fields.jobTitle.label}
            </label>
            <Input
              {...form.register("jobTitle")}
              placeholder={i18n.en.fields.jobTitle.placeholder}
            />
            {form.formState.errors.jobTitle && (
              <p className="text-destructive text-sm">
                {form.formState.errors.jobTitle.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {i18n.en.fields.companySize.label}
            </label>
            <Select
              onValueChange={(value) =>
                form.setValue(
                  "companySize",
                  value as DemoRequestFormValues["companySize"],
                )
              }
              defaultValue={form.getValues("companySize")}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={i18n.en.fields.companySize.placeholder}
                />
              </SelectTrigger>
              <SelectContent>
                {COMPANY_SIZES.map((size) => (
                  <SelectItem key={size} value={size}>
                    {i18n.en.fields.companySize.options[size]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.companySize && (
              <p className="text-destructive text-sm">
                {form.formState.errors.companySize.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.message.label}
          </label>
          <Textarea
            {...form.register("message")}
            placeholder={i18n.en.fields.message.placeholder}
            className="min-h-[100px]"
          />
          {form.formState.errors.message && (
            <p className="text-destructive text-sm">
              {form.formState.errors.message.message}
            </p>
          )}
        </div>

        {children ?? (
          <div className="flex w-full justify-end">
            <DemoRequestFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function DemoRequestFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<DemoRequestFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
