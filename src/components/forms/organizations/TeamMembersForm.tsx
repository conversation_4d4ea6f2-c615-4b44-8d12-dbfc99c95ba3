import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { membersSchema } from "@/lib/validation/schemas/members";

const i18n = {
  en: {
    fields: {
      email: {
        label: "Email",
        placeholder: "<EMAIL>",
      },
      role: {
        label: "Role",
        placeholder: "Select role",
        options: {
          owner: "Owner",
          admin: "Admin",
          billing: "Billing",
          member: "Member",
          viewer: "Viewer",
        },
      },
    },
    actions: {
      submit: "Continue",
      addMember: "Add Another Team Member",
      removeMember: "Remove",
    },
  },
};

export type TeamMembersValues = z.infer<typeof membersSchema>;

export type TeamMembersFormProps = PropsWithChildren<
  Parameters<typeof useForm<TeamMembersValues>>[0] & {
    onSubmit: (values: TeamMembersValues) => void;
  }
>;

export function TeamMembersForm({
  children,
  onSubmit,
  ...props
}: TeamMembersFormProps) {
  const form = useForm<TeamMembersValues>({
    resolver: zodResolver(membersSchema),
    defaultValues: {
      members: [{ email: "", role: "member" }],
    },
    ...props,
  });

  const addMember = () => {
    const members = form.getValues("members");
    form.setValue("members", [...members, { email: "", role: "member" }]);
  };

  const removeMember = (index: number) => {
    const members = form.getValues("members");
    form.setValue(
      "members",
      members.filter((_, i) => i !== index),
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {form.watch("members").map((_, index) => (
          <div key={index} className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Team Member {index + 1}</h3>
              {index > 0 && (
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => removeMember(index)}
                >
                  {i18n.en.actions.removeMember}
                </Button>
              )}
            </div>

            <FormField
              control={form.control}
              name={`members.${index}.email`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{i18n.en.fields.email.label}</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={i18n.en.fields.email.placeholder}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`members.${index}.role`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{i18n.en.fields.role.label}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={i18n.en.fields.role.placeholder}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(i18n.en.fields.role.options).map(
                        ([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ),
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        ))}

        <Button
          type="button"
          variant="outline"
          onClick={addMember}
          className="w-full"
        >
          {i18n.en.actions.addMember}
        </Button>

        {children ?? (
          <div className="flex justify-end">
            <TeamMembersFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function TeamMembersFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<TeamMembersValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
