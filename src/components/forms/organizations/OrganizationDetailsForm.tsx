import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { organizationSchema } from "@/lib/validation/schemas/organizations";

const i18n = {
  en: {
    fields: {
      name: {
        label: "Organization Name",
        placeholder: "Acme Corp",
      },
      industry: {
        label: "Industry",
        placeholder: "Select industry",
        options: {
          logistics: "Logistics",
          manufacturing: "Manufacturing",
          retail: "Retail",
          other: "Other",
        },
      },
      type: {
        label: "Organization Type",
        placeholder: "Select organization type",
        options: {
          individual: "Individual",
          private: "Private Company",
          non_profit: "Non Profit",
          government: "Government",
        },
      },
      size: {
        label: "Company Size",
        placeholder: "Select company size",
        options: {
          "1-10": "1-10 employees",
          "11-50": "11-50 employees",
          "51-200": "51-200 employees",
          "201+": "201+ employees",
        },
      },
      address: {
        label: "Address (Optional)",
        placeholder: "123 Main St, City, State",
      },
    },
    actions: {
      submit: "Continue",
    },
  },
};

export type OrganizationDetailsValues = z.infer<typeof organizationSchema>;

export type OrganizationDetailsFormProps = PropsWithChildren<
  Parameters<typeof useForm<OrganizationDetailsValues>>[0] & {
    onSubmit: (values: OrganizationDetailsValues) => void;
  }
>;

export function OrganizationDetailsForm({
  children,
  onSubmit,
  ...props
}: OrganizationDetailsFormProps) {
  const form = useForm<OrganizationDetailsValues>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: undefined,
      industry: undefined,
      size: undefined,
      type: undefined,
      address: undefined,
    },
    ...props,
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.fields.name.label}</FormLabel>
              <FormControl>
                <Input
                  placeholder={i18n.en.fields.name.placeholder}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="industry"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.fields.industry.label}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={i18n.en.fields.industry.placeholder}
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(i18n.en.fields.industry.options).map(
                    ([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ),
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.fields.type.label}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={i18n.en.fields.type.placeholder}
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(i18n.en.fields.type.options).map(
                    ([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ),
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="size"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.fields.size.label}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={i18n.en.fields.size.placeholder}
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(i18n.en.fields.size.options).map(
                    ([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ),
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.fields.address.label}</FormLabel>
              <FormControl>
                <Input
                  placeholder={i18n.en.fields.address.placeholder}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {children ?? (
          <div className="flex justify-end">
            <OrganizationDetailsFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function OrganizationDetailsFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrganizationDetailsValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
