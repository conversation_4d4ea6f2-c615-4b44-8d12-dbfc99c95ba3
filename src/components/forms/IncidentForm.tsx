import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";
import type { Enums } from "@/supabase/types";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { cn } from "@/lib/utils";
import { DescriptionField } from "./fields/Description";
import { SummaryField } from "./fields/Summary";
import {
  INCIDENT_SEVERITIES,
  IncidentSeverityField,
} from "./fields/types/IncidentSeverity";
import { INCIDENT_TYPES, IncidentTypeField } from "./fields/types/IncidentType";

const i18n = {
  en: {
    title: {
      label: "Title",
      description: "Summarize the incident in a few words",
      placeholder: "Enter a brief title of the incident",
    },
    description: {
      label: "Description",
      description: "Detailed description of the incident",
      placeholder: "Enter the incident description",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const incidentFormSchema = z.object({
  id: z.string().optional(),
  organizationId: z.string().optional(),
  providerId: z.string().optional(),
  shiftId: z.string().optional(),
  title: z
    .string()
    .min(5, { message: "Title must be at least 5 characters long" }),
  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters long" }),
  severity: z
    .enum(
      INCIDENT_SEVERITIES as [
        Enums<"incident_severity">,
        ...Enums<"incident_severity">[],
      ],
    )
    .default(INCIDENT_SEVERITIES[0]),
  type: z
    .enum(
      INCIDENT_TYPES as [Enums<"incident_type">, ...Enums<"incident_type">[]],
    )
    .default(INCIDENT_TYPES[0]),
  status: z
    .enum(["reported", "investigating", "resolved", "closed"] as const)
    .optional(),
});

export type IncidentFormValues = z.infer<typeof incidentFormSchema>;
export type IncidentFormProps = PropsWithChildren<
  Parameters<typeof useForm<IncidentFormValues>>[0] & {
    onSubmit?: (values: IncidentFormValues) => void | Promise<void>;
  }
>;

export default function IncidentForm({
  children,
  onSubmit = () => void 0,
  ...props
}: IncidentFormProps) {
  const form = useForm<IncidentFormValues>({
    ...props,
    resolver: zodResolver(incidentFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <SummaryField
          name="title"
          label={i18n.en.title.label}
          description={i18n.en.title.description}
          placeholder={i18n.en.title.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        <IncidentTypeField name="type" />
        <IncidentSeverityField name="severity" />

        {children ?? (
          <div className="flex w-full justify-center">
            <IncidentFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function IncidentFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<IncidentFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
