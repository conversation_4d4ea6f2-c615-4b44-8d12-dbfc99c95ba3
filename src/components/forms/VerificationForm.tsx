import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { SummaryField } from "@/components/forms/fields/Summary";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      stop_id: {
        label: "Stop",
        description: "The stop where verification occurred",
        placeholder: "Select a stop",
      },
      driver_id: {
        label: "Driver",
        description: "The driver being verified",
        placeholder: "Select a driver",
      },
      vehicle_id: {
        label: "Vehicle",
        description: "The vehicle being verified",
        placeholder: "Select a vehicle",
      },
      document_id: {
        label: "Document",
        description: "The document being verified",
        placeholder: "Select a document",
      },
      verified: {
        label: "Verified",
        description: "Whether the verification was successful",
      },
      notes: {
        label: "Notes",
        description: "Additional notes about the verification",
        placeholder: "Enter verification notes",
      },
    },
    actions: {
      submit: "Save Verification",
      cancel: "Cancel",
    },
  },
};

const verificationFormSchema = z.object({
  stop_id: z.string().optional(),
  driver_id: z.string().optional(),
  vehicle_id: z.string().optional(),
  document_id: z.string().optional(),
  verified: z.boolean().default(false),
  notes: z.string().optional(),
});

export type VerificationFormValues = z.infer<typeof verificationFormSchema>;
export type VerificationFormProps = PropsWithChildren<
  Parameters<typeof useForm<VerificationFormValues>>[0] & {
    onSubmit?: (values: VerificationFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function VerificationForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: VerificationFormProps) {
  const form = useForm<VerificationFormValues>({
    ...props,
    resolver: zodResolver(verificationFormSchema),
    defaultValues: {
      stop_id: "",
      driver_id: "",
      vehicle_id: "",
      document_id: "",
      verified: false,
      notes: "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.stop_id.label}
          </label>
          <p className="text-muted-foreground text-xs">
            {i18n.en.fields.stop_id.description}
          </p>
          <Select
            onValueChange={(value) => form.setValue("stop_id", value)}
            defaultValue={form.getValues("stop_id")}
          >
            <SelectTrigger>
              <SelectValue placeholder={i18n.en.fields.stop_id.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {/* TODO: Fetch stops dynamically */}
              <SelectItem value="stop-1">Stop 1</SelectItem>
              <SelectItem value="stop-2">Stop 2</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.driver_id.label}
          </label>
          <p className="text-muted-foreground text-xs">
            {i18n.en.fields.driver_id.description}
          </p>
          <Select
            onValueChange={(value) => form.setValue("driver_id", value)}
            defaultValue={form.getValues("driver_id")}
          >
            <SelectTrigger>
              <SelectValue placeholder={i18n.en.fields.driver_id.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {/* TODO: Fetch drivers dynamically */}
              <SelectItem value="driver-1">Driver 1</SelectItem>
              <SelectItem value="driver-2">Driver 2</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.vehicle_id.label}
          </label>
          <p className="text-muted-foreground text-xs">
            {i18n.en.fields.vehicle_id.description}
          </p>
          <Select
            onValueChange={(value) => form.setValue("vehicle_id", value)}
            defaultValue={form.getValues("vehicle_id")}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={i18n.en.fields.vehicle_id.placeholder}
              />
            </SelectTrigger>
            <SelectContent>
              {/* TODO: Fetch vehicles dynamically */}
              <SelectItem value="vehicle-1">Vehicle 1</SelectItem>
              <SelectItem value="vehicle-2">Vehicle 2</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {i18n.en.fields.document_id.label}
          </label>
          <p className="text-muted-foreground text-xs">
            {i18n.en.fields.document_id.description}
          </p>
          <Select
            onValueChange={(value) => form.setValue("document_id", value)}
            defaultValue={form.getValues("document_id")}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={i18n.en.fields.document_id.placeholder}
              />
            </SelectTrigger>
            <SelectContent>
              {/* TODO: Fetch documents dynamically */}
              <SelectItem value="document-1">Document 1</SelectItem>
              <SelectItem value="document-2">Document 2</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="verified"
            checked={form.getValues("verified")}
            onCheckedChange={(checked) => {
              form.setValue("verified", checked === true);
            }}
          />
          <label
            htmlFor="verified"
            className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {i18n.en.fields.verified.label}
          </label>
        </div>

        <SummaryField
          name="notes"
          label={i18n.en.fields.notes.label}
          description={i18n.en.fields.notes.description}
          placeholder={i18n.en.fields.notes.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <VerificationFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function VerificationFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<VerificationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
