"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@/components/ui/button";

import { EmailField } from "@/components/forms/fields/Email";
import { PhoneNumberField } from "@/components/forms/fields/PhoneNumber";
import { SummaryField } from "@/components/forms/fields/Summary";
import {
  CONTACT_TYPES,
  ContactTypeField,
} from "@/components/forms/fields/types/ContactType";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    fields: {
      first_name: {
        label: "First Name",
        description: "Contact's first name",
        placeholder: "Enter first name",
      },
      last_name: {
        label: "Last Name",
        description: "Contact's last name",
        placeholder: "Enter last name",
      },
      email: {
        label: "Email",
        description: "Contact's email address",
        placeholder: "Enter email address",
      },
      phone: {
        label: "Phone Number",
        description: "Contact's phone number",
        placeholder: "Enter phone number",
      },
      type: {
        label: "Contact Type",
        description: "Role or type of contact",
        placeholder: "Select contact type",
      },
    },
    actions: {
      submit: "Save Contact",
      cancel: "Cancel",
    },
  },
};

const contactFormSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  type: z.enum(
    CONTACT_TYPES as [
      (typeof CONTACT_TYPES)[number],
      ...(typeof CONTACT_TYPES)[number][],
    ],
    {
      required_error: "Please select a contact type",
    },
  ),
  organization_id: z.string().optional(),
  location_id: z.string().optional(),
});

export type ContactFormValues = z.infer<typeof contactFormSchema>;
export type ContactFormProps = PropsWithChildren<
  Parameters<typeof useForm<ContactFormValues>>[0] & {
    onSubmit?: (values: ContactFormValues) => void | Promise<void>;
    onCancel?: () => void;
  }
>;

export default function ContactForm({
  children,
  onSubmit = () => void 0,
  onCancel,
  ...props
}: ContactFormProps) {
  const form = useForm<ContactFormValues>({
    ...props,
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      type: CONTACT_TYPES[0],
      organization_id: undefined,
      location_id: undefined,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <SummaryField
          name="first_name"
          label={i18n.en.fields.first_name.label}
          description={i18n.en.fields.first_name.description}
          placeholder={i18n.en.fields.first_name.placeholder}
        />

        <SummaryField
          name="last_name"
          label={i18n.en.fields.last_name.label}
          description={i18n.en.fields.last_name.description}
          placeholder={i18n.en.fields.last_name.placeholder}
        />

        <EmailField
          name="email"
          label={i18n.en.fields.email.label}
          description={i18n.en.fields.email.description}
          placeholder={i18n.en.fields.email.placeholder}
        />

        <PhoneNumberField
          name="phone"
          label={i18n.en.fields.phone.label}
          description={i18n.en.fields.phone.description}
          placeholder={i18n.en.fields.phone.placeholder}
        />

        <ContactTypeField
          name="type"
          label={i18n.en.fields.type.label}
          description={i18n.en.fields.type.description}
          placeholder={i18n.en.fields.type.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end gap-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={form.formState.isSubmitting}
              >
                {i18n.en.actions.cancel}
              </Button>
            )}
            <ContactFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ContactFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ContactFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
