import type { FileUploadFieldProps } from "./FileUpload";

import { FileUploadField } from "./FileUpload";

const i18n = {
  en: {
    document: {
      label: "Document Upload",
      description: "Upload a document",
      validation: {
        type: "File must be a valid document type",
      },
    },
  },
};

const DOCUMENT_TYPES = [
  // PDF
  "application/pdf",
  // Word
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  // Excel
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  // PowerPoint
  "application/vnd.ms-powerpoint",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  // Text
  "text/plain",
  // Rich Text
  "application/rtf",
  // OpenDocument
  "application/vnd.oasis.opendocument.text",
  "application/vnd.oasis.opendocument.spreadsheet",
  "application/vnd.oasis.opendocument.presentation",
] as const;

export interface DocumentUploadFieldProps
  extends Omit<FileUploadFieldProps, "accept"> {
  allowedTypes?: (typeof DOCUMENT_TYPES)[number][];
}

export function DocumentUploadField({
  label = i18n.en.document.label,
  description = i18n.en.document.description,
  allowedTypes = ["application/pdf"],
  ...props
}: DocumentUploadFieldProps) {
  return (
    <FileUploadField
      {...props}
      label={label}
      description={description}
      accept={allowedTypes}
      showPreview={false}
    />
  );
}
