import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/components/ui/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const i18n = {
  en: {
    temperature: {
      label: "Temperature",
      description: "Enter the temperature value",
      placeholder: "Enter temperature",
      validation: {
        type: "Please enter a valid temperature",
        min: "Temperature must be at least {min}°{unit}",
        max: "Temperature must be at most {max}°{unit}",
      },
      units: {
        C: "Celsius (°C)",
        F: "Fahrenheit (°F)",
        K: "Kelvin (K)",
      },
    },
  },
};

export type TemperatureUnit = keyof typeof i18n.en.temperature.units;
// eslint-disable-next-line react-refresh/only-export-components
export const TEMPERATURE_UNITS = ["C", "F", "K"] as const;

// Temperature conversion functions
const convertTemperature = (
  value: number,
  from: TemperatureUnit,
  to: TemperatureUnit,
) => {
  if (from === to) return value;

  // First convert to Celsius as base unit
  let celsius = value;
  switch (from) {
    case "F":
      celsius = (value - 32) * (5 / 9);
      break;
    case "K":
      celsius = value - 273.15;
      break;
  }

  // Then convert from Celsius to target unit
  switch (to) {
    case "C":
      return celsius;
    case "F":
      return celsius * (9 / 5) + 32;
    case "K":
      return celsius + 273.15;
    default:
      return value;
  }
};

export interface TemperatureFieldProps
  extends Omit<InputProps, "name" | "type"> {
  name?: string;
  unitName?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  min?: number;
  max?: number;
  defaultUnit?: TemperatureUnit;
  allowedUnits?: TemperatureUnit[];
}

export function TemperatureField({
  name,
  unitName,
  label = i18n.en.temperature.label,
  description = i18n.en.temperature.description,
  placeholder = i18n.en.temperature.placeholder,
  min = -273.15, // Absolute zero in Celsius
  max,
  defaultUnit = "C",
  allowedUnits = TEMPERATURE_UNITS as unknown as TemperatureUnit[],
  ...props
}: TemperatureFieldProps) {
  const form = useFormContext();

  return (
    <div className="space-y-2">
      <FormField
        control={form.control}
        name={name}
        rules={{
          validate: {
            isNumber: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return !isNaN(num) || i18n.en.temperature.validation.type;
            },
            minValue: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              const unit = form.watch(unitName) || defaultUnit;
              const minInUnit = convertTemperature(min, "C", unit);
              return (
                num >= minInUnit ||
                i18n.en.temperature.validation.min
                  .replace("{min}", minInUnit.toFixed(1))
                  .replace("{unit}", unit)
              );
            },
            maxValue: (value) => {
              if (value === undefined || value === "" || max === undefined)
                return true;
              const num = Number(value);
              const unit = form.watch(unitName) || defaultUnit;
              const maxInUnit = convertTemperature(max, "C", unit);
              return (
                num <= maxInUnit ||
                i18n.en.temperature.validation.max
                  .replace("{max}", maxInUnit.toFixed(1))
                  .replace("{unit}", unit)
              );
            },
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <div className="flex gap-2">
              <FormControl>
                <Input
                  type="number"
                  inputMode="decimal"
                  step="0.1"
                  placeholder={placeholder}
                  className="w-[180px]"
                  {...field}
                  {...props}
                  onChange={(e) => {
                    if (e.target.value === "") {
                      field.onChange("");
                      return;
                    }

                    const value = parseFloat(e.target.value);
                    if (!isNaN(value)) {
                      field.onChange(value.toString());
                    }
                  }}
                />
              </FormControl>
              {unitName && (
                <FormField
                  control={form.control}
                  name={unitName}
                  defaultValue={defaultUnit}
                  render={({ field: unitField }) => (
                    <Select
                      value={unitField.value}
                      onValueChange={(value: TemperatureUnit) => {
                        const oldUnit = unitField.value as TemperatureUnit;
                        const newUnit = value as TemperatureUnit;
                        const currentValue = field.value;

                        // Convert the current value to the new unit
                        if (currentValue !== "") {
                          const converted = convertTemperature(
                            parseFloat(currentValue),
                            oldUnit,
                            newUnit,
                          );
                          field.onChange(converted.toFixed(1));
                        }

                        unitField.onChange(value);
                      }}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[130px]">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allowedUnits.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {i18n.en.temperature.units[unit]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              )}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
