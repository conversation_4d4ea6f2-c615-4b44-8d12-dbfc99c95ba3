"use client";

import type { DateRange } from "react-day-picker";

import { forwardRef, useState } from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { CalendarProps } from "@/components/ui/calendar";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { displayDateForTimeZone, setDateForTimeZone } from "@/lib/dates";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    label: "Date",
    description: "The date on which the work order will be scheduled.",
    placeholder: "Pick a date",
  },
};

export interface DatePickerFieldProps {
  timeZone?: string;
  name: string;
  rangeName?: ["startDate", "endDate"];
  label?: string;
  description?: string;
  placeholder?: string;
  type?: CalendarProps["mode"];
  numberOfMonths?: CalendarProps["numberOfMonths"];
  disabled?: CalendarProps["disabled"];
  initialFocus?: CalendarProps["initialFocus"];
  locale?: CalendarProps["locale"];
}

export const DatePickerField = forwardRef<HTMLDivElement, DatePickerFieldProps>(
  function DatePickerField({
    timeZone = "local",
    name = "date",
    rangeName = ["startDate", "endDate"],
    label = i18n.en.label,
    description = i18n.en.description,
    placeholder = i18n.en.placeholder,
    type = "single",
    numberOfMonths = 2,
    disabled = undefined, // Changed from default to undefined to allow selection of any date
    initialFocus = false,
    ...props
  }: DatePickerFieldProps) {
    const [calendarOpen, setCalendarOpen] = useState(false);
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => {
          let text: string;
          let value: Date | string | DateRange | undefined;

          if (type === "range") {
            const [startDate, endDate] = rangeName;
            const start = form.getValues(startDate) ?? field.value;
            const end = form.getValues(endDate);
            value = {
              from: start ? displayDateForTimeZone(start, timeZone) : undefined,
              to: end ? displayDateForTimeZone(end, timeZone) : undefined,
            };
            text =
              [
                value.from ? format(value.from, "PPP") : undefined,
                value.to ? format(value.to, "PPP") : undefined,
              ]
                .filter(Boolean)
                .join(" - ")
                .trim() || placeholder;
          } else if (field.value instanceof Date) {
            value = displayDateForTimeZone(field.value, timeZone);
            text = format(value as Date, "PPP");
          } else {
            text = placeholder;
            value = undefined;
          }

          return (
            <FormItem className="flex flex-col gap-1">
              <FormLabel>{label}</FormLabel>
              <FormDescription>{description}</FormDescription>
              <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
                <div className="flex w-full items-center justify-between gap-2">
                  <PopoverTrigger asChild className="flex-1">
                    <FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        className={cn(
                          "pl-3 text-left font-normal",

                          !field.value && "text-muted-foreground",
                        )}
                      >
                        <span>{text}</span>
                        <CalendarIcon className="ml-auto size-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                </div>
                <PopoverContent
                  className="w-auto p-0"
                  align="center"
                  sideOffset={8}
                  style={{ zIndex: 100 }}
                  usePortal={true}
                >
                  <Calendar
                    {...props}
                    mode={type as "range" | undefined}
                    selected={value as DateRange | undefined}
                    onSelect={(_value: DateRange | undefined) => {
                      const setOptions = {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true,
                      };

                      if (_value) {
                        if (type === "range") {
                          const [startDate, endDate] = rangeName;
                          const { from, to } = (_value ?? {}) as DateRange;

                          const start = from
                            ? setDateForTimeZone(from, timeZone)
                            : undefined;
                          const end = to
                            ? setDateForTimeZone(to, timeZone)
                            : undefined;

                          if (start)
                            form.setValue(startDate, start, setOptions);
                          if (end) form.setValue(endDate, end, setOptions);
                        } else {
                          const date: Date | undefined = _value
                            ? setDateForTimeZone(
                                _value as unknown as Date,
                                timeZone,
                              )
                            : undefined;

                          field.onChange(date);
                          setCalendarOpen(false);
                        }
                      }
                    }}
                    disabled={disabled}
                    initialFocus={initialFocus}
                    numberOfMonths={numberOfMonths}
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    );
  },
);

DatePickerField.displayName = "DatePickerField";
