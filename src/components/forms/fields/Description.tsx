import { useFormContext } from "react-hook-form";

import type { TextareaProps } from "@/components/ui/textarea";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";

const i18n = {
  en: {
    description: {
      label: "Description",
      description: "Detailed description",
      placeholder: "Enter a detailed description",
    },
  },
};

export interface DescriptionFieldProps extends Omit<TextareaProps, "name"> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  minLength?: number;
}

export function DescriptionField({
  name,
  label = i18n.en.description.label,
  description = i18n.en.description.description,
  placeholder = i18n.en.description.placeholder,
  minLength = 10,
  ...props
}: DescriptionFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      rules={{
        minLength: {
          value: minLength,
          message: `Description must be at least ${minLength} characters long`,
        },
      }}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <Textarea placeholder={placeholder} {...field} {...props} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
