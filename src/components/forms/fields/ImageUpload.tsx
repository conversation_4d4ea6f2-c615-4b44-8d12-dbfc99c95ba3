import type { FileUploadFieldProps } from "./FileUpload";

import { FileUploadField } from "./FileUpload";

const i18n = {
  en: {
    image: {
      label: "Image Upload",
      description: "Upload an image",
      validation: {
        dimensions: "Image dimensions must be at least {width}x{height} pixels",
        aspectRatio: "Image aspect ratio must be {ratio}",
      },
    },
  },
};

export interface ImageUploadFieldProps
  extends Omit<FileUploadFieldProps, "accept"> {
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  aspectRatio?: number;
  className?: string;
}

export function ImageUploadField({
  label = i18n.en.image.label,
  description = i18n.en.image.description,
  minWidth,
  minHeight,
  maxWidth,
  maxHeight,
  aspectRatio,
  ...props
}: ImageUploadFieldProps) {
  return (
    <FileUploadField
      {...props}
      label={label}
      description={description}
      accept={["image/jpeg", "image/png", "image/gif", "image/webp"]}
      showPreview={true}
      validate={{
        dimensions: (value: FileList | File) => {
          if (!value || (!minWidth && !minHeight && !maxWidth && !maxHeight))
            return true;
          const files = value instanceof FileList ? Array.from(value) : [value];

          return new Promise((resolve) => {
            let validFiles = 0;
            files.forEach((file) => {
              const img = new Image();
              img.onload = () => {
                const valid =
                  (!minWidth || img.width >= minWidth) &&
                  (!minHeight || img.height >= minHeight) &&
                  (!maxWidth || img.width <= maxWidth) &&
                  (!maxHeight || img.height <= maxHeight);

                if (!valid) {
                  resolve(
                    i18n.en.image.validation.dimensions
                      .replace("{width}", minWidth?.toString() ?? "any")
                      .replace("{height}", minHeight?.toString() ?? "any"),
                  );
                }

                validFiles++;
                if (validFiles === files.length) {
                  resolve(true);
                }
              };
              img.src = URL.createObjectURL(file);
            });
          });
        },
        aspectRatio: (value: FileList | File) => {
          if (!value || !aspectRatio) return true;
          const files = value instanceof FileList ? Array.from(value) : [value];

          return new Promise((resolve) => {
            let validFiles = 0;
            files.forEach((file) => {
              const img = new Image();
              img.onload = () => {
                const ratio = img.width / img.height;
                const valid = Math.abs(ratio - aspectRatio) < 0.01; // Allow small deviation

                if (!valid) {
                  resolve(
                    i18n.en.image.validation.aspectRatio.replace(
                      "{ratio}",
                      aspectRatio.toString(),
                    ),
                  );
                }

                validFiles++;
                if (validFiles === files.length) {
                  resolve(true);
                }
              };
              img.src = URL.createObjectURL(file);
            });
          });
        },
      }}
    />
  );
}
