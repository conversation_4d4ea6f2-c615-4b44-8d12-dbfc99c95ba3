import { forwardRef } from "react";
import { useFormContext } from "react-hook-form";

import type { TimePickerProps } from "@/components/ui/time-picker";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { TimePicker } from "@/components/ui/time-picker";
import { getTimeFromInteger, setTimeToInteger } from "@/lib/dates";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    label: "Time",
    description: "Time of the day",
  },
};

export interface TimePickerFieldProps extends TimePickerProps {
  label?: string;
  description?: string;
  className?: string;
}

export const TimePickerField = forwardRef<HTMLDivElement, TimePickerFieldProps>(
  function TimePickerField(
    {
      name = "time",
      label = i18n.en.label,
      description = i18n.en.description,
      className,
      ...props
    },
    ref,
  ) {
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name as "startTime" | "endTime"}
        render={({ field }) => (
          <FormItem className={cn("w-full", className)}>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <TimePicker
                ref={ref}
                {...props}
                {...field}
                value={field.value ? getTimeFromInteger(field.value) : null}
                onChange={(value) => {
                  field.onChange(setTimeToInteger(value));
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

TimePickerField.displayName = "TimePickerField";
