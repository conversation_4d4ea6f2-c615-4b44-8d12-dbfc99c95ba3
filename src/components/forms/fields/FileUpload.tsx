import { use<PERSON>allback, useRef } from "react";
import { Cross1Icon, FileIcon, UploadIcon } from "@radix-ui/react-icons";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/components/ui/input";

import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

export const Icons = {
  file: FileIcon,
  upload: UploadIcon,
  close: Cross1Icon,
} as const;

const i18n = {
  en: {
    file: {
      label: "File Upload",
      description: "Upload a file",
      placeholder: "Select a file or drag and drop",
      validation: {
        required: "Please select a file",
        size: "File size must be less than {size}MB",
        type: "File type must be one of: {types}",
      },
      button: {
        select: "Select File",
        change: "Change File",
        remove: "Remove",
      },
    },
  },
};

export type FileUploadValidation = Record<
  string,
  (value: FileList | File) => boolean | string | Promise<boolean | string>
>;

export interface FileUploadFieldProps
  extends Omit<InputProps, "type" | "value" | "onChange" | "accept"> {
  name: string;
  label?: string;
  description?: string;
  maxSize?: number; // in MB
  accept?: string[];
  multiple?: boolean;
  showPreview?: boolean;
  validate?: FileUploadValidation;
}

function formatFileSize(bytes: number) {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

export function FileUploadField({
  name,
  label,
  description,
  maxSize = 10, // Default to 10MB
  accept,
  multiple = false,
  showPreview = true,
  validate,
  className,
  disabled,
  required,
  ...props
}: FileUploadFieldProps) {
  const form = useFormContext();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const onDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();

      if (disabled) return;

      const files = Array.from(event.dataTransfer.files);
      if (!multiple && files.length > 1) {
        files.length = 1;
      }

      form.setValue(name, multiple ? files : files[0], {
        shouldValidate: true,
      });
    },
    [form, name, multiple, disabled],
  );

  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  const handleSelectButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    fileInputRef.current?.click();
  };

  return (
    <FormField
      control={form.control}
      name={name}
      rules={{
        required: required && i18n.en.file.validation.required,
        validate: {
          size: (value: FileList | File) => {
            if (!value) return true;
            const files =
              value instanceof FileList ? Array.from(value) : [value];
            return files.every(
              (file) =>
                file.size <= maxSize * 1024 * 1024 ||
                i18n.en.file.validation.size.replace(
                  "{size}",
                  maxSize.toString(),
                ),
            );
          },
          type: (value: FileList | File) => {
            if (!value || !accept?.length) return true;
            const files =
              value instanceof FileList ? Array.from(value) : [value];
            return files.every(
              (file) =>
                accept.some((type) => file.type.startsWith(type)) ||
                i18n.en.file.validation.type.replace(
                  "{types}",
                  accept.join(", "),
                ),
            );
          },
          ...validate,
        },
      }}
      render={({ field }) => {
        const files =
          field.value instanceof FileList
            ? Array.from(field.value)
            : field.value
              ? [field.value]
              : [];
        const hasFiles = files.length > 0;

        return (
          <FormItem className={className}>
            {label && <FormLabel>{label}</FormLabel>}
            {description && <FormDescription>{description}</FormDescription>}
            <FormControl>
              <div
                className={cn(
                  "border-muted-foreground/25 hover:bg-muted/25 relative flex min-h-[150px] cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed px-6 py-4 text-center transition-colors",
                  disabled && "cursor-not-allowed opacity-60",
                )}
                onDrop={onDrop}
                onDragOver={onDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <Input
                  {...props}
                  ref={fileInputRef}
                  className="absolute inset-0 cursor-pointer opacity-0"
                  type="file"
                  accept={accept?.join(",")}
                  multiple={multiple}
                  disabled={disabled}
                  onChange={(e) => {
                    const files = e.target.files;
                    if (!files || files.length === 0) return;
                    field.onChange(multiple ? files : files[0]);
                  }}
                />

                <div className="flex flex-col items-center gap-2">
                  <Icons.upload className="text-muted-foreground h-8 w-8" />
                  {hasFiles ? (
                    <div className="space-y-2">
                      {files.map((file, index) => (
                        <div
                          key={index}
                          className="bg-muted flex items-center gap-2 rounded-md p-2 text-sm"
                        >
                          <Icons.file className="h-4 w-4" />
                          <span className="flex-1 truncate">{file.name}</span>
                          <span className="text-muted-foreground text-xs">
                            {formatFileSize(file.size)}
                          </span>
                          {showPreview && file.type.startsWith("image/") && (
                            <img
                              src={URL.createObjectURL(file)}
                              alt={file.name}
                              className="h-8 w-8 rounded object-cover"
                            />
                          )}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0 hover:bg-transparent"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              if (multiple) {
                                const newFiles = files.filter(
                                  (_, i) => i !== index,
                                );
                                field.onChange(
                                  newFiles.length > 0
                                    ? Object.assign(
                                        new DataTransfer().files,
                                        newFiles.map((f, i) => [i, f]),
                                      )
                                    : null,
                                );
                              } else {
                                field.onChange(null);
                              }
                            }}
                          >
                            <Icons.close className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <>
                      <div className="text-sm font-medium">
                        {i18n.en.file.placeholder}
                      </div>
                      <Button
                        type="button"
                        variant="secondary"
                        size="sm"
                        onClick={handleSelectButtonClick}
                      >
                        {i18n.en.file.button.select}
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
