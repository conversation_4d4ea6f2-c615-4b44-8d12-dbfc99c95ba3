import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const i18n = {
  en: {
    industry: {
      label: "Industry",
      description: "The primary industry of your organization",
      placeholder: "Select industry",
      options: {
        agriculture: "Agriculture",
        automotive: "Automotive",
        construction: "Construction",
        consulting: "Consulting",
        education: "Education",
        energy: "Energy & Utilities",
        entertainment: "Entertainment & Media",
        finance: "Finance & Banking",
        food: "Food & Beverage",
        government: "Government",
        healthcare: "Healthcare",
        hospitality: "Hospitality & Tourism",
        insurance: "Insurance",
        logistics: "Logistics & Transportation",
        manufacturing: "Manufacturing",
        mining: "Mining & Resources",
        non_profit: "Non-Profit",
        pharmaceuticals: "Pharmaceuticals",
        real_estate: "Real Estate",
        retail: "Retail",
        technology: "Technology & IT",
        telecommunications: "Telecommunications",
        other: "Other",
      },
    },
  },
};

// eslint-disable-next-line react-refresh/only-export-components
export const COMPANY_INDUSTRIES = [
  "agriculture",
  "automotive",
  "construction",
  "consulting",
  "education",
  "energy",
  "entertainment",
  "finance",
  "food",
  "government",
  "healthcare",
  "hospitality",
  "insurance",
  "logistics",
  "manufacturing",
  "mining",
  "non_profit",
  "pharmaceuticals",
  "real_estate",
  "retail",
  "technology",
  "telecommunications",
  "other",
] as const;

export type CompanyIndustry = (typeof COMPANY_INDUSTRIES)[number];

export interface CompanyIndustrySelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function CompanyIndustrySelect({
  placeholder = i18n.en.industry.placeholder,
  value,
  onChange,
  ...props
}: CompanyIndustrySelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {COMPANY_INDUSTRIES.map((industry) => (
          <SelectItem key={industry} value={industry}>
            {i18n.en.industry.options[industry]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface CompanyIndustryFieldProps extends CompanyIndustrySelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function CompanyIndustryField({
  name = "industry",
  label = i18n.en.industry.label,
  description = i18n.en.industry.description,
  placeholder = i18n.en.industry.placeholder,
  ...props
}: CompanyIndustryFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <CompanyIndustrySelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
