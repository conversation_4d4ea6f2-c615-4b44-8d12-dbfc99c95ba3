import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const i18n = {
  en: {
    size: {
      label: "Company Size",
      description: "The number of employees in the company",
      placeholder: "Select company size",
      options: {
        "1-10": "1-10 employees",
        "11-50": "11-50 employees",
        "51-200": "51-200 employees",
        "201-500": "201-500 employees",
        "501-1000": "501-1000 employees",
        "1001+": "1001+ employees",
      },
    },
  },
};

// eslint-disable-next-line react-refresh/only-export-components
export const COMPANY_SIZES = [
  "1-10",
  "11-50",
  "51-200",
  "201-500",
  "501-1000",
  "1001+",
] as const;

export type CompanySize = (typeof COMPANY_SIZES)[number];

export interface CompanySizeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function CompanySizeSelect({
  placeholder = i18n.en.size.placeholder,
  value,
  onChange,
  ...props
}: CompanySizeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {COMPANY_SIZES.map((size) => (
          <SelectItem key={size} value={size}>
            {i18n.en.size.options[size]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface CompanySizeFieldProps extends CompanySizeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function CompanySizeField({
  name = "size",
  label = i18n.en.size.label,
  description = i18n.en.size.description,
  placeholder = i18n.en.size.placeholder,
  ...props
}: CompanySizeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <CompanySizeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
