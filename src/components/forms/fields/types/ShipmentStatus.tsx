import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    status: {
      label: "Status",
      description: "The status of the shipment",
      placeholder: "Select the shipment status",
      options: {
        pending: "Pending",
        scheduled: "Scheduled",
        assigned: "Assigned",
        confirmed: "Confirmed",
        in_progress: "In Progress",
        completed: "Completed",
        cancelled: "Cancelled",
      },
    },
  },
};

export const SHIPMENT_STATUSES = [
  "pending",
  "scheduled",
  "assigned",
  "confirmed",
  "in_progress",
  "completed",
  "cancelled",
] as const as Enums<"shipment_status">[];

export interface ShipmentStatusSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function ShipmentStatusSelect({
  placeholder = i18n.en.status.placeholder,
  value,
  onChange,
  ...props
}: ShipmentStatusSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {SHIPMENT_STATUSES.map((status) => (
          <SelectItem key={status} value={status}>
            {i18n.en.status.options[status]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface ShipmentStatusFieldProps extends ShipmentStatusSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function ShipmentStatusField({
  name,
  label = i18n.en.status.label,
  description = i18n.en.status.description,
  placeholder = i18n.en.status.placeholder,
  ...props
}: ShipmentStatusFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <ShipmentStatusSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
