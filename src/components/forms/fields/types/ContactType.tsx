import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Contact Type",
      description: "The type of contact",
      placeholder: "Select the contact type",
      options: {
        billing: "Billing",
        manager: "Manager",
        dispatcher: "Dispatcher",
        safety_officer: "Safety Officer",
        maintenance: "Maintenance",
        warehouse: "Warehouse",
        customs: "Customs",
        logistics: "Logistics",
        sales: "Sales",
        support: "Support",
        other: "Other",
      },
    },
  },
};

export const CONTACT_TYPES = [
  "billing",
  "manager",
  "dispatcher",
  "safety_officer",
  "maintenance",
  "warehouse",
  "customs",
  "logistics",
  "sales",
  "support",
  "other",
] as const as Enums<"contact_type">[];

export interface ContactTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function ContactTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: ContactTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {CONTACT_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface ContactTypeFieldProps extends ContactTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function ContactTypeField({
  name,
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: ContactTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <ContactTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
