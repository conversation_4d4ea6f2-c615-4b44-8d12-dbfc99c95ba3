import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Contract Type",
      description: "The type of contract",
      placeholder: "Select the contract type",
      options: {
        bill_of_lading: "Bill of Lading",
        agreement: "Agreement",
        service_agreement: "Service Agreement",
        lease_agreement: "Lease Agreement",
        insurance_certificate: "Insurance Certificate",
        power_of_attorney: "Power of Attorney",
        other: "Other",
      },
    },
  },
};

export const CONTRACT_TYPES = [
  "bill_of_lading",
  "agreement",
  "service_agreement",
  "lease_agreement",
  "insurance_certificate",
  "power_of_attorney",
  "other",
] as const as Enums<"contract_type">[];

export interface ContractTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function ContractTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: ContractTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {CONTRACT_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface ContractTypeFieldProps extends ContractTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function ContractTypeField({
  name,
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: ContractTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <ContractTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
