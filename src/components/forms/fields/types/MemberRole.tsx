import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    role: {
      label: "Role",
      description: "The role for the member",
      placeholder: "Select the role for the member",
      options: {
        admin: "Admin",
        billing: "Billing",
        member: "Member",
        viewer: "Viewer",
      },
    },
  },
};

export const ROLES = [
  "admin",
  "billing",
  "member",
  "viewer",
] as const as Enums<"members_role">[];

export interface MemberRoleSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function MemberRoleSelect({
  placeholder = i18n.en.role.placeholder,
  value,
  onChange,
  ...props
}: MemberRoleSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {ROLES.map((role) => (
          <SelectItem key={role} value={role}>
            {i18n.en.role.options[role]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface MemberRoleFieldProps extends MemberRoleSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function MemberRoleField({
  name,
  label = i18n.en.role.label,
  description = i18n.en.role.description,
  placeholder = i18n.en.role.placeholder,
  ...props
}: MemberRoleFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <MemberRoleSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
