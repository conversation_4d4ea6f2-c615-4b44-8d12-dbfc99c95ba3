import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    severity: {
      label: "Incident Severity",
      description: "The severity level of the incident",
      placeholder: "Select the incident severity",
      options: {
        low: "Low",
        medium: "Medium",
        high: "High",
        critical: "Critical",
      },
    },
  },
};

export const INCIDENT_SEVERITIES = [
  "low",
  "medium",
  "high",
  "critical",
] as const as Enums<"incident_severity">[];

export interface IncidentSeveritySelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function IncidentSeveritySelect({
  placeholder = i18n.en.severity.placeholder,
  value,
  onChange,
  ...props
}: IncidentSeveritySelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {INCIDENT_SEVERITIES.map((severity) => (
          <SelectItem key={severity} value={severity}>
            {i18n.en.severity.options[severity]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface IncidentSeverityFieldProps
  extends IncidentSeveritySelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function IncidentSeverityField({
  name,
  label = i18n.en.severity.label,
  description = i18n.en.severity.description,
  placeholder = i18n.en.severity.placeholder,
  ...props
}: IncidentSeverityFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <IncidentSeveritySelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
