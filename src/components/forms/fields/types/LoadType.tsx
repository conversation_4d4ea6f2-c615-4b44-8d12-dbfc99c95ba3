import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Load Type",
      description: "The type of load",
      placeholder: "Select the load type",
      options: {
        general: "General",
        bulk_dry: "Bulk Dry",
        bulk_liquid: "Bulk Liquid",
        container_20ft: "20ft Container",
        container_40ft: "40ft Container",
        container_reefer: "Reefer Container",
        container_flat_rack: "Flat Rack Container",
        container_open_top: "Open Top Container",
        container_tank: "Tank Container",
        breakbulk: "Breakbulk",
        ro_ro: "Roll-on/Roll-off",
        heavy_lift: "Heavy Lift",
        project_cargo: "Project Cargo",
        dangerous_goods: "Dangerous Goods",
        temperature_controlled: "Temperature Controlled",
        livestock: "Livestock",
        vehicles: "Vehicles",
        machinery: "Machinery",
        perishables: "Perishables",
        valuables: "Valuables",
        other: "Other",
      },
    },
  },
};

export const LOAD_TYPES = [
  "general",
  "bulk_dry",
  "bulk_liquid",
  "container_20ft",
  "container_40ft",
  "container_reefer",
  "container_flat_rack",
  "container_open_top",
  "container_tank",
  "breakbulk",
  "ro_ro",
  "heavy_lift",
  "project_cargo",
  "dangerous_goods",
  "temperature_controlled",
  "livestock",
  "vehicles",
  "machinery",
  "perishables",
  "valuables",
  "other",
] as const as Enums<"load_type">[];

export interface LoadTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function LoadTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: LoadTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {LOAD_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface LoadTypeFieldProps extends LoadTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function LoadTypeField({
  name = "type",
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: LoadTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <LoadTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
