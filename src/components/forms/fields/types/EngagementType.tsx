import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Engagement Type",
      description: "The type of engagement",
      placeholder: "Select the engagement type",
      options: {
        driver_request: "Driver Request",
        organization_offer: "Organization Offer",
      },
    },
  },
};

export const ENGAGEMENT_TYPES = [
  "driver_request",
  "organization_offer",
] as const as Enums<"engagement_type">[];

export interface EngagementTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function EngagementTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: EngagementTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {ENGAGEMENT_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface EngagementTypeFieldProps extends EngagementTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function EngagementTypeField({
  name,
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: EngagementTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <EngagementTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
