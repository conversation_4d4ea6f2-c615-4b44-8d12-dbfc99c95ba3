import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    mode: {
      label: "Shipment Mode",
      description: "The mode of the shipment",
      placeholder: "Select the shipment mode",
      options: {
        open: "Open",
        closed: "Closed",
      },
    },
  },
};

export const SHIPMENT_MODES = [
  "open",
  "closed",
] as const as Enums<"shipment_mode">[];

export interface ShipmentModeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function ShipmentModeSelect({
  placeholder = i18n.en.mode.placeholder,
  value,
  onChange,
  ...props
}: ShipmentModeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {SHIPMENT_MODES.map((mode) => (
          <SelectItem key={mode} value={mode}>
            {i18n.en.mode.options[mode]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface ShipmentModeFieldProps extends ShipmentModeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function ShipmentModeField({
  name,
  label = i18n.en.mode.label,
  description = i18n.en.mode.description,
  placeholder = i18n.en.mode.placeholder,
  ...props
}: ShipmentModeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <ShipmentModeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
