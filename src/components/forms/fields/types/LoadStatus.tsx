import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    status: {
      label: "Status",
      description: "The status of the load",
      placeholder: "Select the load status",
      options: {
        pending: "Pending",
        packaged: "Packaged",
        loaded: "Loaded",
        in_transit: "In Transit",
        delivered: "Delivered",
        missing: "Missing",
        damaged: "Damaged",
        rejected: "Rejected",
        returned: "Returned",
        customs_hold: "Customs Hold",
      },
    },
  },
};

export const LOAD_STATUSES = [
  "pending",
  "packaged",
  "loaded",
  "in_transit",
  "delivered",
  "missing",
  "damaged",
  "rejected",
  "returned",
  "customs_hold",
] as const as Enums<"load_status">[];

export interface LoadStatusSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function LoadStatusSelect({
  placeholder = i18n.en.status.placeholder,
  value,
  onChange,
  ...props
}: LoadStatusSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {LOAD_STATUSES.map((status) => (
          <SelectItem key={status} value={status}>
            {i18n.en.status.options[status]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface LoadStatusFieldProps extends LoadStatusSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function LoadStatusField({
  name,
  label = i18n.en.status.label,
  description = i18n.en.status.description,
  placeholder = i18n.en.status.placeholder,
  ...props
}: LoadStatusFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <LoadStatusSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
