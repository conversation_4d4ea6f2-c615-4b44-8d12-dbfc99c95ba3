import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    status: {
      label: "Status",
      description: "The status of the member",
      placeholder: "Select the member status",
      options: {
        pending: "Pending",
        active: "Active",
        inactive: "Inactive",
        declined: "Declined",
      },
    },
  },
};

export const MEMBER_STATUSES = [
  "pending",
  "active",
  "inactive",
  "declined",
] as const as Enums<"member_status">[];

export interface MemberStatusSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function MemberStatusSelect({
  placeholder = i18n.en.status.placeholder,
  value,
  onChange,
  ...props
}: MemberStatusSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {MEMBER_STATUSES.map((status) => (
          <SelectItem key={status} value={status}>
            {i18n.en.status.options[status]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface MemberStatusFieldProps extends MemberStatusSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function MemberStatusField({
  name,
  label = i18n.en.status.label,
  description = i18n.en.status.description,
  placeholder = i18n.en.status.placeholder,
  ...props
}: MemberStatusFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <MemberStatusSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
