import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Location Type",
      description: "The type of location",
      placeholder: "Select the location type",
      options: {
        billing: "Billing",
        commercial: "Commercial",
        industrial: "Industrial",
        government: "Government",
        public: "Public",
        residential: "Residential",
        warehouse: "Warehouse",
        distribution_center: "Distribution Center",
        retail: "Retail",
        other: "Other",
      },
    },
  },
};

export const LOCATION_TYPES = [
  "billing",
  "commercial",
  "industrial",
  "government",
  "public",
  "residential",
  "warehouse",
  "distribution_center",
  "retail",
  "other",
] as const as Enums<"location_type">[];

export interface LocationTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function LocationTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: LocationTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {LOCATION_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface LocationTypeFieldProps extends LocationTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function LocationTypeField({
  name,
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: LocationTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <LocationTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
