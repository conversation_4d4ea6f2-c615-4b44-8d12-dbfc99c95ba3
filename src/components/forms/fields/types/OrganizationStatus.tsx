import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    status: {
      label: "Status",
      description: "The status of the organization",
      placeholder: "Select the organization status",
      options: {
        pending: "Pending",
        active: "Active",
        suspended: "Suspended",
        inactive: "Inactive",
      },
    },
  },
};

export const ORGANIZATION_STATUSES = [
  "pending",
  "active",
  "suspended",
  "inactive",
] as const as Enums<"organization_status">[];

export interface OrganizationStatusSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function OrganizationStatusSelect({
  placeholder = i18n.en.status.placeholder,
  value,
  onChange,
  ...props
}: OrganizationStatusSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {ORGANIZATION_STATUSES.map((status) => (
          <SelectItem key={status} value={status}>
            {i18n.en.status.options[status]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface OrganizationStatusFieldProps
  extends OrganizationStatusSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function OrganizationStatusField({
  name,
  label = i18n.en.status.label,
  description = i18n.en.status.description,
  placeholder = i18n.en.status.placeholder,
  ...props
}: OrganizationStatusFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <OrganizationStatusSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
