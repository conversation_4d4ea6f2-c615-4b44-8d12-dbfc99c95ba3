import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    source: {
      label: "Shipment Source",
      description: "The source of the shipment",
      placeholder: "Select the shipment source",
      options: {
        driver: "Driver",
        organization: "Organization",
        system: "System",
      },
    },
  },
};

export const SHIPMENT_SOURCES = [
  "driver",
  "organization",
  "system",
] as const as Enums<"shipment_source">[];

export interface ShipmentSourceSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function ShipmentSourceSelect({
  placeholder = i18n.en.source.placeholder,
  value,
  onChange,
  ...props
}: ShipmentSourceSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {SHIPMENT_SOURCES.map((source) => (
          <SelectItem key={source} value={source}>
            {i18n.en.source.options[source]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface ShipmentSourceFieldProps extends ShipmentSourceSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function ShipmentSourceField({
  name,
  label = i18n.en.source.label,
  description = i18n.en.source.description,
  placeholder = i18n.en.source.placeholder,
  ...props
}: ShipmentSourceFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <ShipmentSourceSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
