import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Manifest Type",
      description: "The type of manifest",
      placeholder: "Select the manifest type",
      options: {
        cargo: "Cargo",
        container: "Container",
        out_of_gauge: "Out of Gauge",
        customs: "Customs",
      },
    },
  },
};

export const MANIFEST_TYPES = [
  "cargo",
  "container",
  "out_of_gauge",
  "customs",
] as const as Enums<"manifest_type">[];

export interface ManifestTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function ManifestTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: ManifestTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {MANIFEST_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface ManifestTypeFieldProps extends ManifestTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function ManifestTypeField({
  name,
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: ManifestTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <ManifestTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
