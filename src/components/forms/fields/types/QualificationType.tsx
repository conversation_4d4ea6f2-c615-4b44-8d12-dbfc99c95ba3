import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Qualification Type",
      description: "The type of qualification",
      placeholder: "Select the qualification type",
      options: {
        commercial_drivers_license: "Commercial Driver's License",
        hazmat_endorsement: "HAZMAT Endorsement",
        medical_certificate: "Medical Certificate",
        defensive_driving_certificate: "Defensive Driving Certificate",
        tanker_endorsement: "Tanker Endorsement",
        doubles_triples_endorsement: "Doubles/Triples Endorsement",
        other: "Other",
      },
    },
  },
};

export const QUALIFICATION_TYPES = [
  "commercial_drivers_license",
  "hazmat_endorsement",
  "medical_certificate",
  "defensive_driving_certificate",
  "tanker_endorsement",
  "doubles_triples_endorsement",
  "other",
] as const as Enums<"qualification_type">[];

export interface QualificationTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function QualificationTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: QualificationTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {QUALIFICATION_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface QualificationTypeFieldProps
  extends QualificationTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function QualificationTypeField({
  name,
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: QualificationTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <QualificationTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
