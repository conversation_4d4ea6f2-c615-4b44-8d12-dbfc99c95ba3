import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    status: {
      label: "Status",
      description: "The status of the incident",
      placeholder: "Select the incident status",
      options: {
        reported: "Reported",
        investigating: "Investigating",
        resolved: "Resolved",
        closed: "Closed",
      },
    },
  },
};

export const INCIDENT_STATUSES = [
  "reported",
  "investigating",
  "resolved",
  "closed",
] as const as Enums<"incident_status">[];

export interface IncidentStatusSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function IncidentStatusSelect({
  placeholder = i18n.en.status.placeholder,
  value,
  onChange,
  ...props
}: IncidentStatusSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {INCIDENT_STATUSES.map((status) => (
          <SelectItem key={status} value={status}>
            {i18n.en.status.options[status]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface IncidentStatusFieldProps extends IncidentStatusSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function IncidentStatusField({
  name,
  label = i18n.en.status.label,
  description = i18n.en.status.description,
  placeholder = i18n.en.status.placeholder,
  ...props
}: IncidentStatusFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <IncidentStatusSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
