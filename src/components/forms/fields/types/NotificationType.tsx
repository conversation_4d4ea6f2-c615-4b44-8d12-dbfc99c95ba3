import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    type: {
      label: "Notification Type",
      description: "The type of notification",
      placeholder: "Select the notification type",
      options: {
        shipment_update: "Shipment Update",
        incident_report: "Incident Report",
        system_alert: "System Alert",
        payment_update: "Payment Update",
      },
    },
  },
};

export const NOTIFICATION_TYPES = [
  "shipment_update",
  "incident_report",
  "system_alert",
  "payment_update",
] as const as Enums<"notification_type">[];

export interface NotificationTypeSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function NotificationTypeSelect({
  placeholder = i18n.en.type.placeholder,
  value,
  onChange,
  ...props
}: NotificationTypeSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {NOTIFICATION_TYPES.map((type) => (
          <SelectItem key={type} value={type}>
            {i18n.en.type.options[type]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface NotificationTypeFieldProps
  extends NotificationTypeSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function NotificationTypeField({
  name,
  label = i18n.en.type.label,
  description = i18n.en.type.description,
  placeholder = i18n.en.type.placeholder,
  ...props
}: NotificationTypeFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <NotificationTypeSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
