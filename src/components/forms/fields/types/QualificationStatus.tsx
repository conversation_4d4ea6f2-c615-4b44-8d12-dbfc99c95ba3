import { useFormContext } from "react-hook-form";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Enums } from "@/supabase/types";

const i18n = {
  en: {
    status: {
      label: "Status",
      description: "The status of the qualification",
      placeholder: "Select the qualification status",
      options: {
        pending: "Pending",
        verified: "Verified",
        expired: "Expired",
        revoked: "Revoked",
      },
    },
  },
};

export const QUALIFICATION_STATUSES = [
  "pending",
  "verified",
  "expired",
  "revoked",
] as const as Enums<"qualification_status">[];

export interface QualificationStatusSelectProps extends SelectProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export function QualificationStatusSelect({
  placeholder = i18n.en.status.placeholder,
  value,
  onChange,
  ...props
}: QualificationStatusSelectProps) {
  return (
    <Select value={value} onValueChange={onChange} {...props}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {QUALIFICATION_STATUSES.map((status) => (
          <SelectItem key={status} value={status}>
            {i18n.en.status.options[status]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export interface QualificationStatusFieldProps
  extends QualificationStatusSelectProps {
  name?: string;
  label?: string;
  description?: string;
}

export function QualificationStatusField({
  name,
  label = i18n.en.status.label,
  description = i18n.en.status.description,
  placeholder = i18n.en.status.placeholder,
  ...props
}: QualificationStatusFieldProps) {
  const form = useFormContext();
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <QualificationStatusSelect
              {...props}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
