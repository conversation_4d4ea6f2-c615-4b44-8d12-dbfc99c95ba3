import type { SelectFieldProps } from "@/components/forms/fields/Select";
import type { Enums } from "@/supabase/types";

import { SelectField } from "@/components/forms/fields/Select";

// eslint-disable-next-line react-refresh/only-export-components
export const STOP_TYPES = [
  "origin",
  "destination",
  "pickup",
  "dropoff",
  "rest",
  "gas",
  "maintenance",
  "customs",
  "weigh_station",
  "other",
] as const as Enums<"stop_type">[];

export type StopTypeFieldProps = Omit<SelectFieldProps, "options">;

export function StopTypeField(props: StopTypeFieldProps) {
  return (
    <SelectField
      {...props}
      options={STOP_TYPES.map((type) => ({
        label: type
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" "),
        value: type,
      }))}
    />
  );
}
