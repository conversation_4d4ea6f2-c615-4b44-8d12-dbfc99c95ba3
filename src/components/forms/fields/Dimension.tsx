import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/components/ui/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const i18n = {
  en: {
    dimension: {
      label: "Dimension",
      description: "Enter the dimension value",
      placeholder: "Enter dimension",
      validation: {
        type: "Please enter a valid dimension",
        min: "Dimension must be at least {min} {unit}",
        max: "Dimension must be at most {max} {unit}",
      },
      units: {
        mm: "Millimeters (mm)",
        cm: "Centimeters (cm)",
        m: "Meters (m)",
        in: "Inches (in)",
        ft: "Feet (ft)",
      },
    },
  },
};

export type DimensionUnit = keyof typeof i18n.en.dimension.units;
// eslint-disable-next-line react-refresh/only-export-components
export const DIMENSION_UNITS = ["mm", "cm", "m", "in", "ft"] as const;

const UNIT_CONVERSIONS = {
  mm: { to: { cm: 0.1, m: 0.001, in: 0.0393701, ft: 0.00328084 } },
  cm: { to: { mm: 10, m: 0.01, in: 0.393701, ft: 0.0328084 } },
  m: { to: { mm: 1000, cm: 100, in: 39.3701, ft: 3.28084 } },
  in: { to: { mm: 25.4, cm: 2.54, m: 0.0254, ft: 0.0833333 } },
  ft: { to: { mm: 304.8, cm: 30.48, m: 0.3048, in: 12 } },
};

export interface DimensionFieldProps extends Omit<InputProps, "name" | "type"> {
  name?: string;
  unitName?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  min?: number;
  max?: number;
  defaultUnit?: DimensionUnit;
  allowedUnits?: DimensionUnit[];
}

export function DimensionField({
  name,
  unitName,
  label = i18n.en.dimension.label,
  description = i18n.en.dimension.description,
  placeholder = i18n.en.dimension.placeholder,
  min = 0,
  max,
  defaultUnit = "cm",
  allowedUnits = DIMENSION_UNITS as unknown as DimensionUnit[],
  ...props
}: DimensionFieldProps) {
  const form = useFormContext();

  const convertDimension = (
    value: number,
    from: DimensionUnit,
    to: DimensionUnit,
  ) => {
    if (from === to) return value;
    return value * UNIT_CONVERSIONS[from].to[to];
  };

  return (
    <div className="space-y-2">
      <FormField
        control={form.control}
        name={name}
        rules={{
          validate: {
            isNumber: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return !isNaN(num) || i18n.en.dimension.validation.type;
            },
            isPositive: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return num >= 0 || "Dimension must be a positive number";
            },
            minValue: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              const unit = form.watch(unitName) || defaultUnit;
              const minInUnit = convertDimension(min, "m", unit);
              return (
                num >= minInUnit ||
                i18n.en.dimension.validation.min
                  .replace("{min}", minInUnit.toFixed(2))
                  .replace("{unit}", unit)
              );
            },
            maxValue: (value) => {
              if (value === undefined || value === "" || max === undefined)
                return true;
              const num = Number(value);
              const unit = form.watch(unitName) || defaultUnit;
              const maxInUnit = convertDimension(max, "m", unit);
              return (
                num <= maxInUnit ||
                i18n.en.dimension.validation.max
                  .replace("{max}", maxInUnit.toFixed(2))
                  .replace("{unit}", unit)
              );
            },
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <div className="flex gap-2">
              <FormControl>
                <Input
                  type="number"
                  inputMode="decimal"
                  step="0.01"
                  min={0}
                  placeholder={placeholder}
                  className="w-[180px]"
                  {...field}
                  {...props}
                  onChange={(e) => {
                    if (e.target.value === "") {
                      field.onChange("");
                      return;
                    }

                    const value = parseFloat(e.target.value);
                    if (!isNaN(value)) {
                      field.onChange(value.toString());
                    }
                  }}
                />
              </FormControl>
              {unitName && (
                <FormField
                  control={form.control}
                  name={unitName}
                  defaultValue={defaultUnit}
                  render={({ field: unitField }) => (
                    <Select
                      value={unitField.value}
                      onValueChange={(value: DimensionUnit) => {
                        const oldUnit = unitField.value as DimensionUnit;
                        const newUnit = value as DimensionUnit;
                        const currentValue = field.value;

                        // Convert the current value to the new unit
                        if (currentValue !== "") {
                          const converted = convertDimension(
                            parseFloat(currentValue),
                            oldUnit,
                            newUnit,
                          );
                          field.onChange(converted.toFixed(3));
                        }

                        unitField.onChange(value);
                      }}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[130px]">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allowedUnits.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {i18n.en.dimension.units[unit]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              )}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
