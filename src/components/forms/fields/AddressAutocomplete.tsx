import { forwardRef, useCallback, useEffect, useRef, useState } from "react";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { useFormContext } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useAddressAutocomplete } from "@/hooks/use-address-autocomplete";
import { MapboxSuggestion } from "@/lib/mapbox";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    label: "Address",
    description: "The address",
    placeholder: "Search address...",
  },
};

export type AddressOption = MapboxSuggestion;

interface AddressAutocompleteProps {
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  value?: string;
  selection?: AddressOption;
  onChange?: (value: string | undefined) => void;
  onSelect?: (value: AddressOption | undefined) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

export const AddressAutocomplete = forwardRef<
  HTMLInputElement,
  AddressAutocompleteProps
>(
  (
    {
      disabled = false,
      placeholder = i18n.en.placeholder,
      className,
      // value,
      selection,
      onChange,
      onSelect,
      onFocus,
      onBlur,
    }: AddressAutocompleteProps,
    ref,
  ) => {
    const [searchTerm, setSearchTerm] = useState("");
    const {
      suggestions = [],
      loading,
      getSuggestions,
      clearSuggestions,
    } = useAddressAutocomplete();
    const searchTimeout = useRef<NodeJS.Timeout>();

    const handleChange = useCallback(
      (value: string | undefined) => {
        onChange?.(value);
        setSearchTerm(value || "");
      },
      [onChange],
    );

    const handleSelect = useCallback(
      (id: string) => {
        const option = suggestions.find((option) => option.mapbox_id === id);
        onSelect?.(option);
        onChange?.(option?.formatted);
        setSearchTerm(option?.formatted || "");
      },
      [onSelect, onChange, suggestions],
    );

    useEffect(() => {
      if (searchTerm.length >= 3) {
        if (searchTimeout.current) {
          clearTimeout(searchTimeout.current);
        }
        searchTimeout.current = setTimeout(() => {
          getSuggestions(searchTerm);
        }, 300);
      } else {
        clearSuggestions();
      }

      return () => {
        if (searchTimeout.current) {
          clearTimeout(searchTimeout.current);
        }
      };
    }, [searchTerm, getSuggestions, clearSuggestions]);

    return (
      <Command shouldFilter={false}>
        <CommandInput
          className={cn(className)}
          placeholder={placeholder}
          value={searchTerm}
          ref={ref}
          disabled={disabled}
          onFocus={onFocus}
          onBlur={onBlur}
          onValueChange={handleChange}
        />
        <CommandList>
          {loading && (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-4 w-4 animate-spin" />
            </div>
          )}
          <CommandEmpty>No address found.</CommandEmpty>
          <CommandGroup>
            {suggestions?.map((option) => (
              <CommandItem
                key={option.mapbox_id}
                value={option.mapbox_id}
                onSelect={handleSelect}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selection?.mapbox_id === option.mapbox_id
                      ? "opacity-100"
                      : "opacity-0",
                  )}
                />
                {option.formatted}
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </Command>
    );
  },
);

AddressAutocomplete.displayName = "AddressAutocomplete";

export const AddressAutocompletePopover = forwardRef<
  HTMLInputElement,
  AddressAutocompleteProps
>(
  (
    {
      disabled,
      placeholder,
      className,
      value,
      selection,
      onChange,
      onSelect,
      onFocus,
      onBlur,
    }: AddressAutocompleteProps,
    ref,
  ) => {
    const [open, setOpen] = useState(false);

    const handleSelect = useCallback(
      (option: AddressOption | undefined) => {
        onSelect?.(option);
        onChange?.(option?.formatted);
        setOpen(false);
      },
      [onSelect, onChange],
    );

    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            {selection?.formatted || value || placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0">
          <AddressAutocomplete
            ref={ref}
            disabled={disabled}
            placeholder={placeholder}
            className={className}
            value={value}
            selection={selection}
            onChange={onChange}
            onSelect={handleSelect}
            onFocus={onFocus}
            onBlur={onBlur}
          />
        </PopoverContent>
      </Popover>
    );
  },
);

AddressAutocompletePopover.displayName = "AddressAutocompletePopover";

export interface AddressAutocompleteFieldProps {
  name?: string;
  label?: string;
  description?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export const AddressAutocompleteField = forwardRef<
  HTMLInputElement,
  AddressAutocompleteFieldProps
>(
  (
    {
      name = "address",
      label = i18n.en.label,
      description = i18n.en.description,
      placeholder = i18n.en.placeholder,
      className,
      disabled,
    },
    ref,
  ) => {
    const { control } = useFormContext();

    return (
      <FormField
        control={control}
        name={name as "address"}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            {description && <FormDescription>{description}</FormDescription>}
            <FormControl>
              <AddressAutocompletePopover
                value={field.value}
                onChange={field.onChange}
                onSelect={field.onChange}
                onBlur={field.onBlur}
                placeholder={placeholder}
                disabled={disabled}
                className={className}
                ref={ref}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

AddressAutocompleteField.displayName = "AddressAutocompleteField";
