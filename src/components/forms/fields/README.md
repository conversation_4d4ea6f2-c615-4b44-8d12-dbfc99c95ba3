# Form Fields

This directory contains reusable form field components built with React Hook Form and Shadcn UI. The components are designed to be flexible, type-safe, and easy to use in forms throughout the application.

## Generic Fields

### Text Fields

- `Summary` - Single-line text input for brief summaries with minimum length validation
- `Description` - Multi-line text input for detailed descriptions with minimum length validation
- `Email` - Email input with validation for correct email format
- `PhoneNumber` - Phone number input with formatting and validation

### Numeric Fields

- `Number` - Generic number input with support for:
  - Min/max values
  - Integer/decimal mode
  - Positive/negative numbers
  - Step size

- `Currency` - Monetary value input with:
  - Currency symbol
  - Decimal formatting
  - Currency code selection

### Measurement Fields

All measurement fields support unit conversion, validation, and customizable units.

- `Weight` - Weight input supporting:
  - Units: kg, lb, g, oz, mt
  - Automatic unit conversion
  - Min/max validation in base unit (kg)

- `Dimension` - Length/dimension input supporting:
  - Units: mm, cm, m, in, ft
  - Automatic unit conversion
  - Min/max validation in base unit (m)

- `Volume` - Volume input supporting:
  - Units: ml, l, gal, cu.ft, cu.m
  - Automatic unit conversion
  - Min/max validation in base unit (l)

- `Temperature` - Temperature input supporting:
  - Units: Celsius, Fahrenheit, Kelvin
  - Automatic unit conversion
  - Min/max validation in base unit (°C)

### Time-Related Fields

- `Duration` - Duration input supporting:
  - Units: minutes, hours, days, weeks, months
  - Decimal/integer modes
  - Min/max duration validation

- `DatePicker` - Single date selection with:
  - Calendar popup
  - Min/max date constraints
  - Custom date formatting

- `DateRange` - Date range selection with:
  - Start/end date validation
  - Min/max duration
  - Custom date formatting

- `TimeRange` - Time range selection with:
  - Start/end time validation
  - Min/max duration
  - 12/24 hour format support

- `DateTime` - Combined date and time selection with:
  - Calendar and time picker
  - Min/max datetime validation
  - Custom formatting

### File Upload Fields

- `FileUpload` - Base file upload component with:
  - Drag and drop support
  - File type validation
  - Size limits
  - Multiple file support
  - File preview

- `ImageUpload` - Specialized image upload with:
  - Image preview
  - Dimension validation
  - Aspect ratio validation
  - Common image format support

- `DocumentUpload` - Specialized document upload with:
  - Common document format support (PDF, Word, Excel, etc.)
  - No preview (for security)

### Location Fields

- `AddressAutocomplete` - Address input with:
  - Google Places API integration
  - Address component breakdown
  - Formatted address display

## Type-Specific Fields

The `types/` directory contains form field components for handling enumerated types in the application. These components are typically implemented as select dropdowns with predefined options from the database enums. Each component includes:

- Internationalization support
- Type-safe option handling
- Integration with React Hook Form
- Consistent styling via Shadcn UI
- Loading state support

Examples include fields for various statuses (ShipmentStatus, LoadStatus, etc.), types (DocumentType, LoadType, etc.), and other enumerated values used throughout the application. These components follow a consistent pattern and integrate seamlessly with the form system while ensuring type safety with the database schema.

## Company Fields

The `company/` directory contains form field components specific to organization and company information:

- `CompanyIndustry` - Industry selection with:
  - 20+ industry options
  - Internationalization support
  - Integration with React Hook Form

- `CompanySize` - Company size selection with:
  - Employee count ranges
  - Internationalization support
  - Integration with React Hook Form

## Usage Example

```tsx
import { useForm } from "react-hook-form";

import { DateRangeField } from "@/components/forms/fields/DateRange";
import { ShipmentStatusField } from "@/components/forms/fields/types/ShipmentStatus";
import { WeightField } from "@/components/forms/fields/Weight";
import { Form } from "@/components/ui/form";

function ShipmentForm() {
  const form = useForm();

  return (
    <Form {...form}>
      <form>
        <WeightField name="weight" unitName="weightUnit" min={0} max={1000} />
        <DateRangeField
          startName="pickupDate"
          endName="deliveryDate"
          minDuration={1}
        />
        <ShipmentStatusField name="status" />
      </form>
    </Form>
  );
}
```
