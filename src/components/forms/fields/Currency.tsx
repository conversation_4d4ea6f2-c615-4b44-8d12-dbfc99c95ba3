import type { ChangeEvent, LegacyRef } from "react";

import { forwardRef, useCallback } from "react";
import { useMaskito } from "@maskito/react";
import { mergeRefs } from "@react-aria/utils";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/components/ui/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { mask, transformCurrency } from "@/lib/masks/currency";

const i18n = {
  en: {
    label: "Amount",
    description: "The amount",
    placeholder: "Enter the amount",
  },
};

export interface CurrencyInputProps extends InputProps {
  placeholder?: string;
}

export const CurrencyInput = forwardRef<
  HTMLInputElement | HTMLElement,
  CurrencyInputProps
>(function CurrencyInputCombined({ value, onChange, ...props }, ref) {
  const inputRef = useMaskito({ options: mask });

  const onInputChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const { value = "" } = event.target;
      const number = parseFloat(value.replace(/[$,]/g, "")).toString();
      onChange?.({
        ...event,
        target: {
          ...event.target,
          value: number,
        },
      });
    },
    [onChange],
  );

  return (
    <Input
      {...props}
      ref={mergeRefs(inputRef, ref) as LegacyRef<HTMLInputElement>}
      value={transformCurrency((value ?? 0).toString())}
      onInput={onInputChange}
      onChange={onInputChange}
    />
  );
});

CurrencyInput.displayName = "CurrencyInput";

export default CurrencyInput;

export interface CurrencyFieldProps extends CurrencyInputProps {
  name?:
    | "threshold"
    | "amount"
    | "paymentRate"
    | "billingRate"
    | "paymentAmount";
  label?: string;
  description?: string;
}

export const CurrencyField = forwardRef<HTMLInputElement, CurrencyFieldProps>(
  function CurrencyField(
    {
      name = "amount",
      label = i18n.en.label,
      description = i18n.en.description,
      ...props
    },
    ref,
  ) {
    const form = useFormContext<{
      [key: string]: string;
    }>();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <CurrencyInput {...props} {...field} ref={ref} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);
