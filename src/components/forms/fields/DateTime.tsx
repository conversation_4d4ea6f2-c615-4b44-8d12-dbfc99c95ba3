import type { TimeValue } from "react-aria";

import { format, isValid, parse, set } from "date-fns";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/components/ui/input";

import { Calendar } from "@/components/ui/calendar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { TimePicker } from "@/components/ui/time-picker";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    dateTime: {
      label: "Date & Time",
      description: "Select date and time",
      validation: {
        invalidDate: "Please enter a valid date",
        invalidTime: "Please enter a valid time",
        beforeMin: "Must be after {min}",
        afterMax: "Must be before {max}",
      },
      date: {
        label: "Date",
        placeholder: "Select date",
      },
      time: {
        label: "Time",
      },
    },
  },
};

export interface DateTimeFieldProps extends Omit<InputProps, "name" | "type"> {
  name?: string;
  label?: string;
  description?: string;
  minDateTime?: Date;
  maxDateTime?: Date;
  disabled?: boolean;
  dateFormat?: string;
}

const parseDateTime = (
  date: string,
  time: TimeValue | null,
  dateFormat = "yyyy-MM-dd",
) => {
  if (!date || !time) return null;
  const parsedDate = parse(date, dateFormat, new Date());
  if (!isValid(parsedDate)) return null;
  return set(parsedDate, {
    hours: time.hour,
    minutes: time.minute,
    seconds: 0,
    milliseconds: 0,
  });
};

export function DateTimeField({
  name,
  label = i18n.en.dateTime.label,
  description = i18n.en.dateTime.description,
  minDateTime,
  maxDateTime,
  disabled = false,
  dateFormat = "yyyy-MM-dd",
  className,
  ...props
}: DateTimeFieldProps) {
  const form = useFormContext();

  // Split the datetime field into date and time parts
  const datePart = form.watch(`${name}.date`);
  const timePart = form.watch(`${name}.time`);

  // Combine date and time when either changes
  const updateDateTime = (newDate?: string, newTime?: TimeValue | null) => {
    const date = newDate ?? datePart;
    const time = newTime ?? timePart;
    if (!date || !time) return;

    const dateTime = parseDateTime(date, time, dateFormat);
    if (dateTime) {
      form.setValue(name, dateTime.toISOString());
    }
  };

  return (
    <FormField
      control={form.control}
      name={name}
      rules={{
        validate: {
          isValid: (value) => {
            if (!value) return true;
            const date = new Date(value);
            return isValid(date) || i18n.en.dateTime.validation.invalidDate;
          },
          afterMin: (value) => {
            if (!value || !minDateTime) return true;
            const date = new Date(value);
            return (
              date >= minDateTime ||
              i18n.en.dateTime.validation.beforeMin.replace(
                "{min}",
                format(minDateTime, dateFormat),
              )
            );
          },
          beforeMax: (value) => {
            if (!value || !maxDateTime) return true;
            const date = new Date(value);
            return (
              date <= maxDateTime ||
              i18n.en.dateTime.validation.afterMax.replace(
                "{max}",
                format(maxDateTime, dateFormat),
              )
            );
          },
        },
      }}
      render={({ field }) => {
        const value = field.value ? new Date(field.value) : undefined;

        return (
          <FormItem className={cn("space-y-2", className)}>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
              <div className="flex-1">
                <FormLabel>{i18n.en.dateTime.date.label}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Input
                        {...props}
                        disabled={disabled}
                        placeholder={i18n.en.dateTime.date.placeholder}
                        value={value ? format(value, dateFormat) : ""}
                        onChange={() => {}} // Handled by Calendar
                      />
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={value}
                      onSelect={(date) => {
                        if (date) {
                          const dateStr = format(date, dateFormat);
                          updateDateTime(dateStr, timePart);
                        }
                      }}
                      disabled={(date) =>
                        disabled ||
                        (minDateTime ? date < minDateTime : false) ||
                        (maxDateTime ? date > maxDateTime : false)
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="flex-1">
                <FormLabel>{i18n.en.dateTime.time.label}</FormLabel>
                <FormControl>
                  <TimePicker
                    isDisabled={disabled}
                    value={timePart}
                    onChange={(time) => {
                      updateDateTime(datePart, time);
                    }}
                  />
                </FormControl>
              </div>
            </div>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
