import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/components/ui/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const i18n = {
  en: {
    summary: {
      label: "Summary",
      description: "Brief summary in a few words",
      placeholder: "Enter a brief summary",
    },
  },
};

export interface SummaryFieldProps extends Omit<InputProps, "name"> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  minLength?: number;
}

export function SummaryField({
  name,
  label = i18n.en.summary.label,
  description = i18n.en.summary.description,
  placeholder = i18n.en.summary.placeholder,
  minLength = 5,
  ...props
}: SummaryFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      rules={{
        minLength: {
          value: minLength,
          message: `Summary must be at least ${minLength} characters long`,
        },
      }}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <Input placeholder={placeholder} {...field} {...props} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
