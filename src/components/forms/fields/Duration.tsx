import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/components/ui/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const i18n = {
  en: {
    duration: {
      label: "Duration",
      description: "Enter the duration value",
      placeholder: "Enter duration",
      validation: {
        type: "Please enter a valid duration",
        min: "Duration must be at least {min} {unit}",
        max: "Duration must be at most {max} {unit}",
      },
      units: {
        minutes: "Minutes",
        hours: "Hours",
        days: "Days",
        weeks: "Weeks",
        months: "Months",
      },
    },
  },
};

export type DurationUnit = keyof typeof i18n.en.duration.units;
export const DURATION_UNITS = [
  "minutes",
  "hours",
  "days",
  "weeks",
  "months",
] as const;

// Conversion to minutes as base unit
const UNIT_CONVERSIONS = {
  minutes: {
    to: { hours: 1 / 60, days: 1 / 1440, weeks: 1 / 10080, months: 1 / 43200 },
  },
  hours: { to: { minutes: 60, days: 1 / 24, weeks: 1 / 168, months: 1 / 720 } },
  days: { to: { minutes: 1440, hours: 24, weeks: 1 / 7, months: 1 / 30 } },
  weeks: { to: { minutes: 10080, hours: 168, days: 7, months: 1 / 4.345 } },
  months: { to: { minutes: 43200, hours: 720, days: 30, weeks: 4.345 } },
};

export interface DurationFieldProps extends Omit<InputProps, "name" | "type"> {
  name?: string;
  unitName?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  min?: number;
  max?: number;
  defaultUnit?: DurationUnit;
  allowedUnits?: DurationUnit[];
  showDecimal?: boolean;
}

export function DurationField({
  name,
  unitName,
  label = i18n.en.duration.label,
  description = i18n.en.duration.description,
  placeholder = i18n.en.duration.placeholder,
  min = 0,
  max,
  defaultUnit = "hours",
  allowedUnits = DURATION_UNITS as unknown as DurationUnit[],
  showDecimal = false,
  ...props
}: DurationFieldProps) {
  const form = useFormContext();

  const convertDuration = (
    value: number,
    from: DurationUnit,
    to: DurationUnit,
  ) => {
    if (from === to) return value;
    return value * UNIT_CONVERSIONS[from].to[to];
  };

  return (
    <div className="space-y-2">
      <FormField
        control={form.control}
        name={name}
        rules={{
          validate: {
            isNumber: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return !isNaN(num) || i18n.en.duration.validation.type;
            },
            isPositive: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return num >= 0 || "Duration must be a positive number";
            },
            isInteger: (value) => {
              if (value === undefined || value === "" || showDecimal)
                return true;
              const num = Number(value);
              return Number.isInteger(num) || "Duration must be a whole number";
            },
            minValue: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              const unit = form.watch(unitName) || defaultUnit;
              const minInUnit = convertDuration(min, "hours", unit);
              return (
                num >= minInUnit ||
                i18n.en.duration.validation.min
                  .replace("{min}", minInUnit.toFixed(showDecimal ? 1 : 0))
                  .replace("{unit}", unit)
              );
            },
            maxValue: (value) => {
              if (value === undefined || value === "" || max === undefined)
                return true;
              const num = Number(value);
              const unit = form.watch(unitName) || defaultUnit;
              const maxInUnit = convertDuration(max, "hours", unit);
              return (
                num <= maxInUnit ||
                i18n.en.duration.validation.max
                  .replace("{max}", maxInUnit.toFixed(showDecimal ? 1 : 0))
                  .replace("{unit}", unit)
              );
            },
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <div className="flex gap-2">
              <FormControl>
                <Input
                  type="number"
                  inputMode={showDecimal ? "decimal" : "numeric"}
                  step={showDecimal ? "0.1" : "1"}
                  min={0}
                  placeholder={placeholder}
                  className="w-[180px]"
                  {...field}
                  {...props}
                  onChange={(e) => {
                    if (e.target.value === "") {
                      field.onChange("");
                      return;
                    }

                    const value = parseFloat(e.target.value);
                    if (!isNaN(value)) {
                      field.onChange(value.toString());
                    }
                  }}
                />
              </FormControl>
              {unitName && (
                <FormField
                  control={form.control}
                  name={unitName}
                  defaultValue={defaultUnit}
                  render={({ field: unitField }) => (
                    <Select
                      value={unitField.value}
                      onValueChange={(value: DurationUnit) => {
                        const oldUnit = unitField.value as DurationUnit;
                        const newUnit = value as DurationUnit;
                        const currentValue = field.value;

                        // Convert the current value to the new unit
                        if (currentValue !== "") {
                          const converted = convertDuration(
                            parseFloat(currentValue),
                            oldUnit,
                            newUnit,
                          );
                          field.onChange(
                            converted.toFixed(showDecimal ? 1 : 0),
                          );
                        }

                        unitField.onChange(value);
                      }}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[130px]">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allowedUnits.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {i18n.en.duration.units[unit]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              )}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
