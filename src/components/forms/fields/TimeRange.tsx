import { useFormContext } from "react-hook-form";

import type { TimePickerProps } from "@/components/ui/time-picker";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { TimePicker } from "@/components/ui/time-picker";
import { cn } from "@/lib/utils";

const i18n = {
  en: {
    timeRange: {
      label: "Time Range",
      description: "Select start and end times",
      validation: {
        invalidStart: "Please enter a valid start time",
        invalidEnd: "Please enter a valid end time",
        startBeforeEnd: "Start time must be before end time",
        startAfterMin: "Start time must be after {min}",
        endBeforeMax: "End time must be before {max}",
        minDuration: "Duration must be at least {min} minutes",
        maxDuration: "Duration must be at most {max} minutes",
      },
      start: {
        label: "Start Time",
      },
      end: {
        label: "End Time",
      },
    },
  },
};

type TimeRangeDisabled = boolean | { start?: boolean; end?: boolean };

export interface TimeRangeFieldProps
  extends Omit<TimePickerProps, "value" | "onChange" | "isDisabled"> {
  startName?: string;
  endName?: string;
  label?: string;
  description?: string;
  minTime?: number; // minutes since midnight
  maxTime?: number; // minutes since midnight
  minDuration?: number; // in minutes
  maxDuration?: number; // in minutes
  disabled?: TimeRangeDisabled;
  className?: string;
}

export function TimeRangeField({
  startName = "startTime",
  endName = "endTime",
  label = i18n.en.timeRange.label,
  description = i18n.en.timeRange.description,
  minTime,
  maxTime,
  minDuration = 0,
  maxDuration,
  disabled = false,
  className,
  ...props
}: TimeRangeFieldProps) {
  const form = useFormContext();

  const isStartDisabled =
    typeof disabled === "boolean" ? disabled : disabled?.start;
  const isEndDisabled =
    typeof disabled === "boolean" ? disabled : disabled?.end;

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex flex-col gap-2">
        <FormLabel>{label}</FormLabel>
        <FormDescription>{description}</FormDescription>
      </div>
      <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
        <FormField
          control={form.control}
          name={startName}
          rules={{
            validate: {
              isValid: (value) => {
                if (value === undefined || value === null) return true;
                return (
                  (value >= 0 && value < 1440) ||
                  i18n.en.timeRange.validation.invalidStart
                );
              },
              afterMinTime: (value) => {
                if (
                  value === undefined ||
                  value === null ||
                  minTime === undefined
                )
                  return true;
                return (
                  value >= minTime || i18n.en.timeRange.validation.startAfterMin
                );
              },
              beforeEndTime: (value) => {
                if (value === undefined || value === null) return true;
                const endTime = form.watch(endName);
                if (endTime === undefined || endTime === null) return true;
                return (
                  value < endTime || i18n.en.timeRange.validation.startBeforeEnd
                );
              },
            },
          }}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormLabel>{i18n.en.timeRange.start.label}</FormLabel>
              <FormControl>
                <TimePicker
                  {...props}
                  isDisabled={isStartDisabled}
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={endName}
          rules={{
            validate: {
              isValid: (value) => {
                if (value === undefined || value === null) return true;
                return (
                  (value >= 0 && value < 1440) ||
                  i18n.en.timeRange.validation.invalidEnd
                );
              },
              beforeMaxTime: (value) => {
                if (
                  value === undefined ||
                  value === null ||
                  maxTime === undefined
                )
                  return true;
                return (
                  value <= maxTime || i18n.en.timeRange.validation.endBeforeMax
                );
              },
              afterStartTime: (value) => {
                if (value === undefined || value === null) return true;
                const startTime = form.watch(startName);
                if (startTime === undefined || startTime === null) return true;
                return (
                  value > startTime ||
                  i18n.en.timeRange.validation.startBeforeEnd
                );
              },
              minDuration: (value) => {
                if (value === undefined || value === null || minDuration === 0)
                  return true;
                const startTime = form.watch(startName);
                if (startTime === undefined || startTime === null) return true;
                const duration = value - startTime;
                return (
                  duration >= minDuration ||
                  i18n.en.timeRange.validation.minDuration.replace(
                    "{min}",
                    minDuration.toString(),
                  )
                );
              },
              maxDuration: (value) => {
                if (
                  value === undefined ||
                  value === null ||
                  maxDuration === undefined
                )
                  return true;
                const startTime = form.watch(startName);
                if (startTime === undefined || startTime === null) return true;
                const duration = value - startTime;
                return (
                  duration <= maxDuration ||
                  i18n.en.timeRange.validation.maxDuration.replace(
                    "{max}",
                    maxDuration.toString(),
                  )
                );
              },
            },
          }}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormLabel>{i18n.en.timeRange.end.label}</FormLabel>
              <FormControl>
                <TimePicker
                  {...props}
                  isDisabled={isEndDisabled}
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
