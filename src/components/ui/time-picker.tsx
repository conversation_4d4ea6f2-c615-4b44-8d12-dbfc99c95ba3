"use client";

import type { AriaTimeFieldProps, TimeValue } from "react-aria";
import type {
  DateFieldState,
  DateSegment as IDateSegment,
  TimeFieldStateOptions,
} from "react-stately";

import { forwardRef, useRef } from "react";
import { useDateSegment, useLocale, useTimeField } from "react-aria";
import { useTimeFieldState } from "react-stately";

import { useFormField } from "@/components/ui/form";
import { cn } from "@/lib/utils";

interface DateSegmentProps {
  segment: IDateSegment;
  state: DateFieldState;
}

function DateSegment({ segment, state }: DateSegmentProps) {
  const ref = useRef(null);

  const {
    segmentProps: { ...segmentProps },
  } = useDateSegment(segment, state, ref);

  return (
    <div
      {...segmentProps}
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground focus:rounded-[2px] focus:outline-hidden",
        segment.type !== "literal" ? "px-[1px]" : "",
        segment.isPlaceholder ? "text-muted-foreground" : "",
      )}
    >
      {segment.text}
    </div>
  );
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface TimeFieldLocalProps extends AriaTimeFieldProps<TimeValue> {}

function TimeField(props: TimeFieldLocalProps) {
  const { formItemId, formLabelId } = useFormField();
  const ref = useRef<HTMLDivElement>(null);
  const { locale } = useLocale();
  const state = useTimeFieldState({
    ...props,
    locale,
  });
  const {
    fieldProps: { ...fieldProps },
    // labelProps,
  } = useTimeField(
    {
      "aria-labelledby": formLabelId,
      ...props,
    },
    state,
    ref,
  );

  return (
    <div
      aria-labelledby={formLabelId}
      id={formItemId}
      {...fieldProps}
      ref={ref}
      className={cn(
        "border-input ring-offset-background focus-within:ring-ring focus-visible:ring-ring inline-flex h-10 w-full flex-1 rounded-md border bg-transparent px-3 py-2 text-sm focus-within:ring-2 focus-within:ring-offset-2 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden",
        props.isDisabled ? "cursor-not-allowed opacity-50" : "",
      )}
    >
      {state.segments.map((segment, i) => (
        <DateSegment key={i} segment={segment} state={state} />
      ))}
    </div>
  );
}

export interface TimePickerProps
  extends TimeFieldLocalProps,
    Omit<TimeFieldStateOptions<TimeValue>, "locale"> {}

const TimePicker = forwardRef<HTMLDivElement, TimePickerProps>(
  (props, _forwardedRef) => {
    return <TimeField {...props} />;
  },
);

TimePicker.displayName = "TimePicker";

export { TimePicker };
