"use client";

import * as React from "react";
import { DesktopIcon, MoonIcon, SunIcon } from "@radix-ui/react-icons";
import { ThemeProvider, useTheme } from "next-themes";

import type { ButtonProps } from "./button";

import { cn } from "@/lib/utils";
import { Button } from "./button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./dropdown-menu";
import { Tabs, TabsList, TabsTrigger } from "./tabs";

function ThemeToggle({
  className,
  variant = "outline",
  size = "icon",
  side = "bottom",
  sideOffset = 5,
  align = "end",
}: {
  className?: string;
} & ButtonProps &
  Pick<
    React.ComponentProps<typeof DropdownMenuContent>,
    "side" | "sideOffset" | "align"
  >) {
  const { setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn("min-w-9", className)}
        >
          <SunIcon className="size-5 scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
          <MoonIcon className="absolute size-5 scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} side={side} sideOffset={sideOffset}>
        <DropdownMenuItem onClick={() => setTheme("light")}>
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function ThemeTabs() {
  const { setTheme, theme } = useTheme();

  return (
    <Tabs defaultValue="system" value={theme} onValueChange={setTheme}>
      <TabsList>
        <TabsTrigger value="light">
          <SunIcon className="size-5 scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
          <span>Light</span>
        </TabsTrigger>
        <TabsTrigger value="system">
          <DesktopIcon className="size-5" />
          <span>System</span>
        </TabsTrigger>
        <TabsTrigger value="dark">
          <MoonIcon className="size-5 scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
          <span>Dark</span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}

export { ThemeProvider, ThemeToggle, ThemeTabs, useTheme };
