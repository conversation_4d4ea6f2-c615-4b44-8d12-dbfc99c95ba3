# UI Components

This directory contains our foundational UI components, built on top of Shadcn UI with our custom styling and functionality. These are the basic building blocks used throughout the application to maintain consistency and reduce duplication.

> **Quick Start**
>
> - Need a basic UI component? Check here first
> - Components are styled with our theme
> - Built on Shadcn/Radix primitives
> - Minimal logic, mostly presentational
> - Import from `@/components/ui`

> [!NOTE] These components provide the foundation for our UI. They are customized versions of Shadcn components, ensuring consistent styling and behavior across the application. Always check here before creating new basic components or using HTML elements directly.

## Component Types

1. Input Components:
   - `Button` - Styled button variants
   - `Input` - Text input field
   - `Select` - Dropdown selection
   - `Checkbox` - Checkbox input
   - `RadioGroup` - Radio button group
   - `Switch` - Toggle switch
   - `Textarea` - Multi-line input

2. Display Components:
   - `Card` - Content container
   - `Badge` - Status/label display
   - `Avatar` - User/entity avatar
   - `Alert` - Notification display
   - `Progress` - Progress indicator

3. Layout Components:
   - `Separator` - Visual divider
   - `ScrollArea` - Scrollable container
   - `AspectRatio` - Aspect ratio box
   - `Container` - Width container

4. Overlay Components:
   - `Dialog` - Modal dialog
   - `Sheet` - Slide-out panel
   - `Popover` - Floating content
   - `Tooltip` - Hover information

5. Navigation Components:
   - `Tabs` - Tabbed interface
   - `NavigationMenu` - Dropdown navigation
   - `Breadcrumb` - Page navigation
   - `Pagination` - Page controls

## File Structure

```
ui/
├── input/          # Input components
├── display/        # Display components
├── layout/         # Layout primitives
├── overlay/        # Overlay components
├── navigation/     # Navigation components
├── utils/          # Shared utilities
└── README.md       # This file
```

## Component Pattern

UI components typically follow this pattern:

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
}

export function Button({
  className,
  variant = 'default',
  size = 'md',
  loading = false,
  disabled,
  children,
  ...props
}: ButtonProps) {
  return (
    <button
      className={cn(
        buttonVariants({ variant, size }),
        { 'opacity-50 cursor-wait': loading },
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <Spinner className="mr-2" />}
      {children}
    </button>
  );
}
```

## Best Practices

1. **Component Design**
   - Extend Shadcn components
   - Maintain accessibility
   - Support all states (hover, focus, etc.)
   - Follow design system

2. **Props Pattern**
   - Extend HTML attributes
   - Use consistent naming
   - Provide sensible defaults
   - Support className prop

3. **Styling**
   - Use Tailwind classes
   - Follow color system
   - Support dark mode
   - Maintain spacing scale

4. **Composition**
   - Keep components simple
   - Enable easy composition
   - Support children prop
   - Allow style overrides

5. **Documentation**
   - Document props
   - Provide usage examples
   - Note accessibility features
   - List variants/sizes

## Examples

### Simple Component

```typescript
export function Badge({
  variant = 'default',
  className,
  ...props
}: BadgeProps) {
  return (
    <div
      className={cn(
        'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold',
        badgeVariants[variant],
        className
      )}
      {...props}
    />
  );
}
```

### Composite Component

```typescript
export function Select({
  children,
  className,
  error,
  ...props
}: SelectProps) {
  return (
    <SelectPrimitive.Root {...props}>
      <SelectPrimitive.Trigger
        className={cn(
          'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2',
          { 'border-destructive': error },
          className
        )}
      >
        <SelectPrimitive.Value />
        <SelectPrimitive.Icon />
      </SelectPrimitive.Trigger>
      <SelectPrimitive.Portal>
        <SelectPrimitive.Content>
          {children}
        </SelectPrimitive.Content>
      </SelectPrimitive.Portal>
    </SelectPrimitive.Root>
  );
}
```

## When to Use UI Components

Use these components when you need:

1. Basic UI elements (buttons, inputs, etc.)
2. Consistent styling across features
3. Accessible, tested components
4. Standard interactive elements

## When to Customize

Create a custom variant or component when:

1. Existing variants don't match design
2. New functionality is needed
3. Different behavior is required
4. Special styling is necessary

Remember to consider extending existing components before creating new ones.
