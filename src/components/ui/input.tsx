import type { VariantProps } from "class-variance-authority";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

// eslint-disable-next-line react-refresh/only-export-components
export const inputVariants = cva(
  // "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  "placeholder:text-muted-foreground focus-visible:ring-ring flex w-full bg-transparent text-sm whitespace-nowrap transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        primary:
          "border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md border shadow-xs",
        outline:
          "border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md border shadow-xs",
        ghost:
          "appearance-none border-none outline-hidden focus-visible:ring-0",
      },
      size: {
        sm: "h-8 rounded-md px-3 text-xs",
        md: "h-9 px-3 py-1",
        lg: "h-10 rounded-md px-8",
        bare: "h-9",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  },
);

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof inputVariants> {
  asChild?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, size, variant, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "input";
    return (
      <Comp
        type={type}
        className={cn(inputVariants({ size, variant, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = "Input";

export { Input };
