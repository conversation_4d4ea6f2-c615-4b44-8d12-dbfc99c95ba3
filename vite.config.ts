import path from "path";
import type { PluginOption } from "vite";

import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react-swc";
import { componentTagger } from "lovable-tagger";
import { defineConfig } from "vite";

function manualChunks(id: string) {
  if (id.includes("node_modules")) {
    return "vendor";
  }

  switch (true) {
    case id.includes("src/pages/public"):
      return "pages-public";
    case id.includes("src/pages/app/drivers"):
      return "pages-drivers";
    case id.includes("src/pages/app/console"):
      return "pages-console";
    case id.includes("src/pages/app"):
      return "pages-app";
    case id.includes("src/routes"):
    case id.includes("src/pages"):
      return "pages";
    case id.includes("src/api"):
      return "api";
    case id.includes("src/components"):
    case id.includes("src/contexts"):
    case id.includes("src/hooks"):
    case id.includes("src/utils"):
      return "shared";
    default:
      return null;
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    tailwindcss(),
    mode === "development" && componentTagger(),
  ].filter(Boolean) as PluginOption[],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks,
      },
    },
  },
}));
