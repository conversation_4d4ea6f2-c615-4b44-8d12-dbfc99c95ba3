# QuikSkope Web App

> [!KNOWLEDGE]
>
> ## Project Architecture
>
> **Directory Structure**
>
> ```
> src/                      # Source code
> ├── api/                  # API hooks and mutations (data layer)
> ├── components/           # UI components
> ├── contexts/             # React contexts (user, etc)
> ├── hooks/                # Custom hooks
> ├── integrations/         # External third-party integrations (Stripe, etc)
> ├── lib/                  # Utilities
> ├── pages/                # Page components
> ├── routes/               # Route definitions
> └── supabase/             # Supabase client and types (first-party integration)
> ```
>
> ## @/api
>
> The API layer uses React Query + Supabase for data operations.
>
> **Query Organization**
>
> - Organized by domain (console, drivers, user)
> - All hooks use `use-` prefix
> - Return `{ data, error, isLoading }`
>
> **Query Patterns**
>
> ```typescript
> // List queries with filters
> ["resources", "list", { filters }][
>   // Single resource
>   ("resources", "get", id)
> ][
>   // Nested resources
>   ("parent", "child", "list", parentId)
> ];
> ```
>
> **Type Safety**
>
> - Import types from `@/supabase/types`:
>   - `Tables<"resource">` - Table types
>   - `TablesInsert<"resource">` - Insert types
>   - `TablesUpdate<"resource">` - Update types
>   - `Enums<"enum_type">` - Enum types
>
> **Mutations**
>
> - Extract mutation logic to `mutationFn`
> - Invalidate relevant queries on success
> - Handle errors consistently
> - Examples: useCreateLoad, useUpdateShipment
>
> **Edge Functions**
>
> - Called through Supabase client
> - Follow mutation pattern
> - Handle errors consistently
> - Examples: useProcessShipment, useCalculateMetrics
>
> ## @/components
>
> Components are strictly presentational - they never fetch or mutate data directly.
>
> **Data Flow**
>
> - Data operations are not directly handled in components - they are handled by hooks in the `@/api` layer
> - Data and callbacks passed via props
> - Data fetching lives in pages or widgets
> - Widgets (`src/components/widgets`) are the only exception to the data rule as hybrid components that can use data and presentation logic
>
> **Component Categories**
>
> `@/components/ui/`
>
> - Foundational UI components built on Shadcn/Radix
> - Consistent styling and behavior across app
> - Input components (Button, Input, Select, etc.)
> - Display components (Card, Badge, Avatar)
> - Layout primitives (Separator, ScrollArea)
> - Overlay components (Dialog, Sheet, Popover)
> - Navigation components (Tabs, NavigationMenu)
>
> `@/components/forms/`
>
> - Built with react-hook-form + zod validation
> - Common fields (Currency, DatePicker, etc.)
> - Domain-specific fields (LoadType, ShipmentMode)
> - Consistent patterns for state and submission
> - Type-safe with proper TypeScript interfaces
> - Accessible with proper ARIA attributes
>
> `@/components/widgets/`
>
> - Self-contained features with data + UI
> - Connect to API endpoints via hooks
> - Handle loading and error states
> - Manage their own data mutations
> - Can implement custom UI when needed
> - Examples: LoadsList, ShipmentCard, AnalyticsDashboard
>
> `@/components/shared/`
>
> - Reusable components with behavior + UI
> - Range from simple utilities to complex systems
> - Generic enough for cross-feature use
> - Include their own hooks and utilities
> - Examples: CopyButton, DataTable, FileUploader
>
> `@/components/common/`
>
> - Visual representations of domain entities
> - Ensure consistent display across app
> - Entity displays (LoadBadge, ShipmentCard)
> - Status indicators (LoadStatus, PaymentStatus)
> - Entity metadata (LoadDimensions, ShipmentRoute)
>
> `@/components/layouts/`
>
> - Define structural organization of pages
> - Handle responsive behavior and breakpoints
> - Application layouts (AppLayout, ConsoleLayout)
> - Page layouts (DashboardLayout, SplitLayout)
> - Content layouts (CardGrid, SidebarLayout)
>
> `@/components/auth/`
>
> - Built on Supabase Auth
> - Handle all auth flows (email, OAuth, magic link)
> - Auth forms (SignIn, SignUp, MagicLink)
> - OAuth providers (Google, GitHub, Azure)
> - Protection components (RequireAuth, RequireRole)
>
> `@/components/selectors/`
>
> - Selection interfaces for entity data
> - Support single/multiple selection modes
> - Entity selectors (LoadSelector, ShipmentSelector)
> - Relationship selectors (DriverSelector, TeamSelector)
> - Attribute selectors (StatusSelector, TypeSelector)
>
> `@/components/maps/`
>
> - Built on Mapbox GL JS
> - Support static and interactive modes
> - Base maps (StaticMap, InteractiveMap)
> - Map features (LocationMarker, RouteLayer)
> - Map controls (ZoomControls, LayerToggle)
>
> ## @/pages
>
> Pages are the intersection of data and UI.
>
> **Page Architecture**
>
> - Use hooks from `@/api/` for data
> - Compose components from `@/components/`
> - Handle loading and error states
> - Manage route-level state
>
> **Data Integration**
>
> - API hooks (useLoads, useShipments)
> - Mutations (useCreateLoad, useUpdateShipment)
> - Context providers (OrganizationProvider)
> - Route params and queries
>
> ## @/routes
>
> Routes define the application's navigation structure.
>
> **Route Organization**
>
> - `public.tsx` - Marketing, landing pages (no auth)
> - `auth.tsx` - Auth flows (no session)
> - `app.tsx` - Protected views (requires auth)
> - `console.tsx` - Admin views (requires roles)
> - `drivers.tsx` - Driver features
> - `components.tsx` - Shared components
>
> **Route Pattern**
>
> ```tsx
> {
>   element: <AuthGuard />,          // Protection
>   children: [{
>     element: <Layout />,           // Layout
>     children: [{
>       path: "entity",              // Feature
>       children: [
>         { index: true, element: <List /> },
>         { path: ":id", element: <Detail /> }
>       ]
>     }]
>   }]
> }
> ```
>
> ## Technology Stack
>
> **Frontend**
>
> - React + TypeScript
> - Vite for building
> - TailwindCSS for styling
> - Shadcn/UI components
> - React Query for data
> - React Hook Form + Zod
>
> **Backend**
>
> - Supabase client
> - Edge functions (Deno)
> - Postgres database

> IMPORTANT: The Supabase client is located in the supabase folder in the project root ("~/src/supabase/client/index.ts"). The database types are also in the supabase folder ("~/src/supabase/types/index.ts"). Please do not update the types anywhere else. All code can access @/supabase/client and @/supabase/types. DO NOT USE src/integrations !!!! USE src/supabase !!! When Pulling the database types.

## Project Structure

```
src/                          # Source code directory
├── api/                      # API related code
├── components/               # React components
├── contexts/                 # React context providers
├── hooks/                    # Custom React hooks
├── lib/                      # Utility functions and shared code
├── pages/                    # Page components
├── routes/                   # Route definitions
├── supabase/                # Supabase client and types
├── validation/              # Form and data validation schemas
├── App.tsx                  # Main App component
├── main.tsx                 # Application entry point
├── index.css               # Global styles
└── [entry-files]           # TypeScript environment declarations

supabase/                    # Supabase backend
├── functions/               # Edge Functions
│   ├── _shared/             # Shared utilities across functions
│   └── [function-name]/     # Secret management function
└── config.toml              # Supabase configuration
```

## Technology Stack

This project is built with modern web technologies:

- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Backend**: Supabase
- **Package Manager**: bun
- **Development Tools**:
  - ESLint for code linting
  - Prettier for code formatting
  - PostCSS for CSS processing

## Development Guidelines

### Working with the API Layer

1. Create hooks in the appropriate domain folder under `src/api/`
2. Use the `use-` prefix for all hook names (e.g., `useLoads`, `useCreateShipment`)
3. Follow the established query pattern for consistency:
   - List queries: `["resources", "list", { filters }]`
   - Single resource: `["resources", "get", id]`
   - Nested resources: `["parent", "child", "list", parentId]`
4. Import types from `@/supabase/types` for type safety
5. Extract mutation logic to a separate `mutationFn`
6. Invalidate relevant queries on successful mutations
7. Implement consistent error handling
8. For edge functions, call through the Supabase client and follow the mutation pattern

### Working with Components

1. Keep components strictly presentational - no direct data fetching or mutations
2. Pass data and callbacks via props
3. Place components in the appropriate category folder:
   - `ui/`: Foundational UI components (buttons, inputs, cards)
   - `forms/`: Form components with react-hook-form + zod validation
   - `widgets/`: Self-contained features with data + UI
   - `shared/`: Reusable components with behavior + UI
   - `common/`: Visual representations of domain entities
   - `layouts/`: Page structure and responsive behavior
   - `auth/`: Authentication components
   - `selectors/`: Selection interfaces for entity data
   - `maps/`: Map-related components
4. Ensure components are accessible with proper ARIA attributes
5. Implement responsive design for all components

### Working with Pages

1. Use hooks from `@/api/` for data operations
2. Compose components from `@/components/`
3. Handle loading and error states appropriately
4. Manage route-level state
5. Use context providers where needed (e.g., `OrganizationProvider`)
6. Utilize route parameters and queries for dynamic content

### Working with Routes

1. Place routes in the appropriate file:
   - `public.tsx`: Marketing and landing pages (no auth)
   - `auth.tsx`: Authentication flows (no session)
   - `app.tsx`: Protected views (requires authentication)
   - `console.tsx`: Admin views (requires specific roles)
   - `drivers.tsx`: Driver-specific features
   - `components.tsx`: Shared components
2. Follow the established route pattern with protection, layout, and feature structure
3. Implement proper route nesting for related features

### Working with Supabase

1. Access the Supabase client from `@/supabase/client`
2. Import database types from `@/supabase/types`
3. NEVER update types anywhere else in the codebase
4. DO NOT use `src/integrations` for Supabase - always use `src/supabase`
5. For edge functions, place them in the `supabase/functions/` directory
6. Share utilities across functions using the `_shared/` directory

### Code Quality Standards

1. Use TypeScript for all new code
2. Follow ESLint and Prettier configurations
3. Write unit tests for critical functionality
4. Document complex logic with clear comments
5. Use meaningful variable and function names
6. Keep functions small and focused on a single responsibility
7. Optimize performance for data-heavy operations
8. Ensure proper error handling throughout the application
9. Prefer server-side operations for data transformations when possible

### Page Development Workflow

When creating or modifying pages, follow this workflow to ensure consistency and maintainability:

1. **Check and Use API Types**
   - Always use types directly from the API layer instead of creating custom interfaces
   - Use `ReturnType<typeof useApiHook>["data"]` to type data from API hooks:
     ```typescript
     // Example from Locations.tsx
     locations?: ReturnType<typeof useListLocations>["data"];
     ```
   - Import enum types from Supabase for type safety:
     ```typescript
     // Example from Locations.tsx
     import { Enums } from "@/supabase/types";
     type: locationType ? (locationType as Enums<"location_type">) : undefined,
     ```
   - Leverage TypeScript's inference capabilities with API hooks

2. **Identify Reusable Components**
   - Check for existing shared components before creating new ones:
     - Dialog components (`DialogConfirmation`)
     - Error handling components (`ErrorAlert`)
     - Search and filter components
   - Use existing hooks for common functionality:
     ```typescript
     // Example from Locations.tsx - reusing search hooks
     const pagination = useSearchPaginationValue({
       group: "location",
       defaultPageSize: 10,
       defaultPageIndex: 0,
     });
     const locationsQuery = useSearchTextValue("location");
     const locationType = useSearchFilterValue<string>("type", "location");
     ```

3. **Separate Data and Presentation Layers**
   - Create two components for each page:
     - Data component (e.g., `LocationsPage`): Handles data fetching, state, and mutations
     - Presentation component (e.g., `LocationsView`): Purely presentational, receives data and callbacks as props
   - Example structure:

     ```typescript
     // Data component
     export default function LocationsPage() {
       // Data fetching, state management, and handlers
       return <LocationsView {...props} />;
     }

     // Presentation component
     export function LocationsView({
       loading,
       error,
       data,
       onAction,
     }: Props) {
       // UI rendering only
     }
     ```

4. **Implement Data Layer**
   - Set up state management for the page
     ```typescript
     const [deleteLocationId, setDeleteLocationId] = useState<string | null>(
       null,
     );
     ```
   - Connect to API hooks with proper parameters
     ```typescript
     const {
       data: locations,
       isLoading,
       error,
       refetch,
     } = useListLocations({
       pageIndex: pagination.pageIndex,
       pageSize: pagination.pageSize,
       search: locationsQuery,
       type: locationType
         ? (locationType as Enums<"location_type">)
         : undefined,
     });
     ```
   - Implement handlers for user actions
     ```typescript
     const handleDelete = async (locationId: string) => {
       try {
         await deleteLocation.mutateAsync({ id: locationId });
         await refetch();
         toast({
           title: "Success",
           description: i18n.en.toast.deleteSuccess,
         });
       } catch (error) {
         toast({
           variant: "destructive",
           title: "Error",
           description: i18n.en.toast.deleteError,
         });
         throw error;
       }
     };
     ```

5. **Implement Presentation Layer**
   - Focus solely on rendering UI based on props
   - Handle loading and error states appropriately
   - Implement responsive design
   - Pass callbacks for user actions

6. **Additional Best Practices**
   - Use internationalization objects for text content
     ```typescript
     const i18n = {
       en: {
         title: "Locations",
         addButton: "Add Location",
         // ...
       },
       links: {
         create: "/app/console/locations/create",
       },
     };
     ```
   - Implement proper error handling and user feedback
   - Refetch data after successful mutations
   - Use consistent prop naming and typing
   - Disable interactive elements during loading states
     ```typescript
     <Button asChild disabled={loading}>
       <Link to={i18n.links.create}>{i18n.en.addButton}</Link>
     </Button>
     ```

Following this workflow ensures consistent, maintainable, and type-safe pages throughout the application.
