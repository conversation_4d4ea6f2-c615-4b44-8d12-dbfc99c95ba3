import { dirname, join, resolve } from "path";
import type { StorybookConfig } from "@storybook/react-vite";

import { mergeConfig } from "vite";

export function getAbsolutePath(value: string): string {
  return dirname(require.resolve(join(value, "package.json")));
}

export function resolvePath(value: string): string {
  return resolve(process.cwd(), value);
}

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  staticDirs: [resolve("public")],
  addons: [
    getAbsolutePath("@chromatic-com/storybook"),
    getAbsolutePath("storybook-addon-remix-react-router"),
    getAbsolutePath("@storybook/addon-docs"),
    getAbsolutePath("@storybook/addon-a11y"),
    getAbsolutePath("@storybook/addon-vitest"),
  ],
  framework: {
    name: "@storybook/react-vite",
    options: {},
  },
  viteFinal(config, { configType }) {
    const alias = {
      "@/": resolve(process.cwd(), "src"),
    };

    if (configType === "DEVELOPMENT") {
      // Your development configuration goes here
    }
    if (configType === "PRODUCTION") {
      // Your production configuration goes here.
    }

    return mergeConfig(config, {
      resolve: {
        alias,
      },
      define: {
        // Clerk
        "process.env.VITE_PUBLIC_CLERK_SIGN_IN_URL": JSON.stringify(
          process.env.VITE_PUBLIC_CLERK_SIGN_IN_URL,
        ),
        "process.env.VITE_PUBLIC_CLERK_SIGN_UP_URL": JSON.stringify(
          process.env.VITE_PUBLIC_CLERK_SIGN_UP_URL,
        ),
        "process.env.VITE_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL":
          JSON.stringify(
            process.env.VITE_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL,
          ),
        "process.env.VITE_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL":
          JSON.stringify(
            process.env.VITE_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL,
          ),
        "process.env.VITE_PUBLIC_CLERK_PUBLISHABLE_KEY": JSON.stringify(
          process.env.VITE_PUBLIC_CLERK_PUBLISHABLE_KEY,
        ),
        // Google Maps API Key
        "process.env.VITE_PUBLIC_GOOGLE_MAPS_API_KEY": JSON.stringify(
          process.env.VITE_PUBLIC_GOOGLE_MAPS_API_KEY,
        ),
        // Supabase
        "process.env.VITE_PUBLIC_SUPABASE_URL": JSON.stringify(
          process.env.VITE_PUBLIC_SUPABASE_URL,
        ),
        "process.env.VITE_PUBLIC_SUPABASE_ANON_KEY": JSON.stringify(
          process.env.VITE_PUBLIC_SUPABASE_ANON_KEY,
        ),
      },
    });
  },
};
export default config;
