import type { StoryContext } from "@storybook/react-vite";
import type { FunctionComponent } from "react";

import React, { useEffect } from "react";
import { Toaster } from "sonner";

import { ThemeProvider, useTheme } from "../src/components/ui/theme";

import "../src/styles/index.css";

export function ThemeSwitcher({ theme }: { theme: string }) {
  const { setTheme } = useTheme();

  useEffect(() => {
    setTheme(theme);
  }, [theme, setTheme]);

  return null;
}

export function ThemeDecorator(
  Story: FunctionComponent,
  context: StoryContext,
) {
  const theme = context.globals.theme || "light";
  return (
    <ThemeProvider attribute="class" defaultTheme={theme}>
      <ThemeSwitcher theme={theme} />
      <Story />
      <Toaster />
    </ThemeProvider>
  );
}
